package cn.yizhoucp.ump.api.feign.common;


import cn.yizhoucp.ms.core.base.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "ump-services", contextId = "send-gift-check")
public interface SendGiftCheckFeignService {

    /**
     * 送礼验证
     *
     * @param appId
     * @param unionId
     * @param activityCode
     * @return
     */
    @GetMapping("/api/inner/activity/send-gift-check")
    Result<Boolean> sendGiftCheck(@RequestParam("activityCode") String activityCode, @RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid, @RequestParam("ext") String ext);

}
