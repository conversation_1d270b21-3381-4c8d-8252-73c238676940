package cn.yizhoucp.ump.api.feign;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.enums.AppFunctionEnum;
import cn.yizhoucp.ms.core.base.enums.AppScene;
import cn.yizhoucp.ms.core.base.enums.coin.UseCoinScene;
import cn.yizhoucp.ms.core.vo.CommonResultVO;
import cn.yizhoucp.ms.core.vo.Page;
import cn.yizhoucp.ms.core.vo.coinservices.UserPrizeRecordVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.PrizeItem;
import cn.yizhoucp.ump.api.vo.luckBag.DrawGiftProductVO;
import cn.yizhoucp.ump.api.vo.luckBag.LBRoomIndexVO;
import cn.yizhoucp.ump.api.vo.luckBag.LuckyBagProductIndexVO;
import cn.yizhoucp.ump.api.vo.luckBag.LuckyBagProgressVO;
import cn.yizhoucp.ump.api.vo.redPacket.LuckyBagFightIndexVO;
import cn.yizhoucp.ump.api.vo.redPacket.LuckyBagIndexVO;
import cn.yizhoucp.ump.api.vo.redPacket.LuckyDrawResultVO;
import com.alibaba.fastjson.JSONObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * 手气福袋
 *
 * @author: lianghu
 */
@FeignClient(value = "ump-services", contextId = "luckyBagV2")
public interface LuckyBagV2FeignService {

    /**
     * 购买抽奖次数
     *
     * @param count       次数
     * @param useCoupon   是否使用体验券
     * @param scene       场景（TODO 待删除）
     * @return cn.yizhoucp.ms.core.base.Result<com.alibaba.fastjson.JSONObject>
     */
    @Deprecated
    @RequestMapping("/v2/api/inner/lucky-bag/buy-lucky-draw-count")
    Result<JSONObject> buyLuckyDrawCount(@RequestParam("count") Long count,
                                         @RequestParam(value = "useCoupon", required = false) Boolean useCoupon,
                                         @RequestParam(value = "scene", required = false) UseCoinScene scene);

    /**
     * 购买抽奖次数
     *
     * @param count       次数
     * @param useCoupon   是否使用体验券
     * @param appScene    统一场景
     * @param appFunction 功能
     * @return cn.yizhoucp.ms.core.base.Result<com.alibaba.fastjson.JSONObject>
     */
    @RequestMapping("/v2/api/inner/lucky-bag/buy-lucky-draw-count")
    Result<JSONObject> buyLuckyDrawCount(@RequestParam("count") Long count,
                                         @RequestParam(value = "useCoupon", required = false) Boolean useCoupon,
                                         @RequestParam(value = "appScene", required = false) AppScene appScene,
                                         @RequestParam(value = "appFunction", required = false) AppFunctionEnum appFunction);

    @RequestMapping("/v2/api/inner/lucky-bag/buy-lucky-draw-count-by-astrology-value")
    Result<JSONObject> buyLuckyDrawCountByAstrologyValue(@RequestParam("count") Long count);

    /**
     * 开手气福袋
     *
     * @param count        抽奖次数
     * @param luckDrawType 抽奖类型
     * @return LuckyDrawResultVO
     */
    @RequestMapping("/v2/api/inner/lucky-bag/open-lucky-draw")
    Result<LuckyDrawResultVO> openLuckyDraw(@RequestParam("count") Long count, @RequestParam(value = "luckDrawType", required = false) String luckDrawType);

    /**
     * 获取首页信息
     *
     * @param
     * @return LuckyBagIndexVO
     */
    @RequestMapping("/v2/api/inner/lucky-bag/lucky-draw-index")
    Result<LuckyBagIndexVO> luckyDrawIndex();

    @RequestMapping("/v2/api/inner/lucky-bag/send-coupon")
    Result<Boolean> sendCoupon(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uids") String uids, @RequestParam("effectiveDay") Integer effectiveDay, @RequestParam("count") Long count);

    /**
     * 获取用户抽奖首页-打怪版
     *
     * @return LuckyBagIndexVO
     */
    @GetMapping("/v2/api/inner/lucky-bag/lucky-draw-fight-index")
    Result<LuckyBagFightIndexVO> luckyDrawFightIndex();

    /**
     * 获取房间高爆信息
     *
     * @param
     * @return LuckyBagIndexVO
     */
    @RequestMapping("/v2/api/inner/lucky-bag/lucky-draw-room-index")
    Result<LBRoomIndexVO> luckDrawRoomIndex(@RequestParam("from") String from, @RequestParam("relationId") Long relationId);

    /**
     * 获取用户抽奖记录
     *
     * @param pageIndex
     * @param pageSize
     * @param activityId
     * @param bussinessType
     * @return cn.yizhoucp.ms.core.base.Result<cn.yizhoucp.ms.core.vo.Page < cn.yizhoucp.ms.core.vo.coinservices.UserPrizeRecordVO>>
     */
    @RequestMapping(value = "/v2/api/inner/lucky-bag/user-prize-record", method = RequestMethod.GET)
    Result<Page<UserPrizeRecordVO>> getUserPrizeRecord(@RequestParam("pageIndex") Integer pageIndex, @RequestParam("pageSize") Integer pageSize,
                                                       @RequestParam("activityId") Long activityId, @RequestParam(value = "bussinessType", required = false) String bussinessType);

    /**
     * 手气福袋奖池奖品
     *
     * @param luckDrawType 抽奖类型
     * @return List<CoinGiftProductVO>
     */
    @RequestMapping("/v2/api/inner/lucky-bag-draw-pool")
    Result<List<DrawGiftProductVO>> luckDrawPool(@RequestParam("luckDrawType") String luckDrawType);


    /**
     * 手气福袋奖池奖品
     *
     * @return List<CoinGiftProductVO>
     */
    @RequestMapping("/v2/api/inner/lucky-bag-draw-pool-by-room")
    Result<List<DrawGiftProductVO>> luckDrawPoolByRoom(@RequestParam("from") String from, @RequestParam("relationId") Long relationId);

    /**
     * 获取当前的奖池展示
     * @param from
     * @param relationId
     * @return
     */
    @RequestMapping("/v2/api/inner/lucky-bag-draw-pool-key-by-room")
    Result<String> luckDrawPoolKeyByRoom(@RequestParam("from") String from, @RequestParam("relationId") Long relationId);

    /**
     * 手气福袋奖商品列表
     *
     * @param appId   应用id
     * @param unionId 应用唯一标识
     * @param uid     用户id
     * @return LuckyBagProductIndexVO
     */
    @RequestMapping("/v2/api/inner/lucky-bag/product-list")
    Result<LuckyBagProductIndexVO> productList(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid);

    /**
     * 客户端展示过弹窗
     *
     * @return boolean
     */
    @RequestMapping("/v2/api/inner/lucky-bag/show-high-probability-pop-push")
    Result<Boolean> showHighProbabilityPopPush();

    /**
     * 校验用户是否进入活动福袋页
     *
     * @param unionId 应用唯一标识
     * @param appId   应用id
     * @param uid     用户id
     * @return Boolean
     */
    @RequestMapping("/v2/api/inner/lucky-bag/active-condition")
    Result<Boolean> activeCondition(@RequestParam("unionId") String unionId, @RequestParam("appId") Long appId, @RequestParam("uid") Long uid);

    /**
     * 是否全服高爆
     * @param unionId
     * @param appId
     * @param uid
     * @return
     */
    @RequestMapping("/v2/api/inner/lucky-bag/active-high-probability")
    public Result<Boolean> activeHighProbability(@RequestParam("unionId") String unionId, @RequestParam("appId") Long appId, @RequestParam("uid") Long uid);

    /**
     * 获取全服福气值
     *
     * @return Long
     */
    @RequestMapping("/v2/api/inner/lucky-bag/blessing-value")
    Result<LuckyBagProgressVO> getBlessingValue();

    /**
     * 手动关闭高爆
     *
     * @param appId 应用id
     * @return Boolean
     */
    @RequestMapping("/v2/api/inner/lucky-bag/close-high-probability")
    Result<Boolean> closeHighProbability(@RequestParam("appId") Long appId);


    @RequestMapping("/v2/api/inner/lucky-bag/luck-draw-click-luck")
    Result<CommonResultVO> userClickRoomBubble(@RequestParam("from") String from, @RequestParam("relationId") Long relationId, @RequestParam("bubbleKey") String bubbleKey, @RequestParam("bubbleUserId") Long bubbleUserId);

    @GetMapping("/v2/api/inner/astrology/get-replace-gift")
    Result<Map<Long, PrizeItem>> getReplaceGift(@RequestParam("uid") Long uid);

}
