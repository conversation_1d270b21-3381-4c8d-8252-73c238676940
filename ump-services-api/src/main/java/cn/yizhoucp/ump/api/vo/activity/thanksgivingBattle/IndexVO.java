package cn.yizhoucp.ump.api.vo.activity.thanksgivingBattle;

import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class IndexVO {

    /** 按 1-5 排 */
    private List<Integer> ptspGiftNum;
    private UserVO userVO;
    private UserVO toUserVO;
    /** 场景 1-7 */
    private Integer scene;
    /** 甜蜜值 */
    private Long sweetValue;
    private RankVO board;

}
