package cn.yizhoucp.ump.api.feign.activity;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ump.api.vo.activity.inception.InceptionIndexVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "ump-services", contextId = "inception")
public interface InceptionFeign {

    @GetMapping("/api/inner/activity/inception/exchange")
    Result<InceptionIndexVO.Gift> exchange(@RequestParam("giftCode") String giftCode);

    @GetMapping("/api/inner/activity/inception/draw-log")
    Result<Object> getDrawLog(@RequestParam("activityCode") String activityCode, @RequestParam("poolCode") String poolCode);

    @GetMapping("/api/inner/activity/inception/claim_reward")
    Result<List<InceptionIndexVO.Gift>> claimReward();

    @GetMapping("/api/inner/activity/inception/get-date-rank")
    Result<InceptionIndexVO.Rank> getDateRank(@RequestParam("date") String date);

    @GetMapping("/api/inner/activity/inception/get-total-rank")
    Result<InceptionIndexVO.Rank> getTotalRank();

    @GetMapping("/api/inner/activity/inception/send-total-rank")
    Result<Boolean> sendTotalRank();

    @GetMapping("/api/inner/activity/inception/send-daily-rank")
    Result<Boolean> sendTotalRank(@RequestParam(value = "date", required = false) String date);



}
