package cn.yizhoucp.ump.api.vo.luckBag.alchemy;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Description 炼金炉抽奖参数VO
 * @date 2022-11-20 14:12
 */
@Data
public class LuckDrawVO {

    /** 礼物1key */
    @NotNull(message = "礼物不可为空")
    private String leftGiftKey;
    /** 礼物1个数 */
    @NotNull(message = "礼物数量不可为空")
    @Min(1)
    private Integer leftNum;
    /** 礼物2key */
    @NotNull(message = "礼物不可为空")
    private String rightGiftKey;
    /** 礼物1个数 */
    @NotNull(message = "礼物数量不可为空")
    @Min(1)
    private Integer rightNum;

    //上报埋点使用的
    private Long leftGiftCoin;
    private Long rightGiftCoin;
    private Long leftGiftTotalCoin;
    private Long rightGiftTotalCoin;



}
