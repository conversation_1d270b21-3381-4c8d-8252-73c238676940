package cn.yizhoucp.ump.api.feign;

import cn.yizhoucp.dto.PiggyIndexDTO;
import cn.yizhoucp.dto.SweetCabinDTO;
import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.vo.CommonResultVO;
import cn.yizhoucp.ump.api.vo.officialCombine.sweetCabin.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 官宣甜蜜小屋
 *
 * <AUTHOR>
 */
@FeignClient(value = "ump-services", contextId = "sweetCabin")
public interface SweetCabinFeignService {

    /**
     * 校验是否有小屋
     *
     * @param uid      用户id
     * @param otherUid 对方id
     * @return Boolean
     */
    @GetMapping("/api/inner/sweet-cabin/check-cabin-exist")
    Result<Boolean> checkCabinExist(@RequestParam("uid") Long uid, @RequestParam("otherUid") Long otherUid);

    /**
     * 获取小屋信息
     *
     * @param uid      用户id
     * @param otherUid 对方id
     * @return SweetCabinDTO
     */
    @GetMapping("/api/inner/sweet-cabin/get-sweet-cabin-info")
    Result<SweetCabinDTO> getSweetCabinInfo(@RequestParam("uid") Long uid, @RequestParam("otherUid") Long otherUid);

    /**
     * 获取小屋信息
     *
     * @param id 用户id
     * @return SweetCabinDTO
     */
    @GetMapping("/api/inner/sweet-cabin/get-sweet-cabin-info-by-id")
    Result<SweetCabinDTO> getSweetCabinInfo(@RequestParam("id") Long id);

    /**
     * 购买小屋
     *
     * @param unionId  唯一标识
     * @param appId    应用id
     * @param uid      用户id
     * @param otherUid 对方id
     * @return CommonResultVO
     */
    @GetMapping("/api/inner/sweet-cabin/buy-sweet-cabin")
    Result<CommonResultVO> buySweetCabin(@RequestParam("unionId") String unionId, @RequestParam("appId") Long appId,
                                         @RequestParam("uid") Long uid, @RequestParam("otherUid") Long otherUid);

    /**
     * 获取小屋主页信息
     *
     * @param uid      用户id
     * @param otherUid 对方id
     * @param sex      用户性别
     * @param enter    是否进入小屋
     * @return SweetCabinInfoVO
     */
    @GetMapping("/api/inner/sweet-cabin/home-page")
    Result<SweetCabinInfoVO> getHomePage(@RequestParam("uid") Long uid, @RequestParam("otherUid") Long otherUid,
                                         @RequestParam("sex") String sex, @RequestParam("enter") Boolean enter);

    /**
     * 获取对方最近活跃时长
     *
     * @param id  小屋id
     * @param uid 用户id
     * @return Long
     */
    @GetMapping("/api/inner/sweet-cabin/get-other-active-time")
    Result<Long> getOtherActiveTime(@RequestParam("id") Long id, @RequestParam("uid") Long uid);

    /**
     * 获取小屋等级奖励
     *
     * @param id 小屋id
     * @return CabinPrizeListVO
     */
    @GetMapping("/api/inner/sweet-cabin/get-level-prize-list")
    Result<CabinPrizeListVO> getLevelPrizeList(@RequestParam("id") Long id);

    /**
     * 设置小屋公开/隐藏
     *
     * @param id     小屋id
     * @param action 动作
     * @return Boolean
     */
    @GetMapping("/api/inner/sweet-cabin/set-status")
    Result<Boolean> setCabinStatus(@RequestParam("id") Long id, @RequestParam("action") Boolean action);

    /**
     * 用户展示过升级信息
     *
     * @param appId 应用id
     * @param uid   用户id
     * @param id    小屋id
     * @return Boolean
     */
    @GetMapping("/api/inner/sweet-cabin/show-upgrade")
    Result<Boolean> showUpgrade(@RequestParam("appId") Long appId, @RequestParam("uid") Long uid,
                                @RequestParam("id") Long id);

    /**
     * 获取恋屋币日志记录
     *
     * @param id     小屋id
     * @param lastId 上一页记录
     * @return List<CabinLhLogVO>
     */
    @GetMapping("/api/inner/sweet-cabin/cabin-lh-log-list")
    Result<List<CabinLhLogVO>> getCabinLhLogList(@RequestParam("id") Long id, @RequestParam("lastId") Long lastId);

    /**
     * 获取足迹列表
     *
     * @param id     小屋id
     * @return List<CabinLhLogVO>
     */
    @GetMapping("/api/inner/sweet-cabin/cabin-record-list")
    Result<List<String>> getCabinRecordList(@RequestParam("id") Long id);

    /**
     * 拾取恋屋币
     *
     * @param uid 用户id
     * @param id  记录id
     * @return Boolean
     */
    @GetMapping("/api/inner/sweet-cabin/pick-up-lh")
    Result<Boolean> pickUpLh(@RequestParam("appId") Long appId, @RequestParam("uid") Long uid, @RequestParam("id") Long id);

    /**
     * 兑换恋屋币
     *
     * @param unionId 应用唯一标识
     * @param appId   应用id
     * @param uid     用户id
     * @param coin    兑换数量
     * @return Boolean
     */
    @GetMapping("/api/inner/sweet-cabin/exchange-lh")
    Result<Boolean> exchangeLh(@RequestParam("unionId") String unionId, @RequestParam("appId") Long appId,
                               @RequestParam("uid") Long uid, @RequestParam("id") Long id, @RequestParam("coin") Long coin);

    /**
     * 擦亮小屋
     *
     * @param uid  用户id
     * @param id   小屋id
     * @param time 次数
     * @return Boolean
     */
    @GetMapping("/api/inner/sweet-cabin/polish")
    Result<Boolean> polishCabin(@RequestParam("uid") Long uid, @RequestParam("id") Long id, @RequestParam("time") Integer time);

    /**
     * 装修小屋
     *    加靓屋值
     *
     * @param unionId 应用唯一标识
     * @param appId   应用id
     * @param uid     用户id
     * @param id      小屋id
     * @param time    次数
     * @return Boolean
     */
    @GetMapping("/api/inner/sweet-cabin/decorate")
    Result<Boolean> decorateCabin(@RequestParam("unionId") String unionId, @RequestParam("appId") Long appId, @RequestParam("uid") Long uid,
                                         @RequestParam("id") Long id, @RequestParam("time") Integer time);

    /**
     * 获取抽奖奖励列表
     *
     * @param unionId 应用唯一标识
     * @param uid     用户id
     * @param id      小屋id
     * @return PrizeListVO
     */
    @GetMapping("/api/inner/sweet-cabin/get-prize-list")
    Result<PrizeListVO> getPrizeList(@RequestParam("unionId") String unionId, @RequestParam("uid") Long uid, @RequestParam("id") Long id);

    /**
     * 获取排行榜信息
     *
     * @param id  小屋id
     * @param uid 用户id
     * @return RankVO
     */
    @GetMapping("/api/inner/sweet-cabin/ranking")
    Result<RankVO> getRanking(@RequestParam("id") Long id, @RequestParam("uid") Long uid);

    /**
     * 获取储钱罐页面
     *
     * @param id  小屋id
     * @return PiggyIndexDTO
     */
    @GetMapping("/api/inner/sweet-cabin/piggy-index")
    Result<PiggyIndexDTO> getPiggyIndex(@RequestParam("id") Long id);

    /**
     * 兑换礼物
     *
     * @param id    小屋id
     * @param uid   用户id
     * @param key   礼物key
     * @param count 兑换数量
     * @return Boolean
     */
    @GetMapping("/api/inner/sweet-cabin/exchange-gift")
    Result<Boolean> exchangeGift(@RequestParam("id") Long id, @RequestParam("uid") Long uid,
                                       @RequestParam("key") String key, @RequestParam("count") Integer count);

    /**
     * 获取小屋任务数据
     *
     * @param id  小屋id
     * @param uid 用户id
     * @return CabinTaskVO
     */
    @GetMapping("/api/inner/sweet-cabin/cabin-task")
    Result<CabinTaskVO> getCabinTask(@RequestParam("id") Long id, @RequestParam("uid") Long uid);

    /**
     * 获取小屋任务栏数据
     *
     * @param appId 应用id
     * @param uid   用户id
     * @return CabinTaskbarVO
     */
    @GetMapping("/api/inner/sweet-cabin/cabin-taskbar")
    Result<CabinTaskbarVO> getCabinTaskbar(@RequestParam("appId") Long appId, @RequestParam("uid") Long uid);

    /**
     * 领取任务奖励
     *
     * @param id   小屋id
     * @param uid  用户id
     * @param code 任务code
     * @return Boolean
     */
    @GetMapping("/api/inner/sweet-cabin/receive-prize")
    Result<Boolean> receivePrize(@RequestParam("id") Long id, @RequestParam("uid") Long uid, @RequestParam("code") String code);

    /**
     * 获取续期商品
     *
     * @param id  小屋id
     * @param uid 用户id
     * @return RenewalProductVO
     */
    @GetMapping("/api/inner/sweet-cabin/renewal-product-list")
    Result<RenewalProductVO> getRenewalProductList(@RequestParam("id") Long id, @RequestParam("uid") Long uid);

    /**
     * 官宣续期
     *
     * @param id   小屋id
     * @param uid  用户id
     * @param code 商品code
     * @return Boolean
     */
    @GetMapping("/api/inner/sweet-cabin/exchange-renewal-product")
    Result<Boolean> exchangeRenewalProduct(@RequestParam("id") Long id, @RequestParam("uid") Long uid,
                                                    @RequestParam("code") String code);

    /**
     * 续期任务提醒
     *
     * @param appId 应用id
     * @param id    小屋id
     * @param uid   用户id
     * @param from  来源
     * @return Boolean
     */
    @GetMapping("/api/inner/sweet-cabin/renewal-notice")
    Result<Boolean> notice(@RequestParam("appId") Long appId, @RequestParam("id") Long id, @RequestParam("uid") Long uid, @RequestParam("from") String from);

}
