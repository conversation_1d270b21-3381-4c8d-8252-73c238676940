package cn.yizhoucp.ump.api.feign.admin;

import cn.yizhoucp.dto.activitySkinDTO.ActivitySkinAdminUrlResponseDTO;
import cn.yizhoucp.dto.activitySkinDTO.ActivitySkinDirectoryRequestDTO;
import cn.yizhoucp.dto.activitySkinDTO.ActivitySkinDirectoryResponseDTO;
import cn.yizhoucp.dto.activitySkinDTO.ActivitySkinResourceRequestDTO;
import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ump.config.JacksonConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "ump-services", contextId = "activity-skin-resource", configuration = JacksonConfig.class)
public interface ActivitySkinResourceAdminFeignService {


    @PostMapping("/api/inner/ump/activity-skin/directory/save")
    Result<Boolean> save(@RequestBody ActivitySkinDirectoryRequestDTO param);

    @GetMapping("/api/inner/ump/activity-skin/directory/delete")
    Result<Boolean> delete(@RequestParam("resourceKey") String resourceKey);

    @GetMapping("/api/inner/ump/activity-skin/directory/get")
    Result<ActivitySkinDirectoryResponseDTO> get(@RequestParam(value = "resourceKey", required = false) String resourceKey, @RequestParam(value = "parentKey", required = false) String parentKey, @RequestParam(value = "page", defaultValue = "1") Integer page, @RequestParam(value = "size", defaultValue = "10") Integer size);

    @GetMapping("/api/inner/ump/activity-skin/resource/get")
    Result<Object> getResource(@RequestParam("resourceKey") String resourceKey);

    @PostMapping("/api/inner/ump/activity-skin/resource/save")
    Result<Boolean> saveResource(@RequestBody ActivitySkinResourceRequestDTO param);

    @GetMapping("/api/inner/ump/activity-skin/resource/get-admin-url")
    Result<List<ActivitySkinAdminUrlResponseDTO>> getAdminUrl(@RequestParam(value = "activityCode",required = false) String activityCode);
}
