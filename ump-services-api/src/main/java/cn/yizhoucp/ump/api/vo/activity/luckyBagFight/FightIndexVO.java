package cn.yizhoucp.ump.api.vo.activity.luckyBagFight;

import cn.yizhoucp.ump.api.vo.activity.luckyBagFight.inner.RankItem;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 打怪兽主页
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FightIndexVO {

    /** 用户信息 */
    private UserInfoVO userInfo;

    /** 轮播信息 */
    private List<String> noticeList;

    /** 伤害排行榜 */
    private List<RankItem> rankList;

    /** 打怪截止时间 */
    private DeadlineVO deadline;

    /** 怪物信息 */
    private List<MonsterInfoVO> monsterInfoList;

    /** 武器信息 */
    private UserWeaponVO weaponInfo;

    private String hurtValueDesc;

    private Long roomFamilyId;

    private String pageMonsterType;

    /** 是否轮训查询结果 */
    private Integer loopTimeMillis = -1;

}
