package cn.yizhoucp.ump.api.feign.astrology;

import cn.yizhoucp.ms.core.base.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "ump-services", contextId = "astrology-room-rank")
public interface AstrologyRoomRankFeignService {
    /**
     *
     * @param roomIds JSONObject.toJSONString(List<Long> roomIds)
     * @return
     */
    @GetMapping("/api/inner/astrology-room-rank/get-top-by-room-ids")
    Result<Long> getTopByRoomIds(@RequestParam("roomIds") String roomIds);

    @GetMapping("/api/inner/astrology-room-rank/top-room-ids-page")
    Result<List<Long>> topRoomIdsPage(@RequestParam("start") Long start, @RequestParam("size") Long size);
}
