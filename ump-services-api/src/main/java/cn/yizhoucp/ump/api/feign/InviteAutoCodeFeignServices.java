package cn.yizhoucp.ump.api.feign;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ump.api.param.activity.ReportDownloadParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;

@FeignClient(value = "ump-services", contextId = "invite-auto-code")
public interface InviteAutoCodeFeignServices {

    @PostMapping("/api/inner/ump/auto-code/report-download")
    Result<Boolean> reportDownload(@RequestParam("uaStr") String uaStr, @RequestParam("ip") String ip, @RequestBody ReportDownloadParam param);
    @PostMapping("/api/inner/ump/auto-code/match-code")
    Result<String> matchCode(@RequestParam("uaStr") String uaStr, @RequestParam("ip") String ip, @RequestBody ReportDownloadParam param);
}
