package cn.yizhoucp.ump.api.feign.activity.astrology;

import cn.yizhoucp.ms.core.base.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(
        value = "ump-services",
        contextId = "star-wish-card-collection-game"
)
public interface StarWishCardCollectionGameFeignService {

    @GetMapping("/api/inner/activity/star_wish_card_collection_game/exchange_card")
    Result<List<Integer>> exchangeCard(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid, @RequestParam("cardCode") String cardCode);

    @GetMapping("/api/inner/activity/star_wish_card_collection_game/mission_receive")
    Result<Boolean> missionReceive(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid, @RequestParam("taskCode") String taskCode);

}
