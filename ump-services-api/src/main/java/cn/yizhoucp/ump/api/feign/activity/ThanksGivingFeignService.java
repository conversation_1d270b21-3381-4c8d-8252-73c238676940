package cn.yizhoucp.ump.api.feign.activity;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.vo.activity.thanksgiving.DrawLogVO;
import cn.yizhoucp.ump.api.vo.activity.thanksgiving.RankVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 感恩节活动
 *
 * @author: lianghu
 */
@FeignClient(value = "ump-services", contextId = "thanksGivingActivity")
public interface ThanksGivingFeignService {

    @GetMapping("/api/inner/activity/thanks-giving/get-rank-info")
    Result<RankVO> getRankInfo(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid);

    @GetMapping("/api/inner/activity/thanks-giving/get-draw-log-info")
    Result<DrawLogVO> getDrawLogInfo(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid);

}
