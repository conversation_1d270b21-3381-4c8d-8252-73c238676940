package cn.yizhoucp.ump.api.feign.activity;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.vo.activity.activity520.IndexVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.DrawReturn;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

@FeignClient(value = "ump-services", contextId = "activity520")
public interface Activity520FeignService {

    @GetMapping("/api/inner/activity/activity-520/get-index")
    Result<IndexVO> getIndex(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid);

    @GetMapping("/api/inner/activity/activity-520/take-prize")
    Result<DrawReturn> takePrize(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid, @RequestParam("index") Integer index);

    @GetMapping("/api/inner/activity/activity-520/get-chat-symbol")
    Result<Map<Long, Boolean>> getChatSymbolMap(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid, @RequestParam("toUidListStr") String toUidListStr);
}
