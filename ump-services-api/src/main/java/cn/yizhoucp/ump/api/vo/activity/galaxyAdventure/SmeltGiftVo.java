package cn.yizhoucp.ump.api.vo.activity.galaxyAdventure;

import cn.yizhoucp.ump.api.vo.jimu.draw.PrizeItem;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 银河熔炼实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SmeltGiftVo {

    /**
     * 子活动码
     */
    private String subActivityCode;


    /**
     * 熔炼方案
     */
    private List<PrizeStrategy> strategyList;

}
