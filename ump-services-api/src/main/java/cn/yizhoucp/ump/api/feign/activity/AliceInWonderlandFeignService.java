package cn.yizhoucp.ump.api.feign.activity;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.vo.umpServices.activity.common.jimu.DrawReturn;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "ump-services", contextId = "alice-in-Wonderland")
public interface AliceInWonderlandFeignService {

    @GetMapping("/api/inner/activity/alice_in_wonderland/draw")
    Result<DrawReturn> draw(@RequestParam("type") String type, @RequestParam("poolCode") String poolCode, @RequestParam("times") Integer times, @RequestParam("extValue") String extValue);
}
