package cn.yizhoucp.ump.api.vo.activity.legendaryStarPark;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class LegendaryStarParkRankVO {
    private LotteryInfo lotteryInfo;
    private MyselfRank myselfRank;
    private List<RankList> rankList;

    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    @Builder
    public static class LotteryInfo {
        private long totalDraws;
        private long currentDraws;
        private List<Task> tasks;
    }

    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    @Builder
    public static class Task {
        private long taskId;
        private long requiredDraws;
        private long isRewardClaimed;
        private GiftInfo giftInfo;
    }

    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    @Builder
    public static class GiftInfo {
        private String giftIcon;
        private String giftName;
        private Long giftValue;
    }

    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    @Builder
    public static class MyselfRank {
        private Long id;
        private Long rank;
        private String icon;
        private String name;
        private Long value;
        private Long currentCheckpoint;
        private List<MyselfRankDrawnGiftList> drawnGiftList;
    }

    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    @Builder
    public static class MyselfRankDrawnGiftList {
        private String giftName;
        private String giftIcon;
        private Long giftValue;
        private Long drawnCount;
    }

    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    @Builder
    public static class RankList {
        private long id;
        private long rank;
        private String icon;
        private String name;
        private long value;
        private List<MyselfRankDrawnGiftList> drawnGiftList;
    }

    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    @Builder
    public static class RankListDrawnGiftList {
        private String giftName;
        private String giftIcon;
        private long giftValue;
        private long drawnCount;
    }
}