package cn.yizhoucp.ump.api.feign;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ump.api.vo.depth.CardiacLuckDrawPrizeVO;
import com.alibaba.fastjson.JSONObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "ump-services", contextId = "ump-depth-draw")
public interface DepthDrawFeignService {
    @RequestMapping("/api/inner/ump/depth/draw")
    Result<CardiacLuckDrawPrizeVO> draw(@RequestParam("drawPoolTypeKey") String drawPoolTypeKey,
                                               @RequestParam("useDown") Boolean useDown);

    @RequestMapping("/api/inner/ump/depth/draw-pool")
    Result<List<JSONObject>> getCardiacLuckDrawPool();
}
