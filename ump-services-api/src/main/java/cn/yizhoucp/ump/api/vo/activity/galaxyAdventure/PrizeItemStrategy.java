package cn.yizhoucp.ump.api.vo.activity.galaxyAdventure;

import cn.yizhoucp.ump.api.vo.jimu.draw.PrizeItem;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 占星策略兑换方案
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrizeItemStrategy {



    private PrizeItem originPrize;

    /**
     * 兑换要求数量
     */
    private Integer requireNum;


}
