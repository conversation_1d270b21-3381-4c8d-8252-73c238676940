package cn.yizhoucp.ump.api.vo.officialCombine.sweetCabin;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 甜蜜小屋任务信息
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CabinTaskVO {

    /** 已领取浪漫值 */
    private Double romanticValue;
    /** 总浪漫值 */
    private Long totalRomanticValue;
    /** 是否展示新手引导 */
    private Boolean showGuide;
    /** 任务详情 */
    private List<CabinTaskDetailVO> taskList;

}
