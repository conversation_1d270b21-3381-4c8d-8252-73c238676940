package cn.yizhoucp.ump.api.feign.activity;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ump.api.vo.activity.goddessFestival2023.IndexVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @Date 2023/2/22 17:16
 * @Version 1.0
 */
@FeignClient(value = "ump-services", contextId = "goddessFestival2023FeignService")
public interface GoddessFestival2023FeignService {

    @GetMapping("/api/inner/activity/goddess-festival-2023/get-index")
    Result<IndexVO> getIndex(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId,
                             @RequestParam("uid") Long uid);

}
