package cn.yizhoucp.ump.api.feign.astrology;

import cn.yizhoucp.ms.core.base.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

@FeignClient(value = "ump-services", contextId = "astrology-call-back-job")
public interface AstrologyCallBackJobFeignService {
    @GetMapping("/api/job/ump/astrology-call-back/send-message")
    Result sendMessage();
}
