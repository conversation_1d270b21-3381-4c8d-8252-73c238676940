package cn.yizhoucp.ump.api.vo.luckBag.style;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 首页样式（打怪版）
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FightIndexStyleVO implements Serializable {

    /** 标题 */
    private String indexTitleImg;
    /** 福袋背景 */
    private String indexBgImg;
    /** 顶部手气福袋按钮背景 */
    private List<TopBtnVO> indexTopBtnList;
    /** 武器列表 */
    private List<WeaponVO> indexWeaponList;
    /** 武器介绍 */
    private String indexWeaponDesc;
    /** 武器规则图 */
    private String indexWeaponRuleImg;
    /** 弹幕背景 */
    private String indexBarrageBgImg;
    /** 按钮背景 */
    private String indexBtnBg;
    /** 礼物区背景底色 */
    private String indexGiftBgColor;
    /** 单抽按钮 */
    private String indexDrawBtn1;
    /** 十抽按钮 */
    private String indexDrawBtn10;
    /** 百抽按钮 */
    private String indexDrawBtn100;
    /** 福袋 icon */
    private String indexBagIcon;
    /** 保底背景 */
    private String indexGuarantBgImg;
    /** 保底文案背景 */
    private String indexGuarantTextColor;
}
