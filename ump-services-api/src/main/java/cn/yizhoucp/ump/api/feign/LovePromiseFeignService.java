package cn.yizhoucp.ump.api.feign;

import cn.yizhoucp.dto.LovePromiseDTO;
import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.vo.landingservices.HomeLovePromiseVO;
import cn.yizhoucp.ump.api.vo.lovepromise.LovePromiseBuyRingVO;
import cn.yizhoucp.ump.api.vo.lovepromise.LovePromiseDetailVO;
import cn.yizhoucp.ump.api.vo.lovepromise.LovePromiseRingListVO;
import cn.yizhoucp.ump.api.vo.lovepromise.LovePromiseWallVO;
import cn.yizhoucp.ump.api.vo.lovepromise.UserIntimacyVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "ump-services", contextId = "love-promise")
public interface LovePromiseFeignService {

    @GetMapping("/api/inner/love-promise/love-promise-wall")
    Result<LovePromiseWallVO> lovePromiseWall();

    @GetMapping("/api/inner/love-promise/can-apply-list")
    Result<List<UserIntimacyVO>> canApplyList(@RequestParam("uid") Long uid);

    @GetMapping("/api/inner/love-promise/can-apply-list-with-from")
    Result<List<UserIntimacyVO>> canApplyList(@RequestParam("uid") Long uid, @RequestParam("from") String from);

    @GetMapping("/api/inner/love-promise/getProposeInfoByUid1AndUid2")
    Result<Boolean> getProposeInfoByUid1AndUid2(@RequestParam("uid") Long uid,@RequestParam("toUid") Long toUid);

    @GetMapping("/api/inner/love-promise/getProposeInfoByUid1AndUid2V2")
    Result<Boolean> getProposeInfoByUid1AndUid2V2(@RequestParam("uid") Long uid,@RequestParam("toUid") Long toUid);

    @GetMapping("/api/inner/love-promise/ring-list")
    Result<LovePromiseRingListVO> ringList(@RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid);

    @GetMapping("/api/inner/love-promise/buy-ring")
    Result<LovePromiseBuyRingVO> buyRing(@RequestParam("toUid") Long toUid, @RequestParam("ringId") Long ringId);

    @GetMapping("/api/inner/love-promise/buy-ring-with-from")
    Result<LovePromiseBuyRingVO> buyRing(@RequestParam("toUid") Long toUid, @RequestParam("ringId") Long ringId, @RequestParam("from") String from);

    @GetMapping("/api/inner/love-promise/choose-ceremony")
    Result chooseCeremony(@RequestParam("lovePromiseId") Long lovePromiseId, @RequestParam("type") String type, @RequestParam(value = "ceremonyOpenTime", required = false) String ceremonyOpenTime);

    @GetMapping("/api/inner/love-promise/ceremony-index")
    Result<LovePromiseDetailVO> ceremonyIndex(@RequestParam("lovePromiseId") Long lovePromiseId);

    @GetMapping("/api/inner/love-promise/complete")
    Result<LovePromiseDTO> complete(@RequestParam("lovePromiseId") Long lovePromiseId);

    @GetMapping("/api/inner/love-promise/cert")
    Result<LovePromiseDetailVO> cert(@RequestParam("lovePromiseId") Long lovePromiseId);

    @GetMapping("/api/inner/love-promise/cancel")
    Result cancel(@RequestParam("lovePromiseId") Long lovePromiseId);

    @GetMapping("/api/inner/love-promise/show-detail-by-uid")
    Result<HomeLovePromiseVO> getShowDetailByUid(@RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid);

    @GetMapping("/api/inner/love-promise/have-promise-record")
    Result<Boolean> havePromiseRecord(@RequestParam("uid1") Long uid1, @RequestParam("uid2") Long uid2);

    @GetMapping("/api/inner/love-promise/edit-ceremony-time")
    Result editCeremonyTime(@RequestParam("lovePromiseId") Long lovePromiseId, @RequestParam("ceremonyOpenTime") String ceremonyOpenTime);

    @GetMapping("/api/inner/love-promise/get-list-by-room-ids")
    Result<List<Long>> getListByRoomIds(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("vestChannel") String vestChannel, @RequestParam("uid") Long uid, @RequestParam("roomIds") List<Long> roomIds);

    @GetMapping("/api/inner/love-promise/get-by-room-id")
    Result<LovePromiseDetailVO> getByRoomId(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("vestChannel") String vestChannel, @RequestParam("uid") Long uid, @RequestParam("roomId") Long roomId);

    @GetMapping("/api/inner/love-promise/enter-room")
    Result<LovePromiseDTO> enterRoom(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("vestChannel") String vestChannel, @RequestParam("uid") Long uid, @RequestParam("lovePromiseId") Long lovePromiseId);

}
