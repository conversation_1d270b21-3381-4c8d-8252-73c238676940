package cn.yizhoucp.ump.api.feign.activity;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ump.api.vo.activity.carnival.IndexVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.PrizeItem;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;

/**
 * 占星狂欢月
 */
@FeignClient(value = "ump-services", contextId = "carnivalMonth")
public interface CarnivalMonthFeignServices {
    @GetMapping("/api/inner/ump/carnival/index")
    Result<IndexVO> index();

    @GetMapping("/api/inner/ump/carnival/open-luck-bag")
    Result<PrizeItem> openLuckBag(@RequestParam("luckBagIndex") Integer luckBagIndex);

    @GetMapping("/api/inner/ump/carnival/receive-append-reward")
    Result<PrizeItem> receiveAppendReward(@RequestParam("taskIndex") Integer taskIndex);
}
