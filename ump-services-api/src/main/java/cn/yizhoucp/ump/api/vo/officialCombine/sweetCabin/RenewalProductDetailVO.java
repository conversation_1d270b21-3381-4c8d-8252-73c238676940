package cn.yizhoucp.ump.api.vo.officialCombine.sweetCabin;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 官宣续期商品
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RenewalProductDetailVO {

    /** 商品code */
    private String code;
    /** 商品icon */
    private String icon;
    /** 天数 */
    private Integer days;
    /** 价格 */
    private Long price;
    /** 状态 0-不可兑换；1-可兑换 */
    private Boolean canExchange;

}
