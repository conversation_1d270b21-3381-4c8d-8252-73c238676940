package cn.yizhoucp.ump.api.vo.officialCombine.sweetCabin;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 甜蜜小屋信息
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SweetCabinInfoVO {

    private Long id;
    /** 恋屋币 */
    private Long lhCoin;
    /** 当前等级 */
    private Integer level;
    /** 甜蜜值 */
    private Long sweetValue;
    /** 下一等级甜蜜值 */
    private Long nextLevelSweetValue;
    /** 下一等级靓屋值 */
    private Long nextLevelBeautifulValue;
    /** 靓屋值 */
    private Long beautifulValue;
    /** 是否展示升级信息 */
    private Boolean showUpgrade;
    /** 是否展示擦亮小屋提示 */
    private Boolean showPolish;
    /** 是否展示装修小屋提示 */
    private Boolean showDecorate;
    /** 小屋是否公开(true-公开/false-隐藏) */
    private Boolean open;
    /** 小屋信息 */
    private List<CabinBaseVO> cabinList;
    /** 待领取恋屋币详情 */
    private List<CabinLhRecordVO> recordList;
    /** 存在状态 old；new；none */
    private String existStatus;
    /** 对方活跃时间 */
    private Long otherActiveTime;
    /** 是否展示任务 */
    private Boolean showTask;
    /** 是否展示任务新手引导 */
    private Boolean showTaskGuide;

}
