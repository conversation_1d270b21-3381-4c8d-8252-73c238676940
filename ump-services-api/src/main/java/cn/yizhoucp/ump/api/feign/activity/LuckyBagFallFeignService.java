package cn.yizhoucp.ump.api.feign.activity;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.enums.EnvType;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.vo.activity.luckyBagFall.IndexVO;
import com.google.common.collect.Lists;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Objects;

@FeignClient(value = "ump-services", contextId = "luckyBagFall")
public interface LuckyBagFallFeignService {

    @GetMapping("/api/inner/activity/lucky-bag-fall/get-index")
    Result<IndexVO> getIndex(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid);


    @GetMapping("/api/inner/activity/lucky-bag-fall/exchange")
    Result<Boolean> exchange(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid);

}
