package cn.yizhoucp.ump.api.vo.activity.thanksgiving;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 抽奖记录
 *
 * @author: lianghu
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DrawLogItem implements Serializable {

    private String id;
    /** 奖品文案 */
    private String text;
    /** 日期文案 */
    private String date;
    /** 扣除资源文案 */
    private String deductText;

}
