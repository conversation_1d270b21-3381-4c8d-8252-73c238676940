package cn.yizhoucp.ump.api.feign.activity;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ump.api.vo.activity.luckyBagHunt.IndexVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "ump-services", contextId = "luckyBagHuntFeignService")
public interface LuckyBagHuntFeignService {

    @GetMapping("/api/inner/lucky-bag-hunt/get-index")
    Result<IndexVO> getIndex(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid);

}
