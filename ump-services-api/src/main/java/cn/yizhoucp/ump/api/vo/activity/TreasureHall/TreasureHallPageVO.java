package cn.yizhoucp.ump.api.vo.activity.TreasureHall;

import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import lombok.*;

import java.util.List;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class TreasureHallPageVO {

    private Long drawItem;
    private List<DrawGift> drawGifts;
    private List<Task> taskList;
    private List<ExchangeGift> exchangeGifts;
    private GuardianRoom guardianRoom;
    private RankVO totalRank;


    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class GuardianRoom {
        private RoomInfo roomInfo;
        private UserInfo userInfo;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class UserInfo {
        private String userName;
        private String userAvatar;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class RoomInfo {
        private String roomName;
        private String roomAvatar;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Getter
    @Setter
    public static class Gift {
        private String giftKey;
        private String giftName;
        private Long giftValue;
        private String giftIcon;
        private Long giftNum;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class DrawGift extends Gift {
        private String giftType;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class ExchangeGift extends Gift {
        private Long exchangeNum;
        private Boolean isExchange;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class Task {
        private Integer id;
        private Boolean isRewarded;
        private Long progress;
        private Long totalProgress;
    }

}
