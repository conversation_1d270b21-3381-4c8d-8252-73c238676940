package cn.yizhoucp.ump.api.vo.activity.lovePostcard;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 活动主页
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IndexVO {

    /** 弹幕列表 */
    private List<CityPostcardVO> cityList;
    /** 弹幕列表 */
    private List<String> barrageList;
    /** 邮票数 */
    private Integer stampNum;
    /** 金邮票数 */
    private Integer goldStampNum;
    /** 活动结束时间 */
    private Long activityEndTime;
    /** 可抽奖次数 */
    private Integer drawCount;
    /** 交友大厅id */
    private Long chatroomId;
    /** 家族id */
    private Long familyId;
    /** 家族会话id */
    private Long chatId;

}
