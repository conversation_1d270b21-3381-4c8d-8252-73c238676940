package cn.yizhoucp.ump.api.feign.activity;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ump.api.vo.activity.kingAdvanced.DateKingLeaderboardVO;
import com.alibaba.fastjson.JSONObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
        value = "ump-services",
        contextId = "kingAdvance"
)
public interface KingAdvanceFeignService {


    @GetMapping("/api/inner/activity/king-advanced/kingDate")
    Result<JSONObject> kingDate(@RequestParam("activityCode") String activityCode, @RequestParam("date") String date);
}
