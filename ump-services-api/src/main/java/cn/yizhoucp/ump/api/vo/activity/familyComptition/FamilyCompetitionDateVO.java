package cn.yizhoucp.ump.api.vo.activity.familyComptition;

import cn.yizhoucp.ump.api.vo.jimu.rank.RankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class FamilyCompetitionDateVO {

    private List<RankItem> dayFTLeaderboard;
    private RankItem userDayFTLeaderboard;
}
