package cn.yizhoucp.ump.api.vo.swordsman;

import cn.yizhoucp.ump.api.vo.GiftBaseInfoDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description 拍卖详情
 * @createDate 2024/11/20
 */
@Data
public class AuctionDetailVO {

    // 奖励列表
    private List<GiftBaseInfoDTO> recordList;

    // 拍卖榜单
    private List<UserVO> auctionList;

    // 我的排名
    private UserVO selfRank;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserVO {
        private String avatar;
        private String name;
        // 投注数量
        private Long betCount;
        // 刺客用户需要隐藏
        private boolean hide;
        // 语音状态
        private boolean voiceStatus;
        // 我的排名
        private Long rank;
    }


}
