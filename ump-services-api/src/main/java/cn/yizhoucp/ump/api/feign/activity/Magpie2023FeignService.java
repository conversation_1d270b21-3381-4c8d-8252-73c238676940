package cn.yizhoucp.ump.api.feign.activity;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ump.api.vo.activity.magpie2023.IndexVO;
import cn.yizhoucp.ump.api.vo.activity.magpie2023.LoveDrawPoolVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.DrawLogVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.DrawReturn;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "ump-services", contextId = "magpie2023")
public interface Magpie2023FeignService {

    @RequestMapping("/api/inner/activity/magpie2023/get-index")
    Result<IndexVO> getIndexInfo(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid);

    @RequestMapping("/api/inner/activity/magpie2023/love-pool/exchange-draw-time")
    Result<Long> exchangeDrawTime(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid);

    @RequestMapping("/api/inner/activity/magpie2023/love-pool/draw-log")
    Result<DrawLogVO> lovePoolDrawLog(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid);

    @RequestMapping("/api/inner/activity/magpie2023/love-pool/draw")
    Result<DrawReturn> lovePoolDraw(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid);

    @RequestMapping("/api/inner/activity/magpie2023/xsms-pool/draw")
    Result<DrawReturn> xsmsPoolDraw(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid);

    @RequestMapping("/api/inner/activity/magpie2023/couple-board/hide")
    Result<Boolean> coupleBoardHide(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid, @RequestParam("hide") Boolean hide);

    @RequestMapping("/api/inner/activity/magpie2023/love-pool/refresh")
    Result<LoveDrawPoolVO> lovePoolRefresh(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid);

}
