package cn.yizhoucp.ump.api.feign.activity;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ump.api.vo.activity.luckyCharm.IndexVO;
import cn.yizhoucp.ump.api.vo.activity.luckyCharm.RankVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "ump-services", contextId = "luckyCharm")
public interface LuckyCharmFeignService {

    @GetMapping("/api/inner/activity/lucky-charm/get-index")
    Result<IndexVO> getIndex(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid);

    @GetMapping("/api/inner/activity/lucky-charm/get-rank")
    Result<RankVO> getRank(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid);

}
