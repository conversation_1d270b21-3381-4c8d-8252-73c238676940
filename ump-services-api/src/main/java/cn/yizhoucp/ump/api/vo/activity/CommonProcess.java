package cn.yizhoucp.ump.api.vo.activity;

import cn.yizhoucp.ump.api.vo.jimu.draw.PrizeItem;
import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CommonProcess {
    private PrizeItem gift;

    private String text;

    private Long curTimes;

    private Long allTimes;

    private Integer status;

    public static CommonProcess ofNoStart() {
        return CommonProcess.builder().curTimes(0L).status(CommonStatus.NO_START.getCode()).build();
    }

    public static CommonProcess ofNoSuccess() {
        return CommonProcess.builder().curTimes(0L).status(CommonStatus.NO_SUCCESS.getCode()).build();
    }

    public static CommonProcess ofFinish(Long curTimes) {
        return CommonProcess.builder().curTimes(curTimes).status(CommonStatus.FINISH.getCode()).build();
    }

    public static CommonProcess ofOpened(Long curTimes) {
        return CommonProcess.builder().curTimes(curTimes).status(CommonStatus.OPENED.getCode()).build();
    }

    public static CommonProcess processing(Long curTimes, Long needTimes) {
        CommonStatus cs = curTimes >= needTimes ? CommonStatus.OPENED : CommonStatus.NO_SUCCESS;
        return CommonProcess.builder().curTimes(Math.min(curTimes, needTimes)).allTimes(needTimes).status(cs.getCode()).build();
    }


    public enum CommonStatus {
        NO_START(0, "未开始"),
        NO_SUCCESS(1, "未完成、待完成"),
        WAIT_OPEN(2, "待开启"),
        OPENED(3, "已开启、已领取、已完成"),
        FINISH(4, "已结束(未完成)、已领完"),
        ;

        @Getter
        private Integer code;

        @Getter
        private String desc;

        CommonStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }
}
