package cn.yizhoucp.ump.api.vo.jimu.task;

import cn.yizhoucp.ms.core.vo.umpServices.activity.common.jimu.inner.PrizeItem;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TakePrizeReturn {

    /** 奖品列表 */
    private List<PrizeItem> prizeItemList;

    private Object extData;

}
