package cn.yizhoucp.ump.api.enums;

import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.jpa.IBaseJpaEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 广告位类型
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AdSpaceTypeEnum implements IBaseJpaEnum {

    default_type(0, "default_type", "等待初始化类型（废弃）", "", ServicesAppIdEnum.lanling),
    single_chat_banner(0, "single_chat_banner", "私聊banner", "features.activities.im", ServicesAppIdEnum.lanling),
    family_banner(1, "family_banner", "家族banner", "features.activities.family", ServicesAppIdEnum.lanling),
    gift_banner(2, "gift_banner", "礼物banner", "", ServicesAppIdEnum.lanling),
    single_chat_buoy(3, "single_chat_buoy", "私聊浮标", "features.activities.singleChat", ServicesAppIdEnum.lanling),
    family_buoy(4, "family_buoy", "家族浮标", "features.activities.familyChat", ServicesAppIdEnum.lanling),
    chat_room_buoy(5, "chat_room_buoy", "交友大厅浮标", "features.activities.chatroomChat", ServicesAppIdEnum.lanling),
    splash(6, "splash", "开屏页", "features.splash.image", ServicesAppIdEnum.lanling),
    chat_list_page_banner(7, "chat_list_page_banner", "聊天室列表banner", "features.activities.chatroom", ServicesAppIdEnum.lanling),
    broadcast_room_list_banner(8, "broadcast_room_list_banner", "直播间列表banner（废弃）", "features.activities.live", ServicesAppIdEnum.lanling),
    live_broadcasting_room_buoy(9, "live_broadcasting_room_buoy", "直播间浮标（废弃）", "features.activities.liveRoomChat", ServicesAppIdEnum.lanling),
    home_page_banner(10, "home_page_banner", "首页banner", "features.activities.homePage", ServicesAppIdEnum.coudui),
    home_page_banner_for_yutian(11, "home_page_banner_for_yutian", "语甜首页模块", "features.activities.homePageForYuTian", ServicesAppIdEnum.yutian),
    home_page_buoy(12, "home_page_buoy", "首页浮标", "features.activities.homeAdvertise", ServicesAppIdEnum.lanling),
    chatie_single_chat_banner(13, "chatie_single_chat_banner", "chatie-私聊banner（废弃）", "features.activities.imForChatie", ServicesAppIdEnum.chatie),
    chatie_family_banner(14, "chatie_family_banner", "chatie-家族banner（废弃）", "features.activities.familyForChatie", ServicesAppIdEnum.chatie),
    coudui_single_chat_banner(15, "coudui_single_chat_banner", "凑对-私聊banner", "features.activities.officialAnnouncementForCoudui", ServicesAppIdEnum.coudui),
    voice_chat_room_buoy(16, "voice_chat_room_buoy", "语音房浮标", "features.activities.voiceChatroomChat", ServicesAppIdEnum.lanling),
    chatie_family_buoy(17, "chatie_family_buoy", "chatie-家族浮标（废弃）", "features.activities.familyBuoyForChatie", ServicesAppIdEnum.chatie),
    deep_relation_buoy(18, "deep_releation_buoy", "深度关系结果页浮", "features.activities.reachIntimacy", ServicesAppIdEnum.lanling),
    coudui_square_banner(19, "coudui_square_banner", "凑对-广场banner", "", ServicesAppIdEnum.coudui),
    coudui_window_banner1(20, "coudui_window_banner1", "凑对-橱窗banner2-3", "", ServicesAppIdEnum.coudui),
    coudui_window_banner2(21, "coudui_window_banner2", "凑对-橱窗banner4-5", "", ServicesAppIdEnum.coudui),
    coudui_window_banner3(22, "coudui_window_banner3", "凑对-橱窗banner8-9", "", ServicesAppIdEnum.coudui),
    deep_relation_buoy_pre(23, "deep_relation_buoy_pre", "深度关系准备页浮标", "", ServicesAppIdEnum.lanling),
    deep_relation_buoy_pay(24, "deep_relation_buoy_pay", "深度关系支付页浮标", "", ServicesAppIdEnum.lanling),
    deep_relation_buoy2_pre(25, "deep_relation_buoy2_pre", "恋人关系准备页浮标", "", ServicesAppIdEnum.lanling),
    deep_relation_buoy2_pay(26, "deep_relation_buoy2_pay", "恋人关系支付页浮标", "", ServicesAppIdEnum.lanling),
    deep_relation_buoy2(27, "deep_relation_buoy2", "恋人关系结果页浮标", "", ServicesAppIdEnum.lanling),
    saka_single_chat_banner(28, "saka_single_chat_banner", "saka-私聊banner", "", ServicesAppIdEnum.chatie),
    saka_family_banner(29, "saka_family_banner", "saka-家族banner", "", ServicesAppIdEnum.chatie),
    astrology_page_buoy(30, "astrology_page_buoy", "占星页浮标", "features.activities.astrologyPageBuoy", ServicesAppIdEnum.lanling),
    home_risk_banner(31, "home_risk_banner", "首页风控banner", "", ServicesAppIdEnum.lanling),
    activity_center_welfare(32, "activity_center_welfare", "福利", "", ServicesAppIdEnum.lanling),
    activity_center_play(33, "activity_center_play", "玩法", "", ServicesAppIdEnum.lanling),
    activity_center_announcements(34, "activity_center_announcements", "公告", "", ServicesAppIdEnum.lanling),
    ;


    private Integer index;
    private String code;
    private String desc;
    @Deprecated
    private String remoteConfigKey;
    private ServicesAppIdEnum belongApp;
    public static final String PRE_PREFIX = "pre.";

    private static final Map<String, AdSpaceTypeEnum> unionIdMap = new ConcurrentHashMap<>();

    static {
        for (AdSpaceTypeEnum item : values()) {
            if (Objects.nonNull(item.getBelongApp())) {
                unionIdMap.put(item.getBelongApp().getUnionId(), item);
            }
        }
    }

    public static AdSpaceTypeEnum findByUnionId(String unionId) {
        if (StringUtils.isBlank(unionId)) {
            return null;
        }
        return unionIdMap.get(unionId);
    }

    /**
     * 根据 code 获取枚举
     *
     * @param code code
     * @return AdSpaceTypeEnum
     */
    public static AdSpaceTypeEnum findByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (AdSpaceTypeEnum type : values()) {
            if (StringUtils.equals(type.getCode(), code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 获取所有枚举键值对
     *
     * @return List<Map < String, String>>
     */
    public static List<Map<String, String>> getList() {
        List<Map<String, String>> result = new ArrayList<>();
        Map<String, String> map = new HashMap<>();
        for (AdSpaceTypeEnum type : values()) {
            map.put("code", type.getCode());
            map.put("desc", type.getDesc());
        }
        result.add(map);
        return result;
    }

    @Override
    public Integer getIdx() {
        return this.index;
    }
}
