package cn.yizhoucp.ump.api.vo.officialCombine;

import cn.yizhoucp.ump.api.vo.officialCombine.inner.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 官宣重构页面信息
 *
 * @author: lianghu
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OfficialCombineInfoVO implements Serializable {

    /**
     * 页面索引
     * 0 - 首页
     * 1 - 亲密列表页
     * 2 - 准备页（支付定金页）
     * 3 - 商品列表页
     * 4 - sku 页
     * 5 - 结算页
     * 6 - 官宣告白页
     * 7 - 结成页
     */
    private Integer index;
    /** 对方 uid */
    private Long toUid;
    /** 当前用户性别 */
    private String userSex;
    /** 男性头像 */
    private String maleAvatar;
    /** 男性昵称 */
    private String maleName;
    /** 女性头像 */
    private String femaleAvatar;
    /** 女性昵称 */
    private String femaleName;
    /** 对方活跃标签 */
    private Boolean tagStatus;
    /** 是否为 owner */
    private Boolean owner;
    /**
     * 福利礼包标识
     * 0 - 不可激活
     * 1 - 可激活
     * 2 - 已激活
     */
    private Integer giftBagStatus;
    /** 福利礼包倒计时 */
    private Long giftBagCountDown;
    /** 首页信息（官宣墙） */
    private IndexPageInfo indexPageInfo;
    /** 商品列表页信息 */
    private ProductPageInfo productPageInfo;
    /** sku 页信息 */
    private SkuPageInfo skuPageInfo;
    /** 结算页信息 */
    private SettlePageInfo settlePageInfo;
    /** 官宣告白页信息 */
    private CombinePageInfo combinePageInfo;
    /** 结成页信息 */
    private CompletedPageInfo completedPageInfo;
    /** 提示锁定弹窗 */
    private Boolean lock;
    /** 官宣关系锁定弹窗内容 */
    private LockInfo lockInfo;
    /** 扩展信息 */
    private Object extData;

}
