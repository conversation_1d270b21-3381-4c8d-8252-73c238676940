package cn.yizhoucp.ump.api.vo.activity.aliceInWonderland;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
public class DreamLandVO {

    /**
     *用户信息
     */
    private UserInfoVO userInfo;

    /**
     * 密友信息
     */
    private UserInfoVO friendInfo;

    /**
     * 任务是否开启
     */
    private Boolean isOpen;

    /**
     * 当前等级
     */
    private Integer curLevel;

    /**
     * 喂养值
     */
    private Long feedingValue;

    /**
     * 节点信息
     */
    private List<Nodes> nodes;

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class Nodes{

        private Integer level;

        private Long repeat;

        private AliceGiftVO rewards;

        /**
         * 是否领取
         */
        private Boolean isReceived;

        /**
         * 是否解锁
         */
        private Boolean isUnlock;

        /**
         * 解锁需要的饲养值
         */
        private Long  needFeedingValue;
    }


    @Data
    public static class UserInfoVO{

        private Long uid;

        private String userName;

        private String avatar;

        private Integer camp;

    }
}
