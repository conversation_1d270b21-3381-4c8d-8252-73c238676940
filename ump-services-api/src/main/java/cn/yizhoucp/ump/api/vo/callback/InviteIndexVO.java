package cn.yizhoucp.ump.api.vo.callback;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 邀请唤回主页信息
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InviteIndexVO {

    /** 完成任务需要邀请的人数 */
    private Integer completeNum;
    /** 已经邀请的人数 */
    private Integer inviteNum;
    /** 完成任务需要充值/提现的总数 */
    private Long completeAmount;
    /** 当前充值/提现的总数 */
    private Long curAmount;
    /** 分享文案 */
    private String shareContent;

}
