package cn.yizhoucp.ump.api.vo.activity.wlzb;

import cn.yizhoucp.ump.api.vo.product.ProductVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 首页信息
 *
 * <AUTHOR>
 * @Date 2023/3/8 15:46
 * @Version 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IndexVO implements Serializable {

    /** 活动开启倒计时 秒 */
    private Long countDown;
    /** 拥有秘籍数 */
    private Long resources;
    /** 押注列表 */
    private List<Integer> stakeList;
    /** 商品列表 */
    private List<ProductVO> products;
    /** 名誉值 */
    private Long reputationVal;
    /** 账户积分 */
    private Double point;
    private Boolean isStake;
    /** 判断用户设备是否是ios */
    private Boolean isIOS;
    private Long gameId;
    private Long nextGameId;
    /** 比赛记录 */
    private List<GameItem> gameHistory;


}
