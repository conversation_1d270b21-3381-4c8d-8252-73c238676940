package cn.yizhoucp.ump.api.feign.activity;


import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ump.api.vo.activity.legendaryStarPark.LegendaryStarParkHistoryCaptainVO;
import cn.yizhoucp.ump.api.vo.activity.legendaryStarPark.LegendaryStarParkRankVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "ump-services", contextId = "legendary-star-park")
public interface LegendaryStarParkFeignService {

    @GetMapping("/api/inner/activity/legendaryStarPark/get-rank")
    Result<LegendaryStarParkRankVO> getRank(@RequestParam(value = "activityCode", required = false) String activityCode, @RequestParam(value = "poolCode", required = false) String poolCode);

    @GetMapping("/api/inner/activity/legendaryStarPark/claimedReward")
    Result<Boolean> claimedReward(@RequestParam(value = "sceneCode", required = false) String sceneCode, @RequestParam(value = "taskId", required = false) String taskId,@RequestParam(value = "extData", required = false) String extData);

    @GetMapping("/api/inner/activity/legendaryStarPark/captains/history")
    Result<LegendaryStarParkHistoryCaptainVO> getCaptainsHistory(@RequestParam(value = "poolCode", required = false) String poolCode, @RequestParam(value = "roomId",required = false) Long roomId);

}
