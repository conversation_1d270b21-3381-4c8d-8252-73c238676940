package cn.yizhoucp.ump.api.vo.activity.aliceInWonderland;

import lombok.Data;

import java.util.List;

@Data
public class ForestWonderlandVO {

    /**
     * 礼物墙
     */
    private List<GiftWallVO> giftWall;

    /**
     * 道具奖励
     */
    private List<AliceGiftVO> propRewards;

    /**
     * 能否领取奖励
     */
    private Boolean canReceiveRewards;

    /**
     * 是否已经领取奖励
     */
    private Boolean isReceived;


    @Data
    public static class GiftWallVO{
        /**
         * 礼物名称
         */
        private String giftName;

        /**
         * 礼物值
         */
        private Long valueGold;

        /**
         * 需要收集数量
         */
        private Integer requireCount;

        /**
         * 已收集数量
         */
        private Long collectCount;

        /**
         * 是否完成
         */
        private Boolean isComplete;

        /**
         * 礼物墙坐标
         */
        private Integer index;
    }
}
