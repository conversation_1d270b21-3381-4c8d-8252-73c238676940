package cn.yizhoucp.ump.api.vo.activity.autumnFestival;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 提交告白请求
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SubmitConfessionVO {

    /** 应用id */
    private Long appId;
    /** 对方用户id */
    private Long uid;
    /** 当前用户id */
    private Long loginUid;
    /** 告白内容 */
    private String content;

}
