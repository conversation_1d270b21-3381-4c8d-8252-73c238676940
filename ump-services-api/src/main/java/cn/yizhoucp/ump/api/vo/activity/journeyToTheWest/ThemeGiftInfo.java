package cn.yizhoucp.ump.api.vo.activity.journeyToTheWest;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class ThemeGiftInfo {

    List<ThemeLevel> levelList;

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class ThemeLevel{
        private Long level;
        private JourneyToTheWestIndexVO.Gift themeReward;
        private List<JourneyToTheWestIndexVO.RewardGift> rewardGiftList;
        private List<JourneyToTheWestIndexVO.TaskGift> taskGiftList;
    }
}
