package cn.yizhoucp.ump.api.vo.officialCombine.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 获取页面信息参数
 *
 * @author: lianghu
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PageInfoParam implements Serializable {

    /** 通用参数 */
    private Long toUid;

    /** 首页参数 */
    private String type;

    /** 亲密列表页参数 */
    private Integer pageNo;
    private Integer pageSize;
    private Long limitIntimacy;

    /** 来源 */
    private String from;


}
