package cn.yizhoucp.ump.api.feign.activity;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ump.api.vo.activity.base.DrawPoolItemRefreshVO;
import cn.yizhoucp.ump.api.vo.activity.base.GiftVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.DrawReturn;
import cn.yizhoucp.ump.api.vo.jimu.draw.PrizeItem;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

@FeignClient(value = "ump-services", contextId = "misty-star-continent")
public interface MistyStarContinentFeignService {

    @RequestMapping("/api/inner/activity/misty-star-continent/get-can-open-box")
    Result<String> getCanOpenBox(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid);

    @RequestMapping("/api/inner/activity/misty-star-continent/get-gift-replace")
    Result<Map<Long, PrizeItem>> getGiftReplace(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid);

    @RequestMapping("/api/inner/activity/misty-star-continent/open-box")
    Result<DrawReturn> openBox(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid, @RequestParam("poolCode") String poolCode);

    @RequestMapping("/api/inner/activity/misty-star-continent/refresh-draw-pool")
    Result<DrawPoolItemRefreshVO> refreshDrawPool(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid, @RequestParam("type") String type);

    @RequestMapping("/api/inner/activity/misty-star-continent/replace-gift")
    Result<Boolean> replaceGift(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid, @RequestParam("replace") Boolean replace);

}
