package cn.yizhoucp.ump.api.vo.luckBag.admin;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 活动奖池后台VO
 * @date 2022-07-12 23:17
 */
@Data
public class DrawPoolAdminVO {

    private Long id;
    /** 活动 code */
    private String activityCode;
    /** 奖池编号 */
    private String poolCode;
    /** 奖池描述 */
    private String poolName;

    /** 奖池类型  */
    private String poolType;

    /** 最低规则 转化为  DrawLimitRule */
    private String limitRule;

    /** 奖池业务 Key */
    private String bizKey;
    /** 奖池状态 */
    private Integer status;
    /** 开始时间 */
    private String startTime;
    /** 结束时间 */
    private String endTime;
    /** 创建时间 */
    private String createTime;
    /** 更新时间 */
    private String updateTime;

    /** 应用唯一ID */
    private String unionId;

    /** 中奖率 */
    private Double drawRatio = 0d;

    /** 返奖率 */
    private Double returnRatio = 0d;

    /** 优先级 */
    private Integer priorityLevel;

    /** 生效奖池类型 DrawRulePoolType */
    private String drawRulePoolType;

    /** 奖池类型 DrawRuleType */
    private String ruleType;

    private Integer limitCount;

    /** 导入用户ID */
    private String importUids;

    private Integer loopCount;

    /** 次数，用于前段展示 */
    private Integer times;

    /** 类型比例 over/lower 大于小于操作 */
    private String rateType;
    /** 用户抽奖次数限制 */
    private Integer rateTimes;
    /** 用户抽奖返奖率 */
    private Double returnRate;
    /** 单词金币, 用于计算返奖率 */
    private Integer singleCoin;

    /** 抽奖的配置 */
    private List<DrawPoolAdminItemVO> drawItems;
}
