package cn.yizhoucp.ump.api.vo.lovepromise;

import cn.yizhoucp.dto.LovePromiseEquityDTO;
import cn.yizhoucp.dto.LovePromiseRingDTO;
import cn.yizhoucp.ms.core.vo.productServices.CoinGiftProductVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LovePromiseRingVO {
    private Long id;

    private String icon;

    private String ringSvgaIcon;

    private String ringBigIcon;

    private String cardShowIcon;

    private String homeShowIcon;

    private Long coin;

    private String name;

    private String ringNameIcon;

    private LovePromiseRingBagVO giftBag;

    private LovePromiseEquityVO equity;

    public static LovePromiseRingVO of(LovePromiseRingDTO lovePromiseRingDO, List<CoinGiftProductVO> coinGiftProductVOS, List<LovePromiseEquityDTO> lovePromiseEquityDOS) {
        LovePromiseRingBagVO giftBag = LovePromiseRingBagVO.ofByCoinGiftProductVOS(coinGiftProductVOS);
        LovePromiseEquityVO equity = LovePromiseEquityVO.ofByLovePromiseEquityDOS(lovePromiseEquityDOS);
        return LovePromiseRingVO.builder()
                .id(lovePromiseRingDO.getId())
                .icon(lovePromiseRingDO.getIcon())
                .homeShowIcon(lovePromiseRingDO.getHomeShowIcon())
                .ringSvgaIcon(lovePromiseRingDO.getSvgaIcon())
                .ringBigIcon(lovePromiseRingDO.getBigIcon())
                .cardShowIcon(lovePromiseRingDO.getCardShowIcon())
                .coin(lovePromiseRingDO.getNeedCoin())
                .name(lovePromiseRingDO.getName())
                .giftBag(giftBag)
                .equity(equity)
                .ringNameIcon(lovePromiseRingDO.getRingNameIcon())
                .build();
    }
}
