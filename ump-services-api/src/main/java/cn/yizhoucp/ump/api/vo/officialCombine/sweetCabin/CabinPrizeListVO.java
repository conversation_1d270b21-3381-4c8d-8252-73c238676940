package cn.yizhoucp.ump.api.vo.officialCombine.sweetCabin;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 甜蜜小屋奖励列表
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CabinPrizeListVO {

    /** 当前等级名 */
    private String curLevelName;
    /** 下一等级名 */
    private String nextLevelName;
    /** 等级奖励列表 */
    private List<CabinPrizeListDetailVO> levelPrizeList;

}
