package cn.yizhoucp.ump.api.vo.depth;

import lombok.Data;

import java.io.Serializable;

/**
 * 用户心动抽奖后返回前端数据
 * <AUTHOR> 2022-01-19
 */
@Data
public class CardiacLuckDrawVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 抽奖流水记录的ID
     */
    private Long cardiacLuckDrawLogId;

    /**
     * 抽中的奖励的类型
     * @See cn.yizhoucp.lanling.api.project.biz.enums.depth.CardiacLuckDrawPrizeTypeEnum
     */
    private String type;

    /**
     * 抽中的奖励的名称
     */
    private String prizeName;

    /**
     * 抽中的奖励的图片
     */
    private String prizeIcon;

    /**
     * 抽中的奖励的描述
     */
    private String prizeDesc;

}