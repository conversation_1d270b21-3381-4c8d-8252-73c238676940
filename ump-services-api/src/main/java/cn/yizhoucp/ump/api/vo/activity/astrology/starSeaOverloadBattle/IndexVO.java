package cn.yizhoucp.ump.api.vo.activity.astrology.starSeaOverloadBattle;

import cn.yizhoucp.ump.api.vo.activity.astrology.springStarCeremony.StallVO;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.api.vo.jimu.task.TaskVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class IndexVO implements Serializable {

    private static final long serialVersionUID = -4329740542628951153L;

    private List<Boolean> openStatus;
    private Integer astrologyTimesToday;
    private TaskVO task;
    private Integer curPhase;
    private TaskVO phaseOneTask;
    private TaskVO phaseTwoTask;
    private Integer curRank;
    private RankVO starSeaRank;
    private RankVO overloadRank;
    private OverloadVO overload;

}
