package cn.yizhoucp.ump.api.vo.activity.astrology.starWishCardCollectionGame;

import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.api.vo.jimu.task.TaskVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class IndexVO implements Serializable {

    private static final long serialVersionUID = 7430839030353291687L;

    private List<Boolean> crystalPrayStatus;
    private Integer todayStarWishValue;
    private TaskVO starWishTask;
    private List<Integer> myCardCollection;
    private RankVO starWishRank;
    private RankVO cardCollectionRank;
    private List<Object> broadcast;

}
