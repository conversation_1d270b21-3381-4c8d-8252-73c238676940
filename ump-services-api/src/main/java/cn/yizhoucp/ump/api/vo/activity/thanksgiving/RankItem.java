package cn.yizhoucp.ump.api.vo.activity.thanksgiving;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 排行榜项
 *
 * @author: lianghu
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RankItem implements Serializable {

    private Long rank;
    private Long uid;
    private String icon;
    private String name;
    private Long coinNum;

}
