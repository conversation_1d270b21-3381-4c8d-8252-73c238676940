package cn.yizhoucp.ump.api.feign;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.vo.CommonListVO;
import cn.yizhoucp.ms.core.vo.CommonResultVO;
import cn.yizhoucp.ump.api.vo.activity.luckyBagFight.FightIndexVO;
import cn.yizhoucp.ump.api.vo.activity.luckyBagFight.ProductInfoVO;
import cn.yizhoucp.ump.api.vo.activity.luckyBagFight.RankListVO;
import cn.yizhoucp.ump.api.vo.activity.luckyBagFight.WeaponIndexVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 打怪兽
 *
 * @author: dongming
 */
@FeignClient(value = "ump-services", contextId = "luckyBagFight")
public interface LuckyBagFightFeignService {

    /**
     * 获取打怪兽主页信息
     *
     * @param appId       应用id
     * @param unionId     应用唯一标识
     * @param uid         用户id
     * @param vestChannel 马甲包渠道
     * @param from        来源 LuckBagFromEnum
     * @param relationId  关联id（家族id、语音房id...）
     * @return FightIndexVO
     */
    @GetMapping("/api/inner/lucky-bag-fight/index")
    Result<FightIndexVO> index(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId,
                               @RequestParam("uid") Long uid, @RequestParam("vestChannel") String vestChannel,
                               @RequestParam("from") String from, @RequestParam("relationId") Long relationId);

    /**
     * 获取打怪兽伤害排名-实时
     *
     * @param appId       应用id
     * @param unionId     应用唯一标识
     * @param uid         用户id
     * @param vestChannel 马甲包渠道
     * @param from        来源 LuckBagFromEnum
     * @param relationId  关联id（家族id、语音房id...）
     * @return CommonListVO
     */
    @GetMapping("/api/inner/lucky-bag-fight/damage-rank")
    Result<CommonListVO> damageRank(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId,
                                    @RequestParam("uid") Long uid, @RequestParam("vestChannel") String vestChannel,
                                    @RequestParam("from") String from, @RequestParam("relationId") Long relationId);

    /**
     * 攻击
     *
     * @param appId       应用id
     * @param unionId     应用唯一标识
     * @param uid         用户id
     * @param vestChannel 马甲包渠道
     * @param from        来源 LuckBagFromEnum
     * @param relationId  关联id（家族id、语音房id...）
     * @param count       攻击次数
     * @return FightIndexVO
     */
    @GetMapping("/api/inner/lucky-bag-fight/attack")
    Result<FightIndexVO> attack(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId,
                                @RequestParam("uid") Long uid, @RequestParam("vestChannel") String vestChannel,
                                @RequestParam("from") String from, @RequestParam("relationId") Long relationId, @RequestParam("count") Integer count);

    /**
     * 获取武器主页信息
     *
     * @param appId       应用id
     * @param unionId     应用唯一标识
     * @param uid         用户id
     * @param vestChannel 马甲包渠道
     * @return WeaponIndexVO
     */
    @GetMapping("/api/inner/lucky-bag-fight/weapon/index")
    Result<WeaponIndexVO> weaponIndex(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId,
                                      @RequestParam("uid") Long uid, @RequestParam("vestChannel") String vestChannel);

    /**
     * 穿戴装备
     *
     * @param appId       应用id
     * @param unionId     应用唯一标识
     * @param uid         用户id
     * @param vestChannel 马甲包渠道
     * @param weaponCode  装备code
     * @return CommonResultVO
     */
    @GetMapping("/api/inner/lucky-bag-fight/weapon/wear-weapon")
    Result<CommonResultVO> wearWeapon(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId,
                                      @RequestParam("uid") Long uid, @RequestParam("vestChannel") String vestChannel,
                                      @RequestParam("weaponCode") String weaponCode);

    /**
     * 合成装备
     *
     * @param appId       应用id
     * @param unionId     应用唯一标识
     * @param uid         用户id
     * @param vestChannel 马甲包渠道
     * @param weaponCode  装备code
     * @return CommonResultVO
     */
    @GetMapping("/api/inner/lucky-bag-fight/weapon/conflate-weapon")
    Result<CommonResultVO> conflateWeapon(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId,
                                          @RequestParam("uid") Long uid, @RequestParam("vestChannel") String vestChannel,
                                          @RequestParam("weaponCode") String weaponCode);

    /**
     * 获取武器商城主页信息
     *
     * @param appId       应用id
     * @param unionId     应用唯一标识
     * @param uid         用户id
     * @param vestChannel 马甲包渠道
     * @return ProductInfoVO
     */
    @GetMapping("/api/inner/lucky-bag-fight/shop/index")
    Result<ProductInfoVO> productIndex(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId,
                                       @RequestParam("uid") Long uid, @RequestParam("vestChannel") String vestChannel);

    /**
     * 兑换商品
     *
     * @param appId       应用id
     * @param unionId     应用唯一标识
     * @param uid         用户id
     * @param vestChannel 马甲包渠道
     * @param productCode  装备code
     * @return CommonResultVO
     */
    @GetMapping("/api/inner/lucky-bag-fight/shop/exchange")
    Result<CommonResultVO> exchange(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId,
                                    @RequestParam("uid") Long uid, @RequestParam("vestChannel") String vestChannel,
                                    @RequestParam("productCode") String productCode);

    /**
     * 兑换记录
     *
     * @param appId       应用id
     * @param unionId     应用唯一标识
     * @param uid         用户id
     * @param vestChannel 马甲包渠道
     * @return CommonListVO
     */
    @GetMapping("/api/inner/lucky-bag-fight/shop/exchange-record")
    Result<CommonListVO> exchangeRecord(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId,
                                        @RequestParam("uid") Long uid, @RequestParam("vestChannel") String vestChannel);

    /**
     * 排行榜
     *
     * @param appId       应用id
     * @param unionId     应用唯一标识
     * @param uid         用户id
     * @param vestChannel 马甲包渠道
     * @return CommonListVO
     */
    @GetMapping("/api/inner/lucky-bag-fight/rank-list")
    Result<RankListVO> rankList(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId,
                                @RequestParam("uid") Long uid, @RequestParam("vestChannel") String vestChannel);

}
