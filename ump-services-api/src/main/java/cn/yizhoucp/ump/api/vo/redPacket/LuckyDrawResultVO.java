package cn.yizhoucp.ump.api.vo.redPacket;

import cn.yizhoucp.ms.core.vo.coinservices.LuckDrawPrizeVO;
import lombok.Data;

import java.util.List;

/**
 * 手气抽奖结果
 *
 * <AUTHOR>
 */
@Data
public class LuckyDrawResultVO {

    /** 剩余次数 */
    private Long remainCount;

    /** 剩余体验券数 */
    private Long remainCouponCount;

    /** 礼物列表 */
    private List<LuckDrawPrizeVO> awardList;

    /** 特殊礼物（保底礼物） */
    private String desc;

    /** 占星分享信息 */
    private AstrologyShareInfoVO shareInfo;

}
