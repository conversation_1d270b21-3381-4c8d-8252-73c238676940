package cn.yizhoucp.ump.api.feign.activity;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ump.api.vo.activity.midAutumn2023.IndexVO;
import cn.yizhoucp.ump.api.vo.activity.midAutumn2023.RabbitVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.DrawLogVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.DrawReturn;
import cn.yizhoucp.ump.api.vo.jimu.draw.PrizeItem;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "ump-services", contextId = "mid-autumn2023")
public interface MidAutumn2023FeignService {

    @RequestMapping("/api/inner/activity/mid-autumn2023/draw-log")
    Result<DrawLogVO> drawLog(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid);

    @RequestMapping("/api/inner/activity/mid-autumn2023/draw")
    Result<DrawReturn> draw(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid, @RequestParam("poolCode") String poolCode);

    @RequestMapping("/api/inner/activity/mid-autumn2023/yyh_gift_draw")
    Result<cn.yizhoucp.ms.core.vo.umpServices.activity.common.jimu.DrawReturn> draw(@RequestParam("type") String type, @RequestParam("poolCode") String poolCode, @RequestParam("times") Integer times, @RequestParam("extValue") String extValue);

    @RequestMapping("/api/inner/activity/mid-autumn2023/exchange-gold-rabbit")
    Result<RabbitVO> exchangeGoldRabbit(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid);

    @RequestMapping("/api/inner/activity/mid-autumn2023/refresh-draw-pool")
    Result<List<PrizeItem>> refreshDrawPool(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid, @RequestParam("poolCode") String poolCode);

    @RequestMapping("/api/inner/activity/mid-autumn2023/board/secret")
    Result<Boolean> boardSecret(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid, @RequestParam("secret") Boolean secret);

    @RequestMapping("/api/inner/activity/mid-autumn2023/get-index")
    Result<IndexVO> getIndexInfo(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid);

}
