package cn.yizhoucp.ump.api.param.jimu;

import cn.yizhoucp.ms.core.param.BaseParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 按钮事件参数
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class ButtonEventParam {

    private BaseParam baseParam;

    /* 活动code */
    private String activityCode;

    /* 事件code 区分同一活动下的不同事件 */
    private String eventCode;

    /* 关联Id[非必传] */
    private Long otherId;

    /* 业务key 区分同一事件下的不同参数 */
    private String bizKey;

    /* 补充参数 JSONString */
    private String extDate;

    public Boolean check() {
        return StringUtils.isNotBlank(activityCode) && StringUtils.isNotBlank(eventCode);
    }
}
