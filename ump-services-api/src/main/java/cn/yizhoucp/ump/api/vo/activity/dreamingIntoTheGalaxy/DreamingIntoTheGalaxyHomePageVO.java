package cn.yizhoucp.ump.api.vo.activity.dreamingIntoTheGalaxy;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class DreamingIntoTheGalaxyHomePageVO {

    /*补梦积分*/
    private Long points;
    /*最大抽奖数*/
    private Long  maxDraws;
    /*补梦碎片*/
    private Long fragments;
    /*每日积分到10弹窗*/
    private Boolean pointsReachedPopup;
    /*昨日是否上榜弹窗*/
    private Boolean isYesterdayRankedPopup;
    /*上榜信息*/
    private String yesterdayRankedInfo;
    /*兑换礼物列表*/
    private List<GiftExchangeItem> giftExchangeList;
    /*排行榜*/
    private Rank rankList;
    /*中奖信息*/
    private Object broadcast;
    /*兑换播报*/
    private Object broadExchangeCast;

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class  GiftExchangeItem{
        /*礼物id*/
        private Integer giftId;
        /*礼物名*/
        private String giftName;
        /*礼物icon*/
        private String giftIcon;
        /*所需碎片*/
        private Integer fragments;
        /*礼物价值*/
        private Integer giftValue;
        /*礼物key*/
        private String giftKey;
        /*装扮类型*/
        private String giftType;
        /*装扮子类型*/
        private String giftSubType;

    }

    @Data
    public static class Rank {
        //我的排名
        private Object myRank;
        //排名
        private Object rankList;
    }
}
