package cn.yizhoucp.ump.api.feign;


import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.model.PageResult;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.userservices.UserBasicVO;
import cn.yizhoucp.ump.api.vo.HomePageDTO;
import cn.yizhoucp.ump.api.vo.officialCombine.*;
import cn.yizhoucp.ump.api.vo.officialCombine.inner.IndexPageInfo;
import cn.yizhoucp.ump.api.vo.officialCombine.param.ConfirmCartParam;
import com.alibaba.fastjson.JSONObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 官宣
 *
 * @author: lianghu
 */
@FeignClient(value = "ump-services", contextId = "officialCombine")
public interface OfficialCombineFeignService {

    /**
     * 获取页面信息
     *
     * @return
     */
    @RequestMapping("/api/inner/official-combine/get-page-info")
    Result<OfficialCombineInfoVO> getPageInfo(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("vestChannel") String vestChannel, @RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid, @RequestParam("type") String type, @RequestParam("from") String from);

    /**
     * 获取已结成官宣信息
     *
     * @return
     */
    @RequestMapping("/api/inner/official-combine/get-has-combined")
    Result<OfficialCombineUserVO> getHasCombined(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("vestChannel") String vestChannel, @RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid);

    /**
     * 获取已结成官宣信息
     *
     * @return HomePageDTO
     */
    @RequestMapping("/api/inner/official-combine/get-home-page")
    Result<HomePageDTO> getHomePage(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("vestChannel") String vestChannel, @RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid);

    /**
     * 获取官宣信息 by roomId
     *
     * @return
     */
    @RequestMapping("/api/inner/official-combine/get-combine-by-room-id")
    Result<OfficialCombineUserVO> getCombineByRoomId(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("vestChannel") String vestChannel, @RequestParam("uid") Long uid, @RequestParam("roomId") Long roomId);

    /**
     * 获取亲密列表
     *
     * @return
     */
    @RequestMapping("/api/inner/official-combine/get-intimacy-list")
    Result<PageResult<UserBasicVO>> getIntimacyList(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("vestChannel") String vestChannel, @RequestParam("uid") Long uid, @RequestParam("pageNo") Long pageNo, @RequestParam("pageSize") Long pageSize, @RequestParam("limitIntimacy") Long limitIntimacy);

    /**
     * 支付定金
     *
     * @param toUid
     * @return
     */
    @RequestMapping("/api/inner/official-combine/pay-deposit")
    Result<Boolean> payDeposit(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("vestChannel") String vestChannel, @RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid);

    /**
     * 提交商品
     *
     * @param
     * @return
     */
    @RequestMapping("/api/inner/official-combine/confirm-product")
    Result<Boolean> confirmProduct(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("vestChannel") String vestChannel, @RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid, @RequestParam("productId") Long productId);

    /**
     * 提交购物车
     *
     * @param
     * @return
     */
    @PostMapping("/api/inner/official-combine/confirm-cart")
    Result<Boolean> commitSkuInfo(@RequestBody ConfirmCartParam param);

    /**
     * 支付订单
     *
     * @param
     * @return
     */
    @RequestMapping("/api/inner/official-combine/pay-order")
    Result<Boolean> payOrder(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("vestChannel") String vestChannel, @RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid);


    @RequestMapping("/api/inner/official-combine/change-cart")
    Result<Boolean> changeCart(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("vestChannel") String vestChannel, @RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid);

    /**
     * 预约 & 编辑官宣时间
     *
     * @param toUid
     * @return
     */
    @RequestMapping("/api/inner/official-combine/reserve-date-time")
    Result<Boolean> reserveDateTime(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("vestChannel") String vestChannel, @RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid, @RequestParam("dateTime") String dateTime);

    /**
     * 开启 & 进入官宣房
     *
     * @param toUid
     * @return
     */
    @RequestMapping("/api/inner/official-combine/enter-room")
    Result<OfficialCombineUserVO> enterRoom(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("vestChannel") String vestChannel, @RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid);

    /**
     * 完成官宣
     *
     * @param toUid
     * @return
     */
    @RequestMapping("/api/inner/official-combine/complete-combine")
    Result<Boolean> completeCombine(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("vestChannel") String vestChannel, @RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid);

    /**
     * 取消官宣
     *
     * @param toUid
     * @return
     */
    @RequestMapping("/api/inner/official-combine/cancel-combine")
    Result<OfficialCombineCancelCheckVO> cancelCombine(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("vestChannel") String vestChannel, @RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid);

    /**
     * 查看清单
     *
     * @param toUid
     * @return
     */
    @RequestMapping("/api/inner/official-combine/combine-gift-info")
    Result<GiftListVO> combineGiftInfo(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("vestChannel") String vestChannel, @RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid);

    /**
     * 激活福利礼包
     *
     * @param toUid
     * @return
     */
    @RequestMapping("/api/inner/official-combine/active-gift-bag")
    Result<Boolean> activeGiftBag(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("vestChannel") String vestChannel, @RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid);

    /**
     * 向男生发送邀请卡片消息
     *
     * @param
     * @return
     */
    @RequestMapping("/api/inner/official-combine/send-male-invite-card")
    Result<Boolean> sendMaleInviteCardMsg(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("vestChannel") String vestChannel, @RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid);

    /**
     * 获取女生私聊浮窗（卡片）
     *
     * @param
     * @return
     */
    @RequestMapping("/api/inner/official-combine/get-female-chat-pop")
    Result<JSONObject> getFemaleChatPop(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("vestChannel") String vestChannel, @RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid);

    /**
     * 女生关闭私聊浮窗（卡片）
     *
     * @param param
     * @return
     */
    @RequestMapping("/api/inner/official-combine/close-chat-pop")
    Result<Boolean> closeChatPopHandle(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("vestChannel") String vestChannel, @RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid);


    /**
     * 获取付全款的官宣次数
     * @param appId
     * @param unionId
     * @param vestChannel
     * @param uid
     * @param toUid
     * @return
     */
    @RequestMapping("/api/inner/official-combine/get-completed-combine")
    Result<Integer> getCompletedCombine(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("vestChannel") String vestChannel, @RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid);

    /**
     * 获取官宣房型列表
     * @param appId
     * @param unionId
     * @param vestChannel
     * @param uid
     * @return
     */
    @RequestMapping("/api/inner/official-combine/get-combine-room")
    Result<List<Long>> getListByRoomIds(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("vestChannel") String vestChannel, @RequestParam("uid") Long uid, @RequestParam("roomIds") List<Long> roomIds);

    /**
     * 是否有过官宣记录
     * @return
     */
    @RequestMapping("/api/inner/official-combine/get-has-combine-by-male-female")
    Result<Boolean> getHasCombineByMaleIdAndFemaleId(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("vestChannel") String vestChannel, @RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid);

    /**
     * 根据 男生女生获取官宣信息
     * @return
     */
    @RequestMapping("/api/inner/official-combine/get-combine-by-male-female")
    Result<OfficialCombineUserVO> getCombineByMaleIdAndFemaleId(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("vestChannel") String vestChannel, @RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid);

    /**
     * 个人 IM 页官宣定金倒计时提示
     * @return
     */
    @RequestMapping("/api/inner/official-combine/get-deposit-expired-notice")
    Result<DepositExpiredNoticeVO> getDepositExpiredNotice(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("vestChannel") String vestChannel, @RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid);

    /**
     * 获取私聊页的官宣浮标
     *
     * @return List<ChatBuoyVO>
     */
    @GetMapping("/api/inner/official-combine/get-single-chat-buoy-list")
    Result<List<ChatBuoyVO>> getSingleChatBuoyList(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("vestChannel") String vestChannel, @RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid);

    /**
     * 获取官宣首页数据
     * @return
     */
    @RequestMapping("/api/inner/official-combine/get-index-page-info")
    Result<IndexPageInfo> getIndexPageInfo(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("vestChannel") String vestChannel, @RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid);

    /**
     * 选择官宣对象
     * @return
     */
    @RequestMapping("/api/inner/official-combine/unprecedented-love/choose-other")
    Result<Boolean> chooseOfficialCombineObj(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("vestChannel") String vestChannel, @RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid);

    /**
     * 通过用户ID进行官宣的查询
     * @param userIds
     * @return
     */
    @RequestMapping("/api/inner/official-combine/get-combine-by-uids")
    Result<List<OfficialCombineUserVO>> getCombineByUids(@RequestParam("strUserIds") String strUserIds);

    /**
     * 获取付全款的金额
     * @param idList
     * @return
     */
    @RequestMapping("/api/inner/official-combine/get-combine-order-price")
    Result<Map<Long, Long>> getCombineOrderPrice(@RequestParam("idList") List<Long> idList);

    


}
