package cn.yizhoucp.ump.api.vo.officialCombine.sweetCabin;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 甜蜜小屋任务栏信息（首页展示）
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CabinTaskbarVO {

    /** 是否展示红点 */
    private Boolean showRedDot;
    /** 总浪漫值 */
    private Long otherUid;
    /** 是否展示新手引导 */
    private Long chatId;
    /** 小屋地址 */
    private String url;
    /** 任务详情 */
    private List<CabinTaskbarDetailVO> taskList;

}
