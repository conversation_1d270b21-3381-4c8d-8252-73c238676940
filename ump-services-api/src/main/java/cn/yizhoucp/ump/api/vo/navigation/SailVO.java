package cn.yizhoucp.ump.api.vo.navigation;

import cn.yizhoucp.ump.api.vo.GiftBaseInfoDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SailVO {
    /** 是否触发海神保护 */
    private Boolean actionProtect;

    /** 前行/后退海里数 */
    private Integer mileModify;

    /** 实际抽到的海里数 */
    private Integer realMileModify;

    /** 抽到的船 */
    private String drawBoat;

    /** 首次召唤船只 */
    private Boolean firstCallBoat;

    private Integer beforeMile;

    private Integer afterMile;

    private String userBoat;
}
