package cn.yizhoucp.ump.api.feign.activity;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ump.api.vo.activity.astrologyAdventure.IndexVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.DrawReturn;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "ump-services", contextId = "astrology-adventure")
public interface AstrologyAdventureFeignService {

    @RequestMapping("/api/inner/activity/astrology-adventure/get-index")
    Result<IndexVO> getIndexInfo(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid);

    @RequestMapping("/api/inner/activity/astrology-adventure/open-box")
    Result<DrawReturn> openBox(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid, @RequestParam("poolCode") String poolCode);

    @RequestMapping("/api/inner/activity/astrology-adventure/get-draw-pool")
    Result<String> getDrawPool(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid);

    @RequestMapping("/api/inner/activity/astrology-adventure/can-open-box")
    Result<Boolean> canOpenBox(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid);

}
