package cn.yizhoucp.ump.api.feign.jimu;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.vo.umpServices.activity.common.jimu.DrawReturn;
import cn.yizhoucp.ump.api.vo.jimu.buoyBar.ChatBarVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.DrawLogNewVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.DrawLogVO;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.api.vo.jimu.task.TakePrizeReturn;
import cn.yizhoucp.ump.api.vo.jimu.task.TaskVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "ump-services", contextId = "jimu")
public interface JimuFeignService {

    @RequestMapping("/api/inner/activity/jimu/get-index")
    Result<Object> getIndex(@RequestParam("activityCode") String activityCode, @RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid,@RequestParam(value = "extData", required = false) String extData);

    @RequestMapping("/api/inner/activity/jimu/draw")
    Result<DrawReturn> jimuDraw(@RequestParam("activityCode") String activityCode, @RequestParam("type") String type, @RequestParam("poolCode") String poolCode, @RequestParam("times") Integer times, @RequestParam("extValue") String extValue);

    @RequestMapping("/api/inner/activity/jimu/chat-buoy-bar")
    Result<ChatBarVO> getBuoyBar(@RequestParam("activityCode") String activityCode, @RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid);

    @RequestMapping("/api/inner/activity/jimu/mission-list")
    Result<TaskVO> missionList(@RequestParam("activityCode") String activityCode, @RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid);

    @RequestMapping("/api/inner/activity/jimu/take-prize")
    Result<TakePrizeReturn> takePrize(@RequestParam("activityCode") String activityCode, @RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid, @RequestParam("taskCode") String taskCode, @RequestParam("toUid") Long toUid);

    @RequestMapping("/api/inner/activity/jimu/get-draw-log")
    Result<DrawLogVO> getDrawLog(@RequestParam("activityCode") String activityCode, @RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid);

    @RequestMapping("/api/inner/activity/jimu/draw_log")
    Result<DrawLogNewVO> drawLogNew(@RequestParam("activityCode") String activityCode, @RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid);

    @RequestMapping("/api/inner/activity/jimu/draw_log_latest")
    Result<DrawLogNewVO> drawLogLatest(@RequestParam("activityCode") String activityCode, @RequestParam("poolCode") String poolCode);

    @RequestMapping("/api/inner/activity/jimu/get-draw-log-by-page")
    Result<DrawLogVO> getDrawLogByPage(@RequestParam("activityCode") String activityCode, @RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid, @RequestParam("pageNo") Integer pageNo, @RequestParam("pageSize") Integer pageSize);

    @GetMapping("/api/inner/activity/jimu/get-rank")
    Result<RankVO> getRank(@RequestParam("activityCode") String activityCode, @RequestParam("rankKey") String rankKey, @RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid, @RequestParam(name = "otherUid", required = false) Long otherUid);

    @RequestMapping("/api/inner/activity/jimu/button/event")
    Result<Object> event(@RequestParam("activityCode") String activityCode,
                         @RequestParam("eventCode") String eventCode,
                         @RequestParam(value = "otherId", required = false) Long otherId,
                         @RequestParam(value = "bizKey", required = false) String bizKey,
                         @RequestParam(value = "extDate", required = false) String extDate);

    @GetMapping("/api/inner/activity/jimu/button/get-friend")
    Result<Object> friendList(@RequestParam("activityCode") String activityCode,
                              @RequestParam("friendCode") String friendCode,
                              @RequestParam(value = "extDate", required = false) String extDate);

    @GetMapping("/api/inner/activity/jimu/button/get-rank-list")
    Result<RankVO> rankList(
            @RequestParam("activityCode") String activityCode,
            @RequestParam(value = "listCode", required = false) String listCode,
            @RequestParam(value = "listType", required = false) String listType,
            @RequestParam(value = "listData", required = false) String listData,
            @RequestParam(value = "otherUid", required = false) Long otherUid,
            @RequestParam(value = "extDate", required = false) String extDate);
}
