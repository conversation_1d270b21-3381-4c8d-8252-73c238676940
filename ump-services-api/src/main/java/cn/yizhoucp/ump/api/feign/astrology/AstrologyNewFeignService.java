package cn.yizhoucp.ump.api.feign.astrology;

import cn.yizhoucp.ms.core.base.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "ump-services", contextId = "ump-astrology-new")
public interface AstrologyNewFeignService {
    @RequestMapping("/api/inner/astrology-new/enter-h5")
    Result<Boolean> enterH5(@RequestParam("uid") Long uid);
}
