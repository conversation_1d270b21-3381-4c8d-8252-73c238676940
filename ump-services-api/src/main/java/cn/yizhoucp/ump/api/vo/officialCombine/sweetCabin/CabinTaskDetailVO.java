package cn.yizhoucp.ump.api.vo.officialCombine.sweetCabin;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 甜蜜小屋任务信息
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CabinTaskDetailVO {

    private String code;
    /** 展示 icon */
    private String icon;
    /** 任务名 */
    private String name;
    /** 奖励描述 */
    private String prizeDesc;
    /** 0-未完成；1-待领取；2-已领取 */
    private Integer status;

}
