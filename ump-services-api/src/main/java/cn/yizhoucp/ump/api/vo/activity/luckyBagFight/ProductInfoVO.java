package cn.yizhoucp.ump.api.vo.activity.luckyBagFight;

import cn.yizhoucp.ump.api.vo.jimu.draw.PrizeItem;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 商城信息
 *
 * @author: lianghu
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProductInfoVO {

    /** 用户水晶数量 */
    private Long crystalNum;
    /** 商品列表 */
    private List<PrizeItem> productList;

}
