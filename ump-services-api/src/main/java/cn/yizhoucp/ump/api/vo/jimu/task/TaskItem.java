package cn.yizhoucp.ump.api.vo.jimu.task;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TaskItem implements Serializable {

    /** 任务 code */
    private String taskCode;
    /** 任务路由 */
    private String taskRoute;
    /** 任务 icon */
    private String icon;
    /** 任务标题 */
    private String taskTitle;
    /** 任务描述 */
    private String taskDesc;
    /** 任务状态 */
    private Integer taskStatus;
    /** 当前完成次数 */
    private Integer curFinishTimes;
    /** 最大完成次数 */
    private Integer maxFinishTimes;
    /** 任务拓展信息 */
    private Object extData;
    private Integer buttonStatus;

}
