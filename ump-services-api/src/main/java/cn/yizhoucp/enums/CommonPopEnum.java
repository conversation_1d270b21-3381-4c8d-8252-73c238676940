package cn.yizhoucp.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CommonPopEnum {

    IOS_RECHARGE_GUIDE_POP_COMMON("iOS-recharge-guide-pop-common", "iOS 充值引导弹窗图片弹窗"),
    IOS_RECHARGE_GUIDE_POP_H5("iOS-recharge-guide-pop-h5", "iOS 充值引导弹窗 h5 弹窗"),
    IOS_WITHDRAW_GUIDE_POP_COMMON("iOS-withdraw-guide-pop-common", "iOS 提现引导弹窗 h5 弹窗"),
    IOS_WITHDRAW_GUIDE_POP_H5("iOS-withdraw-guide-pop-h5", "iOS 提现引导弹窗 h5 弹窗"),
    WARN_POP_H5("warn-pop-h5", "色情话术治理警告 h5 弹窗"),
    THANKS_GIVING_POP_1("thanks-giving-mission-1-pop", "完成任务 1 感恩卡弹窗"),
    THANKS_GIVING_POP_3("thanks-giving-mission-3-pop", "完成任务 3 感恩卡弹窗"),

    ;
    private String code;
    private String desc;

}
