package cn.yizhoucp.enums.navigation;

import lombok.Getter;

@Getter
public enum SailTypeEnum {
    NORMAL("普通航行"),
    GOD("海神航行");

    String desc;

    SailTypeEnum(String desc) {
        this.desc = desc;
    }

    public static SailTypeEnum of(String code) {
        if (code == null) {
            return NORMAL;
        }
        for (SailTypeEnum type : values()) {
            if (type.name().equalsIgnoreCase(code)) {
                return type;
            }
        }
        return NORMAL;
    }
}
