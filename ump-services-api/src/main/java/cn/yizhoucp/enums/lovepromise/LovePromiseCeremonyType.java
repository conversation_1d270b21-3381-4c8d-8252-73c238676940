package cn.yizhoucp.enums.lovepromise;

public enum LovePromiseCeremonyType {

    open("open", "公开定情"),
    secret("secret", "私密定情")
    ;

    private String code;

    private String desc;

    LovePromiseCeremonyType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public static LovePromiseCeremonyType ofByCode(String code) {
        for (LovePromiseCeremonyType lovePromiseCeremonyType : LovePromiseCeremonyType.values()) {
            if (lovePromiseCeremonyType.getCode().equals(code)) {
                return lovePromiseCeremonyType;
            }
        }
        return null;
    }
}
