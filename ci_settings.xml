<?xml version="1.0" encoding="UTF-8"?>
<settings
        xmlns="http://maven.apache.org/SETTINGS/1.0.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">
    <pluginGroups></pluginGroups>
    <proxies></proxies>
    <servers>
    <server>
        <id>my-deploy-release</id>
        <username>mvnupload</username>
        <password>admin123</password>
    </server>
    <server>
        <id>my-deploy-snapshot</id>
        <username>mvnupload</username>
        <password>admin123</password>
    </server>
    </servers> 
    <mirrors>
        <mirror>
            <id>aliyunmaven</id>
            <mirrorOf>external:*,!yizhoucp</mirrorOf>
            <name>aliyunmaven</name>
            <url>https://maven.aliyun.com/repository/public</url>
        </mirror>
        <mirror>
            <id>yizhoucp</id>
            <mirrorOf>yizhoucp</mirrorOf>
            <name>yizhoucp Repositories</name>
            <url>https://nexus-hz.yizhoucp.cn/repository/maven-public/</url>
        </mirror>
    </mirrors>
    <profiles></profiles>
</settings>
