package cn.yizhoucp.ump.biz.project.biz.manager.activity.palpitatingHeartYou;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.activity.palpitatingHeartYou.CardStatistics;
import cn.yizhoucp.ump.api.vo.activity.palpitatingHeartYou.PHYIndexVO;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.FeignUserService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class PalpitatingHeartYouIndexManagerTest {

    @Mock
    private RedisManager redisManager;

    @InjectMocks
    private PalpitatingHeartYouIndexManager PHYIndexManager;

    @Mock
    private PalpitatingHeartYouRankManager PHYRankManager;

    @Mock
    private FeignUserService feignUserService;

    @InjectMocks
    private PalpitatingHeartYouIndexManager indexManager;

    private BaseParam param;
    private PHYIndexVO indexVO;
    private Long uid;

    @BeforeEach
    public void setUp() {
        param = mock(BaseParam.class);
        indexVO = new PHYIndexVO();
        uid = 1L;
        lenient().when(feignUserService.getBasic(anyLong(), eq(1L))).thenReturn(Result.successResult(new UserVO()));
    }

    /**
     * 获取卡片统计数据
     */
    @Test
    public void cardStatistics_ValidUid_ReturnsExpectedData() {
        // Arrange
        when(redisManager.hget(anyString(), eq("redRoses"))).thenReturn(5);
        when(redisManager.hget(anyString(), eq("lovePoetry"))).thenReturn(3);
        when(redisManager.hget(anyString(), eq("pledgeRing"))).thenReturn(2);
        when(redisManager.hget(anyString(), eq("pearlString"))).thenReturn(1);

        // Act
        CardStatistics result = PHYIndexManager.cardStatistics(uid);

        // Assert
        assertEquals(5L, result.getRedRoses());
        assertEquals(3L, result.getLovePoetry());
        assertEquals(2L, result.getPledgeRing());
        assertEquals(1L, result.getPearlString());
    }

    /**
     * 获取卡片统计数据，Redis中没有数据
     */
    @Test
    public void cardStatistics_ValidUid_NoData_ReturnsZeroValues() {
        // Arrange
        when(redisManager.hget(anyString(), anyString())).thenReturn(null);

        // Act
        CardStatistics result = PHYIndexManager.cardStatistics(uid);

        // Assert
        assertEquals(0L, result.getRedRoses());
        assertEquals(0L, result.getLovePoetry());
        assertEquals(0L, result.getPledgeRing());
        assertEquals(0L, result.getPearlString());
    }


    /**
     * 获取排行榜数据
     */
    @Test
    public void indexAccompanyLeaderboard_RankVONotNull_ReturnsExpectedData() {
        // Arrange
        List<RankItem> rankItemList = new ArrayList<>();
        rankItemList.add(RankItem.builder().name("User1").icon("avatar1").rank(1L).value(100L).build());

        RankVO rankVO = new RankVO();
        rankVO.setRankList(rankItemList);
        rankVO.setMyselfRank(RankItem.builder().name("Myself").icon("myAvatar").rank(2L).value(200L).build());

        when(PHYRankManager.getRank(any(RankContext.class))).thenReturn(rankVO);

        // Act
        PHYIndexVO result = indexManager.indexAccompanyLeaderboard(indexVO, param);

        // Assert
        assertEquals(1, result.getAccompanyLeaderboard().size());
        assertEquals("User1", result.getAccompanyLeaderboard().get(0).getUserName());
        assertEquals("Myself", result.getUserAccompanyLeaderboard().getUserName());
    }

    @Test
    public void indexAccompanyLeaderboard_RankVOIsNull_UserInfoFromService_ReturnsExpectedData() {
        // Arrange
        when(PHYRankManager.getRank(any(RankContext.class))).thenReturn(null);
        UserVO userVO=new UserVO();
        userVO.setName("Test User");
        userVO.setAvatar("test_avatar");
        Result<UserVO> userServiceResult = Result.successResult(userVO);
        when(feignUserService.getBasic(isNull(), eq(1L))).thenReturn(userServiceResult);

        // Act
        PHYIndexVO result = indexManager.indexAccompanyLeaderboard(indexVO, param);

        // Assert
        assertEquals("Test User", result.getUserAccompanyLeaderboard().getUserName());
        assertEquals("test_avatar", result.getUserAccompanyLeaderboard().getAvatar());
    }

/*
    @Test
    public void indexAccompanyLeaderboard_UserServiceError_ThrowsException() {
        // Arrange
        when(PHYRankManager.getRank(any(RankContext.class))).thenReturn(null);
        when(feignUserService.getBasic(anyLong(), eq(1L))).thenReturn(Result.failResult(ErrorCode.LANLING_GET_COIN_BALANCE_ERROR));

        // Act and Assert
        ServiceException exception = assertThrows(ServiceException.class, () -> {
            indexManager.indexAccompanyLeaderboard(indexVO, param);
        });
        assertEquals(ErrorCode.LANLING_GET_COIN_BALANCE_ERROR, exception.getErrorCode());
    }
*/

}
