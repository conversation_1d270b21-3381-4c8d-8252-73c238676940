/*
package api.project.biz.manager.userActivity.admin;

import api.project.BaseDbUnitAndRedisTest;
import cn.yizhoucp.ump.biz.project.biz.manager.userActivity.admin.CycleActivityAdminManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.CycleActivityJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.CycleActivityDO;
import api.project.framework.util.RandomUtil;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ump.api.enums.ActivityCheckListEnum;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;


@Import(CycleActivityAdminManager.class)
class CycleActivityAdminManagerTest extends BaseDbUnitAndRedisTest {

    @Resource
    private CycleActivityAdminManager cycleActivityAdminManager;
    @Resource
    private CycleActivityJpaDAO cycleActivityJpaDAO;

    @Test
    void getListByTime() {
        // 构建前置数据
        CycleActivityDO activityDO = cycleActivityJpaDAO.save(RandomUtil.randomPojo(CycleActivityDO.class,
                r -> {
                    r.setAppId(ServicesAppIdEnum.lanling.getAppId());
                    r.setUnionId(ServicesAppIdEnum.lanling.getUnionId());
                    r.setBelongActivityCode(ActivityCheckListEnum.GODDESS_TRAIN.getCode());
                    r.setStatus(1);
                    r.setStartTime(LocalDateTime.now().minusDays(1));
                    r.setEndTime(LocalDateTime.now());
                }));

        // 验证有效期外查询结果
        List<CycleActivityDO> result = cycleActivityAdminManager.getListByTime(
                ServicesAppIdEnum.lanling.getAppId(),
                ServicesAppIdEnum.lanling.getUnionId(),
                ActivityCheckListEnum.GODDESS_TRAIN,
                LocalDateTime.now().minusDays(1).plusHours(1).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli(),
                LocalDateTime.now().minusHours(1).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        assertEquals(0, result.size());

        // 验证有效期内查询结果
        List<CycleActivityDO> result2 = cycleActivityAdminManager.getListByTime(
                ServicesAppIdEnum.lanling.getAppId(),
                ServicesAppIdEnum.lanling.getUnionId(),
                ActivityCheckListEnum.GODDESS_TRAIN,
                LocalDateTime.now().minusDays(1).minusHours(1).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli(),
                LocalDateTime.now().plusHours(1).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        assertEquals(1, result2.size());
        assertEquals(activityDO.getId(), result2.get(0).getId());

    }

    @Test
    void getListByCodeList() {
        // 构建前置数据
        CycleActivityDO activityDO = cycleActivityJpaDAO.save(RandomUtil.randomPojo(CycleActivityDO.class,
                r -> {
                    r.setAppId(ServicesAppIdEnum.lanling.getAppId());
                    r.setUnionId(ServicesAppIdEnum.lanling.getUnionId());
                    r.setStatus(1);
                    r.setActivityCode("cycleActivity1");
                }));
        CycleActivityDO activityDO2 = cycleActivityJpaDAO.save(RandomUtil.randomPojo(CycleActivityDO.class,
                r -> {
                    r.setAppId(ServicesAppIdEnum.lanling.getAppId());
                    r.setUnionId(ServicesAppIdEnum.lanling.getUnionId());
                    r.setStatus(1);
                    r.setActivityCode("cycleActivity2");
                }));

        // 验证查询结果
        List<CycleActivityDO> result = cycleActivityAdminManager.getListByCodeList(
                ServicesAppIdEnum.lanling.getAppId(),
                ServicesAppIdEnum.lanling.getUnionId(),
                Lists.newArrayList("cycleActivity1", "cycleActivity2", "cycleActivity3"));
        assertEquals(2, result.size());
    }
}*/
