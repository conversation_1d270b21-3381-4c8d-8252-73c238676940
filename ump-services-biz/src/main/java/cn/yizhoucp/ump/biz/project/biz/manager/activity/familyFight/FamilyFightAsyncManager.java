package cn.yizhoucp.ump.biz.project.biz.manager.activity.familyFight;

import cn.yizhoucp.ms.core.base.enums.UserPackageBizType;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.familyFight.TreasurePrizeEnum;
import cn.yizhoucp.ms.core.base.UserPackageScene;
import cn.yizhoucp.ms.core.base.config.ThreadPoolConfig;
import cn.yizhoucp.ms.core.base.enums.dressup.DressUpType;
import cn.yizhoucp.ms.core.vo.umpServices.activity.familyFight.PrizeVO;
import cn.yizhoucp.ump.biz.project.biz.remoteService.lanling.FeignLanlingService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.product.PackageProductRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 为家族而战活动
 * <AUTHOR>
 */
@Service
@Slf4j
public class FamilyFightAsyncManager {

    @Resource
    FeignLanlingService feignLanlingService;

    @Resource
    PackageProductRemoteService packageProductRemoteService;

    /**
     * 下发奖励
     * @param appId     应用id
     * @param uid       用户id
     * @param prizeList 奖励
     */
    @Async(ThreadPoolConfig.ASYNC_EXECUTOR_NAME)
    public void sendPrize(Long appId, Long uid, List<PrizeVO> prizeList) {
        for (PrizeVO prize : prizeList) {
            int day = prize.getNum();
            switch (prize.getCode()) {
                case 0:
                    feignLanlingService.sendDressUp(appId, uid, DressUpType.mount, TreasurePrizeEnum.wzgl.getKey(), day);
                    break;
                case 1:
                    feignLanlingService.sendDressUp(appId, uid, DressUpType.mount, TreasurePrizeEnum.xmt.getKey(), day);
                    break;
                case 2:
                    packageProductRemoteService.sendGiftToPackage(appId, uid, (long) day, TreasurePrizeEnum.xhn.getKey(), UserPackageBizType.GIFT.getCode(), UserPackageScene.FAMILY_FIGHT_ACTIVITY.getCode(),
                            null, -1L, null, Boolean.TRUE, Boolean.TRUE);
                    break;
                case 3:
                    feignLanlingService.sendDressUp(appId, uid, DressUpType.entry_special_effect, TreasurePrizeEnum.zcjb.getKey(), day);
                    break;
                case 4:
                    feignLanlingService.sendDressUp(appId, uid, DressUpType.entry_special_effect, TreasurePrizeEnum.kxdr.getKey(), day);
                    break;
                case 5:
                    feignLanlingService.membershipBatchSend(uid.toString(), "system", appId, day);
                    break;
                case 6:
                    packageProductRemoteService.sendGiftToPackage(appId, uid, (long) day, TreasurePrizeEnum.shzl.getKey(), UserPackageBizType.GIFT.getCode(), UserPackageScene.FAMILY_FIGHT_ACTIVITY.getCode(),
                            null, -1L, null, Boolean.TRUE, Boolean.TRUE);
                    break;
                default:
                    packageProductRemoteService.sendGiftToPackage(appId, uid, (long) day, TreasurePrizeEnum.hys.getKey(), UserPackageBizType.GIFT.getCode(), UserPackageScene.FAMILY_FIGHT_ACTIVITY.getCode(),
                            null, -1L, null, Boolean.TRUE, Boolean.TRUE);
            }
        }
    }

}
