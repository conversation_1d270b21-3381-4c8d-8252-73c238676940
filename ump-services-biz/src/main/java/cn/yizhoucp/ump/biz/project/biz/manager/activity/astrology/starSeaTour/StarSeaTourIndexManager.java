package cn.yizhoucp.ump.biz.project.biz.manager.activity.astrology.starSeaTour;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.enums.ServicesNameEnum;
import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.ms.core.base.util.WeightRandomUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.starter.cassandra.base.kafka.YzKafkaProducerManager;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.activity.base.GiftVO;
import cn.yizhoucp.ump.api.vo.activity.starSeaTour.IndexVO;
import cn.yizhoucp.ump.api.vo.jimu.task.TaskItem;
import cn.yizhoucp.ump.api.vo.jimu.task.TaskVO;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaTourConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.IndexManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityInfoUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ActivityDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dal.service.ScenePrizeService;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.math3.util.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaTourConstant.ACTIVITY_CODE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaTourConstant.CRYSTAL_NUM;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaTourConstant.CRYSTAL_STAR;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaTourConstant.GODS_GUIDE_CONSUME;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaTourConstant.GODS_GUIDE_SUCCESS_PROB;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaTourConstant.GODS_GUIDE_TIMES;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaTourConstant.GUIDE_BOARD;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaTourConstant.ORDINARY_GUIDE_CONSUME;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaTourConstant.ORDINARY_GUIDE_SUCCESS_PROB;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaTourConstant.STAR_EXPLORE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaTourConstant.STAR_EXPLORE_TIMES;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaTourConstant.STAR_TASK_COMPLETE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaTourConstant.STAR_TASK_STATUS;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaTourConstant.SUPPLY;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaTourConstant.SUPPLY_LEFT;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaTourConstant.TASK_STAR_RECEIVE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaTourConstant.TASK_STAR_SEA_RECEIVE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaTourConstant.TOUR_BOARD;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaTourConstant.TOUR_BOARD_OVERALL;

@Service
@Slf4j
public class StarSeaTourIndexManager implements IndexManager {

    @Resource
    private RedisManager redisManager;
    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private NotifyComponent notifyComponent;
    @Resource
    private ScenePrizeService scenePrizeService;
    @Resource
    private StarSeaTourRankManager starSeaTourRankManager;
    @Resource
    private YzKafkaProducerManager yzKafkaProducerManager;
    @Resource
    private StarSeaTourBizManager starSeaTourBizManager;

    @Override
    public IndexVO getIndex(BaseParam param, Long toUid, String extData) {
        IndexVO indexVO = new IndexVO();

        List<Boolean> starExploreStatus = Lists.newArrayListWithCapacity(5);
        starExploreStatus.add(Boolean.TRUE.equals(redisManager.hasKey(String.format(STAR_EXPLORE, param.getUid(), starSeaTourBizManager.getDate(), StarSeaTourConstant.Star.VENUS.name()))));
        starExploreStatus.add(Boolean.TRUE.equals(redisManager.hasKey(String.format(STAR_EXPLORE, param.getUid(), starSeaTourBizManager.getDate(), StarSeaTourConstant.Star.JUPITER.name()))));
        starExploreStatus.add(Boolean.TRUE.equals(redisManager.hasKey(String.format(STAR_EXPLORE, param.getUid(), starSeaTourBizManager.getDate(), StarSeaTourConstant.Star.MERCURY.name()))));
        starExploreStatus.add(Boolean.TRUE.equals(redisManager.hasKey(String.format(STAR_EXPLORE, param.getUid(), starSeaTourBizManager.getDate(), StarSeaTourConstant.Star.MARS.name()))));
        starExploreStatus.add(Boolean.TRUE.equals(redisManager.hasKey(String.format(STAR_EXPLORE, param.getUid(), starSeaTourBizManager.getDate(), StarSeaTourConstant.Star.SATURN.name()))));
        indexVO.setStarExploreStatus(starExploreStatus);

        indexVO.setSupplyCur(Optional.ofNullable(redisManager.getInteger(String.format(SUPPLY, param.getUid(), starSeaTourBizManager.getDate()))).orElse(0));

        TaskVO starSeaTask = new TaskVO();
        List<TaskItem> taskItemList = Lists.newArrayListWithCapacity(3);
        for (StarSeaTourConstant.Star star : StarSeaTourConstant.Star.values()) {
            if (!StringUtils.isBlank(star.getTaskCode())) {
                TaskItem taskItem = new TaskItem();
                taskItem.setTaskCode(star.getTaskCode());
                taskItem.setCurFinishTimes(Math.min(star.getMaxFinishTimes(), Optional.ofNullable(redisManager.getInteger(String.format(STAR_EXPLORE_TIMES, param.getUid(), star.name()))).orElse(0)));
                taskItem.setExtData(star.getTaskReceiveLimit());
                if ((Integer) taskItem.getExtData() <= 0) {
                    taskItem.setButtonStatus(-2);
                } else if (taskItem.getCurFinishTimes() < star.getMaxFinishTimes()) {
                    taskItem.setButtonStatus(0);
                } else if (Boolean.TRUE.equals(redisManager.hasKey(String.format(TASK_STAR_SEA_RECEIVE, param.getUid(), star.name())))) {
                    taskItem.setButtonStatus(-1);
                } else {
                    taskItem.setButtonStatus(1);
                }
                taskItemList.add(taskItem);
            }
        }
        starSeaTask.setTaskItemList(taskItemList);
        indexVO.setStarSeaTask(starSeaTask);

        redisManager.setnx(String.format(CRYSTAL_NUM, param.getUid(), starSeaTourBizManager.getDate()), 1L, DateUtil.ONE_MONTH_SECOND);
        redisManager.setnx(String.format(CRYSTAL_STAR, param.getUid(), starSeaTourBizManager.getDate()), 1L, DateUtil.ONE_MONTH_SECOND);
        indexVO.setCrystalLeftToday(Optional.ofNullable(redisManager.getInteger(String.format(CRYSTAL_NUM, param.getUid(), starSeaTourBizManager.getDate()))).orElse(0));
        indexVO.setCrystalLevel(Optional.ofNullable(redisManager.getInteger(String.format(CRYSTAL_STAR, param.getUid(), starSeaTourBizManager.getDate()))).orElse(1));
        indexVO.setSupplyLeft(Optional.ofNullable(redisManager.getInteger(String.format(SUPPLY_LEFT, param.getUid(), starSeaTourBizManager.getDate()))).orElse(0));
        indexVO.setSureRiseStarTimes(3 - Optional.ofNullable(redisManager.getInteger(String.format(GODS_GUIDE_TIMES, param.getUid(), starSeaTourBizManager.getDate()))).orElse(0));

        List<Integer> guideSupplyNeed = Lists.newArrayListWithCapacity(2);
        guideSupplyNeed.add(ORDINARY_GUIDE_CONSUME[indexVO.getCrystalLevel()]);
        guideSupplyNeed.add(GODS_GUIDE_CONSUME[indexVO.getCrystalLevel()]);
        indexVO.setGuideSupplyNeed(guideSupplyNeed);

        TaskVO starTask = new TaskVO();
        taskItemList = Lists.newArrayListWithCapacity(3);
        Boolean hasKey = redisManager.hasKey(String.format(STAR_TASK_COMPLETE, param.getUid(), starSeaTourBizManager.getDate()));
        for (StarSeaTourConstant.StarTask st : StarSeaTourConstant.StarTask.values()) {
            TaskItem taskItem = new TaskItem();
            taskItem.setTaskCode(st.getTaskCode());
            Integer starTaskStatus = Optional.ofNullable(redisManager.getInteger(String.format(STAR_TASK_STATUS, param.getUid(), starSeaTourBizManager.getDate(), st.getTaskCode()))).orElse(0);
            taskItem.setCurFinishTimes(Boolean.TRUE.equals(redisManager.hasKey(String.format(STAR_TASK_STATUS, param.getUid(), starSeaTourBizManager.getDate(), st.getTaskCode()))) ? 1 : 0);
            if (starTaskStatus == 0) {
                taskItem.setButtonStatus(0);
            } else if (Boolean.TRUE.equals(redisManager.hasKey(String.format(TASK_STAR_RECEIVE, param.getUid(), starSeaTourBizManager.getDate(), st.getTaskCode())))) {
                taskItem.setButtonStatus(-1);
            } else {
                taskItem.setButtonStatus(1);
            }
            if (Objects.equals(taskItem.getButtonStatus(), 0) && Boolean.TRUE.equals(hasKey)) {
                taskItem.setButtonStatus(-3);
            }
            taskItemList.add(taskItem);
        }
        starTask.setTaskItemList(taskItemList);
        indexVO.setStarTask(starTask);

        ActivityDO activityDO = ActivityInfoUtil.get();
        LocalDateTime endTime = activityDO.getEndTime();
        LocalDateTime now = LocalDateTime.now().truncatedTo(ChronoUnit.HOURS);
        indexVO.setLastThreeDay(endTime != null && ChronoUnit.HOURS.between(now, endTime) <= 130);

        indexVO.setTourRank(starSeaTourRankManager.getRank(
                RankContext.builder()
                        .activityCode(ACTIVITY_CODE)
                        .rankKey(Boolean.TRUE.equals(indexVO.getLastThreeDay()) ? TOUR_BOARD_OVERALL : String.format(TOUR_BOARD, starSeaTourBizManager.getDate()))
                        .type(RankContext.RankType.user)
                        .param(param)
                        .supportDiff(Boolean.TRUE)
                        .build()));
        indexVO.setGuideRank(starSeaTourRankManager.getRank(
                RankContext.builder()
                        .activityCode(ACTIVITY_CODE)
                        .rankKey(GUIDE_BOARD)
                        .type(RankContext.RankType.user)
                        .supportDiff(Boolean.TRUE)
                        .param(param)
                        .build()));

        return indexVO;
    }

    public GiftVO taskStarSeaReceive(BaseParam param, String taskCode) {
        if (StringUtils.isBlank(taskCode)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }

        for (StarSeaTourConstant.Star star : StarSeaTourConstant.Star.values()) {
            if (star.getTaskCode().equals(taskCode)) {
//                Long taskStarSeaReceiveLimit = redisManager.incrLong(String.format(TASK_STAR_SEA_RECEIVE_LIMIT, star.name()), 1L, DateUtil.ONE_MONTH_SECOND);
//                if (taskStarSeaReceiveLimit == null || taskStarSeaReceiveLimit > star.getTaskReceiveLimit()) {
//                    throw new ServiceException(ErrorCode.INVALID_PARAM, "奖励已领完，已无法领取");
//                }

                Integer starExploreTimes = redisManager.getInteger(String.format(STAR_EXPLORE_TIMES, param.getUid(), star.name()));
                if (starExploreTimes == null || starExploreTimes < star.getMaxFinishTimes()) {
                    throw new ServiceException(ErrorCode.INVALID_PARAM, "您未完成任务，无法领取奖励");
                }

                if (!Boolean.TRUE.equals(redisManager.setnx(String.format(TASK_STAR_SEA_RECEIVE, param.getUid(), star.name()), System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND))) {
                    throw new ServiceException(ErrorCode.INVALID_PARAM, "您已领取礼物，无法再次领取");
                }

                List<ScenePrizeDO> scenePrizeDOList = scenePrizeService.getListBySceneCode(ACTIVITY_CODE, taskCode);
                sendPrizeManager.sendPrize(
                        BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(param.getUid()).build(),
                        scenePrizeDOList.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, param.getUid())).collect(Collectors.toList())
                );
                for (ScenePrizeDO scenePrizeDO : scenePrizeDOList) {
                    notifyComponent.npcNotify(
                            ServicesAppIdEnum.lanling.getUnionId(),
                            param.getUid(),
                            String.format("恭喜您在“星海巡游记”活动中获得%s金币礼物“%s”，礼物已发放至您的背包，请注意查收哦～", scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeDesc())
                    );
                    this.allActivityReceiveAward(String.format("sea_star_task_exploration_%s_10", star.name().toLowerCase()), "", scenePrizeDO.getPrizeValue(), scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeNum(), param.getUid());
                }

                return GiftVO.builder().icon(scenePrizeDOList.get(0).getPrizeIcon()).name(scenePrizeDOList.get(0).getPrizeDesc()).value(scenePrizeDOList.get(0).getPrizeValueGold()).build();
            }
        }

        throw new ServiceException(ErrorCode.INVALID_PARAM);
    }

    public Boolean guide(BaseParam param, Integer type) {
        if (!Objects.equals(type, 0) && !Objects.equals(type, 1)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }

        Integer crystalNum = Optional.ofNullable(redisManager.getInteger(String.format(CRYSTAL_NUM, param.getUid(), starSeaTourBizManager.getDate()))).orElse(1);
        if (crystalNum <= 0) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "您当前没有水晶，无法进行守护");
        }

        int[] guideSucessProb = Objects.equals(type, 0) ? ORDINARY_GUIDE_SUCCESS_PROB : GODS_GUIDE_SUCCESS_PROB;
        int[] guideConsume = Objects.equals(type, 0) ? ORDINARY_GUIDE_CONSUME : GODS_GUIDE_CONSUME;

        Integer crystalStar = Optional.ofNullable(redisManager.getInteger(String.format(CRYSTAL_STAR, param.getUid(), starSeaTourBizManager.getDate()))).orElse(1);
        Integer supplyLeft = Optional.ofNullable(redisManager.getInteger(String.format(SUPPLY_LEFT, param.getUid(), starSeaTourBizManager.getDate()))).orElse(0);
        if (supplyLeft < guideConsume[crystalStar]) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "您的补给不足，请前往占星，补充补给");
        }
        if (crystalStar >= 10) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "您的水晶已满级，无法继续守护，请先替换占星礼物吧～");
        }

        redisManager.decrLong(String.format(SUPPLY_LEFT, param.getUid(), starSeaTourBizManager.getDate()), guideConsume[crystalStar], DateUtil.ONE_MONTH_SECOND);

        if (type == 1) {
            Long godsGuideTimes = redisManager.incrLong(String.format(GODS_GUIDE_TIMES, param.getUid(), starSeaTourBizManager.getDate()), 1L, DateUtil.ONE_MONTH_SECOND);
            if (godsGuideTimes != null && godsGuideTimes >= 3) {
                Long crystalStarNew = redisManager.incrLong(String.format(CRYSTAL_STAR, param.getUid(), starSeaTourBizManager.getDate()), 1L, DateUtil.ONE_MONTH_SECOND);
                if (crystalStarNew == 9) {
                    starSeaTourRankManager.incrRankValue(param.getUid(), 1L, GUIDE_BOARD);
                }
                redisManager.delete(String.format(GODS_GUIDE_TIMES, param.getUid(), starSeaTourBizManager.getDate()));

                this.allActivityLottery("god_protect", guideConsume[crystalStar], crystalStar, Math.toIntExact(crystalStarNew), param.getUid());
                return true;
            }
        }

        List<Pair> pairs = new ArrayList<>();
        pairs.add(new Pair("open", guideSucessProb[crystalStar]));
        pairs.add(new Pair("happy", 10000 - guideSucessProb[crystalStar]));
        String random = (String) new WeightRandomUtil(pairs).random();
        if (!"open".equals(random)) {
            this.allActivityLottery(Objects.equals(type, 0) ? "normal_protect" : "god_protect", guideConsume[crystalStar], crystalStar, crystalStar, param.getUid());
            return false;
        }

        Long crystalStarNew = redisManager.incrLong(String.format(CRYSTAL_STAR, param.getUid(), starSeaTourBizManager.getDate()), 1L, DateUtil.ONE_MONTH_SECOND);
        if (crystalStarNew == 9) {
            starSeaTourRankManager.incrRankValue(param.getUid(), 1L, GUIDE_BOARD);
        }
        redisManager.delete(String.format(GODS_GUIDE_TIMES, param.getUid(), starSeaTourBizManager.getDate()));

        this.allActivityLottery(Objects.equals(type, 0) ? "normal_protect" : "god_protect", guideConsume[crystalStar], crystalStar, Math.toIntExact(crystalStarNew), param.getUid());
        return true;
    }

    public Integer taskGuideReceive(BaseParam param, String taskCode) {
        for (StarSeaTourConstant.StarTask starTask : StarSeaTourConstant.StarTask.values()) {
            if (starTask.getTaskCode().equals(taskCode)) {
                if (!Boolean.TRUE.equals(redisManager.setnx(String.format(TASK_STAR_RECEIVE, param.getUid(), starSeaTourBizManager.getDate(), taskCode), System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND))) {
                    throw new ServiceException(ErrorCode.INVALID_PARAM, "今日已领取水晶，请明日再来哦~");
                }
                redisManager.set(String.format(CRYSTAL_STAR, param.getUid(), starSeaTourBizManager.getDate()), 1L, DateUtil.ONE_MONTH_SECOND);
                this.allActivityReceiveAward(taskCode, "", "", 0L, 0, param.getUid());
                return Math.toIntExact(Optional.ofNullable(redisManager.incrLong(String.format(CRYSTAL_NUM, param.getUid(), starSeaTourBizManager.getDate()), 1L, DateUtil.ONE_MONTH_SECOND)).orElse(0L));
            }
        }

        throw new ServiceException(ErrorCode.INVALID_PARAM);
    }

    @Override
    public String getActivityCode() {
        return ACTIVITY_CODE;
    }

    @Override
    public String getTemplateType() {
        return null;
    }

    public void allActivityReceiveAward(String taskType, String planetType, String awardKey, Long awardAmount, Integer awardCount, Long uid) {
        Map<String, Object> params = Maps.newHashMapWithExpectedSize(6);
        params.put("activity_type", "sea_star_parade_activity");
        params.put("attribute_type", "astrology_activity");
        params.put("task_type", taskType);
        params.put("planet_type", planetType);
        params.put("award_key", awardKey);
        params.put("award_amount", awardAmount);
        params.put("award_count", awardCount);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_receive_award", params, ServicesNameEnum.ump_services.getCode());
    }

    private void allActivityLottery(String lotteryType, Integer costValue, Integer beforeStar, Integer afterStar, Long uid) {
        Map<String, Object> params = Maps.newHashMapWithExpectedSize(6);
        params.put("activity_type", "sea_star_parade_activity");
        params.put("attribute_type", "astrology_activity");
        params.put("lottery_type", lotteryType);
        params.put("cost_value", costValue);
        params.put("before_star", beforeStar);
        params.put("after_star", afterStar);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_lottery", params, ServicesNameEnum.ump_services.getCode());
    }

}
