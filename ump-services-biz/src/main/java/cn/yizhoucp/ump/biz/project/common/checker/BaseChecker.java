package cn.yizhoucp.ump.biz.project.common.checker;

import cn.yizhoucp.ms.core.vo.userservices.UserBaseVO;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @Date 2022/8/9 14:54
 * @Version 1.0
 */
@Slf4j
@Data
public abstract class BaseChecker implements Checker {

    private Map<CheckerChainEnum, Checker> next = new ConcurrentHashMap<>();

    @Override
    public Boolean check(CheckerContext context, JSONObject param) {
        if (Objects.isNull(context) || Objects.isNull(param) || Objects.isNull(context.getChainEnum())) {
            log.debug("参数错误 context:{}, param:{}", context, param);
            return Boolean.FALSE;
        }
        Boolean flag = doCheck(context, param);
        if (!flag) {
            log.debug("{} 验证失败, context:{}, param:{}", this.getClass().getSimpleName(), context, param);
            return Boolean.FALSE;
        }
        if (Objects.isNull(next) || Objects.isNull(next.get(context.getChainEnum()))) {
            return Boolean.TRUE;
        }
        return next.get(context.getChainEnum()).check(context, param);
    }

    @Override
    public void add(CheckerChainEnum chainEnum, Checker checker) {
        next.put(chainEnum, checker);
    }

    /**
     * 参数这里看
     * @see cn.yizhoucp.ump.biz.project.biz.manager.appAdSpace.AppAdSpaceManager#strategyCheck(String, String, String, Long, String, UserBaseVO)
     * @param context
     * @param param
     * @return
     */
    protected abstract Boolean doCheck(CheckerContext context, JSONObject param);

}
