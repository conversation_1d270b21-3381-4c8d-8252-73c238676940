package cn.yizhoucp.ump.biz.project.dal.mybatis.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * 情感纽带关系合并表实体类
 */
@Data
@TableName("emotional_bond_summary")
public class EmotionalBondSummaryDO {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 主动方用户ID
     */
    private Long userId;

    /**
     * 主动方用户选择的角色
     */
    private String userSelectedRole;

    /**
     * 被动方用户ID
     */
    private Long oppositeId;

    /**
     * 被动方用户选择的角色
     */
    private String oppositeSelectedRole;

    /**
     * 用户ID对合并标识（小ID_大ID）
     */
    private String mergedUserPair;

    /**
     * 关系特效标识
     */
    private String effectKey;

    /**
     * 关系叠加次数
     */
    private Integer bondCount;

    /**
     * 最后一次激活时间
     */
    private Date lastActiveTime;

    /**
     * 合并状态 0-未激活 1-已激活
     */
    private Integer status;

    /**
     * 合并后过期时间
     */
    private Date expireTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
