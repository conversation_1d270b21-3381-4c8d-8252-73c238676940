package cn.yizhoucp.ump.biz.project.biz.constant.activity;

import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

public class LuckyBagHuntConstant {

    /** 活动碎片集合 */
    public static final Set<String> ACTIVITY_GIFT_SET = Sets.newHashSet("BZSP1_GIFT",
            "BZSP2_GIFT",
            "BZSP3_GIFT",
            "BZSP4_GIFT",
            "BZSP5_GIFT",
            "BZSP6_GIFT");
    /** 用户解锁区域 redis-hash（uid） */
    public static final String UNLOCK_AREA_DETAIL = "ump:luckyBagHunt:unlockAreaDetail_%s";
    /** 福袋碎片奖池 */
    public static final String LUCKY_BAG_POOL = "luckyBagHuntExt";
    /** 已解锁全部区域标识 redis-hash */
    public static final String GAME_DONE_FLAG = "ump:luckyBagHunt:gameDoneFlag_%s";
    /** 分区数量 */
    public static final Integer PARTITION_NUM = 8;

    public static final List<String> BZSP_GIFT_LIST = Arrays.asList("BZSP1_GIFT", "BZSP2_GIFT", "BZSP3_GIFT", "BZSP4_GIFT", "BZSP5_GIFT", "BZSP6_GIFT");

    @Getter
    @AllArgsConstructor
    public enum AreaEnum {
        Area1(1), Area2(2), Area3(5), Area4(11), Area5(19), Area6(38), Area7(68), Area8(125);
        private Integer limit;
    }

}
