package cn.yizhoucp.ump.biz.project.biz.enums.activity.luckyCharm;


import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Getter
@AllArgsConstructor
public enum TaskCodeEnum {
    receiveGift("JJL_GIFT", -1L, null),
    sendGiftFamily("JL_GIFT", -1L, null),
    sendGiftRoom("JL_GIFT", -1L, null),
    luckyBag1("JL_GIFT", 1L, "charm1"),
    luckyBag2("JL_GIFT", 3L, "charm3"),
    luckyBag3("JL_GIFT", 10L, "charm10"),
    sendMsg("JL_GIFT", 1L, "charm1"),
    video1("JL_GIFT", 1L, "charm1"),
    video2("JL_GIFT", 3L, "charm3"),
    invite("JL_GIFT", 3L, "charm3");

    public static final Map<String, TaskCodeEnum> map = new ConcurrentHashMap<>();

    private String prizeKey;
    private Long prizeNum;
    private String popCode;

    static {
        for (TaskCodeEnum item : values()) {
            map.put(item.name(), item);
        }
    }

    public static TaskCodeEnum get(String taskCode) {
        if (StringUtils.isBlank(taskCode)) {
            return null;
        }
        return map.get(taskCode);
    }

    public String getBizPopCode(String giftKey) {
        switch (this) {
            case receiveGift:
                if ("JGLR_GIFT".equals(giftKey)) {
                    return "gold-charm1";
                } else if ("XNYH2_GIFT".equals(giftKey)) {
                    return "gold-charm2";
                } else if ("MYSG_gift".equals(giftKey)) {
                    return "gold-charm3";
                }
            case sendGiftFamily:
                if ("XBZ_GIFT".equals(giftKey)) {
                    return "charm1";
                } else if ("XNH_GIFT".equals(giftKey)) {
                    return "charm15";
                } else if ("TYY_GIFT".equals(giftKey)) {
                    return "charm100";
                }
            case sendGiftRoom:
                if ("FQMM_GIFT".equals(giftKey)) {
                    return "charm2";
                } else if ("ZCJL_GIFT".equals(giftKey)) {
                    return "charm20";
                } else if ("WSSF_GIFT".equals(giftKey)) {
                    return "charm145";
                }
            default:
                return getPopCode();
        }
    }

    public Long getPrizeNum(String giftKey) {
        switch (this) {
            case receiveGift:
                if ("JGLR_GIFT".equals(giftKey)) {
                    return 1L;
                } else if ("XNYH2_GIFT".equals(giftKey)) {
                    return 2L;
                } else if ("MYSG_gift".equals(giftKey)) {
                    return 3L;
                }
            case sendGiftFamily:
                if ("XBZ_GIFT".equals(giftKey)) {
                    return 1L;
                } else if ("XNH_GIFT".equals(giftKey)) {
                    return 15L;
                } else if ("TYY_GIFT".equals(giftKey)) {
                    return 100L;
                }
            case sendGiftRoom:
                if ("FQMM_GIFT".equals(giftKey)) {
                    return 2L;
                } else if ("ZCJL_GIFT".equals(giftKey)) {
                    return 20L;
                } else if ("WSSF_GIFT".equals(giftKey)) {
                    return 145L;
                }
            default:
                return getPrizeNum();
        }
    }

}
