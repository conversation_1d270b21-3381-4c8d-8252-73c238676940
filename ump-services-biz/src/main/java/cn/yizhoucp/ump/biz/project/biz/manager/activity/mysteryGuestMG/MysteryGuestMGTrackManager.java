package cn.yizhoucp.ump.biz.project.biz.manager.activity.mysteryGuestMG;

import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.enums.ServicesNameEnum;
import cn.yizhoucp.starter.cassandra.base.kafka.YzKafkaProducerManager;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 埋点
 */
@AllArgsConstructor
@Data
@Service
public class MysteryGuestMGTrackManager {

    @Resource
    private YzKafkaProducerManager yzKafkaProducerManager;

    /**
     * 通用埋点_活动抽奖
     *
     * @param uid
     * @param poolCode
     * @param awardCount  获得奖励的个数
     * @param awardAmount 奖品对应的金币价值
     */
    public void allActivityLottery(Long uid, String poolCode, String awardKey, Long awardAmount, Integer awardCount) {
        Map<String, Object> params = new HashMap<>();
        params.put("activity_type", "secret_magic_show");
        params.put("attribute_type", "platform_activity");
        params.put("pool_code", poolCode);
        params.put("award_key", awardKey);
        params.put("award_amount", awardAmount);
        params.put("award_count", awardCount);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_lottery", params, ServicesNameEnum.ump_services.getCode());
    }

    /**
     * 通用埋点_ 活动领取奖励
     */
    public void allActivityReceiveAward(Long uid, String awardKey, String taskType, Long awardAmount, Integer awardCount) {
        Map<String, Object> params = Maps.newHashMapWithExpectedSize(6);
        params.put("activity_type", "secret_magic_show");
        params.put("attribute_type", "platform_activity");
        params.put("award_key", awardKey);
        params.put("award_type", taskType);
        params.put("award_amount", awardAmount);
        params.put("award_count", awardCount);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_receive_award", params, ServicesNameEnum.ump_services.getCode());
    }

    /**
     * 通用埋点_活动任务完成
     *
     * @param uid
     * @param awardCount 获得奖励的个数
     */
    public void allActivityTaskFinish(Long uid, String taskType, Long awardCount) {
        Map<String, Object> params = Maps.newHashMapWithExpectedSize(6);
        params.put("activity_type", "secret_magic_show");
        params.put("attribute_type", "platform_activity");
        params.put("task_type", taskType);
        params.put("award_count", awardCount);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_task_finish", params, ServicesNameEnum.ump_services.getCode());
    }
}
