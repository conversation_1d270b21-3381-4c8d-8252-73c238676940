package cn.yizhoucp.ump.biz.project.biz.manager.activity.wisteria_love_1.strategy;

import cn.yizhoucp.ump.biz.project.biz.manager.activity.wisteria_love_1.common.WisteriaLove1Enums;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.StrategyManager;
import org.springframework.stereotype.Component;


/**
 * 紫藤萝之恋 策略选择类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 18:05 2025/4/18
 */
@Component
public class WisteriaLove1StrategyChoose extends StrategyManager<WisteriaLove1Enums.ButtonEnum, ExecutableStrategy> {
}
