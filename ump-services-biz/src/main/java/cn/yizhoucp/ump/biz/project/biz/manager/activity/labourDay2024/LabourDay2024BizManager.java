package cn.yizhoucp.ump.biz.project.biz.manager.activity.labourDay2024;

import cn.yizhoucp.ms.core.base.convert.SexType;
import cn.yizhoucp.ms.core.base.enums.GiftTypeEnum;
import cn.yizhoucp.ms.core.base.enums.GiftWay;
import cn.yizhoucp.ms.core.base.env.DynamicEnvManager;
import cn.yizhoucp.ms.core.base.util.AppUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant;
import cn.yizhoucp.ump.biz.project.biz.manager.userActivity.ActivityManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.FeignUserService;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityUrlUtil;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.common.handler.HandlerContext;
import cn.yizhoucp.ump.biz.project.common.handler.component.bizHandler.BuoyInfoHandler;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ActivityDO;
import cn.yizhoucp.ump.biz.project.dto.adSpace.BuoyInfoDTO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.ACTIVITY_CODE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.BOX_GIFT_SET;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.GIFT_VALUE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.INCOME_CALL_WARN_FRIEND;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.LOTTERY_GIFT_KEY;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.MAN_RANK;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.TASK_CUR_FINISH_TIMES;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.TOUR_VALUE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.Task.TASK_1;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.Task.TASK_2;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.Task.TASK_3;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.Task.TASK_4;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.Task.TASK_5;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.Task.TASK_6;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.Task.TASK_7;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.WOMAN_RANK;

@Service
@Slf4j
public class LabourDay2024BizManager implements BuoyInfoHandler {

    @Value("${spring.profiles.active}")
    private String env;
    @Resource
    private Environment environment;
    @Resource
    private RedisManager redisManager;
    @Resource
    private FeignUserService feignUserService;
    @Resource
    private ActivityManager activityManager;
    @Resource
    private LabourDay2024RankManager labourDay2024RankManager;
    @Resource
    private LabourDay2024IndexManager labourDay2024IndexManager;
    @Resource
    private LabourDay2024TrackManager labourDay2024TrackManager;

    @ActivityCheck(activityCode = ACTIVITY_CODE, isThrowException = false)
    public Boolean sendGiftHandle(BaseParam param, List<CoinGiftGivedModel> coinGiftGivedModelList) {
        Long uid = param.getUid();
        Long appId = param.getAppId();
        UserVO userVO = feignUserService.getBasic(uid, appId).successData();
        int stage = stage();

        for (CoinGiftGivedModel coinGiftGivedModel : coinGiftGivedModelList) {
            if (log.isDebugEnabled()) {
                log.debug("coinGiftGivedModel {}", JSON.toJSONString(coinGiftGivedModel));
            }

            Long toUid = coinGiftGivedModel.getToUid();
            UserVO toUserVO = feignUserService.getBasic(toUid, appId).successData();
            String splicUserId = AppUtil.splicUserId(uid, toUid);

            if (GiftWay.NORMAL.getCode().equals(coinGiftGivedModel.getGiftWay())) {
                // 礼物值
                if (GiftTypeEnum.relation.getCode().equals(coinGiftGivedModel.getGiftType())) {
                    LocalDateTime now = LocalDateTime.now();
                    int hour = now.getHour();
                    if (hour >= 20) {
                        Long incomeCallWarnFriend = redisManager.getLong(String.format(INCOME_CALL_WARN_FRIEND, param.getUid()));
                        if (incomeCallWarnFriend == null) {
                            incomeCallWarnFriend = labourDay2024IndexManager.getMaxIntimacyOppositeSexCloseFriend(param);
                        }
                        if (Objects.equals(incomeCallWarnFriend, toUid)) {
                            redisManager.incrLong(String.format(GIFT_VALUE, DateUtil.getNowYyyyMMdd(), splicUserId), coinGiftGivedModel.getCoin(), DateUtil.ONE_MONTH_SECOND);
                        }
                    }
                // 榜单
                } else if (LOTTERY_GIFT_KEY.contains(coinGiftGivedModel.getLotteryGiftKey())) {
                    if (userVO != null && SexType.MAN.equals(userVO.getSex())) {
                        labourDay2024RankManager.incrRankValue(uid, coinGiftGivedModel.getCoin(), String.format(MAN_RANK, stage));
                    }
                    if (toUserVO != null && SexType.WOMAN.equals(toUserVO.getSex())) {
                        labourDay2024RankManager.incrRankValue(toUid, coinGiftGivedModel.getCoin(), String.format(WOMAN_RANK, stage));
                    }
                // 环游值
                } else {
                    if (!"SL_GIFT".equals(coinGiftGivedModel.getGiftKey())) {
                        redisManager.incrLong(String.format(TOUR_VALUE, DateUtil.getNowYyyyMMdd(), splicUserId), coinGiftGivedModel.getCoin(), DateUtil.ONE_MONTH_SECOND);
                    }
                }
            }

            String lotteryGiftKey = coinGiftGivedModel.getLotteryGiftKey();
            Long productCount = coinGiftGivedModel.getProductCount();
            String giftKey = coinGiftGivedModel.getGiftKey();
            // 每日任务
            if (userVO != null) {
                if (SexType.MAN.equals(userVO.getSex())) {
                    if ("LAXH_BOX".equals(lotteryGiftKey)) {
                        Long task2 = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, uid, DateUtil.getNowYyyyMMdd(), TASK_2), productCount, DateUtil.ONE_MONTH_SECOND);
                        if (task2 >= LabourDay2024Constant.Task.TASK_2.getFinishTimes() && task2 - productCount < LabourDay2024Constant.Task.TASK_2.getFinishTimes()) {
                            labourDay2024TrackManager.allActivityTaskFinish(TASK_2.getTaskTitle(), null, null, null, uid);
                        }
                        Long addNum = redisManager.sSetExpire(String.format(BOX_GIFT_SET, uid, DateUtil.getNowYyyyMMdd(), lotteryGiftKey), DateUtil.ONE_MONTH_SECOND, giftKey);
                        if (addNum != null && addNum != 0) {
                            Long task3 = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, uid, DateUtil.getNowYyyyMMdd(), TASK_3), productCount, DateUtil.ONE_MONTH_SECOND);
                            if (task3 >= LabourDay2024Constant.Task.TASK_3.getFinishTimes() && task3 - productCount < LabourDay2024Constant.Task.TASK_3.getFinishTimes()) {
                                labourDay2024TrackManager.allActivityTaskFinish(TASK_3.getTaskTitle(), null, null, null, uid);
                            }
                        }
                    } else if ("LMHX_BOX".equals(lotteryGiftKey)) {
                        Long task4 = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, uid, DateUtil.getNowYyyyMMdd(), TASK_4), productCount, DateUtil.ONE_MONTH_SECOND);
                        if (task4 >= LabourDay2024Constant.Task.TASK_4.getFinishTimes() && task4 - productCount < LabourDay2024Constant.Task.TASK_4.getFinishTimes()) {
                            labourDay2024TrackManager.allActivityTaskFinish(TASK_4.getTaskTitle(), null, null, null, uid);
                        }
                        Long addNum = redisManager.sSetExpire(String.format(BOX_GIFT_SET, uid, DateUtil.getNowYyyyMMdd(), lotteryGiftKey), DateUtil.ONE_MONTH_SECOND, giftKey);
                        if (addNum != null && addNum != 0) {
                            Long task5 = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, uid, DateUtil.getNowYyyyMMdd(), TASK_5), productCount, DateUtil.ONE_MONTH_SECOND);
                            if (task5 >= LabourDay2024Constant.Task.TASK_5.getFinishTimes() && task5 - productCount < LabourDay2024Constant.Task.TASK_5.getFinishTimes()) {
                                labourDay2024TrackManager.allActivityTaskFinish(TASK_5.getTaskTitle(), null, null, null, uid);
                            }
                        }
                    } else if ("XDXH_BOX".equals(lotteryGiftKey)) {
                        Long task6 = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, uid, DateUtil.getNowYyyyMMdd(), TASK_6), productCount, DateUtil.ONE_MONTH_SECOND);
                        if (task6 >= LabourDay2024Constant.Task.TASK_6.getFinishTimes() && task6 - productCount < LabourDay2024Constant.Task.TASK_6.getFinishTimes()) {
                            labourDay2024TrackManager.allActivityTaskFinish(TASK_6.getTaskTitle(), null, null, null, uid);
                        }
                        Long addNum = redisManager.sSetExpire(String.format(BOX_GIFT_SET, uid, DateUtil.getNowYyyyMMdd(), lotteryGiftKey), DateUtil.ONE_MONTH_SECOND, giftKey);
                        if (addNum != null && addNum != 0) {
                            Long task7 = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, uid, DateUtil.getNowYyyyMMdd(), TASK_7), productCount, DateUtil.ONE_MONTH_SECOND);
                            if (task7 >= LabourDay2024Constant.Task.TASK_7.getFinishTimes() && task7 - productCount < LabourDay2024Constant.Task.TASK_7.getFinishTimes()) {
                                labourDay2024TrackManager.allActivityTaskFinish(TASK_7.getTaskTitle(), null, null, null, uid);
                            }
                        }
                    }
                }
            }
            if (toUserVO != null) {
                if (SexType.WOMAN.equals(toUserVO.getSex())) {
                    if ("LAXH_BOX".equals(lotteryGiftKey)) {
                        Long task9 = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, toUid, DateUtil.getNowYyyyMMdd(), TASK_2), productCount, DateUtil.ONE_MONTH_SECOND);
                        if (task9 >= LabourDay2024Constant.Task.TASK_2.getFinishTimes() && task9 - productCount < LabourDay2024Constant.Task.TASK_2.getFinishTimes()) {
                            labourDay2024TrackManager.allActivityTaskFinish(TASK_2.getTaskTitle(), null, null, null, toUid);
                        }
                        Long addNum = redisManager.sSetExpire(String.format(BOX_GIFT_SET, toUid, DateUtil.getNowYyyyMMdd(), lotteryGiftKey), DateUtil.ONE_MONTH_SECOND, giftKey);
                        if (addNum != null && addNum != 0) {
                            Long task10 = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, toUid, DateUtil.getNowYyyyMMdd(), TASK_3), productCount, DateUtil.ONE_MONTH_SECOND);
                            if (task10 >= LabourDay2024Constant.Task.TASK_3.getFinishTimes() && task10 - productCount < LabourDay2024Constant.Task.TASK_3.getFinishTimes()) {
                                labourDay2024TrackManager.allActivityTaskFinish(TASK_3.getTaskTitle(), null, null, null, toUid);
                            }
                        }
                    } else if ("LMHX_BOX".equals(lotteryGiftKey)) {
                        Long task11 = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, toUid, DateUtil.getNowYyyyMMdd(), TASK_4), productCount, DateUtil.ONE_MONTH_SECOND);
                        if (task11 >= LabourDay2024Constant.Task.TASK_4.getFinishTimes() && task11 - productCount < LabourDay2024Constant.Task.TASK_4.getFinishTimes()) {
                            labourDay2024TrackManager.allActivityTaskFinish(TASK_4.getTaskTitle(), null, null, null, toUid);
                        }
                        Long addNum = redisManager.sSetExpire(String.format(BOX_GIFT_SET, toUid, DateUtil.getNowYyyyMMdd(), lotteryGiftKey), DateUtil.ONE_MONTH_SECOND, giftKey);
                        if (addNum != null && addNum != 0) {
                            Long task12 = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, toUid, DateUtil.getNowYyyyMMdd(), TASK_5), productCount, DateUtil.ONE_MONTH_SECOND);
                            if (task12 >= LabourDay2024Constant.Task.TASK_5.getFinishTimes() && task12 - productCount < LabourDay2024Constant.Task.TASK_5.getFinishTimes()) {
                                labourDay2024TrackManager.allActivityTaskFinish(TASK_5.getTaskTitle(), null, null, null, toUid);
                            }
                        }
                    } else if ("XDXH_BOX".equals(lotteryGiftKey)) {
                        Long task13 = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, toUid, DateUtil.getNowYyyyMMdd(), TASK_6), productCount, DateUtil.ONE_MONTH_SECOND);
                        if (task13 >= LabourDay2024Constant.Task.TASK_6.getFinishTimes() && task13 - productCount < LabourDay2024Constant.Task.TASK_6.getFinishTimes()) {
                            labourDay2024TrackManager.allActivityTaskFinish(TASK_6.getTaskTitle(), null, null, null, toUid);
                        }
                        Long addNum = redisManager.sSetExpire(String.format(BOX_GIFT_SET, toUid, DateUtil.getNowYyyyMMdd(), lotteryGiftKey), DateUtil.ONE_MONTH_SECOND, giftKey);
                        if (addNum != null && addNum != 0) {
                            Long task14 = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, toUid, DateUtil.getNowYyyyMMdd(), TASK_7), productCount, DateUtil.ONE_MONTH_SECOND);
                            if (task14 >= LabourDay2024Constant.Task.TASK_7.getFinishTimes() && task14 - productCount < LabourDay2024Constant.Task.TASK_7.getFinishTimes()) {
                                labourDay2024TrackManager.allActivityTaskFinish(TASK_7.getTaskTitle(), null, null, null, toUid);
                            }
                        }
                    }
                }
            }
        }

        return Boolean.TRUE;
    }

    @ActivityCheck(activityCode = ACTIVITY_CODE, isThrowException = false)
    public void initiateFormOfficialCombine(String type, Long fromUid, Long toUid) {
        log.info("type {} fromUid {} toUid {}", type, fromUid, toUid);
        if (type == null || fromUid == null || toUid == null) {
            return;
        }

        if ("form".equals(type)) {
            redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, fromUid, DateUtil.getNowYyyyMMdd(), TASK_1), 1L, DateUtil.ONE_MONTH_SECOND);
            redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, toUid, DateUtil.getNowYyyyMMdd(), TASK_1), 1L, DateUtil.ONE_MONTH_SECOND);
            labourDay2024TrackManager.allActivityTaskFinish(TASK_1.getTaskTitle(), null, null, null, fromUid);
            labourDay2024TrackManager.allActivityTaskFinish(TASK_1.getTaskTitle(), null, null, null, toUid);
        }
    }

    public int stage() {
        ActivityDO activityDO = activityManager.getActivityInfo(BaseParam.builder().unionId("wGF30Qq8c3").build(), ACTIVITY_CODE);
        LocalDateTime startTime = activityDO.getStartTime().truncatedTo(ChronoUnit.DAYS);
        LocalDateTime now = LocalDateTime.now();
        log.debug("startTime {} now {}", startTime, now);

        long between = ChronoUnit.DAYS.between(startTime, now);
        if (between < 7) {
            return 1;
        } else if (between < 14) {
            return 2;
        }  else {
            return 3;
        }
    }

    @Override
    public String getActivityCode() {
        return ACTIVITY_CODE;
    }

    @Override
    public Function<HandlerContext, BuoyInfoDTO> getBuoyInfoHandler() {
        return handlerContext -> {
            if (handlerContext.getToUid() == null) {
                return BuoyInfoDTO.builder().build();
            }

            BaseParam param = BaseParam.ofMDC();
            ActivityDO activityDO = activityManager.getActivityInfo(param, this.getActivityCode());
            return BuoyInfoDTO.builder().routerUrl(this.getActivityUrl(param, env, environment, activityDO.getActivityUrl()) + "&toUid=" + handlerContext.getToUid()).build();
        };
    }

    private String getActivityUrl(BaseParam param, String env, Environment environment, String activityUrl) {
        return String.format(
                ActivityUrlUtil.getH5BaseUrl(param.getUnionId(), env, Boolean.TRUE, environment.getProperty(DynamicEnvManager.GLOBAL_ENV_NAME)) + activityUrl + "?uid=%s&from=chat_buoy",
                param.getUid()
        );
    }

}
