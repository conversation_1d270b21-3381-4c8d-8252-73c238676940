package cn.yizhoucp.ump.biz.project.biz.manager.activity.patRoom.internal;

import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

@Service
public class PatRoomRedisManager {

    @Resource
    private RedisManager redisManager;

    public void incrementTaskProgress(String taskCode, Long num, Long uid) {
        String key = String.format(PatRoomConstant.TASK_FINISHED_KEY, PatRoomConstant.ACTIVITY_CODE, taskCode, uid);
        redisManager.incrLong(key, num, DateUtil.ONE_MONTH_SECOND);
    }

    public void decrementTaskProgress(String taskCode, Long num, Long uid) {
        String key = String.format(PatRoomConstant.TASK_FINISHED_KEY, PatRoomConstant.ACTIVITY_CODE, taskCode, uid);
        redisManager.decrLong(key, num, DateUtil.ONE_MONTH_SECOND);
    }

    public Long getTaskProgress(String taskCode, Long uid) {
        String key = String.format(PatRoomConstant.TASK_FINISHED_KEY, PatRoomConstant.ACTIVITY_CODE, taskCode, uid);
        return Optional.ofNullable(redisManager.getLong(key)).orElse(0L);
    }

    public Boolean getTaskRewarded(String taskName, Long uid) {
        String key = String.format(PatRoomConstant.TASK_REWARDED_KEY, PatRoomConstant.ACTIVITY_CODE, taskName, uid);
        return redisManager.hasKey(key);
    }

    public Boolean setTaskRewarded(String taskName, Long uid) {
        String key = String.format(PatRoomConstant.TASK_REWARDED_KEY, PatRoomConstant.ACTIVITY_CODE, taskName, uid);
        return redisManager.set(key, System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND);
    }

    public JSONObject getRoomActivityRelationId(Long uid) {
        Object result = redisManager.get(String.format(PatRoomConstant.RELATION_KEY, uid));
        if (result == null) {
            return null;
        }
        return JSONObject.parseObject(result.toString());
    }
}
