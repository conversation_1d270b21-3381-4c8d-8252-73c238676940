package cn.yizhoucp.ump.biz.project.biz.manager.activity.midAutumnFestival2024;

import cn.hutool.core.util.ObjectUtil;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.jimu.draw.inner.DrawLogItem;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.log.LogComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.AbstractDrawTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawContext;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawLogParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.doDrawStrategy.common.ProbStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawLogDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.dal.service.DrawLogService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@Slf4j
public class MidAutumn2024DrawManager extends AbstractDrawTemplate {

    @Resource
    private ProbStrategy probStrategy;

    @Resource
    private RedisManager redisManager;

    @Resource
    private LogComponent logComponent;

    @Resource
    private DrawLogService drawLogService;
    @Resource
    private MidAutumn2024TrackManager midAutumn2024TrackManager;


    @Override
    protected void resourceCheck(DrawContext context) {
        for (MidAutumn2024Constant.SendGiftBox item : MidAutumn2024Constant.SendGiftBox.values()) {
            if (item.getBoxKey().equals(context.getDrawParam().getPoolCode())) {
                context.getDrawParam().setNoSendPrize(Boolean.TRUE);
                return;
            }
        }
        if (MidAutumn2024Constant.DRAW_POOL_CODE.equals(context.getDrawParam().getPoolCode())) {
            String drawItemKey = MidAutumn2024Constant.createDrawItemKey(context.getDrawParam().getUid());
            Long drawItem = Optional.ofNullable(redisManager.getLong(drawItemKey)).orElse(0L);
            if (drawItem < 1) {
                throw new ServiceException(ErrorCode.INVALID_PARAM, "相思雀数量不足");
            }
            String drawPoolKey = MidAutumn2024Constant.createPoolCodeKey(context.getDrawParam().getUid());
            String drawPoolJson = redisManager.getString(drawPoolKey);
            if (ObjectUtil.isNull(drawPoolJson)) {
                return;
            }
            Map<Object, Object> poolCodeMap = JSON.parseObject(drawPoolJson, Map.class);
            if (ObjectUtil.isNotNull(poolCodeMap)) {
                context.getDrawParam().setPoolCode(String.valueOf(poolCodeMap.get("poolCode")));
            }
        }
    }


    @Override
    protected void draw(DrawContext context) {
        List<DrawPoolItemDTO> drawPoolItemDTOList = probStrategy.getDrawPoolItems(context.getDrawPoolItemDOS(),
                context.getDrawParam().getTimes(),
                Boolean.TRUE);
        context.setPrizeItemList(drawPoolItemDTOList);
    }

    @Override
    protected void deductResource(DrawContext context) {
        if (MidAutumn2024Constant.POOL_CODES.contains(context.getDrawParam().getPoolCode())) {
            String drawItemKey = MidAutumn2024Constant.createDrawItemKey(context.getDrawParam().getUid());
            redisManager.decrLong(drawItemKey, context.getDrawParam().getTimes(), DateUtil.ONE_MONTH_SECOND);
        }
    }

    @Override
    protected void doCallback(DrawContext context) {
        //抽奖日志
        if (MidAutumn2024Constant.POOL_CODES.contains(context.getDrawParam().getPoolCode())) {
            //抽奖埋点
            for (DrawPoolItemDTO item : context.getPrizeItemList()) {
                midAutumn2024TrackManager.allActivityLottery(context.getDrawParam().getUid(), context.getDrawParam().getPoolCode(), item.getTargetTimes().toString(), item.getDrawPoolItemDO().getItemKey(), item.getDrawPoolItemDO().getItemValueGold());
            }
            DrawParam drawParam = context.getDrawParam();
            List<DrawPoolItemDTO> prizeItemList = context.getPrizeItemList();
            prizeItemList.forEach(item -> item.getDrawPoolItemDO().setPoolCode(MidAutumn2024Constant.DRAW_POOL_CODE));
            logComponent.putDrawLog(drawParam.getBaseParam(), drawParam.getActivityCode(), prizeItemList);
        }
    }

    @Override
    protected List<DrawLogItem> drawLogWrapper(DrawLogParam param, List<DrawLogDO> drawLogDOList) {
        List<DrawLogItem> drawLogItemList = drawLogDOList.stream().map(drawLogDO -> {
            DrawPoolItemDTO drawPoolItemDTO = JSON.parseObject(drawLogDO.getLogJson(), DrawPoolItemDTO.class);
            DrawPoolItemDO drawPoolItemDO = drawPoolItemDTO.getDrawPoolItemDO();
            return DrawLogItem.builder()
                    .icon(drawPoolItemDO.getItemIcon())
                    .valueGold(drawPoolItemDO.getItemValueGold())
                    .text(drawPoolItemDO.getItemName())
                    .itemNum(drawPoolItemDO.getItemNum() * drawPoolItemDTO.getTargetTimes())
                    .time(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm").format(drawLogDO.getCreateTime())).build();
        }).collect(Collectors.toList());
        log.debug("drawLogItemList {}", JSON.toJSONString(drawLogItemList));
        Collections.reverse(drawLogItemList);

        return drawLogItemList;
    }

}
