package cn.yizhoucp.ump.biz.project.biz.manager.activity.dragon_Boat_Festival_2025.strategy;

import cn.hutool.core.collection.CollUtil;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.annotation.NoRepeatSubmit;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.dragon_Boat_Festival_2025.common.DragonBoatFestival2025Constant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.dragon_Boat_Festival_2025.common.DragonBoatFestival2025RedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class DragonBoatFestival2025Claimed implements ExecutableStrategy {
    @Resource
    DragonBoatFestival2025RedisManager dragonBoatFestival2025RedisManager;
    @Resource
    ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    SendPrizeManager sendPrizeManager;

    @Override
    @NoRepeatSubmit(time = 3)
    @ActivityCheck(activityCode = DragonBoatFestival2025Constant.ACTIVITY_CODE)
    public Object execute(ButtonEventParam buttonEventParam) {
        Long uid = buttonEventParam.getBaseParam().getUid();
        Boolean claimed = dragonBoatFestival2025RedisManager.getRewardClaimed(uid);
        if (Boolean.TRUE.equals(claimed)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "已经领取过啦～");
        }
        List<ScenePrizeDO> listBySceneCode = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(),
                DragonBoatFestival2025Constant.ACTIVITY_CODE,
                DragonBoatFestival2025Constant.REWARD_GIFT_KEY
        );
        if (CollUtil.isEmpty(listBySceneCode)) {
            return Boolean.FALSE;
        }
        dragonBoatFestival2025RedisManager.setRewardClaimed(uid);
        sendPrizeManager.sendPrize(
                BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(uid).build(),
                listBySceneCode.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, uid)).collect(Collectors.toList())
        );
        return Boolean.TRUE;
    }
}
