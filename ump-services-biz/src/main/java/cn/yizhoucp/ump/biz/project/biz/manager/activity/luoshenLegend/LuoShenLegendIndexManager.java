package cn.yizhoucp.ump.biz.project.biz.manager.activity.luoshenLegend;

import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.luoshenLegend.common.LuoShenLegendConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.luoshenLegend.common.LuoShenLegendEnums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.luoshenLegend.common.LuoShenLegendRedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.luoshenLegend.vo.LuoShenLegendIndexVO;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.IndexManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


/**
 * 洛神赋首页
 *
 * <AUTHOR>
 * @version V1.0
 * @since 19:24 2025/1/14
 */
@Slf4j
@Service
public class LuoShenLegendIndexManager implements IndexManager {

    @Resource
    private LuoShenLegendRedisManager luoShenLegendRedisManager;

    @Override
    public LuoShenLegendIndexVO getIndex(BaseParam param, Long toUid, String extData) {
        LuoShenLegendIndexVO luoShenLegendIndexVO = new LuoShenLegendIndexVO();
        Long uid = param.getUid();
        luoShenLegendIndexVO.setGoddess(buildGoddess());
        luoShenLegendIndexVO.setFinallyGoddess(buildFinallyGoddess(uid));
        Boolean taskIsClaim = luoShenLegendRedisManager.taskIsClaim(param.getUid());
        luoShenLegendIndexVO.setClaimStatus(taskIsClaim);
        Long progress = luoShenLegendRedisManager.getTaskProgress(param.getUid());
        if(progress > LuoShenLegendConstant.MAX_TASK_PROGRESS){
            progress = LuoShenLegendConstant.MAX_TASK_PROGRESS;
        }
        luoShenLegendIndexVO.setProgress(progress);
        return luoShenLegendIndexVO;
    }


    private LuoShenLegendIndexVO.FinallyGoddess buildFinallyGoddess(Long uid) {
        LuoShenLegendIndexVO.FinallyGoddess finallyGoddess = new LuoShenLegendIndexVO.FinallyGoddess();
        finallyGoddess.setGoldCode(LuoShenLegendEnums.GoddessEnum.CAMELLIA_GOD.getGodCode());
        finallyGoddess.setLockStatus(getFinallyGoddessLockStatus(uid));
        return finallyGoddess;
    }

    private Boolean getFinallyGoddessLockStatus(Long uid) {
        Long progress = luoShenLegendRedisManager.getTaskProgress(uid);
        return progress >= LuoShenLegendConstant.MAX_TASK_PROGRESS;
    }

    private List<LuoShenLegendIndexVO.Goddess> buildGoddess() {
        List<LuoShenLegendIndexVO.Goddess> goddessList = new ArrayList<>();
        LuoShenLegendEnums.GoddessEnum currentLockGoddess = luoShenLegendRedisManager.getCurrentGoddess();
        for (LuoShenLegendEnums.GoddessEnum goddessEnum : LuoShenLegendEnums.GoddessEnum.values()) {
            LuoShenLegendIndexVO.Goddess goddess = new LuoShenLegendIndexVO.Goddess();
            goddess.setGoldCode(goddessEnum.getGodCode());
            goddess.setLockStatus(goddessEnum.equals(currentLockGoddess));
            goddessList.add(goddess);
        }
        return goddessList;
    }


    @Override
    public String getTemplateType() {
        return "";
    }

    @Override
    public String getActivityCode() {
        return "";
    }
}
