package cn.yizhoucp.ump.biz.project.biz.manager.activity.moon_screat.common;

/**
 * 月夜下的秘密常量
 *
 * <AUTHOR>
 * @version V1.0
 * @since 14:42 2025/4/23
 */
public class MoonScreatConstant {
    public static final String ACTIVITY_CODE = "moon_screat";
    public static final String TASK_PROGRESS_KEY = "ump:activity:%s:task_progress:%s:%s:%s:%s";
    public static final String USER_TASK_GIFT_CODE = "user_task_gift";
    public static final String ROOM_TASK_GIFT_CODE = "room_task_gift";
    public static final String ROOM_RANKING_KEY = "room";
    public static final String PERSONAL_RANKING_KEY = "user";
    public static final String PK_SCORE_KEY = "ump:activity:%s:ps_score:%s:%s";
    public static final String HIDDEN_MESSAGE = "???待揭晓";
    public static final String TIME_INFO_KEY = "ump:activity:%s:time_info:%s";
    public static final String USER_CARD_COUNT_KEY = "ump:activity:%s:user_card_count:%s:%s";
    public static final String ROOM_CARD_COUNT_KEY = "ump:activity:%s:room_card_count:%s:%s";
    public static final String USER_FACTION_KEY = "ump:activity:%s:user_faction:%s:%s";
    public static final String USER_RANK_KEY = "ump:activity:%s:rank:user:%s";
    public static final String ROOM_RANK_KEY = "ump:activity:%s:rank:room:%s";
    public static final String SCENE_PRIZE_MAP_KEY = "ump:activity:%s:scene_prize_map";
    public static final String RANK_LOCK_KEY = "ump:activity:%s:rank:lock:%s";
    public static final String USER_CONTRIBUTION_KEY = "ump:activity:%s:user_contribution:%s:%s:%s";
    public static final String ROOM = "room";
    public static final String USER = "user";
    public static final String ICON_BASE_URL="https://res-cdn.nuan.chat/nuanliao-fronted/activity/moons-secret/%s.png";
    public static final String LARGE_ICON_BASE_URL="https://res-cdn.nuan.chat/nuanliao-fronted/activity/moons-secret/%s-large.png";
    public static final String REWARD_CODE = "reward_record";

    // 任务完成标记 key，格式：ump:activity:moon_screat:task_completed:类型(user/room):ID:等级:任务ID
    public static final String TASK_COMPLETED_KEY = "ump:activity:%s:task_completed:%s:%s:%s:%s:%s";

    // 任务完成标记值
    public static final String TASK_COMPLETED_VALUE = "1";

    // 场景奖励缓存 key，格式：ump:activity:moon_screat:scene_prize_cache:场景码
    public static final String SCENE_PRIZE_CACHE_KEY = "ump:activity:%s:scene_prize_cache:%s";

    // 用户收集信息缓存 key，格式：ump:activity:moon_screat:personal_collection_cache:用户ID
    public static final String PERSONAL_COLLECTION_CACHE_KEY = "ump:activity:%s:personal_collection_cache:%s";

    // 房间收集信息缓存 key，格式：ump:activity:moon_screat:room_collection_cache:房间ID
    public static final String ROOM_COLLECTION_CACHE_KEY = "ump:activity:%s:room_collection_cache:%s";

    // 缓存过期时间（1小时，单位：秒）
    public static final int CACHE_EXPIRE_SECONDS = 3600;

    // 每日胜利阵营缓存 key，格式：ump:activity:moon_screat:daily_winner:日期(yyyyMMdd)
    public static final String DAILY_WINNER_KEY = "ump:activity:%s:daily_winner:%s";

    // 缓存过期时间（30天，单位：秒）
    public static final int WINNER_CACHE_EXPIRE_SECONDS = 30 * 24 * 3600;
    public static final String TEST_TIMESTAMP_KEY = "ump:activity:%s:test_timestamp";
}
