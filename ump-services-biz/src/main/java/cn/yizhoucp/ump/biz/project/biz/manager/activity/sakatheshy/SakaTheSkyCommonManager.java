//package cn.yizhoucp.ump.biz.project.biz.manager.activity.sakatheshy;
//
//import cn.yizhoucp.family.api.client.FamilyFeignService;
//import cn.yizhoucp.family.api.dto.family.FamilyInfoDTO;
//import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
//import cn.yizhoucp.starter.redis.manager.RedisManager;
//import cn.yizhoucp.ump.biz.project.biz.util.ActivityTimeUtil;
//import com.baomidou.mybatisplus.core.toolkit.StringUtils;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import java.util.Objects;
//import java.util.Optional;
//
//import static cn.yizhoucp.ump.biz.project.biz.constant.RushSkyConstant.*;
//
///**
// * saka 冲上云霄通用
// *
// * @author: pepper
// */
//@Slf4j
//@Service
//public class SakaTheSkyCommonManager {
//
//    @Resource
//    private FamilyFeignService familyFeignService;
//    @Resource
//    private RedisManager redisManager;
//
//    public Boolean activitySwitch() {
//        String result = (String) redisManager.get("saka_rush_sky_switch");
//        return "true".equals(result);
//    }
//
//    public Boolean useCoupon(Long uid, Long num) {
//        redisManager.hdecr(String.format(USER_COUPON_NUM, ActivityTimeUtil.getFirstDayOfWeekStr(ACTIVITY_CODE)), uid.toString(), num, RedisManager.ONE_DAY_SECONDS * 21);
//        return Boolean.TRUE;
//    }
//
//    public FamilyInfoDTO getFamilyInfoByUid(Long uid) {
//        return familyFeignService.findFamilyInfoByUid(uid, ServicesAppIdEnum.chatie.getAppId()).successData();
//    }
//
//    public FamilyInfoDTO getFamilyInfoById(Long familyId) {
//        // todo: 加 cache
//        return familyFeignService.findFamilyInfoById(familyId, ServicesAppIdEnum.chatie.getAppId()).successData();
//    }
//
//    public Long getFamilyMedalNum(Long familyId, String dateTime) {
//        if (Objects.isNull(familyId)) {
//            return 0L;
//        }
//        if (StringUtils.isBlank(dateTime)) {
//            dateTime = ActivityTimeUtil.getToday(ACTIVITY_CODE);
//        }
//        return Optional.ofNullable((Integer) redisManager.hget(String.format(FAMILY_MEDAL_NUM, dateTime), familyId.toString())).orElse(0).longValue();
//    }
//
//    /**
//     * 获取个人当日可瓜分金币
//     *
//     * @param uid
//     * @param familyId
//     * @return
//     */
//    public Long getUserBonus(Long uid, Long familyId, String dateTime, Long userMedalNum) {
//        if (Objects.isNull(uid) || Objects.isNull(familyId)) {
//            return -1L;
//        }
//        // 获取家族勋章数量
//        Long familyMedalNum = getFamilyMedalNum(familyId, dateTime);
//        if (familyMedalNum.equals(0L)) {
//            return 0L;
//        }
//        // 获取家族可瓜分金币
//        Long oppositeFamilyId = getOppositeFamilyId(familyId, dateTime);
//        if (Objects.isNull(oppositeFamilyId)) {
//            log.info("oppositeFamilyId 为空 uid:{}, familyId:{}", uid, familyId);
//            return -1L;
//        }
//        Long familyBonus = getFamilyBonus(familyId, oppositeFamilyId, dateTime);
//        // 获取个人勋章数量
//        if (Objects.isNull(userMedalNum)) {
//            userMedalNum = getFamilyMemberMedalNum(familyId, uid, dateTime);
//        }
//        return new Double(familyBonus * userMedalNum / familyMedalNum).longValue();
//    }
//
//    public Long getFamilyMemberMedalNum(Long familyId, Long uid, String dateTime) {
//        return Optional.ofNullable((Integer) redisManager.hget(String.format(FAMILY_MEMBER_MEDAL_NUM, dateTime, familyId), uid.toString())).orElse(0).longValue();
//    }
//
//    public Long getUserCouponNum(Long uid) {
//        return Optional.ofNullable((Integer) redisManager.hget(String.format(USER_COUPON_NUM, ActivityTimeUtil.getFirstDayOfWeekStr(ACTIVITY_CODE)), uid.toString())).orElse(0).longValue();
//    }
//
//    public Long getOppositeFamilyId(Long familyId, String dateTime) {
//        Integer oppositeFamilyId = null;
//        if (Objects.isNull(oppositeFamilyId = (Integer) redisManager.hget(String.format(FAMILY_PK_GROUP, dateTime), familyId.toString()))) {
//            oppositeFamilyId = (Integer) redisManager.hget(String.format(FAMILY_PK_GROUP_INVERT, dateTime), familyId.toString());
//        }
//        return Objects.isNull(oppositeFamilyId) ? null : oppositeFamilyId.longValue();
//    }
//
//    /**
//     * 获取家族当日家族可瓜分金币
//     *
//     * @param familyId1
//     * @param familyId2
//     * @return
//     */
//    public Long getFamilyBonus(Long familyId1, Long familyId2, String dateTime) {
//        if (Objects.isNull(familyId1) || Objects.isNull(familyId2)) {
//            return -1L;
//        }
//        // 获取家族今日礼物金币流水
//        Integer familyCoinLog1 = Optional.ofNullable((Integer) redisManager.hget(String.format(FAMILY_PRIZE_COIN_LOG, dateTime), familyId1.toString())).orElse(0);
//        Integer familyCoinLog2 = Optional.ofNullable((Integer) redisManager.hget(String.format(FAMILY_PRIZE_COIN_LOG, dateTime), familyId2.toString())).orElse(0);
//        // 计算可瓜分金币
//        return new Double((familyCoinLog1 + familyCoinLog2) * 0.05).longValue();
//    }
//
//    public Long getUserMedalNum(Long uid) {
//        return Optional.ofNullable((Integer) redisManager.hget(String.format(USER_MEDAL_NUM, ActivityTimeUtil.getFirstDayOfWeekStr(ACTIVITY_CODE)), uid.toString())).orElse(0).longValue();
//    }
//}
