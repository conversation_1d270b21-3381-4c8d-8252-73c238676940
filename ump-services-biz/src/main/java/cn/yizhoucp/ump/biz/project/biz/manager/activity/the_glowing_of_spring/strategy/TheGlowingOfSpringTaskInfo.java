package cn.yizhoucp.ump.biz.project.biz.manager.activity.the_glowing_of_spring.strategy;

import cn.hutool.core.collection.CollUtil;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.loveInProgress.common.LoveInProgressEnums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.moonAboveTheSea.vo.MoonAboveTheSeaTaskVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.the_glowing_of_spring.common.TheGlowingOfSpringConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.the_glowing_of_spring.common.TheGlowingOfSpringEnums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.the_glowing_of_spring.common.TheGlowingOfSpringRedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.user.manager.UserFeignManager;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class TheGlowingOfSpringTaskInfo implements ExecutableStrategy {
    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    private TheGlowingOfSpringRedisManager theGlowingOfSpringRedisManager;
    @Resource
    private UserFeignManager userFeignManager;

    public String getActivityCode() {
        return TheGlowingOfSpringConstant.ACTIVITY_CODE;
    }



    @Override
    public Object execute(ButtonEventParam buttonEventParam) {
        Long uid = buttonEventParam.getBaseParam().getUid();
        List<MoonAboveTheSeaTaskVO> tasks = CollUtil.newArrayList();
        List<String> taskScene = CollUtil.newArrayList();
        for (TheGlowingOfSpringEnums.TaskEnum dailyTaskEnum : TheGlowingOfSpringEnums.TaskEnum.values()) {
            taskScene.add(dailyTaskEnum.getTaskCode());
        }
        List<ScenePrizeDO> scenePrizeDOS = scenePrizeJpaDAO.getListBySceneCodeList(ServicesAppIdEnum.lanling.getAppId(), getActivityCode(), taskScene);
        Map<String, ScenePrizeDO> taskPrizeDOMap = scenePrizeDOS.stream().collect(Collectors.toMap(ScenePrizeDO::getSceneCode, scenePrizeDO -> scenePrizeDO));
        for (TheGlowingOfSpringEnums.TaskEnum dailyTaskEnum : TheGlowingOfSpringEnums.TaskEnum.values()) {
            MoonAboveTheSeaTaskVO task = new MoonAboveTheSeaTaskVO();
            ScenePrizeDO taskPrize = taskPrizeDOMap.get(dailyTaskEnum.getTaskCode());
            if (taskPrize == null) {
                log.error("任务或奖励不存在,taskCode:{},rewardCode:{}", dailyTaskEnum.getTaskCode());
                continue;
            }
            task.setTaskCode(dailyTaskEnum.getTaskCode());
            task.setTotalProgress(dailyTaskEnum.getTotalProgress());
            Long progress = theGlowingOfSpringRedisManager.getTaskProgress(uid, dailyTaskEnum.getTaskCode());
            progress = progress > dailyTaskEnum.getTotalProgress() ? dailyTaskEnum.getTotalProgress() : progress;
            task.setCurrentProgress(progress);
            UserVO userVO = userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), uid);
            if (userVO == null) {
                log.error("用户信息不存在,uid:{}", uid);
                continue;
            }
            String taskDesc;
            taskDesc = String.format(TheGlowingOfSpringConstant.TASK_DESC, taskPrize.getPrizeDesc(), dailyTaskEnum.getTotalProgress());
            task.setTaskDesc(taskDesc);
            String taskRewardDesc =String.format(TheGlowingOfSpringConstant.TASK_REWARD_DESC,getRewardCount(taskPrize.getExtData()));
            task.setTaskRewardDesc(taskRewardDesc);
            task.setClaimButtonStatus(getClaimButtonStatus(uid, dailyTaskEnum));
            task.setTaskIcon(taskPrize.getPrizeIcon());
            tasks.add(task);
        }
        return tasks;
    }

    private Long getRewardCount(String extData) {
        JSONObject jsonObject = JSON.parseObject(extData);
        if (jsonObject != null) {
            return jsonObject.getLong("rewardCount");
        }
        return null;
    }

    private int getClaimButtonStatus(Long uid, TheGlowingOfSpringEnums.TaskEnum dailyTaskEnum) {
        boolean taskClaimed = theGlowingOfSpringRedisManager.getTaskReceive(uid, dailyTaskEnum.getTaskCode());
        if (!taskClaimed) {
            return LoveInProgressEnums.TaskStatusEnum.NOT_TAKEN.getCode();
        }
        Long progress = theGlowingOfSpringRedisManager.getTaskProgress(uid, dailyTaskEnum.getTaskCode());
        boolean taskHasFinished = progress >= dailyTaskEnum.getTotalProgress();
        boolean rewardClaimed = theGlowingOfSpringRedisManager.getRewardClaimed(uid, dailyTaskEnum.getTaskCode());
        if (!taskHasFinished) {
            return LoveInProgressEnums.TaskStatusEnum.TAKEN.getCode();
        }
        if (!rewardClaimed) {
            return LoveInProgressEnums.TaskStatusEnum.FINISHED.getCode();
        }
        return LoveInProgressEnums.TaskStatusEnum.REWARD_TAKEN.getCode();
    }

}
