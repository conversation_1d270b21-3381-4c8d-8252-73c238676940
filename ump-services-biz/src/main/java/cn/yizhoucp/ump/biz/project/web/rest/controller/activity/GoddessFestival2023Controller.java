package cn.yizhoucp.ump.biz.project.web.rest.controller.activity;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.vo.activity.goddessFestival2023.IndexVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.goddessFestival2023.GoddessFestival2023PageManager;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 2023 3.8 女神节
 * <AUTHOR>
 * @Date 2023/2/22 16:55
 * @Version 1.0
 */
@RequestMapping("/api/inner/activity/goddess-festival-2023")
@RestController
public class GoddessFestival2023Controller {

    @Resource
    private GoddessFestival2023PageManager goddessFestival2023PageManager;

    @GetMapping("/get-index")
    public Result<IndexVO> getIndex(BaseParam param) {
        return Result.successResult(goddessFestival2023PageManager.getIndex(param));
    }

}
