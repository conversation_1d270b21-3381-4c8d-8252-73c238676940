package cn.yizhoucp.ump.biz.project.dal.mp.dataobject;

import cn.yizhoucp.ump.api.vo.evaluatereward.CheckVO;
import cn.yizhoucp.ump.api.vo.evaluatereward.RuleVO;
import cn.yizhoucp.ump.biz.project.biz.enums.evalutereward.EvaluateRewardRuleStatusEnum;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.checkerframework.checker.units.qual.A;
import org.checkerframework.checker.units.qual.C;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.Optional;
import java.util.function.Function;

@TableName("evaluate_reward_record")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EvaluateRewardRecordDO implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long ruleId;

    private Long userId;

    private String deviceBrand;

    private String deviceId;

    private String vestChannel;

    private Integer preMarkScore;

    private String userUploadImg;

    private Integer reward;

    private String rewardType;

    /** 状态 {@link EvaluateRewardRuleStatusEnum}.name() */
    @TableField("`status`")
    private String status;

    private Date applyTime;

    private Date createTime;

    private Date updateTime;

    public CheckVO convertToVO() {
        CheckVO checkVO = new CheckVO();
        BeanUtils.copyProperties(this, checkVO);
        checkVO.setStatus(Optional.ofNullable(EvaluateRewardRuleStatusEnum.valueByName(status)).map(EvaluateRewardRuleStatusEnum::getDesc).orElse(null));
        checkVO.setPrize(reward + rewardType);
        return checkVO;
    }
}
