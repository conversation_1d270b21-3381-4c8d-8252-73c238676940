package cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.strategy;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.annotation.NoRepeatSubmit;
import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.MainChineseNewYear2025TrackManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.common.MainChineseNewYear2025Constant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.common.MainChineseNewYear2025Enums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.common.MainChineseNewYear2025RedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.log.LogComponent;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class SellDishStrategy implements MainChineseNewYear2025Strategy {
    @Resource
    private MainChineseNewYear2025RedisManager mainChineseNewYear2025RedisManager;
    @Resource
    private LogComponent logComponent;
    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    private MainChineseNewYear2025TrackManager mainChineseNewYear2025TrackManager;

    @NoRepeatSubmit(time = 3)
    @Override
    public Boolean execute(ButtonEventParam buttonEventParam) {
        String dishKey = buttonEventParam.getBizKey();
        Long uid = buttonEventParam.getBaseParam().getUid();
        MainChineseNewYear2025Enums.DishsEnum dishsEnum = MainChineseNewYear2025Enums.DishsEnum.getByDishCode(dishKey);
        if (dishsEnum == null) {
            return Boolean.FALSE;
        }
        Long num = mainChineseNewYear2025RedisManager.getDishNum(uid, dishsEnum.getDishCode());
        if (num <= 0) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "菜品不足");
        }
        //扣除菜品
        mainChineseNewYear2025RedisManager.decrementDish(uid, dishsEnum.getDishCode(), MainChineseNewYear2025Constant.DEFAULT_INCREMENT_NUM.intValue());
        //增加万能卡
        mainChineseNewYear2025RedisManager.incrementDrawItems(uid, dishsEnum.getSellPrice().longValue());
        //埋点
        mainChineseNewYear2025TrackManager.allActivityReceiveAward("sell_cook", MainChineseNewYear2025Enums.CardEnum.CARD_6.getCardCode(), 0L, dishsEnum.getSellPrice(), uid);
        return Boolean.TRUE;
    }


}
