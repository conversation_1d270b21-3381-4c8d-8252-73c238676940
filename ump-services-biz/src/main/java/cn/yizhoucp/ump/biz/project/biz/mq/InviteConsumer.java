package cn.yizhoucp.ump.biz.project.biz.mq;

import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.starter.cassandra.base.rocketmq.RocketmqAbstractConsumer;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.mission.MissionTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.MissionProcessManager;
import cn.yizhoucp.ms.core.base.TopicConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.param.MissionParam;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Set;

import static cn.yizhoucp.ms.core.base.TopicTagEnum.TOPIC_INVITE_USER_REGISTER;

/**
 * 数据业务相关 mq
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@RocketMQMessageListener(topic = "invite_topic", consumerGroup = "INVITE_UMP_SERVICE_GROUP")
public class InviteConsumer extends RocketmqAbstractConsumer {

    @Resource
    private MissionProcessManager missionProcessManager;

    /** 关注 tag 列表 */
    private static final Set<String> LISTEN_TAG_SET = Sets.newHashSet(TOPIC_INVITE_USER_REGISTER.getTagKey());

    @Override
    protected String getTopic() {
        return TopicConstant.TOPIC_INVITE.getTopicKey();
    }

    @Override
    protected Set<String> getTags() {
        return LISTEN_TAG_SET;
    }

    @Override
    protected Boolean consume(String unionId, Long userId, String tag, String param) {
        if (TOPIC_INVITE_USER_REGISTER.getTagKey().equals(tag)) {
            return this.inviteUserRegister(BaseParam.builder().appId(MDCUtil.getCurAppIdByMdc()).unionId(unionId).uid(userId).build(), param);
        }
        return Boolean.FALSE;
    }

    /**
     * 邀请用户完成注册
     *
     * @param param 消息内容
     * @return boolean
     */
    private boolean inviteUserRegister(BaseParam baseParam, String param) {
        JSONObject jsonObject = JSONObject.parseObject(param);
        // 发起邀请用户
        Long uid = jsonObject.getLong("userId");
        // 被邀请用户
        Long invitedUid = jsonObject.getLong("inviteUserId");
        return missionProcessManager.process(MissionParam.builder()
                .appId(baseParam.getAppId())
                .unionId(baseParam.getUnionId())
                .uid(uid)
                .type(MissionTypeEnum.INVITE)
                .bizParam(jsonObject).build());
    }

}
