package cn.yizhoucp.ump.biz.project.dal.jpa.dao;

import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.GoddessTrainWechatQrCodeDO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 女神培训-群二维码
 *
 * @author: dongming
 */
@Repository
public interface GoddessTrainWechatQrCodeJpaDAO extends JpaRepository<GoddessTrainWechatQrCodeDO, Long>, JpaSpecificationExecutor<GoddessTrainWechatQrCodeDO>, CrudRepository<GoddessTrainWechatQrCodeDO, Long> {

    /**
     * 根据报名时间获取群二维码记录
     *
     * @param appId   应用id
     * @param unionId 应用唯一标识
     * @param time    时间
     * @return GoddessTrainWechatQrCodeDO
     */
    @Transactional
    @Query(value = "select * from goddess_train_wechat_qr_code where app_id = ?1 and union_id = ?2 and sign_up_start_time <= ?3 and sign_up_end_time >= ?3", nativeQuery = true)
    GoddessTrainWechatQrCodeDO findByAppIdAndUnionIdAndBetweenStartTimeAndEndTime(Long appId, String unionId, Date time);

    /**
     * 根据活动代码获取群二维码信息
     *
     * @param appId         应用id
     * @param unionId       应用唯一标识
     * @param activityCode  活动代码
     * @return GoddessTrainWechatQrCodeDO
     */
    GoddessTrainWechatQrCodeDO findByAppIdAndUnionIdAndActivityCode(Long appId, String unionId, String activityCode);

    /**
     * 获取有交集的活动配置
     *
     * @param appId     应用id
     * @param unionId   应用唯一标识
     * @param startTime 培训开始时间
     * @param endTime   培训结束时间
     * @return List<GoddessTrainWechatQrCodeDO>
     */
    @Transactional
    @Query(value = "select * from goddess_train_wechat_qr_code where app_id = ?1 and union_id = ?2 and ((sign_up_start_time <= ?3 and sign_up_end_time >= ?3) or (sign_up_start_time >= ?3 and sign_up_start_time <= ?4))", nativeQuery = true)
    List<GoddessTrainWechatQrCodeDO> findCountByTimeConflict(Long appId, String unionId, Date startTime, Date endTime);

    /**
     * 分页获取群二维码信息
     *
     * @param appId    应用id
     * @param unionId  应用唯一标识
     * @param pageable 分页数据
     * @return Page<GoddessTrainWechatQrCodeDO>
     */
    @Transactional
    @Query(value = "select * from goddess_train_wechat_qr_code where app_id = ?1 and union_id = ?2 order by update_time desc", nativeQuery = true)
    Page<GoddessTrainWechatQrCodeDO> pageByUpdateTimeDesc(Long appId, String unionId, Pageable pageable);

    List<GoddessTrainWechatQrCodeDO> findByIdIn(List<Long> ids);
}
