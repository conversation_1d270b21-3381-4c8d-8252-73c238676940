package cn.yizhoucp.ump.biz.project.dal.jpa.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

/**
 * 积分汇率
 *
 * <AUTHOR>
 */
@Table(name = "point_exchange_rate")
@Entity
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PointExchangeRateDO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    /** 应用唯一标识 */
    private String unionId;
    /** 场景 PointExchangeRateScene */
    private String scene;
    /** 汇率，最多支持三位小数（放大 1000 倍） */
    private Integer exchangeRate;
    /** 状态 PerStatus */
    private Integer status;
    /** 是否存在审核中数据 */
    private Boolean examine;
    /** 创建者 */
    private String createBy;
    /** 更新者 */
    private String updateBy;
    /** 创建时间 */
    private Date createTime;
    /** 更新时间 */
    private Date updateTime;

}
