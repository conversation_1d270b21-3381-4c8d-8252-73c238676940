package cn.yizhoucp.ump.biz.project.web.rest.controller.activity.astrology;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.vo.activity.astrology.springStarCeremony.BalloonOperationResultVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.astrology.springStarCeremony.SpringStarCeremonyIndexManager;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
public class SpringStarCeremonyController {

    @Resource
    private SpringStarCeremonyIndexManager springStarCeremonyIndexManager;

    @GetMapping("/api/inner/activity/spring_star_ceremony/balloon_break")
    public Result<BalloonOperationResultVO> balloonBreak(BaseParam param, String stall, Boolean all, Integer index) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> springStarCeremonyIndexManager.balloonBreak(param, stall, all, index));
    }

    @GetMapping("/api/inner/activity/spring_star_ceremony/balloon_replace")
    public Result<BalloonOperationResultVO> balloonReplace(BaseParam param, String stall) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> springStarCeremonyIndexManager.balloonReplace(param, stall));
    }

    @GetMapping("/api/inner/activity/spring_star_ceremony/balloon_perspective")
    public Result<BalloonOperationResultVO> balloonPerspective(BaseParam param, String stall) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> springStarCeremonyIndexManager.balloonPerspective(param, stall));
    }

}
