package cn.yizhoucp.ump.biz.project.biz.remoteService;

import cn.yizhoucp.ms.core.base.PageVO;
import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ms.core.vo.AllRoomMessageVO;
import cn.yizhoucp.ms.core.vo.CommonResultVO;
import cn.yizhoucp.ms.core.vo.RoomMessageListItemVO;
import cn.yizhoucp.ms.core.vo.roomservices.RoomHomePageVO;
import cn.yizhoucp.ms.core.vo.roomservices.RoomVO;
import cn.yizhoucp.ms.core.vo.roomservices.live.LiveAttachRoomVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 语音房
 *
 * <AUTHOR>
 */
@FeignClient("room-services")
public interface FeignRoomService {

    /**
     * 获取所有在连麦的用户
     *
     * @return List<LiveAttachRoomVO>
     */
    @RequestMapping("/api/live/get-live-attach-users")
    Result<List<LiveAttachRoomVO>> getLiveAttachUsers();

    @RequestMapping("/api/room/room-list")
    Result<PageVO<RoomHomePageVO>> roomList(@RequestParam("tag") String tag, @RequestParam("start") Long start, @RequestParam("num") Integer num, @RequestParam("game") String game);


    /**
     * 获取房间信息(根据roomId)
     *
     * @param roomId
     * @return
     */
    @GetMapping("/api/room/get-room-by-room-id")
    Result<RoomVO> getRoomInfoByRoomId(@RequestParam("roomId") Long roomId, @RequestParam("appId") Long appId);

    /**
     * 房主发送关注红包之后，如果用户未关注，则自下而上弹出浮窗，才用发送特定RTM消息提示客户端弹出弹窗
     *
     * @param appId  应用ID
     * @param roomId 房间ID
     * @return
     */
    @GetMapping("/api/room/send-guide-attention-message")
    Result<RoomVO> sendGuideAttentionMessage(@RequestParam("appId") Long appId, @RequestParam("roomId") Long roomId);

    /**
     * 发送指定公频消息
     *
     * @param roomId 语音房id
     * @param appId  应用id
     * @param type   all 全部开着的房间 one 指定 roomId 房间
     * @return String
     */
    @RequestMapping("/api/inner/room/send-room-custom-message")
    Result<String> sendRoomSpecifiedMessage(@RequestParam("roomId") Long roomId, @RequestParam("appId") Long appId, @RequestParam("type") String type, @RequestParam("memo") String memo);

    /**
     * 根据房间id 获取房间信息
     *
     * @param roomIds 房间id
     * @param appId   应用id
     * @return List<RoomVO>
     */
    @GetMapping("/api/room/get-room-by-room-ids")
    Result<List<RoomVO>> getAssignRoomMessageByRoomIds(@RequestParam("roomIds") String roomIds, @RequestParam("appId") Long appId);

    @RequestMapping("/api/room/server-operation-room")
    Result<RoomVO> serverOperationRoom(@RequestParam("appId") Long appId, @RequestParam("userId") Long userId, @RequestParam("superAdminId") Long superAdminId, @RequestParam("roomId") Long roomId, @RequestParam("tag") String tag, @RequestParam("name") String name, @RequestParam("announcement") String announcement,
                                       @RequestParam("coverUrl") String coverUrl, @RequestParam("mode") String mode, @RequestParam("subType") String subType, @RequestParam("source") String source,
                                       @RequestParam("game") String game, @RequestParam("boothSize") Integer boothSize);

    /**
     * 关闭符合规范的房间
     *
     * @param appId
     * @param roomId
     * @return
     */
    @RequestMapping("/api/room/close-free-room")
    Result<Boolean> closeFreeRoom(@RequestParam("appId") Long appId, @RequestParam("roomId") Long roomId);

    /**
     * 修改语音房位置数量
     *
     * @param roomId 语音房id
     * @param size   修改后的数量
     * @return
     */
    @RequestMapping("/api/room/modify-booth-size")
    Result<CommonResultVO> modifyBoothSize(@RequestParam("roomId") Long roomId, @RequestParam("size") Integer size, @RequestParam("appId") Long appId, @RequestParam("userId") Long userId);

    /**
     * 注意：房间关闭，获取 rtc token 会报错！！
     * 获取指定房主的房间信息以及自己进入该房间需要获取的rtc token  (center)
     * 条件：1、房主id
     * 2、房间状态开启
     * 3、房间类型
     *
     * @param userId 房主id
     * @param source ROOM_SOURCE_PERSONAL 个人    ROOM_SOURCE_COMMUNITY 组织创建
     * @return 指定房主的房间信息 + 进入房间的rtc token
     */
    @GetMapping("/api/room/get-room-by-owner")
    Result<RoomVO> getAssignRoomMessageByUid(@RequestParam("userId") Long userId, @RequestParam("source") String source);

    /**
     * 1、优先根据roomId或者房间信息（不校验状态）  center
     * 2、roomId为空根据调用者id以及房间类型获取是否创建过房间以及房间信息，如果没有创建过该类型房间返回默认参数（图片为创建者头像、房间展示类型为普通房、标签默认为相亲交友房）
     *
     * @param source ROOM_SOURCE_PERSONAL 个人    ROOM_SOURCE_COMMUNITY 组织创建
     * @return 返回指定房间信息（如果房间vo内容为空说明该用户未创建过房间）
     */
    @GetMapping("/api/room/room-message")
    Result<RoomVO> roomMessage(@RequestParam("roomId") Long roomId, @RequestParam("source") String source);

    @GetMapping("/api/inner/room/all-room-message/add-message")
    Result<Boolean> allRoomMessageAdd(@RequestParam("messageType") String messageType,
                                             @RequestParam("id") Long id,
                                             @RequestParam("message") String message,
                                             @RequestParam("sort") Integer sort);

    @GetMapping("/api/inner/room/all-room-message/del-message")
    Result<Boolean> allRoomMessageDel(@RequestParam("messageType") String messageType,
                                             @RequestParam("id") Long id);

    @RequestMapping("/api/room/get-open-room-vos")
    Result<List<RoomVO>> getOpenRoomVOs(@RequestParam("appId") Long appId);
}
