package cn.yizhoucp.ump.biz.project.web.rest.controller.activity;

import cn.yizhoucp.ump.biz.project.biz.manager.activity.familyKey.FamilyKeyManager;
import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ms.core.vo.umpServices.activity.familyKey.*;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 家族钥匙 PK 赛
 *
 * @author: lianghu
 */
@RestController
public class FamilyKeyController {

    @Resource
    private FamilyKeyManager familyKeyManager;

    /**
     * 活动弹幕
     *
     * @return
     */
    @RequestMapping("/api/inner/activity/family-key/barrage-list")
    public Result<List<String>> barrageList() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> familyKeyManager.getBarrageList());
    }

    /**
     * 个人任务页
     *
     * @return
     */
    @RequestMapping("/api/inner/activity/family-key/get-personal-task-page-info")
    public Result<PersonalTaskVO> getPersonTaskPageInfo() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> familyKeyManager.getPersonalTaskPageInfo());
    }

    /**
     * 家族闯关页
     *
     * @return
     */
    @RequestMapping("/api/inner/activity/family-key/get-family-break-through-page-info")
    public Result<FamilyBreakThroughVO> getFamilyBreakThroughPageInfo() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> familyKeyManager.getFamilyBreakThroughPageInfo());
    }

    /**
     * 抽奖页
     *
     * @return
     */
    @RequestMapping("/api/inner/activity/family-key/get-draw-page-info")
    public Result<FamilyKeyDrawVO> getDrawPageInfo() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> familyKeyManager.getDrawPageInfo());
    }

    /**
     * 抽奖
     *
     * @return
     */
    @RequestMapping("/api/inner/activity/family-key/draw")
    public Result<DrawReturnVO> familyKeyDraw(Integer times) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> familyKeyManager.draw(times));
    }

    /**
     * 荣耀榜页
     *
     * @return
     */
    @RequestMapping("/api/inner/activity/family-key/get-rank-page-info")
    public Result<FamilyKeyRankVO> getRankPageInfo() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> familyKeyManager.getRankPageInfo());
    }

    /**
     * 是否展示倒计时
     *
     * @return
     */
    @RequestMapping("/api/inner/activity/family-key/show-count-down")
    public Result<Boolean> showCountdown() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> familyKeyManager.showCountDown());
    }

    /**
     * 下发倒计时奖励
     *
     * @return
     */
    @RequestMapping("/api/inner/activity/family-key/send-count-down-prize")
    public Result<Boolean> sendCountdownPrize() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> familyKeyManager.sendCountDownPrize());
    }


}
