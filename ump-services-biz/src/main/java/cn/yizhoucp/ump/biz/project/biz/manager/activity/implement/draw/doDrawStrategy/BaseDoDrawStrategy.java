package cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.doDrawStrategy;

import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.Magpie2023Constant;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw.DrawRulePoolType;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw.DrawRuleType;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.doDrawStrategy.common.ProbStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.drawStrategy.LotteryManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DoDrawDTO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.KeyWeight;
import cn.yizhoucp.ump.biz.project.biz.manager.commonActivity.ActivityStatusManager;
import cn.yizhoucp.ump.biz.project.biz.manager.userActivity.ActivityManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.FeignUserService;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.dal.service.DrawPoolItemService;
import cn.yizhoucp.ump.biz.project.dal.service.DrawPoolService;
import cn.yizhoucp.ump.biz.project.dto.activity.draw.DrawLimitRule;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaOverloadBattleConstant.ACTIVITY_CODE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaOverloadBattleConstant.DAILY_FIRST_DRAW;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaOverloadBattleConstant.DAILY_FIRST_DRAW_DEVICE_ID;

@Slf4j
@Service
public abstract class BaseDoDrawStrategy implements DoDrawStrategy {

    public static final String IMPORT_USER_KEY = "draw_import_User_times_%s_%s_%s";

    @Resource
    private ProbStrategy probStrategy;

    @Resource
    private ActivityStatusManager activityStatusManager;

    @Resource
    private DrawPoolService drawPoolService;

    @Resource
    private RedisManager redisManager;

    @Resource
    private LotteryManager lotteryManager;

    @Resource
    private DrawPoolItemService drawPoolItemService;

    @Resource
    private FeignUserService feignUserService;

    abstract Integer getHadDrawTimes(Long uid);

    abstract Pair<Integer,Double> getUserReturnRate(Long userId, Long appId);

    @Override
    public Object draw(DrawParam param) {
        String activityCode = param.getActivityCode();
        String bizKey = Optional.ofNullable(param.getType()).orElse(activityCode);
        Long uid = param.getUid();
        Long appId = param.getAppId();
        Integer times = param.getTimes();

        Integer hadDrawTimes = getHadDrawTimes(uid);

        //获取当前用户的返奖率！
        Pair<Integer, Double> userReturnRate = Optional.ofNullable(getUserReturnRate(uid, appId)).orElse(Pair.of(0, 0D));
        Double returnRate = userReturnRate.getRight();
        Integer currentHadDrawTimes = userReturnRate.getLeft();

        //先获取所有奖池 不可能为空
        List<DrawPoolDO> byActivityCodeAndBizKey = drawPoolService.getByActivityCodeAndBizKey(activityCode, bizKey);

        LocalDateTime now = LocalDateTime.now();
        log.info("getByActivityCodeAndBizKey before result {}",JSON.toJSONString(byActivityCodeAndBizKey));
        //过滤不可用奖池
        byActivityCodeAndBizKey = byActivityCodeAndBizKey.stream().filter(one -> now.isAfter(one.getStartTime()) && now.isBefore(one.getEndTime())).collect(Collectors.toList());

        log.info("getByActivityCodeAndBizKey after result {}", JSON.toJSONString(byActivityCodeAndBizKey));
        afterGetDrawPool(byActivityCodeAndBizKey, param);

        Map<Long, DrawPoolDO> poolMapById = byActivityCodeAndBizKey.stream().collect(Collectors.toMap(DrawPoolDO::getId, one -> one));

        // 获取奖池和对应的数量
        Map<Long, Integer> poolAndTimes = getPoolAndTimesByRules(appId, uid, hadDrawTimes, times, hadDrawTimes, currentHadDrawTimes, returnRate, byActivityCodeAndBizKey, getCalcMultiple());

        log.debug("getPoolAndTimesByRules result {}", JSON.toJSONString(poolAndTimes));
        //循环获取对应奖池并抽取奖品
        List<String> prizeKeys = new ArrayList<>();
        Map<String, DrawPoolItemDO> drawPoolItemDOMap = new HashMap<>();
        poolAndTimes.entrySet().forEach(entry -> {
            DrawPoolDO drawPoolDO = poolMapById.get(entry.getKey());
            log.debug("开始抽奖 -> {}", entry.getKey());
            List<DrawPoolItemDO> drawPoolItemDOList = this.getOriginPoolByCode(drawPoolDO.getPoolCode());
            for (DrawPoolItemDO drawPoolItemDO : drawPoolItemDOList) {
                DrawPoolItemDO drawPoolItemDO1 = new DrawPoolItemDO();
                BeanUtils.copyProperties(drawPoolItemDO, drawPoolItemDO1);
                drawPoolItemDOMap.put(drawPoolItemDO.getItemKey(), drawPoolItemDO1);
            }
            log.debug("奖池奖品 -> {}", drawPoolItemDOList);
            List<String> drawResult = lotteryManager.draw(
                    drawPoolDO.getPoolType(),
                    DoDrawDTO.builder().poolCode(drawPoolDO.getPoolCode()).drawTimes(entry.getValue()).uid(uid).originPool(
                            drawPoolItemDOList.stream().collect(Collectors.groupingBy(DrawPoolItemDO::getItemKey, Collectors.summingInt(DrawPoolItemDO::getWeight)))
                                    .entrySet().stream().map(e -> KeyWeight.builder().key(e.getKey()).weight(e.getValue()).build()).collect(Collectors.toList())
                    ).build()
            );
            afterDrawResult(drawResult, drawPoolDO);
            prizeKeys.addAll(drawResult);
        });
        return mergePrize(prizeKeys, drawPoolItemDOMap);
    }

    /**
     * 合并礼物
     *
     * @param prizeKeys
     * @param drawPoolItemDOMap
     * @return
     */
    public List<DrawPoolItemDO> mergePrize(List<String> prizeKeys, Map<String, DrawPoolItemDO> drawPoolItemDOMap) {
        log.debug("keys -> {}", prizeKeys);
        Map<String, DrawPoolItemDO> resultMap = Maps.newHashMap();
        Map<String, Long> collect = prizeKeys.stream().collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
        collect.forEach((key, count) -> {
            DrawPoolItemDO drawPoolItemDO = drawPoolItemDOMap.get(key);
            Integer originTargetTimes = drawPoolItemDO.getTargetTimes();
            if (originTargetTimes == null || originTargetTimes < 1) {
                originTargetTimes = 1;
            }
            drawPoolItemDO.setTargetTimes((int) (originTargetTimes * count));
            resultMap.put(key, drawPoolItemDO);
        });

        List<DrawPoolItemDO> result = new ArrayList<>(resultMap.values());
        result.sort((o1, o2) -> Long.compare(o2.getItemValueGold(), o1.getItemValueGold()));
        log.debug("result -> {}", result);

        // 单抽
        if (result.size() != 1) {
            return result;
        }
        // 星海霸主战活动在线
        if (!Boolean.TRUE.equals(activityStatusManager.activityIsEnable(BaseParam.ofMDC(), ACTIVITY_CODE))) {
            return result;
        }
        UserVO userVO = feignUserService.getBasic(MDCUtil.getCurUserIdByMdc(), ServicesAppIdEnum.lanling.getAppId()).successData();
        if (userVO == null || Boolean.TRUE.equals(redisManager.setIsMember(String.format(DAILY_FIRST_DRAW_DEVICE_ID, DateUtil.getNowYyyyMMdd()), userVO.getDeviceId()))) {
            return result;
        }
        redisManager.sSetExpire(String.format(DAILY_FIRST_DRAW_DEVICE_ID, DateUtil.getNowYyyyMMdd()), DateUtil.ONE_MONTH_SECOND, userVO.getDeviceId());

        // 每日首抽
        if (activityStatusManager.activityIsEnable(BaseParam.ofMDC(), ACTIVITY_CODE) && !Boolean.TRUE.equals(redisManager.setnx(String.format(DAILY_FIRST_DRAW, MDCUtil.getCurUserIdByMdc(), DateUtil.getNowYyyyMMdd()), System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND))) {
            return result;
        }
        // 循环 > 高爆 > 首抽 > 常规
        if ("Singledraw_routine".equals(result.get(0).getPoolCode())) {
            // 替换
            List<DrawPoolItemDO> drawPoolItemDOList = drawPoolItemService.getByPoolCode("DAILY_FIRST_DRAW");
            List<DrawPoolItemDTO> drawPoolItemDTOList = probStrategy.getDrawPoolItems(drawPoolItemDOList, 1, Boolean.TRUE);
            return Collections.singletonList(drawPoolItemDTOList.get(0).getDrawPoolItemDO());
        }

        return result;
    }

    /**
     * 奖池处理， 可以添加/减少奖池，此流程之后会对奖池有效性进行校验
     *
     * @param poolList
     * @param param
     */
    public void afterGetDrawPool(List<DrawPoolDO> poolList, DrawParam param) {}

    /**
     * 抽奖结果处理， 可以直接修改抽奖结果
     *
     * @param drawResult
     * @param drawPool
     */
    public void afterDrawResult(List<String> drawResult, DrawPoolDO drawPool) {}

    public List<DrawPoolItemDO> getOriginPoolByCode(String poolCode) {
        List<DrawPoolItemDO> drawPoolItemDOList = drawPoolItemService.getByPoolCode(poolCode);
        if (CollectionUtils.isEmpty(drawPoolItemDOList)){
            drawPoolItemDOList = drawPoolItemService.getByPoolCodeAndUnionId(poolCode, MDCUtil.getCurUnionIdByMdc());
        }
        return drawPoolItemDOList;
    }

    /**
     * 当前计算次数倍数
     * 如占星高爆5倍消耗、5倍计算
     *
     * @return
     */
    public Integer getCalcMultiple() {
        return 1;
    }

    /**
     * 通过规则获取对应奖池的次数
     * @param appId
     * @param uid
     * @param starTimes 本次之前历史抽取次数
     * @param times     本次抽取次数
     * @param loopTimes 循环次数  开始的时候抽取时间不同所以有这个字段 目前 starTimes=loopTimes
     * @param returnRate 用户当前返奖率 returnRate
     * @param pool      奖池列表
     * @param calcMultiple   计算次数倍数
     * @return
     */
    public Map<Long, Integer> getPoolAndTimesByRules(Long appId, Long uid, Integer starTimes, Integer times, Integer loopTimes, Integer userRateTimes, Double returnRate, List<DrawPoolDO> pool, Integer calcMultiple) {
        log.debug("getPoolAndTimesByRules starTimes {} times {} ",starTimes,times);
        HashMap<Long,Integer> result = Maps.newHashMap();
        List<DrawLimitRule> rules = pool.stream().filter(one -> null != one.getLimitRule()).map(one -> {
            String limitRule = one.getLimitRule();
            DrawLimitRule drawLimitRule = JSON.parseObject(limitRule, DrawLimitRule.class);
            drawLimitRule.setDrawPoolId(one.getId());
            return drawLimitRule;
        }).collect(Collectors.toList());
        //倒叙排列
        Collections.sort(rules,((o1, o2) -> Integer.compare(o2.getPriorityLevel(), o1.getPriorityLevel())));
        log.debug("getPoolAndTimesByRules rules {}",JSON.toJSONString(rules));
        //记录当前抽奖类型
        DrawRulePoolType currentPoolType = DrawRulePoolType.single;
        if (times > 1){
            currentPoolType = DrawRulePoolType.multiple;
        }
        //当前可用次数
        int currentAvailableNum = times;
        int endTimes = starTimes + times;
        //通过规则计算
        for (DrawLimitRule rule : rules) {
            try {
                DrawRulePoolType poolType = rule.getPoolType();
                if (currentAvailableNum < 1 || (!currentPoolType.equals(poolType) && !DrawRulePoolType.all.equals(poolType))){
                    continue;
                }
                //调整为 如果存在条件但是不全的 不能进入该奖池
                if (!StringUtils.isEmpty(rule.getRateType()) || null != rule.getReturnRate() || null != rule.getRateTimes()) {
                    if (!StringUtils.isEmpty(rule.getRateType()) && null != rule.getReturnRate() && null != rule.getRateTimes()) {
                        Double targetRate = rule.getReturnRate();
                        Integer ruleRateTimes = rule.getRateTimes();
                        if (userRateTimes < ruleRateTimes){
                            continue;
                        }
                        //看返奖率是否合适
                        if ("over".equals(rule.getRateType())) {
                            if (targetRate > returnRate) {
                                continue;
                            }
                        } else {
                            if (targetRate < returnRate) {
                                continue;
                            }
                        }
                    } else {
                        //说明参数不全所以该奖池进不去
                        continue;
                    }
                }
                DrawRuleType ruleType = rule.getRuleType();
                int currentTimes = 0;
                switch (ruleType) {
                    case loop:
                        Integer loopCount = rule.getLoopCount();
                        Integer currentCalTimes = times * calcMultiple;
                        currentTimes = (loopTimes + currentCalTimes) / loopCount - loopTimes / loopCount;
                        break;
                    case limitCount:
                        //最低限制条件
                        Integer limitStart = rule.getLimitCount();
                        Integer ruleTimes = rule.getTimes();
                        //次数不符合基础条件跳过
                        if (starTimes < limitStart){
                            continue;
                        }
                        if (ruleTimes == -1) {
                            //这种情况就是保底
                            currentTimes = currentAvailableNum;
                        } else {
                            Integer limitEnd = limitStart + ruleTimes;
                            //在范围内
                            if (starTimes >= limitStart && starTimes < limitEnd) {
                                currentTimes = Math.min(limitEnd, endTimes) - starTimes;
                            }
                        }
                        break;
                    case importUser:
                        if (!StringUtils.isEmpty(rule.getExt())){
                            JSONObject jsonObject = JSON.parseObject(rule.getExt());
                            String userIdsStr = jsonObject.getString("userIds");
                            if (null != userIdsStr) {
                                List<Long> userIds = JSON.parseArray(userIdsStr, Long.class);
                                if (userIds.contains(uid)) {
                                    String importUserKey = getImportUserKey(rule.getDrawPoolId(), appId, uid);
                                    Integer currentRuleTimes = rule.getTimes();
                                    // TODO 检查
                                    if (currentRuleTimes > 0) {
                                        Object o = redisManager.get(importUserKey);
                                        //查看缓存数据进行数据减少
                                        if (null != o) {
                                            Long hadImportTimes = Long.valueOf(o.toString());
                                            long l = currentRuleTimes - hadImportTimes;
                                            if (l > 0) {
                                                currentTimes = Math.min(Integer.valueOf(String.valueOf(l)), currentAvailableNum);
                                            }
                                        } else {
                                            currentTimes = currentRuleTimes;
                                        }
                                    } else {
                                        //这种情况就是保底
                                        currentTimes = currentAvailableNum;
                                    }
                                }
                            }
                        }
                        break;
                    default:
                        break;
                }
                //本次计算的次数是否大于可用次数
                currentTimes = Math.min(currentTimes,currentAvailableNum);
                //这里倒入数据的要记录一下
                if (DrawRuleType.importUser.equals(ruleType)){
                    String importUserKey = getImportUserKey(rule.getDrawPoolId(), appId, uid);
                    redisManager.incrLong(importUserKey, currentTimes, RedisManager.ONE_DAY_SECONDS * 175);
                }
                currentAvailableNum -= currentTimes;
                if (currentTimes > 0){
                    result.put(rule.getDrawPoolId(),currentTimes);
                }
            } catch (Exception e){
                log.error("getPoolAndTimesByRulesError",e);
            }
        }
        return result;
    }

    public String getImportUserKey(Long poolId,Long appId,Long userId){
        return String.format(IMPORT_USER_KEY,poolId,appId,userId);
    }
}
