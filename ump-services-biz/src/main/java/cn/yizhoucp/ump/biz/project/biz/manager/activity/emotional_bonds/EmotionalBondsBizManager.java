package cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.yizhoucp.ms.core.base.config.ThreadPoolConfig;
import cn.yizhoucp.ms.core.base.enums.GiftWay;
import cn.yizhoucp.ms.core.base.util.AppUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.common.EmotionalBondsConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.common.EmotionalBondsRedisManager;
import cn.yizhoucp.ump.biz.project.common.ActivityComponent;
import cn.yizhoucp.ump.biz.project.common.event.GiftGiveEvent;
import cn.yizhoucp.ump.biz.project.dal.mybatis.domain.EmotionalBondSummaryDO;
import cn.yizhoucp.ump.biz.project.dal.mybatis.service.EmotionalBondSummaryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 天生羁绊业务类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 18:18 2025/4/22
 */
@Slf4j
@Service
public class EmotionalBondsBizManager implements ActivityComponent {

    @Resource
    private RedisManager redisManager;

    @Resource
    private EmotionalBondSummaryService emotionalBondSummaryService;

    @Resource
    private EmotionalBondsRedisManager emotionalBondsRedisManager;

    @Resource
    private EmotionalBondsRankManager emotionalBondsRankManager;

    @Override
    public String getActivityCode() {
        return EmotionalBondsConstant.ACTIVITY_CODE;
    }

    @Async(ThreadPoolConfig.ASYNC_EXECUTOR_NAME)
    @EventListener(GiftGiveEvent.class)
    @ActivityCheck(activityCode = EmotionalBondsConstant.ACTIVITY_CODE, isThrowException = false)
    public void eventSendGiftHandle(GiftGiveEvent event) {
        log.info("sendGiftHandle event:{}", event);
        sendGiftHandle(event.getBaseParam(), event.getCoinGiftGivedModels());
    }

    /**
     * 处理礼物奖励，只统计非背包礼物，当赠送方和被赠送方在合并关系列表中有记录并且未过期时，
     * 将礼物的价值按照10金币等于1排行榜值换算统计到排行榜中
     *
     * @param param                  基础参数
     * @param coinGiftGivedModelList 礼物列表
     * @return 处理结果
     */
    public Boolean sendGiftHandle(BaseParam param, List<CoinGiftGivedModel> coinGiftGivedModelList) {
        if (ObjectUtil.isEmpty(coinGiftGivedModelList)) {
            log.info("礼物列表为空");
            return Boolean.FALSE;
        }

        Date currentDate = new Date();
        Long fromUid = param.getUid();

        for (CoinGiftGivedModel gift : coinGiftGivedModelList) {
            // 跳过背包礼物
            if (isBackpackGift(gift)) {
                log.info("跳过背包礼物: {}", gift.getGiftKey());
                continue;
            }

            Long toUid = gift.getToUid();
            Long giftCoin = gift.getCoin(); // 礼物金币价值

            // 创建合并用户对标识（小ID_大ID）
            String mergedUserPair = AppUtil.splicUserId(fromUid, toUid);

            // 查询是否存在有效的关系
            List<EmotionalBondSummaryDO> activeRelationships = emotionalBondSummaryService.getByMergedUserPair(mergedUserPair);
            boolean hasActiveRelationship = false;

            if (CollUtil.isNotEmpty(activeRelationships)) {
                for (EmotionalBondSummaryDO relationship : activeRelationships) {
                    // 检查关系是否与当前被赠送方相关
                    if (relationship.getExpireTime().after(currentDate)) {
                        hasActiveRelationship = true;
                        break;
                    }
                }
            }

            // 如果存在有效关系，则将礼物价值计入排行榜
            if (hasActiveRelationship) {
                // 按照 10 金币 = 1 排行榜值 进行换算
                Long rankValue = giftCoin / 10;

                if (rankValue > 0) {
                    // 更新排行榜
                    emotionalBondsRankManager.incrRankValue(mergedUserPair, rankValue, emotionalBondsRedisManager.getRankKey());
                    emotionalBondsRedisManager.incrementBindRank(fromUid,toUid,rankValue);
                    log.info("更新排行榜成功: 用户对={}, 礼物金币={}, 排行榜值={}", mergedUserPair, giftCoin, rankValue);
                }
            } else {
                log.info("用户对 {} 不存在有效关系，不计入排行榜", mergedUserPair);
            }
        }

        return Boolean.TRUE;
    }

    /**
     * 判断是否为背包礼物
     *
     * @param gift 礼物信息
     * @return 是否为背包礼物
     */
    private boolean isBackpackGift(CoinGiftGivedModel gift) {
        // 判断礼物类型是否为背包礼物
        return StringUtils.isNotEmpty(gift.getGiftWay()) &&
                StringUtils.equalsIgnoreCase(gift.getGiftWay(), GiftWay.PACKET.getCode());
    }
}