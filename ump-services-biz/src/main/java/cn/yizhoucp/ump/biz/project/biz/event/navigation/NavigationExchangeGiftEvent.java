package cn.yizhoucp.ump.biz.project.biz.event.navigation;

import org.springframework.context.ApplicationEvent;

/**
 * 航海兑换礼物事件
 */
public class NavigationExchangeGiftEvent extends ApplicationEvent {

    public NavigationExchangeGiftEvent(NavigationExchangeGiftMessage message) {
        super(message);
    }

    @Override
    public NavigationExchangeGiftMessage getSource() {
        return this.source != null ? (NavigationExchangeGiftMessage) this.source : null;
    }
}
