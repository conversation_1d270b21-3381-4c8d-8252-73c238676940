package cn.yizhoucp.ump.biz.project.biz.manager.activity.newYear2022;


import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.enums.UserPackageBizType;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.userservices.UserBaseVO;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.ms.core.vo.userservices.userPackage.BatchUsePackageVO;
import cn.yizhoucp.ms.core.vo.userservices.userPackage.UsePackageDetailVO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.jimu.draw.DrawLogVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.inner.DrawLogItem;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.NewYear2022Constant;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.NewYear2022Constant.*;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw.inner.PrizeTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.log.LogComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.barrageList.DefaultBarrageManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.AbstractDrawTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawContext;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawLogParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.doDrawStrategy.common.ProbStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.UserRemoteService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.userAccount.UserAccountFeignService;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawLogDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.dal.service.DrawPoolItemService;
import cn.yizhoucp.ump.biz.project.dal.service.DrawPoolService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Example;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.NewYear2022Constant.*;


/**
 * 新年上上签抽奖
 *
 * @author: lianghu
 */
@Slf4j
@Service
public class NewYear2022DrawManager extends AbstractDrawTemplate {

    @Resource
    private UserAccountFeignService userAccountFeignService;
    @Resource
    private LogComponent logComponent;
    @Resource
    private DrawPoolService drawPoolService;
    @Resource
    private DrawPoolItemService drawPoolItemService;
    @Resource
    private ProbStrategy probStrategy;
    @Resource
    private DefaultBarrageManager defaultBarrageManager;
    @Resource
    private UserRemoteService userRemoteService;
    @Resource
    private NewYear2022BizManager newYear2022BizManager;
    @Resource
    private NotifyComponent notifyComponent;
    @Resource
    private RedisManager redisManager;

    @Override
    protected void resourceCheck(DrawContext context) {
        DrawParam drawParam = context.getDrawParam();
        // 任务抽奖不扣除资源
        if ("mission".equals(drawParam.getPoolCode())) {
            return;
        }

        // 根据奖池匹配待扣除资源
        List<UsePackageDetailVO> resources;
        if (DRAW_POOL.love.name().equals(drawParam.getPoolCode())) {
            resources = Lists.newArrayList(
                    UsePackageDetailVO.builder().bizKey(TICKET.XNAQQ_GIFT.name()).num(1).bizType(UserPackageBizType.GIFT.getCode()).build());
        } else if (DRAW_POOL.rich.name().equals(drawParam.getPoolCode())) {
            resources = Lists.newArrayList(
                    UsePackageDetailVO.builder().bizKey(TICKET.XNBFQ_GIFT.name()).num(5).bizType(UserPackageBizType.GIFT.getCode()).build());
        } else if (DRAW_POOL.lucky.name().equals(drawParam.getPoolCode())) {
            resources = Lists.newArrayList(
                    UsePackageDetailVO.builder().bizKey(TICKET.XNHYQ_GIFT.name()).num(1).bizType(UserPackageBizType.GIFT.getCode()).build(),
                    UsePackageDetailVO.builder().bizKey(TICKET.XNSLQ_GIFT.name()).num(1).bizType(UserPackageBizType.GIFT.getCode()).build(),
                    UsePackageDetailVO.builder().bizKey(TICKET.XNKXQ_GIFT.name()).num(1).bizType(UserPackageBizType.GIFT.getCode()).build(),
                    UsePackageDetailVO.builder().bizKey(TICKET.XNJKQ_GIFT.name()).num(1).bizType(UserPackageBizType.GIFT.getCode()).build());
        } else {
            throw new ServiceException(ErrorCode.MISS_PARAM, "奖池编码错误");
        }

        // 扣除资源
//        Boolean result = userAccountFeignService.batchUsePackageCannotFail(BatchUsePackageVO.builder().uid(drawParam.getUid()).useList(resources).build()).successData();
//        if (!Boolean.TRUE.equals(result)) {
//            throw new ServiceException(ErrorCode.ACTIVITY_PARAM_ERROR, "抽奖次数不足哦～");
//        }

        // 兼容处理
        if (DRAW_POOL.lucky.name().equals(drawParam.getPoolCode())) {
            drawParam.setPoolCode("lucky1");
            drawParam.setType("lucky1");
        }
    }

    @Override
    protected void draw(DrawContext context) {
        DrawParam param = context.getDrawParam();

        // 获取奖池属性
        DrawPoolDO drawPool = drawPoolService.getByActivityCodeAndBizKey(param.getActivityCode(), param.getType()).get(0);
        context.setDrawPoolDO(drawPool);

        // 获取奖池奖品列表
        List<DrawPoolItemDO> drawPoolItemDOList = drawPoolItemService.getByPoolCode(param.getType());
        context.setDrawPoolItemDOS(drawPoolItemDOList);

        // 获取本轮抽中奖品
        List<DrawPoolItemDTO> prizeItems = probStrategy.getDrawPoolItems(context);

        // 天选之人
        if (DRAW_POOL.love.name().equals(param.getPoolCode()) && chosen(param.getBaseParam())) {
            prizeItems = Lists.newArrayList(DrawPoolItemDTO.builder().targetTimes(1).drawPoolItemDO(REAL_PRIZE).build());
        } else {
            // 基金处理 & 获取最终抽奖结果
            if (!"mission".equals(param.getPoolCode())) {
                Long bonus = newYear2022BizManager.getBonus(param.getUid());
                Long cost = prizeItems.get(0).getDrawPoolItemDO().getItemValueGold();
                if (bonus < cost) {
                    // 基金不足设置为默认头像框
                    prizeItems = getDefaultPrize(param.getPoolCode());
                    log.info("newYear2022 基金不足 uid:{}", param.getUid());
                } else {
                    // 基金扣除
                    newYear2022BizManager.decrBonus(param.getUid(), cost);
                    log.info("newYear2022 扣除基金 uid:{}, cost:{}", param.getUid(), cost);
                }
            }
        }

        log.info("newYear2022 抽奖结果 uid:{}, prize:{}", param.getUid(), JSON.toJSONString(prizeItems));
        context.setPrizeItemList(prizeItems);
    }

    private Boolean chosen(BaseParam param) {
        String key = String.format(CHOSEN_ONE, param.getUid());
        if (redisManager.hasKey(key)) {
            redisManager.delete(key);
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    private List<DrawPoolItemDTO> getDefaultPrize(String poolCode) {
        List<DrawPoolItemDTO> result;
        if ("love".equals(poolCode)) {
            result = Lists.newArrayList(DrawPoolItemDTO.builder().targetTimes(1).drawPoolItemDO(DEFAULT_LOVE_PRIZE).build());
        } else if ("rich".equals(poolCode)) {
            result = Lists.newArrayList(DrawPoolItemDTO.builder().targetTimes(1).drawPoolItemDO(DEFAULT_RICH_PRIZE).build());
        } else {
            result = Lists.newArrayList(DrawPoolItemDTO.builder().targetTimes(1).drawPoolItemDO(DEFAULT_LUCKY_PRIZE).build());
        }
        return result;
    }

    @Override
    protected void deductResource(DrawContext context) {

    }

    @Override
    protected void doCallback(DrawContext context) {
        DrawParam drawParam = context.getDrawParam();
        UserBaseVO user = userRemoteService.getBasicAll(drawParam.getAppId(), drawParam.getUid(), Boolean.TRUE);
        // 获取奖品信息
        DrawPoolItemDO prize = context.getPrizeItemList().get(0).getDrawPoolItemDO();
        // 添加弹幕
        defaultBarrageManager.putBarrage(String.format("恭喜 %s 在许愿池中获得 %s", getName(user.getName()), prize.getItemName()));
        // 添加抽奖记录
        if (!"mission".equals(drawParam.getPoolCode())) {
            logComponent.putDrawLog(drawParam.getBaseParam(), drawParam.getActivityCode(), Lists.newArrayList(context.getPrizeItemList()));
        }
        // 小助手消息
        String text = "";
        switch (drawParam.getPoolCode()) {
            case "love":
                if (PrizeTypeEnum.PRIZE_REAL.getCode().equals(prize.getItemType())) {
                    text = "恭喜您在“新年上上签”中求得黄金手链！！！请添加客服微信：yizhoujun63提供收货地址领取奖品～";
                } else {
                    text = String.format("恭喜您在“新年上上签”中求得 %s，并有一句话：结发为夫妻结发为夫妻，恩爱两不疑。", prize.getItemName());
                }
                break;
            case "rich":
                text = String.format("恭喜您在“新年上上签”中求得 %s，并有一句话：积极进取，自然财源如“千山万水”源源而来。", prize.getItemName());
                break;
            case "lucky1":
                text = String.format("恭喜您在“新年上上签”中求得 %s，并有一句话：万事顺利，身体健康，财运亨通，合家欢聚。", prize.getItemName());
                break;
        }
        if (StringUtils.isNotBlank(text)) {
            notifyComponent.npcNotify(drawParam.getUid(), text);
        }
    }

    @Override
    protected List<DrawLogItem> drawLogWrapper(DrawLogParam param, List<DrawLogDO> drawLogDOList) {
        List<DrawLogItem> transform = Lists.newArrayList(Lists.transform(drawLogDOList, l -> {
            DrawPoolItemDO obj = JSON.parseObject(l.getLogJson(), DrawPoolItemDTO.class).getDrawPoolItemDO();
            return DrawLogItem.builder()
                    .icon(obj.getItemIcon())
                    .valueGold(obj.getItemValueGold())
                    .text(obj.getItemName())
                    .time(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(obj.getCreateTime())).build();
        }));
        log.debug("transform:{}", JSON.toJSONString(transform));
        Collections.reverse(transform);
        return transform;
    }

    private String getName(String name) {
        if (StringUtils.isBlank(name)) {
            return name;
        }
        if (name.length() < 2) {
            return name;
        } else if (name.length() == 2) {
            return name.charAt(0) + "*";
        } else {
            return name.charAt(0) + "*" + name.charAt(name.length() - 1);
        }
    }

}
