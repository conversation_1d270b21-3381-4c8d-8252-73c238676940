package cn.yizhoucp.ump.biz.project.web.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CoinBoxGuaranteeConfigVO {
    private Long id;

    private String code;

    private String type;

    private String name;

    private Integer consumerCoin;

    private Integer recycle;

    private Integer count;
}
