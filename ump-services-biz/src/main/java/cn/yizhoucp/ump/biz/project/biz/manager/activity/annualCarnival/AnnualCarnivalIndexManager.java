package cn.yizhoucp.ump.biz.project.biz.manager.activity.annualCarnival;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.RandomUtil;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.annotation.NoRepeatSubmit;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.landingservices.GiftProductModel;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.product.client.CoinGiftProductFeignService;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.activity.annualCarnival.AnnualCarnivalIndexVO;
import cn.yizhoucp.ump.api.vo.activity.annualCarnival.AnnualCarnivalPopInfoVO;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.annualCarnival.internal.AnnualCarnivalConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.annualCarnival.internal.AnnualCarnivalEnums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.annualCarnival.internal.AnnualCarnivalRedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.log.LogComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawLogParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.IndexManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.product.FeignProductService;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ActivityJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.DrawPoolItemJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ActivityDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import cn.yizhoucp.user.manager.UserFeignManager;
import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AnnualCarnivalIndexManager implements IndexManager {

    @Resource
    private AnnualCarnivalRankManager annualCarnivalRankManager;
    @Resource
    private AnnualCarnivalDrawManager annualCarnivalDrawManager;
    @Resource
    private AnnualCarnivalRedisManager annualCarnivalRedisManager;
    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private ActivityJpaDAO activityJpaDAO;
    @Resource
    private LogComponent logComponent;
    @Resource
    private RedisManager redisManager;
    @Resource
    private NotifyComponent notifyComponent;
    @Resource
    private UserFeignManager userFeignManager;
    @Resource
    private DrawPoolItemJpaDAO drawPoolItemJpaDAO;
    @Autowired
    private AnnualCarnivalTrackManager annualCarnivalTrackManager;
    @Resource
    private FeignProductService feignProductService;
    @Resource
    private CoinGiftProductFeignService coinGiftProductFeignService;

    @Override
    public AnnualCarnivalIndexVO getIndex(BaseParam param, Long toUid, String extData) {
        AnnualCarnivalIndexVO annualCarnivalIndexVO = new AnnualCarnivalIndexVO();
        annualCarnivalIndexVO.setAddMoreJoy(buildAddMoreJoy(param));
        annualCarnivalIndexVO.setWarmWinterDay(buildWarmWinterDay(param));
        annualCarnivalIndexVO.setKingdomBattle(buildKingdomBattle(param));
        annualCarnivalIndexVO.setTabStatus(buildTabStatus(param));
        return annualCarnivalIndexVO;
    }

    private List<AnnualCarnivalIndexVO.TabStatus> buildTabStatus(BaseParam param) {
        List<AnnualCarnivalIndexVO.TabStatus> tabStatusList = new ArrayList<>();
        LocalDate startTime = getActivityStartTime(param).toLocalDate();
        for (int i = 0; i < 3; i++) {
            AnnualCarnivalIndexVO.TabStatus tabStatus = new AnnualCarnivalIndexVO.TabStatus();
            Long daysBetween = ChronoUnit.DAYS.between(startTime, LocalDate.now());
            if ((i == 0 && daysBetween >= 0) ||
                    (i == 1 && daysBetween > 6) ||
                    (i == 2 && daysBetween > 13)) {
                tabStatus.setOpen(Boolean.TRUE);
            } else {
                tabStatus.setOpen(Boolean.FALSE);
            }
            tabStatus.setTabId(i);
            tabStatusList.add(tabStatus);
        }
        return tabStatusList;
    }

    private AnnualCarnivalIndexVO.KingdomBattle buildKingdomBattle(BaseParam param) {
        AnnualCarnivalIndexVO.KingdomBattle kingdomBattle = new AnnualCarnivalIndexVO.KingdomBattle();
        kingdomBattle.setCurrentStage(getCurrentRankStage(param));
        return kingdomBattle;
    }

    public Long getCurrentRankStage(BaseParam param) {
        LocalDate startTime = getActivityStartTime(param).toLocalDate();
        LocalDate now = LocalDate.now();
        Long daysBetween = ChronoUnit.DAYS.between(startTime, now);
        if (daysBetween >= 14L && daysBetween <= 15L) {
            return 0L;
        } else if (daysBetween >= 16L && daysBetween <= 18L) {
            return 1L;
        } else if (daysBetween >= 19L && daysBetween <= 21L) {
            return 2L;
        } else if (daysBetween == 22) {
            return 3L;
        } else {
            return 4L;
        }
    }

    private AnnualCarnivalIndexVO.WarmWinterDay buildWarmWinterDay(BaseParam param) {
        AnnualCarnivalIndexVO.WarmWinterDay warmWinterDay = new AnnualCarnivalIndexVO.WarmWinterDay();
        warmWinterDay.setBroadCast(JSON.parseArray(String.valueOf(redisManager.lGet(String.format(AnnualCarnivalConstant.BROADCAST, getActivityCode()), 0, 20))));
        warmWinterDay.setDrawItem(annualCarnivalRedisManager.getLuckNumberPlate(param.getUid()));
        warmWinterDay.setLuckyDigit(annualCarnivalRedisManager.getYesterdayLuckDigit());
        warmWinterDay.setPoolCode(AnnualCarnivalConstant.POOL_CODE);
        warmWinterDay.setLuckyNumber(annualCarnivalRedisManager.getLuckyNumber(param.getUid()).toString());
        return warmWinterDay;
    }

    private AnnualCarnivalIndexVO.addMoreJoy buildAddMoreJoy(BaseParam param) {
        AnnualCarnivalIndexVO.addMoreJoy addMoreJoy = new AnnualCarnivalIndexVO.addMoreJoy();
/*
        addMoreJoy.setBroadCast(JSON.parseArray(String.valueOf(redisManager.lGet(AnnualCarnivalConstant.BROADCAST, 0, 20))));
*/
        addMoreJoy.setProgress(buildProgress(param));
        return addMoreJoy;
    }

    private List<AnnualCarnivalIndexVO.Progress> buildProgress(BaseParam param) {
        List<AnnualCarnivalIndexVO.Progress> progressList = new ArrayList<>();
        for (AnnualCarnivalEnums.TargetRewardEnum targetRewardEnum : AnnualCarnivalEnums.TargetRewardEnum.values()) {
            List<DrawPoolItemDO> drawPoolItemDOS = drawPoolItemJpaDAO.getByPoolCode(targetRewardEnum.getTaskCode());
            List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(param.getAppId(), getActivityCode(), targetRewardEnum.getTaskCode());
            if (CollectionUtil.isEmpty(drawPoolItemDOS) || CollectionUtil.isEmpty(scenePrizeDOList)) {
                continue;
            }
            AnnualCarnivalIndexVO.Progress progress = new AnnualCarnivalIndexVO.Progress();
            progress.setCollectedKey(targetRewardEnum.getTaskCode());
            List<AnnualCarnivalIndexVO.CollectedGiftVO> collectedGiftVOS = new ArrayList<>();
            for (DrawPoolItemDO drawPoolItemDO : drawPoolItemDOS) {
                AnnualCarnivalIndexVO.CollectedGiftVO collectedGiftVO = new AnnualCarnivalIndexVO.CollectedGiftVO();
                collectedGiftVO.setCollected(annualCarnivalRedisManager.getIsCollected(param.getUid(), drawPoolItemDO.getPoolCode(), drawPoolItemDO.getItemKey()));
                collectedGiftVO.setGiftName(drawPoolItemDO.getItemName());
                collectedGiftVO.setGiftNum(drawPoolItemDO.getItemNum());
                collectedGiftVO.setGiftIcon(drawPoolItemDO.getItemIcon());
                collectedGiftVO.setGiftValue(drawPoolItemDO.getItemValueGold());
                collectedGiftVOS.add(collectedGiftVO);
            }
            progress.setGifts(collectedGiftVOS);
            progress.setRewards(scenePrizeDOList.stream().map(scenePrizeDO -> AnnualCarnivalIndexVO.GiftVO.builder()
                    .giftName(scenePrizeDO.getPrizeDesc())
                    .giftNum(scenePrizeDO.getPrizeNum())
                    .giftIcon(scenePrizeDO.getPrizeIcon())
                    .giftValue(scenePrizeDO.getPrizeValueGold())
                    .build()).collect(Collectors.toList()));
            progress.setStatus(getCollectedStatus(param.getUid(), targetRewardEnum.getTaskCode()));
            progressList.add(progress);
        }
        return progressList;
    }

    private Long getCollectedStatus(Long uid, String taskCode) {
        Integer collectedCount = annualCarnivalRedisManager.getCollectedReward(uid, taskCode);
        if (collectedCount >= AnnualCarnivalConstant.COLLECT_TIMES_LIMIT) {
            return 1L;
        }
        return 0L;
    }

    @Override
    public String getTemplateType() {
        return "";
    }

    @Override
    public String getActivityCode() {
        return AnnualCarnivalConstant.ACTIVITY_CODE;
    }

    public RankVO getRank(String rankCode) {
        String rankKey = "";
        Long rankLen = 0L;
        Long otherUid = 0L;
        RankContext.RankType rankType = RankContext.RankType.user;
        if (AnnualCarnivalEnums.RankTypeEnum.KoiList.name().equals(rankCode)) {
            rankKey = String.format(AnnualCarnivalConstant.RANK_KEY, getActivityCode(), AnnualCarnivalEnums.RankTypeEnum.KoiList.name());
            rankLen = 20L;
        }
        if (AnnualCarnivalEnums.RankTypeEnum.HeroicList.name().equals(rankCode)) {
            rankKey = String.format(AnnualCarnivalConstant.RANK_KEY, getActivityCode(), AnnualCarnivalEnums.RankTypeEnum.HeroicList.name());
            rankLen = 10L;
        }
        if (AnnualCarnivalEnums.RankTypeEnum.OracleWarList.name().equals(rankCode)) {
            rankKey = String.format(AnnualCarnivalConstant.RANK_KEY, getActivityCode(), AnnualCarnivalEnums.RankTypeEnum.OracleWarList.name());
            rankLen = 10L;
            rankType = RankContext.RankType.cp;
            otherUid = getMaxValueCpUid(BaseParam.ofMDC());
        }
        if (rankCode.contains(AnnualCarnivalEnums.RankTypeEnum.RagnarokList.name())) {
            Long rankStage = getCurrentRankStage(BaseParam.ofMDC());
            rankKey = String.format(AnnualCarnivalConstant.RANK_KEY, getActivityCode(), AnnualCarnivalEnums.RankTypeEnum.RagnarokList.name() + "_" + rankStage);
            rankType = RankContext.RankType.room;
            AnnualCarnivalEnums.StageEnum stageEnum = AnnualCarnivalEnums.StageEnum.getByStage(rankStage.intValue());
            if (stageEnum == null) {
                rankLen = 10L;
            } else {
                rankLen = stageEnum.getRankLen();
            }
        }

        return annualCarnivalRankManager.getRank(RankContext.builder()
                .param(BaseParam.ofMDC())
                .otherUid(otherUid)
                .activityCode(getActivityCode())
                .rankKey(rankKey)
                .rankLen(rankLen)
                .supportDiff(Boolean.TRUE)
                .type(rankType)
                .build());

    }

    private Long getMaxValueCpUid(BaseParam baseParam) {
        String rankKey = String.format(AnnualCarnivalConstant.RANK_KEY, AnnualCarnivalConstant.ACTIVITY_CODE, String.format(AnnualCarnivalConstant.RANK_KEY_GIFT_TO_OPPOSITE_SEX, baseParam.getUid()));
        RankVO rank = annualCarnivalRankManager.getRank(RankContext.builder()
                .param(BaseParam.ofMDC())
                .activityCode(getActivityCode())
                .rankKey(rankKey)
                .rankLen(1L)
                .type(RankContext.RankType.user)
                .build());
        List<RankItem> rankItems = rank.getRankList();
        if (CollectionUtil.isEmpty(rankItems)) {
            return 0L;
        }
        return rankItems.get(0).getId();
    }

    /**
     * 领取奖励
     *
     * @param collectedKey
     * @return
     */
    public List<AnnualCarnivalIndexVO.GiftVO> claimReward(String collectedKey) {
        AnnualCarnivalEnums.TargetRewardEnum targetRewardEnum = AnnualCarnivalEnums.TargetRewardEnum.getByTaskCode(collectedKey);
        if (targetRewardEnum == null) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "参数错误");
        }
        BaseParam param = BaseParam.ofMDC();
        Integer collectedCount = annualCarnivalRedisManager.getCollectedReward(param.getUid(), collectedKey);
        if (collectedCount < AnnualCarnivalConstant.COLLECT_TIMES_LIMIT) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "未达到领取条件");
        }
        //获取奖励
        List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(param.getAppId(), getActivityCode(), collectedKey);
        if (CollectionUtils.isEmpty(scenePrizeDOList)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "未找到对应奖品");
        }
        //下发奖励
        sendPrizeManager.sendPrize(
                param,
                scenePrizeDOList.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, param.getUid())).collect(Collectors.toList())
        );
        //清空领取状态
        annualCarnivalRedisManager.resetCollectedReward(param.getUid(), collectedKey);
        //埋点
        annualCarnivalTrackManager.allActivityTaskFinish(param.getUid(), targetRewardEnum.getTrackType());
        return scenePrizeDOList.stream().map(scenePrizeDO -> AnnualCarnivalIndexVO.GiftVO.builder()
                .giftName(scenePrizeDO.getPrizeDesc())
                .giftNum(scenePrizeDO.getPrizeNum())
                .giftIcon(scenePrizeDO.getPrizeIcon())
                .giftValue(scenePrizeDO.getPrizeValueGold())
                .build()).collect(Collectors.toList());
    }

    public Object drawRecord() {
        BaseParam param = BaseParam.ofMDC();
        Long uid = param.getUid();
        return annualCarnivalDrawManager.drawLogNew(DrawLogParam.builder()
                .appId(param.getAppId())
                .unionId(param.getUnionId())
                .uid(uid)
                .activityCode(AnnualCarnivalConstant.ACTIVITY_CODE)
                .build(), AnnualCarnivalConstant.POOL_CODE);
    }

    public Long getCurrentStage(BaseParam param) {
        LocalDate startTime = getActivityStartTime(param).toLocalDate();
        LocalDate now = LocalDate.now();
        Long daysBetween = ChronoUnit.DAYS.between(startTime, now);
        if (daysBetween < 7L) {
            return 0L;
        } else if (daysBetween < 14L) {
            return 1L;
        } else {
            return 2L;
        }
    }

    public LocalDateTime getActivityStartTime(BaseParam param) {
        ActivityDO activityDO = activityJpaDAO.getByAppIdAndCode(param.getAppId(), getActivityCode());
        if (activityDO == null) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "活动不存在");
        }
        return activityDO.getStartTime();
    }

    public Boolean refreshLuckDigit() {
        Integer randomNumber = RandomUtil.randomInt(1, 9);
        annualCarnivalRedisManager.refreshLuckDigit(randomNumber);
        return Boolean.TRUE;
    }

    @NoRepeatSubmit(time = 3)
    public JSONObject draw() {
        BaseParam param = BaseParam.ofMDC();
        //检查是否有抽奖次数
        Long luckNumberPlate = annualCarnivalRedisManager.getLuckNumberPlate(param.getUid());
        if (luckNumberPlate <= 0) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "号码牌暂不可抽取哦～");
        }
        //抽奖
        String luckNumber = generateLuckyNumber();
        //扣减抽奖次数
        annualCarnivalRedisManager.decrementLuckNumberPlate(param.getUid(), 1L);
        JSONObject result = new JSONObject();
        result.put("luckyNum", luckNumber);
        //记录抽奖结果
        logComponent.putDrawLog(param, AnnualCarnivalConstant.ACTIVITY_CODE, buildPrizeItemList(luckNumber));
        //记录弹窗
        recordBroadcast(param.getUid(), luckNumber);
        //如果没有幸运号保存幸运号码
        Long oldLuckyNumber = annualCarnivalRedisManager.getLuckyNumber(param.getUid());
        if (oldLuckyNumber <= 0) {
            annualCarnivalRedisManager.replaceLuckyNumber(param.getUid(), luckNumber);
        }
        return result;
    }

    private void recordBroadcast(Long uid, String luckNumber) {
        UserVO userVO = userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), uid);
        HashMap<String, String> broadCast = new HashMap<>();
        broadCast.put("userName", userVO.getName());
        broadCast.put("ItemName", luckNumber);
        redisManager.listLpush(String.format(AnnualCarnivalConstant.BROADCAST, getActivityCode()), JSON.toJSONString(broadCast), DateUtil.ONE_MONTH_SECOND);
    }

    private List<DrawPoolItemDTO> buildPrizeItemList(String luckNumber) {
        List<DrawPoolItemDTO> prizeItemList = new ArrayList<>();
        DrawPoolItemDO drawPoolItemDO = DrawPoolItemDO.builder()
                .itemName(luckNumber)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .itemKey("luckyNum")
                .itemNum(1)
                .itemValueGold(0L)
                .status(1)
                .poolCode("luckyNumberPool")
                .build();
        DrawPoolItemDTO drawPoolItemDTO = DrawPoolItemDTO.builder()
                .targetTimes(1)
                .drawPoolItemDO(drawPoolItemDO).build();
        prizeItemList.add(drawPoolItemDTO);
        return prizeItemList;
    }

    private String generateLuckyNumber() {
        StringBuilder luckyNumStr = new StringBuilder();
        for (int i = 0; i < 5; i++) {
            Integer randomNumber = RandomUtil.randomInt(1, 9);
            luckyNumStr.append(randomNumber);
        }
        return luckyNumStr.toString();
    }

    public Boolean sendLuckyNumPrize() {
        Long start = 0L;
        Long batchSize = 1000L;
        Long end = batchSize - 1;
        LocalDate yesterday = LocalDate.now().minusDays(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String luckNumberKey = String.format(AnnualCarnivalConstant.LUCK_NUMBER_KEY, AnnualCarnivalConstant.ACTIVITY_CODE, yesterday.format(formatter));
        List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(1L, AnnualCarnivalConstant.ACTIVITY_CODE, "luckyNumPrize");
        if (CollectionUtil.isEmpty(scenePrizeDOList)) {
            log.warn("AnnualCarnivalIndexManager#sendRankPrize scenePrizeDOList is empty");
            return Boolean.FALSE;
        }
        while (true) {
            Set<ZSetOperations.TypedTuple<Object>> typedTuples = redisManager.reverseRangeByScoreWithScores(luckNumberKey,
                    0, Double.MAX_VALUE, start, end);
            log.info("AnnualCarnivalIndexManager#sendRankPrize typedTuples size {}", typedTuples.size());
            if (CollectionUtils.isEmpty(typedTuples)) {
                break;
            }
            //下发奖励
            sendPrize(typedTuples, scenePrizeDOList);
            start += batchSize;
            end = end + batchSize - 1;
        }

        return Boolean.TRUE;
    }

    private void sendPrize(Set<ZSetOperations.TypedTuple<Object>> typedTuples, List<ScenePrizeDO> scenePrizeDOList) {
        for (ZSetOperations.TypedTuple<Object> typedTuple : typedTuples) {
            Long uid = Long.valueOf(Optional.ofNullable(typedTuple.getValue()).orElse(0).toString());
            Long luckNumber = Optional.ofNullable(typedTuple.getScore()).orElse(0d).longValue();
            Long luckDigit = annualCarnivalRedisManager.getYesterdayLuckDigit();
            Long count = countOccurrencesOfDigit(luckDigit, luckNumber);
            if (count <= 0) {
                String msg = String.format("亲爱的用户，昨日的幸运数字位%s，您在活动中生效幸运号位%s，很遗憾没有包含幸运数字，去别的活动看看吧～", luckDigit, luckNumber);
                notifyComponent.npcNotify(ServicesAppIdEnum.lanling.getUnionId(), uid, msg);
                continue;
            }
            //下发奖励
            List<ScenePrizeDO> prizeDOList = scenePrizeDOList.stream().filter(scenePrizeDO -> Objects.equals(count, scenePrizeDO.getPrizeBelongToRank())).collect(Collectors.toList());
            sendPrizeManager.sendPrize(
                    BaseParam.builder().appId(1L).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(uid).build(),
                    prizeDOList.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, uid)).collect(Collectors.toList())
            );
            //发送通知
            for (ScenePrizeDO scenePrizeDO : prizeDOList) {
                String msg = String.format("亲爱的用户，昨日的幸运数字位%s，您在活动中生效幸运号位%s，包含%s位幸运数字，获得%s1个，奖励已经下发，快去查看吧～", luckDigit, luckNumber, count, scenePrizeDO.getPrizeDesc());
                notifyComponent.npcNotify(ServicesAppIdEnum.lanling.getUnionId(), uid, msg);
                annualCarnivalTrackManager.allActivityReceiveAward("lucky123", scenePrizeDO.getPrizeValue(), scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeNum(), uid);
            }
            annualCarnivalTrackManager.allActivityTaskFinish(uid, "contain_lucky_numbers");
            log.info("sendCampReward uid {} reward {}", uid, "luckyNumPrize");
        }
    }

    private Long countOccurrencesOfDigit(Long luckDigit, Long luckNumber) {
        String numberStr = luckNumber.toString();
        char digitChar = Character.forDigit(luckDigit.intValue(), 10);
        Long count = 0L;
        for (char c : numberStr.toCharArray()) {
            if (c == digitChar) {
                count++;
            }
        }
        return count;
    }

    /**
     * 替换幸运号码
     *
     * @return
     */
    public Boolean replaceLuckyNumber(String luckyNum) {
        BaseParam param = BaseParam.ofMDC();
        try {
            annualCarnivalRedisManager.replaceLuckyNumber(param.getUid(), luckyNum);
        } catch (Exception e) {
            log.warn("替换幸运号码失败 {} error {}", param.getUid(), e.getMessage());
            throw new ServiceException(ErrorCode.INVALID_PARAM, "替换幸运号码失败");
        }
        return Boolean.TRUE;
    }

    public AnnualCarnivalPopInfoVO getAnnualEventInfo() {
        AnnualCarnivalPopInfoVO annualCarnivalPopInfoVO = new AnnualCarnivalPopInfoVO();
        Boolean showAnnualEventPop = getShowAnnualEventPop();
        annualCarnivalPopInfoVO.setShowAnnualEventPop(showAnnualEventPop);
        if (showAnnualEventPop) {
            annualCarnivalPopInfoVO.setAnnualEventPopupUrl(getAnnualEventPopupUrl());
            annualCarnivalPopInfoVO.setGiftInfo(getGiftInfo());
        }
        return annualCarnivalPopInfoVO;
    }

    private GiftProductModel getGiftInfo() {
        String jsonItemKey = JSONObject.toJSONString(Collections.singletonList("LP_GIFT"));
        List<GiftProductModel> giftProductModels = coinGiftProductFeignService.getGiftInfoByKeyList(MDCUtil.getCurAppIdByMdc(), jsonItemKey).getData();

        if (CollectionUtil.isEmpty(giftProductModels)) {
            return null;
        }
        return giftProductModels.get(0);
    }

    private String getAnnualEventPopupUrl() {
        String popUpUrl = "";
        List<ScenePrizeDO> scenePrizeDO = scenePrizeJpaDAO.getListBySceneCode(1L, getActivityCode(), "annualEventPop");
        if (CollectionUtil.isEmpty(scenePrizeDO)) {
            return popUpUrl;
        }
        ScenePrizeDO scenePrizeDO1 = scenePrizeDO.get(0);
        return scenePrizeDO1.getPrizeIcon();
    }

    private Boolean getShowAnnualEventPop() {
        ActivityDO activityDOS = activityJpaDAO.getByAppIdAndCode(ServicesAppIdEnum.lanling.getAppId(), getActivityCode());
        if (activityDOS == null) {
            return Boolean.FALSE;
        }
        Integer status = activityDOS.getStatus();
        if (status == 1 && getCurrentRankStage(BaseParam.ofMDC()) == 3) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
}
