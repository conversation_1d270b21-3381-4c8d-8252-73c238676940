package cn.yizhoucp.ump.biz.project.biz.manager.activity.luoshenLegend;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.ump.api.vo.jimu.draw.DrawReturn;
import cn.yizhoucp.ump.api.vo.jimu.draw.PrizeItem;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.AbstractDrawTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawContext;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.luoshenLegend.common.LuoShenLegendConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.luoshenLegend.common.LuoShenLegendEnums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.luoshenLegend.common.LuoShenLegendRedisManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dal.mybatis.domain.ActivityMessageTemplates;
import cn.yizhoucp.ump.biz.project.dal.mybatis.service.ActivityMessageTemplatesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 洛神赋抽奖类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 19:24 2025/1/14
 */
@Slf4j
@Service
public class LuoShenLegendDrawManager extends AbstractDrawTemplate {

    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    private LuoShenLegendRedisManager luoShenLegendRedisManager;
    @Resource
    private LuoShenLegendTrackManager luoShenLegendTrackManager;
    @Resource
    private ActivityMessageTemplatesService activityMessageTemplatesService;
    @Resource
    private NotifyComponent notifyComponent;

    @Override
    protected void resourceCheck(DrawContext context) {

    }

    @Override
    protected void deductResource(DrawContext context) {

    }

    @Override
    protected void doCallback(DrawContext context) {
    }

    public void sendCamelliaGodGift(BaseParam param, CoinGiftGivedModel coinGiftGivedModel) {
        //发放奖励
        LuoShenLegendEnums.GoddessEnum goddessEnum = LuoShenLegendEnums.GoddessEnum.CAMELLIA_GOD;
        //判断是否已经开启花茶神
        Long taskProgress = luoShenLegendRedisManager.getTaskProgress(param.getUid());
        if (taskProgress < LuoShenLegendConstant.MAX_TASK_PROGRESS) {
            return;
        }
        ScenePrizeDO targetReward = getTargetReward(goddessEnum);
        if (ObjectUtil.equals(targetReward.getPrizeValue(), coinGiftGivedModel.getGiftKey())) {
            //发放奖励
            sendGoldReward(param.getUid(), coinGiftGivedModel, goddessEnum);
        }
    }

    public void sendPrayTask(BaseParam param, CoinGiftGivedModel coinGiftGivedModel) {
        String poolCode = coinGiftGivedModel.getLotteryGiftKey();
        Long uid = param.getUid();
        Integer targetTimes = coinGiftGivedModel.getProductCount().intValue();
        ScenePrizeDO scenePrizeDO = getTaskGift(poolCode);
        if (scenePrizeDO == null) {
            return;
        }
        if (ObjectUtil.equals(poolCode, scenePrizeDO.getPrizeValue())) {
            long progress = luoShenLegendRedisManager.incrementTaskProgress(uid, targetTimes.longValue());
            //解锁山茶花神任务
            if (progress >= LuoShenLegendConstant.MAX_TASK_PROGRESS) {
                luoShenLegendTrackManager.allActivityTaskFinish(uid, LuoShenLegendConstant.UNLOCK_CAMELLIA);
            }
        }
    }

    public void sendGiftByDrawResult(BaseParam param, CoinGiftGivedModel coinGiftGivedModel) {
        //发放奖励
        LuoShenLegendEnums.GoddessEnum goddessEnum = luoShenLegendRedisManager.getCurrentGoddess();
        if (goddessEnum == null) {
            return;
        }
        ScenePrizeDO targetReward = getTargetReward(goddessEnum);
        if (ObjectUtil.equals(targetReward.getPrizeValue(), coinGiftGivedModel.getGiftKey())) {
            //发放奖励
            sendGoldReward(param.getUid(), coinGiftGivedModel, goddessEnum);
        }
    }

    private void sendGoldReward(Long uid, CoinGiftGivedModel coinGiftGivedModel, LuoShenLegendEnums.GoddessEnum goddessEnum) {
        DrawReturn drawReturn = this.draw(DrawParam.builder()
                .activityCode(LuoShenLegendConstant.ACTIVITY_CODE)
                .times(coinGiftGivedModel.getProductCount().intValue())
                .poolCode(goddessEnum.getGodCode())
                .unionId(ServicesAppIdEnum.lanling.getUnionId())
                .uid(uid)
                .build());
        log.info("LuoShenLegendDrawManager#sendGoldReward uid{} drawReturn:{}", uid, drawReturn);
        ActivityMessageTemplates activityMessageTemplates = activityMessageTemplatesService.findByActivityCodeAndSceneCode(LuoShenLegendConstant.ACTIVITY_CODE, LuoShenLegendConstant.REWARD_MSG);
        if (activityMessageTemplates != null) {
            List<PrizeItem> prizeItemList = drawReturn.getPrizeItemList();
            for(PrizeItem prizeItem : prizeItemList){
                String msg = activityMessageTemplates.getMessageTemplate();
                msg = String.format(msg, goddessEnum.getGodName(), prizeItem.getPrizeName(),prizeItem.getTargetTimes());
                notifyComponent.npcNotify(uid, msg);
            }
        }
        for (PrizeItem item : drawReturn.getPrizeItemList()) {
            luoShenLegendTrackManager.allActivityReceiveAward(item.getPrizeKey(), item.getValueGold().longValue(), item.getPrizeNum(), uid);
        }

    }

    private ScenePrizeDO getTargetReward(LuoShenLegendEnums.GoddessEnum goddessEnum) {
        ScenePrizeDO scenePrizeDOCache = luoShenLegendRedisManager.getTargetRewardCache(goddessEnum.getTargetReward());
        if (scenePrizeDOCache != null) {
            return scenePrizeDOCache;
        }
        List<ScenePrizeDO> scenePrizeDOs = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), LuoShenLegendConstant.ACTIVITY_CODE, goddessEnum.getTargetReward());
        if (CollUtil.isEmpty(scenePrizeDOs)) {
            log.error("no reward found for goddessEnum:{}", goddessEnum);
        }
        luoShenLegendRedisManager.setTargetRewardCache(goddessEnum.getTargetReward(), scenePrizeDOs.get(0));
        return scenePrizeDOs.get(0);
    }

    private ScenePrizeDO getTaskGift(String taskGiftKey) {
        ScenePrizeDO scenePrizeDOCache = luoShenLegendRedisManager.getTargetRewardCache(taskGiftKey);
        if (scenePrizeDOCache != null) {
            return scenePrizeDOCache;
        }
        List<ScenePrizeDO> scenePrizeDOs = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), LuoShenLegendConstant.ACTIVITY_CODE, taskGiftKey);
        if (CollUtil.isEmpty(scenePrizeDOs)) {
            return null;
        }
        luoShenLegendRedisManager.setTargetRewardCache(taskGiftKey, scenePrizeDOs.get(0));
        return scenePrizeDOs.get(0);
    }
}
