package cn.yizhoucp.ump.biz.project.biz.manager.activity.astrology.thumpLover;

import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.enums.ServicesNameEnum;
import cn.yizhoucp.starter.cassandra.base.kafka.YzKafkaProducerManager;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 埋点管理
 *
 * <AUTHOR>
 * @date 2024/4/2 16:30
 */
@Service
public class ThumpLoverTrackManager {

    @Resource
    private YzKafkaProducerManager yzKafkaProducerManager;

    /**
     * 买点
     *
     * @param activityModule     活动模块-lottery 抽奖 -task  任务  -list 榜单
     * @param taskType           任务类型 "- level1_lottery 一级抽奖
     *                           - level2_lottery  二级抽奖
     *                           - level3_lottery 三级抽奖
     *                           <p>
     *                           <p>
     *                           - send_real_love_gift_task 送真爱官宣并获得奖励
     *                           - send_dreamlike_gift_task 送梦幻官宣并获得奖励
     *                           - send_normal_gift_task 赠送普通礼物获得奖励
     *                           - 2888_award_gift 获得2888奖励
     *                           <p>
     *                           -on_list上榜
     * @param awardActivityPoint 获得官宣值
     * @param awardKey           奖品 key
     * @param awardAmount        奖品对应的金币价值
     * @param awardCount         获得奖励的个数
     * @param uid                用户ID
     */

    public void allActivityReceiveAward(String activityModule, String taskType, Long awardActivityPoint, String awardKey, Long awardAmount, Integer awardCount, Long uid) {
        Map<String, Object> params = Maps.newHashMapWithExpectedSize(7);
        params.put("activity_type", "splash_love_activity");
        params.put("attribute_type", "official_announcement_activity");
        params.put("activity_module", activityModule);
        params.put("task_type", taskType);
        params.put("award_activity_point", awardActivityPoint);
        params.put("award_key", awardKey);
        params.put("award_amount", awardAmount);
        params.put("award_count", awardCount);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_receive_award", params, ServicesNameEnum.ump_services.getCode());
    }

}
