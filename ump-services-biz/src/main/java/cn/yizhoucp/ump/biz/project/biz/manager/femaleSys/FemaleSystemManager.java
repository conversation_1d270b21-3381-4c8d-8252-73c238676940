package cn.yizhoucp.ump.biz.project.biz.manager.femaleSys;

import cn.yizhoucp.ms.core.base.enums.ABTestTypeEnum;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.pop.PopManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.mission.FeignMissionService;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.LoginPopJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.LoginPopDO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Example;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * 女生体系相关业务
 * <AUTHOR>
 * @Date 2023/4/24 16:51
 * @Version 1.0
 */
@Slf4j
@Component
public class FemaleSystemManager {

    private static final String FEMALE_SYS_POP_IDEMPOTENT = "ump:femaleSysPopIdempotent:%s_%s";
    private static final String POP_BIZ_KEY = "female_sys_v2";

    @Resource
    private PopManager popManager;
    @Resource
    private LoginPopJpaDAO loginPopJpaDAO;
    @Resource
    private RedisManager redisManager;
    @Resource
    private FeignMissionService feignMissionService;

    public void loginHandle(BaseParam param) {
        doPop(param);
    }

    private void doPop(BaseParam param) {

        log.debug("doPop param {}", JSON.toJSONString(param));
        if (Boolean.TRUE.equals(redisManager.setnx(String.format(FEMALE_SYS_POP_IDEMPOTENT, DateUtil.getNowYyyyMMdd(), param.getUid()), System.currentTimeMillis(), DateUtil.ONE_DAY_SECOND))
        && StringUtils.equalsIgnoreCase(feignMissionService.getV3ABTestGroup(param.getUid()).successData(),
                ABTestTypeEnum.experiment_group_a.getType())) {
            log.info("女生体系 v3 A组 uid {} ", param.getUid());
            Optional<LoginPopDO> optionalLoginPopDO = loginPopJpaDAO.findOne(Example.of(LoginPopDO.builder().appId(param.getAppId()).unionId(param.getUnionId()).bizKey(POP_BIZ_KEY).build()));
            if (optionalLoginPopDO.isPresent()) {
                LoginPopDO popDO = optionalLoginPopDO.get();
                popDO.setUrl(popDO.getUrl());
                popManager.popByLoginPopDO(param.getAppId(), param.getUnionId(), param.getUid(), popDO);
            }
        }
    }

}
