package cn.yizhoucp.ump.biz.project.web.rest.controller.activity.aliceInWonderland;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.auth.SecurityUser;
import cn.yizhoucp.ms.core.base.auth.SecurityUtils;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ump.api.vo.activity.aliceInWonderland.AliceInWonderlandHomePageVO;
import cn.yizhoucp.ump.api.vo.activity.aliceInWonderland.DreamLandVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.DrawReturn;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.AliceInWonderlandConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.AliceInWonderland.AliceInWonderlandBizManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.AliceInWonderland.AliceInWonderlandDrawManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.AliceInWonderland.AliceInWonderlandIndexManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;
import cn.yizhoucp.ump.biz.project.web.param.activity.aliceInWondland.AwardRequest;
import cn.yizhoucp.ump.biz.project.web.param.activity.aliceInWondland.SelectCampParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
public class AliceInWonderlandController {

    @Resource
    private AliceInWonderlandIndexManager aliceInWonderlandIndexManager;

    @Resource
    private AliceInWonderlandBizManager aliceInWonderlandBizManager;
    @Resource
    private AliceInWonderlandDrawManager aliceInWonderlandDrawManager;


    /**
     * 切换阵营
     * @return
     */
    @PostMapping("/api/inner/activity/alice_in_wonderland/select-camp")
    public Result<Boolean> selectCamp(@RequestBody SelectCampParam param)
    {
        return  RestBusinessTemplate.executeWithoutTransaction(()->
                aliceInWonderlandIndexManager.selectCamp(param.getUid(),param.getCamp(),param.getIsCampSwitch())
        );
    }

    /**
     * 获取密友列表
     * @param uid
     * @return
     */
    @GetMapping("/api/inner/activity/alice_in_wonderland/get-friend-list")
    public Result<List<DreamLandVO.UserInfoVO>> getFriendList(Long uid)
    {
        return RestBusinessTemplate.executeWithoutTransaction(()->
                aliceInWonderlandBizManager.getFriendList(uid)
        );
    }

    /**
     * 绑定密友
     * @param toUid
     * @return
     */
    @GetMapping("/api/inner/activity/alice_in_wonderland/bind-friend")
    public Result<Boolean> bindFriend(@RequestParam("toUid") Long toUid) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
                    SecurityUser user = SecurityUtils.getCurrentUser();
                    return aliceInWonderlandBizManager.bindFriend(user.getUserId(), toUid);
                }
        );
    }

    /**
     * 领取奖励
     */
    @PostMapping("/api/inner/activity/alice_in_wonderland/get_award")
    public Result<Boolean> getAward(@RequestBody AwardRequest awardRequest)
    {
        return RestBusinessTemplate.executeWithoutTransaction(()->
                aliceInWonderlandBizManager.getAward(awardRequest)
        );
    }

    @GetMapping("/api/inner/activity/alice_in_wonderland/draw")
    public Result<DrawReturn> draw(String type, String poolCode, Integer times, String extValue) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> aliceInWonderlandDrawManager.draw(DrawParam.builder()
                .unionId(MDCUtil.getCurUnionIdByMdc())
                .appId(MDCUtil.getCurAppIdByMdc())
                .uid(MDCUtil.getCurUserIdByMdc())
                .activityCode(AliceInWonderlandConstant.ACTIVITY_CODE)
                .type(type)
                .poolCode(poolCode)
                .times(times)
                .extValue(extValue).build()));
    }

}
