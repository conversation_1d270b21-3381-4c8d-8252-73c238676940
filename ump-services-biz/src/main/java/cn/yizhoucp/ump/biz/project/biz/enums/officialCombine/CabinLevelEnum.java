package cn.yizhoucp.ump.biz.project.biz.enums.officialCombine;

import cn.yizhoucp.ump.api.vo.officialCombine.sweetCabin.CabinBaseVO;
import cn.yizhoucp.ump.api.vo.officialCombine.sweetCabin.CabinPrizeListDetailVO;
import cn.yizhoucp.ump.api.vo.officialCombine.sweetCabin.CabinPrizeListVO;
import cn.yizhoucp.ump.api.vo.officialCombine.sweetCabin.CabinPrizeVO;
import cn.yizhoucp.ump.biz.project.biz.constant.SweetCabinConstant;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 甜蜜小屋等级
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum CabinLevelEnum {

    /** 甜蜜小屋等级 */
    LEVEL_NINE(9, 240L, 9999999999999L, 9999999999999L, "天空之城",
            "https://res-cdn.nuan.chat/officialCombine/cabin/cabin_icon_nine.png",
            "https://res-cdn.nuan.chat/officialCombine/cabin/cabin_icon_gray_nine.png",
            "https://res-cdn.nuan.chat/officialCombine/cabin/cabin_icon_vague_nine.png",
            "https://res-cdn.nuan.chat/depth/cabin/cabin_rank_icon_nine.png",
            Arrays.asList("QCHL_GIFT", "QYYH_GIFT", "MDHY_GIFT"), "lv.9 天空之城", null),
    LEVEL_EIGHT(8, 200L, 14700L, 1944400L, "奇妙之城",
            "https://res-cdn.nuan.chat/officialCombine/cabin/cabin_icon_eight.png",
            "https://res-cdn.nuan.chat/officialCombine/cabin/cabin_icon_gray_eight.png",
            "https://res-cdn.nuan.chat/officialCombine/cabin/cabin_icon_vague_eight.png",
            "https://res-cdn.nuan.chat/depth/cabin/cabin_rank_icon_eight.png",
            Arrays.asList("LCQL_GIFT", "ZGWC_GIFT"), "lv.8 奇妙之城", CabinLevelEnum.LEVEL_NINE),
    LEVEL_SEVEN(7, 160L, 9600L, 944400L, "梦幻城堡",
            "https://res-cdn.nuan.chat/officialCombine/cabin/cabin_icon_seven.png",
            "https://res-cdn.nuan.chat/officialCombine/cabin/cabin_icon_gray_seven.png",
            "https://res-cdn.nuan.chat/officialCombine/cabin/cabin_icon_vague_seven.png",
            "https://res-cdn.nuan.chat/depth/cabin/cabin_rank_icon_seven.png",
            Arrays.asList("YYHZM_GIFT", "QSAY_GIFT"), "lv.7 梦幻城堡", CabinLevelEnum.LEVEL_EIGHT),
    LEVEL_SIX(6, 120L, 5950L, 444400L, "甜蜜庄园",
            "https://res-cdn.nuan.chat/officialCombine/cabin/cabin_icon_six.png",
            "https://res-cdn.nuan.chat/officialCombine/cabin/cabin_icon_gray_six.png",
            "https://res-cdn.nuan.chat/officialCombine/cabin/cabin_icon_vague_six.png",
            "https://res-cdn.nuan.chat/depth/cabin/cabin_rank_icon_six.png",
            Arrays.asList("XRLY_GIFT", "LMSY_GIFT"), "lv.6 甜蜜庄园", CabinLevelEnum.LEVEL_SEVEN),
    LEVEL_FIVE(5, 90L, 3500L, 144400L, "幸福海岛",
            "https://res-cdn.nuan.chat/officialCombine/cabin/cabin_icon_five.png",
            "https://res-cdn.nuan.chat/officialCombine/cabin/cabin_icon_gray_five.png",
            "https://res-cdn.nuan.chat/officialCombine/cabin/cabin_icon_vague_five.png",
            "https://res-cdn.nuan.chat/depth/cabin/cabin_rank_icon_five.png",
            Arrays.asList("HHZL_GIFT", "GBQQL_GIFT"), "lv.5 幸福海岛", CabinLevelEnum.LEVEL_SIX),
    LEVEL_FOUR(4, 60L, 1840L, 39400L, "奢华之家",
            "https://res-cdn.nuan.chat/officialCombine/cabin/cabin_icon_four.png",
            "https://res-cdn.nuan.chat/officialCombine/cabin/cabin_icon_gray_four.png",
            "https://res-cdn.nuan.chat/officialCombine/cabin/cabin_icon_vague_four.png",
            "https://res-cdn.nuan.chat/depth/cabin/cabin_rank_icon_four.png",
            Arrays.asList("SLFX_GIFT", "XDYK_GIFT"), "lv.4 奢华之家", CabinLevelEnum.LEVEL_FIVE),
    LEVEL_THREE(3, 30L, 840L, 14400L, "华丽别墅",
            "https://res-cdn.nuan.chat/officialCombine/cabin/cabin_icon_three.png",
            "https://res-cdn.nuan.chat/officialCombine/cabin/cabin_icon_gray_three.png",
            "https://res-cdn.nuan.chat/officialCombine/cabin/cabin_icon_vague_three.png",
            "https://res-cdn.nuan.chat/depth/cabin/cabin_rank_icon_three.png",
            Arrays.asList("MJZY_GIFT", "AQSJQ_GIFT"), "lv.3 华丽别墅", CabinLevelEnum.LEVEL_FOUR),
    LEVEL_TWO(2, 20L, 390L, 4950L, "浪漫之家",
            "https://res-cdn.nuan.chat/officialCombine/cabin/cabin_icon_two.png",
            "https://res-cdn.nuan.chat/officialCombine/cabin/cabin_icon_gray_two.png",
            "https://res-cdn.nuan.chat/officialCombine/cabin/cabin_icon_vague_two.png",
            "https://res-cdn.nuan.chat/depth/cabin/cabin_rank_icon_two.png",
            Collections.singletonList("TSZY_GIFT"), "lv.2 浪漫之家", CabinLevelEnum.LEVEL_THREE),
    LEVEL_ONE(1, 10L, 120L, 1800L, "温暖小家",
            "https://res-cdn.nuan.chat/officialCombine/cabin/cabin_icon_one.png",
            "https://res-cdn.nuan.chat/officialCombine/cabin/cabin_icon_one.png",
            "https://res-cdn.nuan.chat/officialCombine/cabin/cabin_icon_vague_one.png",
            "https://res-cdn.nuan.chat/depth/cabin/cabin_rank_icon_one.png",
            null, "lv.1 温暖小家", LEVEL_TWO),
    ;

    private static final String LH_COIN_PRIZE_DESC = "每天收益";

    private static final Map<Integer, CabinLevelEnum> INIT_MAP = new HashMap<>();
    private static final Map<String, String> GIFT_MAP = new HashMap<>();
    private static final Map<String, Long> GIFT_PRICE_MAP = new HashMap<>();
    private static final Map<String, String> GIFT_ICON_MAP = new HashMap<>();

    static {
        for (CabinLevelEnum level : values()) {
            INIT_MAP.put(level.getLevel(), level);
        }
        GIFT_MAP.put("TSZY_GIFT", "天使之翼");
        GIFT_MAP.put("MJZY_GIFT", "梦境之月");
        GIFT_MAP.put("AQSJQ_GIFT", "爱情水晶球");
        GIFT_MAP.put("SLFX_GIFT", "散落繁星");
        GIFT_MAP.put("XDYK_GIFT", "心动一刻");
        GIFT_MAP.put("HHZL_GIFT", "花海之恋");
        GIFT_MAP.put("GBQQL_GIFT", "告白气球恋");
        GIFT_MAP.put("XRLY_GIFT", "夏日恋语");
        GIFT_MAP.put("LMSY_GIFT", "浪漫誓约");
        GIFT_MAP.put("YYHZM_GIFT", "以永恒之名");
        GIFT_MAP.put("QSAY_GIFT", "秋时爱语");
        GIFT_MAP.put("LCQL_GIFT", "轮船情侣");
        GIFT_MAP.put("ZGWC_GIFT", "烛光晚餐");
        GIFT_MAP.put("QCHL_GIFT", "倾城婚礼");
        GIFT_MAP.put("QYYH_GIFT", "情缘约会");
        GIFT_MAP.put("MDHY_GIFT", "梦蝶花影");

        GIFT_PRICE_MAP.put("TSZY_GIFT", 999L);
        GIFT_PRICE_MAP.put("MJZY_GIFT", 888L);
        GIFT_PRICE_MAP.put("AQSJQ_GIFT", 666L);
        GIFT_PRICE_MAP.put("SLFX_GIFT", 1314L);
        GIFT_PRICE_MAP.put("XDYK_GIFT", 2888L);
        GIFT_PRICE_MAP.put("HHZL_GIFT", 5200L);
        GIFT_PRICE_MAP.put("GBQQL_GIFT", 5200L);
        GIFT_PRICE_MAP.put("XRLY_GIFT", 19999L);
        GIFT_PRICE_MAP.put("LMSY_GIFT", 19999L);
        GIFT_PRICE_MAP.put("YYHZM_GIFT", 52000L);
        GIFT_PRICE_MAP.put("QSAY_GIFT", 52000L);
        GIFT_PRICE_MAP.put("LCQL_GIFT", 88888L);
        GIFT_PRICE_MAP.put("ZGWC_GIFT", 60000L);
        GIFT_PRICE_MAP.put("QCHL_GIFT", 88888L);
        GIFT_PRICE_MAP.put("QYYH_GIFT", 88888L);
        GIFT_PRICE_MAP.put("MDHY_GIFT", 88888L);

        GIFT_ICON_MAP.put("TSZY_GIFT", "https://res-cdn.nuan.chat/gift-image/2024-09/1725876323573836.png");
        GIFT_ICON_MAP.put("MJZY_GIFT", "https://res-cdn.nuan.chat/gift-image/2024-09/1725884116228277.png");
        GIFT_ICON_MAP.put("AQSJQ_GIFT", "https://res-cdn.nuan.chat/gift-image/2024-09/1725876143820973.png");
        GIFT_ICON_MAP.put("SLFX_GIFT", "https://res-cdn.nuan.chat/gift-image/2024-09/1725875139294473.png");
        GIFT_ICON_MAP.put("XDYK_GIFT", "https://res-cdn.nuan.chat/gift-image/2024-09/1725876102784546.png");
        GIFT_ICON_MAP.put("HHZL_GIFT", "https://res-cdn.nuan.chat/gift-image/2024-09/1725875943453483.png");
        GIFT_ICON_MAP.put("GBQQL_GIFT", "https://res-cdn.nuan.chat/gift-image/2024-09/1725875882888121.png");
        GIFT_ICON_MAP.put("XRLY_GIFT", "https://res-cdn.nuan.chat/gift-image/2024-09/1725884921367616.png");
        GIFT_ICON_MAP.put("LMSY_GIFT", "https://res-cdn.nuan.chat/gift-image/2024-09/1725875758508059.png");
        GIFT_ICON_MAP.put("YYHZM_GIFT", "https://res-cdn.nuan.chat/gift-image/2024-09/1725875174180103.png");
        GIFT_ICON_MAP.put("QSAY_GIFT", "https://res-cdn.nuan.chat/gift-image/2024-09/1725884774796513.png");
        GIFT_ICON_MAP.put("LCQL_GIFT", "https://res-cdn.nuan.chat/gift-image/2024-09/1725875263833530.png");
        GIFT_ICON_MAP.put("ZGWC_GIFT", "https://res-cdn.nuan.chat/gift-image/2024-09/1725875316034687.png");
        GIFT_ICON_MAP.put("QCHL_GIFT", "https://res-cdn.nuan.chat/gift-image/2024-09/1725876206341862.png");
        GIFT_ICON_MAP.put("QYYH_GIFT", "https://res-cdn.nuan.chat/gift-image/2024-09/1725875974294068.png");
        GIFT_ICON_MAP.put("MDHY_GIFT", "https://res-cdn.nuan.chat/gift-image/2024-09/1725884155973589.png");
    }

    /** 等级 */
    private Integer level;
    /** 对应产出恋屋币 */
    private Long lhCoin;
    /** 升级所需甜蜜值 */
    private Long sweetValue;
    /** 升级所需靓屋值 */
    private Long beautifulValue;
    /** 小屋名 */
    private String name;
    /** 点亮后图 */
    private String lightImage;
    /** 未点亮图 */
    private String notLightImage;
    /** 模糊图 */
    private String vagueImage;
    /** 图标 */
    private String icon;
    /** 奖励 */
    private List<String> prizeList;
    /** 描述 */
    private String desc;
    /** 下一等级 */
    private CabinLevelEnum nextLevel;

    public static CabinLevelEnum get(Integer level) {
        return INIT_MAP.get(level);
    }

    public static String getIcon(Integer level) {
        return INIT_MAP.get(level).getIcon();
    }

    public static String getGiftName(String code) {
        return GIFT_MAP.get(code);
    }

    public static Long getGiftPrice(String code) {
        return GIFT_PRICE_MAP.get(code);
    }

    public static List<CabinBaseVO> getList(Integer level) {
        List<CabinBaseVO> result = new ArrayList<>();
        for (CabinLevelEnum cabinLevel : values()) {
            CabinBaseVO curCabin = CabinBaseVO.builder()
                    .name(cabinLevel.getName())
                    .level(cabinLevel.getLevel())
                    .build();
            if (Objects.equals(cabinLevel.getLevel(), level)) {
                curCabin.setIcon(cabinLevel.getLightImage());
                curCabin.setPositioning(Boolean.TRUE);
            } else {
                curCabin.setIcon(level > cabinLevel.getLevel() ? cabinLevel.getLightImage() : cabinLevel.getVagueImage());
                curCabin.setPositioning(Boolean.FALSE);
            }
            List<CabinPrizeVO> prizeList = new ArrayList<>();
            prizeList.add(CabinPrizeVO.builder()
                            .icon(SweetCabinConstant.CABIN_COIN_ICON)
                            .name(LH_COIN_PRIZE_DESC)
                            .price(cabinLevel.getLhCoin() + "恋屋币")
                            .build());
            if (!CollectionUtils.isEmpty(cabinLevel.getPrizeList())) {
                for (String code : cabinLevel.getPrizeList()) {
                    prizeList.add(CabinPrizeVO.builder()
                            .icon(GIFT_ICON_MAP.get(code))
                            .name(GIFT_MAP.get(code))
                            .price(GIFT_PRICE_MAP.get(code) + " 金币")
                            .build());
                }
            }
            curCabin.setPrizeList(prizeList);
            result.add(curCabin);
        }
        return result.stream().sorted(Comparator.comparing(CabinBaseVO::getLevel)).collect(Collectors.toList());
    }

    public static CabinLevelEnum getPreLevel(Integer level) {
        return INIT_MAP.get(level - 1);
    }

    public static CabinPrizeListVO getLevelPrizeList(Integer level) {
        CabinPrizeListVO result = new CabinPrizeListVO();
        result.setCurLevelName(getPreLevel(level + 1).getName());
        CabinLevelEnum nextLevel = getPreLevel(level + 2);
        if (Objects.nonNull(nextLevel)) {
            result.setNextLevelName(getPreLevel(level + 2).getName());
        }
        List<CabinPrizeListDetailVO> levelPrizeList = new ArrayList<>();
        List<CabinLevelEnum> levelList = Arrays.asList(values());
        Collections.reverse(levelList);
        for (CabinLevelEnum cabinLevel : levelList) {
            if (cabinLevel.getLevel() < level) {
                continue;
            }
            CabinPrizeListDetailVO detail = new CabinPrizeListDetailVO();
            if (cabinLevel.getLevel().equals(level)) {
                detail.setStatus(0);
            } else if (cabinLevel.getLevel() > level + 1) {
                detail.setStatus(2);
            } else {
                detail.setStatus(1);
            }
            List<CabinPrizeVO> prizeList = new ArrayList<>();
            prizeList.add(CabinPrizeVO.builder()
                    .icon(SweetCabinConstant.CABIN_COIN_ICON)
                    .name(LH_COIN_PRIZE_DESC)
                    .price(cabinLevel.getLhCoin() + "恋屋币")
                    .build());
            if (!CollectionUtils.isEmpty(cabinLevel.getPrizeList())) {
                for (String code : cabinLevel.getPrizeList()) {
                    prizeList.add(CabinPrizeVO.builder()
                            .icon(GIFT_ICON_MAP.get(code))
                            .name(GIFT_MAP.get(code))
                            .price(GIFT_PRICE_MAP.get(code) + " 金币")
                            .build());
                }
            }
            detail.setPrizeList(prizeList);
            levelPrizeList.add(detail);
            if (levelPrizeList.size() >= 3) {
                break;
            }
        }
        result.setLevelPrizeList(levelPrizeList);
        return result;
    }

}
