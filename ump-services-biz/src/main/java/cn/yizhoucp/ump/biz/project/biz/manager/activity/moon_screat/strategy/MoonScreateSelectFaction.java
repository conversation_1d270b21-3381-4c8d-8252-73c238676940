package cn.yizhoucp.ump.biz.project.biz.manager.activity.moon_screat.strategy;

import cn.hutool.core.util.ObjectUtil;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.vo.roomservices.RoomVO;
import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.moon_screat.MoonScreatRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.moon_screat.common.MoonScreatEnums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.moon_screat.common.MoonScreatRedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import cn.yizhoucp.ump.biz.project.biz.remoteService.FeignRoomService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

@Service
@Slf4j
public  class MoonScreateSelectFaction implements ExecutableStrategy {
    @Resource
    private MoonScreatRedisManager moonScreatRedisManager;
    @Resource
    private FeignRoomService feignRoomService;
    @Resource
    private MoonScreatRankManager moonScreatRankManager;

    @Override
    public Object execute(ButtonEventParam buttonEventParam) {
        Long uid=buttonEventParam.getBaseParam().getUid();
        String factionId=buttonEventParam.getBizKey();
        moonScreatRedisManager.setFaction(uid,factionId);
        //设置数据
        Result<RoomVO> roomVOResult = feignRoomService.roomMessage(null, "ROOM_SOURCE_PERSONAL");
        if (StringUtils.equalsIgnoreCase(ErrorCode.SUCCESS.getCode(), roomVOResult.getCode()) || Objects.isNull(roomVOResult.getData())) {
            log.info("[feignRoomService - roomMessage] to null uid {}", uid);
            RoomVO roomVO = roomVOResult.getData();
            if (ObjectUtil.isNotNull(roomVO.getRoomId())) {
                //添加房间卡片
                Long roomCount;
                if(MoonScreatEnums.FactionsEnum.VILLAGER_CAMP.name().equals(factionId)){
                    roomCount=moonScreatRedisManager.getRoomVillagerCount(roomVO.getRoomId());
                }else{
                    roomCount=moonScreatRedisManager.getRoomWerewolCount(roomVO.getRoomId());
                }
                //排行榜值
                moonScreatRankManager.incrRankValue(roomVO.getRoomId(), roomCount, moonScreatRedisManager.getRoomRankKey(factionId));
            }
        }
        Long userCount;
        if(MoonScreatEnums.FactionsEnum.VILLAGER_CAMP.name().equals(factionId)){
            userCount=moonScreatRedisManager.getUserVillagerCount(uid);
        }else{
            userCount=moonScreatRedisManager.getUserWerewolfCount(uid);
        }
        moonScreatRedisManager.incrementUserContribution(uid,factionId,userCount);
        //设置 pk 值以及个人排行值
        moonScreatRedisManager.incrementPkScore(factionId,userCount);
        moonScreatRankManager.incrRankValue(uid,userCount,moonScreatRedisManager.getUserRankKey(factionId));
        return Boolean.TRUE;
    }
}
