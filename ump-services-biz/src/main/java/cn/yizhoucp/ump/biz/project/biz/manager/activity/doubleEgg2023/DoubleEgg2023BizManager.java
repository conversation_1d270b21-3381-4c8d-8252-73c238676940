package cn.yizhoucp.ump.biz.project.biz.manager.activity.doubleEgg2023;

import cn.yizhoucp.ms.core.base.enums.GiftWay;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.enums.ServicesNameEnum;
import cn.yizhoucp.ms.core.base.util.AppUtil;
import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.starter.cassandra.base.kafka.YzKafkaProducerManager;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.DoubleEgg2023Constant;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw.inner.PrizeSubTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw.inner.PrizeTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.biz.manager.userActivity.ActivityManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.FeignUserService;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ActivityDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dal.service.ScenePrizeService;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.DoubleEgg2023Constant.ACTIVITY_CODE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.DoubleEgg2023Constant.BOARD;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.DoubleEgg2023Constant.MISSION_PROGRESS;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.DoubleEgg2023Constant.SCENE_CP_VALUE_KEY;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.DoubleEgg2023Constant.SCENE_KEY;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.DoubleEgg2023Constant.TOTAL_CP_VALUE_KEY;

@Service
@Slf4j
public class DoubleEgg2023BizManager {

    @Resource
    private DoubleEgg2023RankManager doubleEgg2023RankManager;
    @Resource
    private ActivityManager activityManager;
    @Resource
    private RedisManager redisManager;
    @Resource
    private ScenePrizeService scenePrizeService;
    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private NotifyComponent notifyComponent;
    @Resource
    private FeignUserService feignUserService;
    @Resource
    private YzKafkaProducerManager yzKafkaProducerManager;

    @ActivityCheck(activityCode = ACTIVITY_CODE, isThrowException = false)
    public void sendGiftHandle(BaseParam param, List<CoinGiftGivedModel> coinGiftGivedModelList) {
        for (CoinGiftGivedModel coinGiftGivedModel : coinGiftGivedModelList) {
            if ("SDW_GIFT".equals(coinGiftGivedModel.getGiftKey()) || "XYLSN_GIFT".equals(coinGiftGivedModel.getGiftKey()) || "XGQS_GIFT".equals(coinGiftGivedModel.getGiftKey())
                    || "SDMH_GIFT".equals(coinGiftGivedModel.getLotteryGiftKey()) || "YDMH_GIFT".equals(coinGiftGivedModel.getLotteryGiftKey())) {
                doubleEgg2023RankManager.incrRankValue(coinGiftGivedModel.getFromUid(), coinGiftGivedModel.getCoin(), BOARD);
            }
            if (GiftWay.NORMAL.getCode().equals(coinGiftGivedModel.getGiftWay())) {
                // 获取最高用
                long delta = 10 * coinGiftGivedModel.getCoin();
                redisManager.zIncrby(String.format(TOTAL_CP_VALUE_KEY, coinGiftGivedModel.getFromUid()), coinGiftGivedModel.getToUid(), (double) delta, DateUtil.ONE_MONTH_SECOND);
                redisManager.zIncrby(String.format(TOTAL_CP_VALUE_KEY, coinGiftGivedModel.getToUid()), coinGiftGivedModel.getFromUid(), (double) delta, DateUtil.ONE_MONTH_SECOND);
                // 获取用户在哪个场景
                Integer sceneIndex = Optional.ofNullable((Integer) redisManager.hget(SCENE_KEY, AppUtil.splicUserId(coinGiftGivedModel.getFromUid(), coinGiftGivedModel.getToUid()))).orElse(1);
                // 获取限制场景
                int todaySceneIndex = getTodaySceneIndex(param);
                int sceneCpValue = Optional.ofNullable(redisManager.getInteger(String.format(SCENE_CP_VALUE_KEY, sceneIndex, AppUtil.splicUserId(coinGiftGivedModel.getFromUid(), coinGiftGivedModel.getToUid())))).orElse(0);
                if (sceneIndex <= todaySceneIndex) {
                    UserVO user = feignUserService.getBasic(coinGiftGivedModel.getFromUid(), 1L).successData();
                    UserVO toUser = feignUserService.getBasic(coinGiftGivedModel.getToUid(), 1L).successData();
                    for (DoubleEgg2023Constant.Scene scene : DoubleEgg2023Constant.Scene.values()) {
                        if (scene.getIndex() < sceneIndex) {
                            continue;
                        }
                        if (scene.getIndex() > todaySceneIndex) {
                            redisManager.hset(SCENE_KEY, AppUtil.splicUserId(coinGiftGivedModel.getFromUid(), coinGiftGivedModel.getToUid()), scene.getIndex(), DateUtil.ONE_MONTH_SECOND);
                            break;
                        }

                        if (sceneCpValue < scene.getCpValueLevel1() && sceneCpValue + delta >= scene.getCpValueLevel1()) {
                            // 发奖励
                            List<ScenePrizeDO> scenePrizeDOList = scenePrizeService.getListBySceneCode(ACTIVITY_CODE, "scene" + scene.getIndex() + "_level1");
                            List<ScenePrizeDO> scenePrizeDOs = scenePrizeDOList.stream().filter(scenePrizeDO -> scenePrizeDO.getSex().equals(user.getSex().getCode())).collect(Collectors.toList());
                            sendPrizeManager.sendPrize(
                                    BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(coinGiftGivedModel.getFromUid()).build(),
                                    scenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, coinGiftGivedModel.getFromUid())).collect(Collectors.toList())
                            );
                            for (ScenePrizeDO scenePrizeDO : scenePrizeDOs) {
                                notifyComponent.npcNotify(
                                        ServicesAppIdEnum.lanling.getUnionId(),
                                        coinGiftGivedModel.getFromUid(),
                                        String.format("恭喜您和%s在“双旦送暖迎新岁”活动“%s”场景中CP值成功解锁第%s关，您可获得一个%s金币%s“%s”，奖励已发放至您的账号，请注意查收~", toUser.getName(), scene.getName(), 1, scenePrizeDO.getPrizeValueGold(), this.getPrizeTypeDesc(scenePrizeDO), scenePrizeDO.getPrizeDesc())
                                );
                                this.allActivityReceiveAwardTrack(String.format("scene0%s_breakthrough_success", scene.getIndex()), AppUtil.splicUserId(coinGiftGivedModel.getFromUid(), coinGiftGivedModel.getToUid()), scenePrizeDO.getPrizeValue(), scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeNum(), coinGiftGivedModel.getFromUid());
                            }
                            scenePrizeDOs = scenePrizeDOList.stream().filter(scenePrizeDO -> scenePrizeDO.getSex().equals(toUser.getSex().getCode())).collect(Collectors.toList());
                            sendPrizeManager.sendPrize(
                                    BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(coinGiftGivedModel.getToUid()).build(),
                                    scenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, coinGiftGivedModel.getToUid())).collect(Collectors.toList())
                            );
                            for (ScenePrizeDO scenePrizeDO : scenePrizeDOs) {
                                notifyComponent.npcNotify(
                                        ServicesAppIdEnum.lanling.getUnionId(),
                                        coinGiftGivedModel.getToUid(),
                                        String.format("恭喜您和%s在“双旦送暖迎新岁”活动“%s”场景中CP值成功解锁第%s关，您可获得一个%s金币%s“%s”，奖励已发放至您的账号，请注意查收~", toUser.getName(), scene.getName(), 1, scenePrizeDO.getPrizeValueGold(), this.getPrizeTypeDesc(scenePrizeDO), scenePrizeDO.getPrizeDesc())
                                );
                                this.allActivityReceiveAwardTrack(String.format("scene0%s_breakthrough_success", scene.getIndex()), AppUtil.splicUserId(coinGiftGivedModel.getFromUid(), coinGiftGivedModel.getToUid()), scenePrizeDO.getPrizeValue(), scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeNum(), coinGiftGivedModel.getToUid());
                            }
                            // 任务
                            redisManager.hincr(String.format(MISSION_PROGRESS, DateUtil.format(new Date(), DateUtil.YMD_WITHOUT_LINE), param.getUid()), String.valueOf(scene.getIndex() <= 4 ? 6 : 7), 1, DateUtil.ONE_MONTH_SECOND);
                            allActivityTaskFinishTrack(scene.getIndex() <= 4 ? "christmas_love_song_success" : "lucky_new_year_success", param.getUid());
                        }
                        if (sceneCpValue < scene.getCpValueLevel2() && sceneCpValue + delta >= scene.getCpValueLevel2()) {
                            List<ScenePrizeDO> scenePrizeDOList = scenePrizeService.getListBySceneCode(ACTIVITY_CODE, "scene" + scene.getIndex() + "_level2");
                            List<ScenePrizeDO> scenePrizeDOs = scenePrizeDOList.stream().filter(scenePrizeDO -> scenePrizeDO.getSex().equals(user.getSex().getCode())).collect(Collectors.toList());
                            sendPrizeManager.sendPrize(
                                    BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(coinGiftGivedModel.getFromUid()).build(),
                                    scenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, coinGiftGivedModel.getFromUid())).collect(Collectors.toList())
                            );
                            for (ScenePrizeDO scenePrizeDO : scenePrizeDOs) {
                                notifyComponent.npcNotify(
                                        ServicesAppIdEnum.lanling.getUnionId(),
                                        coinGiftGivedModel.getFromUid(),
                                        String.format("恭喜您和%s在“双旦送暖迎新岁”活动“%s”场景中CP值成功解锁第%s关，您可获得一个%s金币%s“%s”，奖励已发放至您的账号，请注意查收~", toUser.getName(), scene.getName(), 2, scenePrizeDO.getPrizeValueGold(), this.getPrizeTypeDesc(scenePrizeDO), scenePrizeDO.getPrizeDesc())
                                );
                                this.allActivityReceiveAwardTrack(String.format("scene0%s_breakthrough_success", scene.getIndex()), AppUtil.splicUserId(coinGiftGivedModel.getFromUid(), coinGiftGivedModel.getToUid()), scenePrizeDO.getPrizeValue(), scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeNum(), coinGiftGivedModel.getFromUid());
                            }
                            scenePrizeDOs = scenePrizeDOList.stream().filter(scenePrizeDO -> scenePrizeDO.getSex().equals(toUser.getSex().getCode())).collect(Collectors.toList());
                            sendPrizeManager.sendPrize(
                                    BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(coinGiftGivedModel.getToUid()).build(),
                                    scenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, coinGiftGivedModel.getToUid())).collect(Collectors.toList())
                            );
                            for (ScenePrizeDO scenePrizeDO : scenePrizeDOs) {
                                notifyComponent.npcNotify(
                                        ServicesAppIdEnum.lanling.getUnionId(),
                                        coinGiftGivedModel.getToUid(),
                                        String.format("恭喜您和%s在“双旦送暖迎新岁”活动“%s”场景中CP值成功解锁第%s关，您可获得一个%s金币%s“%s”，奖励已发放至您的账号，请注意查收~", toUser.getName(), scene.getName(), 2, scenePrizeDO.getPrizeValueGold(), this.getPrizeTypeDesc(scenePrizeDO), scenePrizeDO.getPrizeDesc())
                                );
                                this.allActivityReceiveAwardTrack(String.format("scene0%s_breakthrough_success", scene.getIndex()), AppUtil.splicUserId(coinGiftGivedModel.getFromUid(), coinGiftGivedModel.getToUid()), scenePrizeDO.getPrizeValue(), scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeNum(), coinGiftGivedModel.getToUid());
                            }
                            redisManager.hincr(String.format(MISSION_PROGRESS, DateUtil.format(new Date(), DateUtil.YMD_WITHOUT_LINE), param.getUid()), String.valueOf(scene.getIndex() <= 4 ? 6 : 7), 1, DateUtil.ONE_MONTH_SECOND);
                            allActivityTaskFinishTrack(scene.getIndex() <= 4 ? "christmas_love_song_success" : "lucky_new_year_success", param.getUid());
                        }
                        if (sceneCpValue < scene.getCpValueLevel3() && sceneCpValue + delta >= scene.getCpValueLevel3()) {
                            List<ScenePrizeDO> scenePrizeDOList = scenePrizeService.getListBySceneCode(ACTIVITY_CODE, "scene" + scene.getIndex() + "_level3");
                            List<ScenePrizeDO> scenePrizeDOs = scenePrizeDOList.stream().filter(scenePrizeDO -> scenePrizeDO.getSex().equals(user.getSex().getCode())).collect(Collectors.toList());
                            sendPrizeManager.sendPrize(
                                    BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(coinGiftGivedModel.getFromUid()).build(),
                                    scenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, coinGiftGivedModel.getFromUid())).collect(Collectors.toList())
                            );
                            for (ScenePrizeDO scenePrizeDO : scenePrizeDOs) {
                                notifyComponent.npcNotify(
                                        ServicesAppIdEnum.lanling.getUnionId(),
                                        coinGiftGivedModel.getFromUid(),
                                        String.format("恭喜您和%s在“双旦送暖迎新岁”活动“%s”场景中CP值成功解锁第%s关，您可获得一个%s金币%s“%s”，奖励已发放至您的账号，请注意查收~", toUser.getName(), scene.getName(), 3, scenePrizeDO.getPrizeValueGold(), this.getPrizeTypeDesc(scenePrizeDO), scenePrizeDO.getPrizeDesc())
                                );
                                this.allActivityReceiveAwardTrack(String.format("scene0%s_breakthrough_success", scene.getIndex()), AppUtil.splicUserId(coinGiftGivedModel.getFromUid(), coinGiftGivedModel.getToUid()), scenePrizeDO.getPrizeValue(), scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeNum(), coinGiftGivedModel.getFromUid());
                            }
                            scenePrizeDOs = scenePrizeDOList.stream().filter(scenePrizeDO -> scenePrizeDO.getSex().equals(toUser.getSex().getCode())).collect(Collectors.toList());
                            sendPrizeManager.sendPrize(
                                    BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(coinGiftGivedModel.getToUid()).build(),
                                    scenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, coinGiftGivedModel.getToUid())).collect(Collectors.toList())
                            );
                            for (ScenePrizeDO scenePrizeDO : scenePrizeDOs) {
                                notifyComponent.npcNotify(
                                        ServicesAppIdEnum.lanling.getUnionId(),
                                        coinGiftGivedModel.getToUid(),
                                        String.format("恭喜您和%s在“双旦送暖迎新岁”活动“%s”场景中CP值成功解锁第%s关，您可获得一个%s金币%s“%s”，奖励已发放至您的账号，请注意查收~", toUser.getName(), scene.getName(), 3, scenePrizeDO.getPrizeValueGold(), this.getPrizeTypeDesc(scenePrizeDO), scenePrizeDO.getPrizeDesc())
                                );
                                this.allActivityReceiveAwardTrack(String.format("scene0%s_breakthrough_success", scene.getIndex()), AppUtil.splicUserId(coinGiftGivedModel.getFromUid(), coinGiftGivedModel.getToUid()), scenePrizeDO.getPrizeValue(), scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeNum(), coinGiftGivedModel.getToUid());
                            }
                            redisManager.hincr(String.format(MISSION_PROGRESS, DateUtil.format(new Date(), DateUtil.YMD_WITHOUT_LINE), param.getUid()), String.valueOf(scene.getIndex() <= 4 ? 6 : 7), 1, DateUtil.ONE_MONTH_SECOND);
                            allActivityTaskFinishTrack(scene.getIndex() <= 4 ? "christmas_love_song_success" : "lucky_new_year_success", param.getUid());
                        }

                        redisManager.incrLong(String.format(SCENE_CP_VALUE_KEY, scene.getIndex(), AppUtil.splicUserId(coinGiftGivedModel.getFromUid(), coinGiftGivedModel.getToUid())), delta, DateUtil.ONE_MONTH_SECOND);

                        if (sceneCpValue + delta < scene.getCpValueLevel3()) {
                            redisManager.hset(SCENE_KEY, AppUtil.splicUserId(coinGiftGivedModel.getFromUid(), coinGiftGivedModel.getToUid()), scene.getIndex(), DateUtil.ONE_MONTH_SECOND);
                            break;
                        } else {
                            delta = delta + sceneCpValue - scene.getCpValueLevel3();
                            sceneCpValue = 0;
                        }
                    }
                } else {
                    redisManager.incrLong(String.format(SCENE_CP_VALUE_KEY, todaySceneIndex, AppUtil.splicUserId(coinGiftGivedModel.getFromUid(), coinGiftGivedModel.getToUid())), delta, DateUtil.ONE_MONTH_SECOND);
                }
            }

            String key = String.format(MISSION_PROGRESS, DateUtil.format(new Date(), DateUtil.YMD_WITHOUT_LINE), param.getUid());
            if ("SDMH_GIFT".equals(coinGiftGivedModel.getLotteryGiftKey())) {
                int missionProgress = Optional.ofNullable((Integer) redisManager.hget(key, 1 + "")).orElse(0);
                for (int i = missionProgress + 2; i <= missionProgress + coinGiftGivedModel.getProductCount() && i <= 2 * 3; i += 2) {
                    allActivityTaskFinishTrack("christmas_box_send", param.getUid());
                }
                redisManager.hincr(key, 1 + "", coinGiftGivedModel.getProductCount(), DateUtil.ONE_MONTH_SECOND);
            } else if ("YDMH_GIFT".equals(coinGiftGivedModel.getLotteryGiftKey())) {
                int missionProgress = Optional.ofNullable((Integer) redisManager.hget(key, 2 + "")).orElse(0);
                for (int i = missionProgress + 1; i <= missionProgress + coinGiftGivedModel.getProductCount() && i <= 1 * 3; i += 1) {
                    allActivityTaskFinishTrack("christmas_box_send", param.getUid());
                }
                redisManager.hincr(key, 2 + "", coinGiftGivedModel.getProductCount(), DateUtil.ONE_MONTH_SECOND);
                allActivityTaskFinishTrack("new_years_day_box_send", param.getUid());
            } else if ("SDW_GIFT".equals(coinGiftGivedModel.getGiftKey())) {
                int missionProgress = Optional.ofNullable((Integer) redisManager.hget(key, 3 + "")).orElse(0);
                for (int i = missionProgress + 100; i <= missionProgress + coinGiftGivedModel.getProductCount() && i <= 100 * 3; i += 100) {
                    allActivityTaskFinishTrack("christmas_box_send", param.getUid());
                }
                redisManager.hincr(key, 3 + "", coinGiftGivedModel.getProductCount(), DateUtil.ONE_MONTH_SECOND);
                allActivityTaskFinishTrack("hundred_stocking_send", param.getUid());
            } else if ("XYLSN_GIFT".equals(coinGiftGivedModel.getGiftKey())) {
                redisManager.hincr(key, 4 + "", coinGiftGivedModel.getProductCount(), DateUtil.ONE_MONTH_SECOND);
                allActivityTaskFinishTrack("starlit_night_girl_send", param.getUid());
            } else if ("XGQS_GIFT".equals(coinGiftGivedModel.getGiftKey())) {
                redisManager.hincr(key, 5 + "", coinGiftGivedModel.getProductCount(), DateUtil.ONE_MONTH_SECOND);
                allActivityTaskFinishTrack("snow_country_love_letter_send", param.getUid());
            }
        }
    }

    public Boolean unlockSceneSendPrize() {
        if (!Boolean.TRUE.equals(redisManager.setnx("ump:double_egg2023:unlock_scene_send_prize_idempotent", System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND))) {
            return Boolean.TRUE;
        }

        List<ScenePrizeDO> scenePrizeDOList = scenePrizeService.getListBySceneCode(ACTIVITY_CODE, "unlock_scene");
//        log.info("scenePrizeDOList {}", JSON.toJSONString(scenePrizeDOList));
        if (CollectionUtils.isEmpty(scenePrizeDOList)) {
            return Boolean.FALSE;
        }

        Map<Object, Object> hmget = redisManager.hmget(SCENE_KEY);
        log.info("hmget {}", hmget);
        if (hmget == null) {
            return Boolean.FALSE;
        }

        hmget.forEach((k, v) -> {
            String splicUserId = (String) k;
            List<Long> splicUserIdList = AppUtil.openSplicUserId(splicUserId);
            if (CollectionUtils.isEmpty(splicUserIdList)) {
                return;
            }

            Integer unlockSceneCount = (Integer) v;
            List<ScenePrizeDO> filteredScenePrizeDOList = scenePrizeDOList.stream().filter(scenePrizeDO -> scenePrizeDO.getNeedCount() <= unlockSceneCount).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filteredScenePrizeDOList)) {
                return;
            }

            sendPrizeManager.sendPrize(
                    BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(splicUserIdList.get(0)).build(),
                    filteredScenePrizeDOList.subList(0, 1).stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, splicUserIdList.get(0))).collect(Collectors.toList())
            );
            notifyComponent.npcNotify(
                    ServicesAppIdEnum.lanling.getUnionId(),
                    splicUserIdList.get(0),
                    String.format("恭喜您在“双旦送暖迎新岁”活动“跨年游记”中成功解锁 %s个 场景，您可获得一个%s金币的礼物%s，礼物已发放至您的背包，请注意查收~", filteredScenePrizeDOList.get(0).getNeedCount(), filteredScenePrizeDOList.get(0).getPrizeValueGold(), filteredScenePrizeDOList.get(0).getPrizeDesc())
            );
            sendPrizeManager.sendPrize(
                    BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(splicUserIdList.get(1)).build(),
                    filteredScenePrizeDOList.subList(0, 1).stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, splicUserIdList.get(1))).collect(Collectors.toList())
            );
            notifyComponent.npcNotify(
                    ServicesAppIdEnum.lanling.getUnionId(),
                    splicUserIdList.get(1),
                    String.format("恭喜您在“双旦送暖迎新岁”活动“跨年游记”中成功解锁 %s个 场景，您可获得一个%s金币的礼物%s，礼物已发放至您的背包，请注意查收~", filteredScenePrizeDOList.get(0).getNeedCount(), filteredScenePrizeDOList.get(0).getPrizeValueGold(), filteredScenePrizeDOList.get(0).getPrizeDesc())
            );
        });

        return Boolean.TRUE;
    }

    public int getTodaySceneIndex(BaseParam param) {
        ActivityDO activityInfo = activityManager.getActivityInfo(param, ACTIVITY_CODE);
        LocalDateTime startTime = activityInfo.getStartTime();
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startTimeDay = LocalDateTime.of(startTime.getYear(), startTime.getMonthValue(), startTime.getDayOfMonth(), 0, 0);
        LocalDateTime nowDay = LocalDateTime.of(now.getYear(), now.getMonthValue(), now.getDayOfMonth(), 0, 0);
        return Math.toIntExact(ChronoUnit.DAYS.between(startTimeDay, nowDay) + 1);
    }

    private String getPrizeTypeDesc(ScenePrizeDO scenePrizeDO) {
        if (scenePrizeDO == null) {
            return "奖励";
        }

        String prizeSubType = scenePrizeDO.getPrizeSubType();
        if (!StringUtils.isBlank(prizeSubType)) {
            PrizeSubTypeEnum prizeSubTypeEnum = PrizeSubTypeEnum.getInstance(prizeSubType);
            if (prizeSubTypeEnum != null) {
                return prizeSubTypeEnum.getDesc();
            }
        }

        String prizeType = scenePrizeDO.getPrizeType();
        if (!StringUtils.isBlank(prizeType)) {
            PrizeTypeEnum prizeTypeEnum = PrizeTypeEnum.getInstance(prizeType);
            if (prizeTypeEnum != null) {
                return prizeTypeEnum.getDesc();
            }
        }

        return "奖励";
    }

    public void allActivityReceiveAwardTrack(String taskType, String splicUserId, String awardKey, Long awardAmount, Integer awardCount, Long uid) {
        Map<String, Object> params = Maps.newHashMapWithExpectedSize(7);
        params.put("activity_type", "christmas_and_new_years_day_activity");
        params.put("activity_attribute", "operation_activity");
        params.put("task_type", taskType);
        params.put("splic_user_id", splicUserId);
        params.put("award_key", awardKey);
        params.put("award_amount", awardAmount);
        params.put("award_count", awardCount);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_task_finish", params, ServicesNameEnum.ump_services.getCode());
    }

    private void allActivityTaskFinishTrack(String taskType, Long uid) {
        Map<String, Object> params = Maps.newHashMapWithExpectedSize(3);
        params.put("activity_type", "christmas_and_new_years_day_activity");
        params.put("activity_attribute", "operation_activity");
        params.put("task_type", taskType);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_task_finish", params, ServicesNameEnum.ump_services.getCode());
    }

}
