package cn.yizhoucp.ump.biz.project.biz.util;

import cn.yizhoucp.ms.core.base.Constant;
import cn.yizhoucp.ms.core.base.enums.EnvType;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

@Slf4j
public class ActivityUrlUtil {

    /**
     * 获取 h5 页面基础链接
     *
     * @param env 环境
     * @return String
     */
    public static String getH5BaseUrl(String unionId, String env) {
        return getH5BaseUrl(unionId, env, Boolean.FALSE, null);
    }

    public static String getH5BaseUrl(String unionId, String env, Boolean dynamicEnv, String dynamicEnvName) {
        log.debug("getH5BaseUrlEnv :{} unionId :{} dynamicEnv : {} dynamicEnvName : {}", env, unionId, dynamicEnv, dynamicEnvName);
        if (StringUtils.isBlank(env)) {
            return null;
        }
        ServicesAppIdEnum servicesAppIdEnum = ServicesAppIdEnum.convertFromUnionId(unionId);
        if (Objects.isNull(servicesAppIdEnum)) {
            return null;
        }
        if (Objects.isNull(dynamicEnv)) {
            dynamicEnv = Boolean.FALSE;
        }
        switch (servicesAppIdEnum) {
            case lanling:
            case huayuan:
            case lianainiang:
            case yumo:
            case coudui:
            case couduis:
            case momogirl:
            case yanyuan:
            case aiyuliaotian:
            case laoxiang:
            case paiyuan:
                return getLanlingBaseUrl(env, dynamicEnv, dynamicEnvName);
            case chatie:
                return getChatieBaseUrl(env, dynamicEnv, dynamicEnvName);
            default:
                return getLanlingBaseUrl(env, dynamicEnv, dynamicEnvName);
        }
    }

    private static String getLanlingBaseUrl(String env, Boolean dynamicEnv, String dynamicEnvName) {
        log.debug("getH5BaseUrlEnv :{} unionId : dynamicEnv : {} dynamicEnvName : {}", env, dynamicEnv, dynamicEnvName);
        if (EnvType.PROD.getEnv().equals(env)) {
            return Constant.PROD_H5_BASE;
        } else if (EnvType.PRE.getEnv().equals(env)) {
            return Constant.PRE_H5_BASE;
        } else if (EnvType.TEST.getEnv().equals(env) && dynamicEnv) {
            return String.format("https://%s-nuanliao-vite.f-test.myrightone.com/", dynamicEnvName);
        } else if (EnvType.DEV.getEnv().equals(env) && dynamicEnv) {
            return String.format("https://%s-nuanliao-vite.f-dev.myrightone.com/", dynamicEnvName);
        } else {
            return Constant.PROD_H5_BASE;
        }
    }

    private static String getChatieBaseUrl(String env, Boolean dynamicEnv, String dynamicEnvName) {
        if (EnvType.CHATIE_PROD.getEnv().equals(env)) {
            return "https://vite-h5.chatie.love/";
        } else if (EnvType.CHATIE_PRE.getEnv().equals(env)) {
            return "https://vite-h5-pre.chatie.love/";
        } else if (EnvType.CHATIE_TEST.getEnv().equals(env) || EnvType.TEST.getEnv().equals(env)) {
            return String.format("https://%s-nuanliao-vite.f-test.myrightone.com/", dynamicEnvName);
        } else if (EnvType.CHATIE_DEV.getEnv().equals(env) || EnvType.DEV.getEnv().equals(env)) {
            return String.format("https://%s-nuanliao-vite.f-dev.myrightone.com/", dynamicEnvName);
        } else {
            return null;
        }
    }

}
