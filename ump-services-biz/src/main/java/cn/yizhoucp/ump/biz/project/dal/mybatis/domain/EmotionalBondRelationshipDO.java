package cn.yizhoucp.ump.biz.project.dal.mybatis.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;

/**
 * 情感纽带关系实体类
 */
@Data
@TableName("emotional_bond_relationships")
public class EmotionalBondRelationshipDO {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 主动方用户ID
     */
    private Long userId;

    /**
     * 被动方用户ID
     */
    private Long oppositeId;

    /**
     * 主动方选择的角色
     */
    private String userRole;

    /**
     * 被动方选择的角色
     */
    private String oppositeRole;

    /**
     * 关系特效标识（用于区分不同特效）
     */
    private String effectKey;

    /**
     * 是否激活状态 0-未激活 1-已激活
     */
    private Integer isActive;

    /**
     * 关系状态 0-未缔结 1-已缔结
     */
    private Integer status;

    /**
     * 关系到期时间
     */
    private Date expireTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}