package cn.yizhoucp.ump.biz.project.biz.manager.activity.dragon_Boat_Festival_2025.strategy;

import cn.yizhoucp.ump.biz.project.biz.manager.activity.dragon_Boat_Festival_2025.common.DragonBoatFestival2025Enums;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.StrategyManager;
import org.springframework.stereotype.Component;


/**
 * 2025端午节策略选择类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 17:21 2025/5/29
 */
@Component
public class DragonBoatFestival2025StrategyChoose extends StrategyManager<DragonBoatFestival2025Enums.ButtonEnum, ExecutableStrategy> {
}
