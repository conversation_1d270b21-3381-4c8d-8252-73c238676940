package cn.yizhoucp.ump.biz.project.biz.constant.activity;

import cn.yizhoucp.ump.api.vo.jimu.rank.RewardVO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;

public class MistyStarContinentConstant {

    public static final String XINGZHI_BOX = "xingzhi_box";
    public static final String XINGCHEN_BOX = "xingchen_box";
    public static final String XINGJU_BOX = "xingju_box";
    public static final String XINGMIAO_BOX = "xingmiao_box";
    /** poolCode:yyyyMMdd:userId */
    public static final String XBX_GIFT_OPEN = "ump:misty_star_continent:open:%s:%s:%s";

    /** yyyyMMdd:userId */
    public static final String ASTROLOGY_TIME = "ump:misty_star_continent:astrology_time:%s:%s";
    /** yyyyMMdd */
    public static final String BOARD = "ump:misty_star_continent:board:%s";

    public static final String ACTIVITY_CODE = "misty_star_continent";

    /** uid */
    public static final String REPLACED_PRIZE_ITEM = "ump:misty_star_continent:replaced_prize_item:%s";

    public static final Map<Long, List<RewardVO>> RANK_REWARD = Maps.newHashMap();
    static {
        RANK_REWARD.put(1L, Lists.newArrayList(
                RewardVO.builder()
                        .rewardName("浪漫银河")
                        .giftKey("Romantic_Galaxy_GIFT")
                        .type("gift")
                        .value(5200L)
                        .icon("https://design-platform-cdn.yizhoucp.cn/gift-image/2022-10/1666896032867676.png")
                        .times("1")
                        .build()));
        RANK_REWARD.put(2L, Lists.newArrayList(
                RewardVO.builder()
                        .rewardName("天鹅湖畔")
                        .giftKey("TEHP_GIFT")
                        .type("gift")
                        .value(3344L)
                        .icon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-09/1694749938479414.png")
                        .times("1")
                        .build()));
        RANK_REWARD.put(3L, Lists.newArrayList(
                RewardVO.builder()
                        .rewardName("甜蜜云海")
                        .giftKey("TMYH_GIFT")
                        .type("gift")
                        .value(1314L)
                        .icon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-05/1683534284054580.png")
                        .times("1")
                        .build()));
    }

}
