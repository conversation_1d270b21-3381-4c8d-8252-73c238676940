package cn.yizhoucp.ump.biz.project.biz.manager.activity.wisteria_love_1.common;

import cn.yizhoucp.ump.biz.project.biz.manager.activity.seasonalFestival.strategy.SeasonalFestivalDemoStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.wisteria_love_1.strategy.WisteriaLove1BuyStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.wisteria_love_1.strategy.WisteriaLoveRefreshSort;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.StrategyEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * 紫藤萝之恋 枚举类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 18:05 2025/4/18
 */
public class WisteriaLove1Enums {

    @AllArgsConstructor
    @Getter
    public enum ButtonEnum implements StrategyEnum {
        DEMO_BUTTON("demo_button", "seasonalFestivalDemoStrategy", SeasonalFestivalDemoStrategy.class),
        BUY("buy", "wisteriaLove1BuyStrategy", WisteriaLove1BuyStrategy.class),
        REFRESH_SHORT("refresh_the_store", "wisteriaLoveRefreshSort", WisteriaLoveRefreshSort.class),
        ;
        private final String strategyName;
        private final String beanName;
        private final Class<? extends ExecutableStrategy> strategyClass;
    }

}
