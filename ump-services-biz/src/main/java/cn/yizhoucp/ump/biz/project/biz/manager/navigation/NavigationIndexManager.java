package cn.yizhoucp.ump.biz.project.biz.manager.navigation;

import cn.yizhoucp.enums.navigation.SailTypeEnum;
import cn.yizhoucp.ms.core.vo.coinservices.UserAccountVO;
import cn.yizhoucp.ms.core.vo.landingservices.GiftProductModel;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.GiftBaseInfoDTO;
import cn.yizhoucp.ump.api.vo.navigation.IndexVO;
import cn.yizhoucp.ump.biz.project.biz.enums.navigation.BoatEnum;
import cn.yizhoucp.ump.biz.project.biz.enums.navigation.MileEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.RandomGameHiddenManager;
import cn.yizhoucp.ump.biz.project.biz.manager.luckyBag.AstrologyValueManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.userAccount.UserAccountRemoteService;
import cn.yizhoucp.ump.biz.project.dal.mp.dataobject.navigation.SailRecordDO;
import cn.yizhoucp.ump.biz.project.dal.mp.dataobject.navigation.UserBoatDO;
import cn.yizhoucp.user.client.UserFeignService;
import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class NavigationIndexManager {

    @Resource
    private UserAccountRemoteService userAccountRemoteService;

    @Resource
    private AstrologyValueManager astrologyValueManager;

    @Resource
    private NavigationPetrolManager petrolManager;

    @Resource
    private NavigationGiftManager giftManager;

    @Resource
    private NavigationUserBoatManager userBoatManager;

    @Resource
    private NavigationManager navigationManager;

    @Resource
    private RedisManager redisManager;

    @Resource
    private NavigationSailRecordManager sailRecordManager;

    @Resource
    private UserFeignService userFeignService;

    @Resource
    private RandomGameHiddenManager randomGameHiddenManager;

    @Resource
    private NavigationGodSixMileProbManager navigationGodSixMileProbManager;

    public IndexVO index(Long appId, Long uid) {
        UserAccountVO userAccount = userAccountRemoteService.getUserAccountByUid(appId, uid);
        Integer petrolRemaining = petrolManager.getPetrolRemaining(uid);
        List<String> noticeList = getNoticeList();
        UserBoatDO userBoat = userBoatManager.getEnableBoat(uid);
        String curBoat = null;
        Integer curMile = null;
        String curMileGiftKey = null;
        String nextMileGiftKey = null;
        Integer normalNeedPetrol = null;
        Integer godNeedPetrol = null;
        Integer navigationValue = Math.toIntExact(astrologyValueManager.getBalanceByUid(uid));

        if (userBoat != null) {
            curBoat = userBoat.getBoat();
            curMile = userBoat.getCurMile();
            if (!MileEnum.SEVEN.getMileValue().equals(curMile)) {
                nextMileGiftKey = giftManager.getCanExchangeGift(curBoat, curMile + 100);
                normalNeedPetrol = navigationManager.getNeedPetrol(uid, BoatEnum.valueOf(curBoat), MileEnum.ofByValue(curMile), Boolean.FALSE);
                godNeedPetrol = navigationManager.getNeedPetrol(uid, BoatEnum.valueOf(curBoat), MileEnum.ofByValue(curMile), Boolean.TRUE);
            }
            curMileGiftKey = giftManager.getCanExchangeGift(curBoat, curMile);
        } else {
            normalNeedPetrol = navigationManager.getNeedPetrol(uid, BoatEnum.A, MileEnum.ZERO, Boolean.FALSE);
            godNeedPetrol = navigationManager.getNeedPetrol(uid, BoatEnum.A, MileEnum.ZERO, Boolean.TRUE);
        }

        GiftBaseInfoDTO curMileGift = curMileGiftKey != null ? giftManager.getGiftBaseInfo(curMileGiftKey) : null;
        GiftBaseInfoDTO nextMileGift = nextMileGiftKey != null ? giftManager.getGiftBaseInfo(nextMileGiftKey) : null;

        String probToast = null;
        if (MileEnum.SIX.getMileValue().equals(curMile)) {
            probToast = navigationGodSixMileProbManager.getToast(uid);
        }
        return IndexVO.builder()
                .lastCoin(Optional.ofNullable(userAccount).map(UserAccountVO::getBalance).orElse(0L))
                .lastPetrol(petrolRemaining)
                .lastNavigationValue(navigationValue)
                .noticeList(noticeList)
                .curMileGift(curMileGift)
                .nextMileGift(nextMileGift)
                .curBoat(curBoat)
                .curMile(curMile)
                .normalNeedPetrol(normalNeedPetrol)
                .godNeedPetrol(godNeedPetrol)
                .free(normalNeedPetrol != null && normalNeedPetrol == 0)
                .showFirstPop(this.showFirstPop(uid))
                .showGiftList(curMileGift == null ? getShowGiftList() : null)
                .probToast(probToast)
                .build();
    }

    private Boolean showFirstPop(Long uid) {
        return redisManager.setnx(String.format(NavigationConstant.FIRST_SHOW_POP_KEY, uid), System.currentTimeMillis(), 30*RedisManager.ONE_DAY_SECONDS);
    }

    private List<String> getNoticeList() {
        String cache = redisManager.getString(NavigationConstant.NOTICE_CACHE);
        if (cache != null) {
            return JSONObject.parseArray(cache, String.class);
        }
        List<SailRecordDO> sailRecordList = sailRecordManager.getNewestNoticeSailRecord();
        List<String> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(sailRecordList)) {
            return result;
        }
        List<Long> uidList = sailRecordList.stream().map(SailRecordDO::getUid).distinct().collect(Collectors.toList());
        Map<Long, UserVO> uidMap = userFeignService.acquireUsersInBulkPost2(uidList).successData();
        for (SailRecordDO sailRecordDO : sailRecordList) {
            UserVO user = uidMap.get(sailRecordDO.getUid());
            if (user == null) {
                continue;
            }
            String name = user.getName();
            // 截取前4个字符
            String showName = name.substring(0, Math.min(name.length(), 4)) + "..";
            String notice = String.format(NavigationConstant.NOTICE, showName, SailTypeEnum.of(sailRecordDO.getSailType()).getDesc(), sailRecordDO.getMileChange());
            result.add(notice);
        }
        redisManager.set(NavigationConstant.NOTICE_CACHE, JSONObject.toJSONString(result), RedisManager.ONE_HOUR_SECONDS);
        return result;
    }


    private List<GiftBaseInfoDTO> getShowGiftList() {
        List<String> giftKeys = NavigationConstant.SHOW_GIFT_KEY_LIST;
        Map<String, GiftProductModel> giftModelByGiftKeyBatch = giftManager.getGiftModelByGiftKeyBatch(giftKeys);
        return giftKeys.stream().map(giftModelByGiftKeyBatch::get)
                .filter(Objects::nonNull)
                .map(GiftBaseInfoDTO::ofByModel)
                .collect(Collectors.toList());
    }
}
