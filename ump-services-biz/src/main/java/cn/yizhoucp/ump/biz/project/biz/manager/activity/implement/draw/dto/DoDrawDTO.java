package cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.function.Function;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DoDrawDTO {
    private String poolCode;

    private Integer drawTimes;

    private Long uid;

    private List<KeyWeight> originPool;
}
