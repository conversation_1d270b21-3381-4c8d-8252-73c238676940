package cn.yizhoucp.ump.biz.project.biz.manager.activity.newYear2022;

import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.enums.ActivityCheckListEnum;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.NewYear2022Constant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.commonActivity.ActivityStatusManager;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityTimeUtil;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityUrlUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.NewYear2022Constant.*;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.NewYear2022Constant.LOGIN_NOTIFY_LOG;

/**
 * 新年上上签业务处理
 *
 * @author: lianghu
 */
@Slf4j
@Service
public class NewYear2022BizManager {

    @Resource
    private ActivityStatusManager activityStatusManager;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private RedisManager redisManager;
    @Resource
    private SendPrizeManager sendPrizeManager;
    @Value("${spring.profiles.active}")
    private String env;
    @Resource
    private NotifyComponent notifyComponent;

    public Boolean sendGiftHandle(BaseParam param, List<CoinGiftGivedModel> modelList) {
        if (!activityStatusManager.activityIsEnable(param, getActivityCode())) {
            return Boolean.FALSE;
        }
        log.info("newYear2022 送礼 uid:{}", param.getUid());
        for (CoinGiftGivedModel item : modelList) {
            // 礼物为签类型
            if (NewYear2022Constant.TICKET.contain(item.getGiftKey())) {
                sendPrizeManager.sendGift(param.getAppId(), item.getGiftKey(), item.getToUid(), item.getProductCount(), 15, getActivityCode());
                log.info("newYear2022 赠送签 uid:{}, toUid:{}, key:{}, num:{}", item.getFromUid(), item.getToUid(), item.getGiftKey(), item.getProductCount());
            }
            // 活动礼物
            if ("YQKN_GIFT".equals(item.getGiftKey()) || "2023ZYQ_GIFT".equals(item.getGiftKey())) {
                // 累计排行榜
                Double richAfter = redisManager.zIncrby(RICH_RANK, item.getFromUid().toString(), Double.valueOf(item.getProductCount() * item.getCoin()), RedisManager.ONE_DAY_SECONDS * 30);
                Double charmAfter = redisManager.zIncrby(CHARM_RANK, item.getToUid().toString(), Double.valueOf(item.getProductCount() * item.getCoin()), RedisManager.ONE_DAY_SECONDS * 30);
                log.info("newYear2022 累计排行榜 key:{}, fuid:{}, richAfter:{}, tuid:{}, charmAfter:{}", item.getGiftKey(), item.getFromUid(), richAfter, item.getToUid(), charmAfter);
            }
        }
        return Boolean.TRUE;
    }

    public Boolean pointHandle(BaseParam param, Long value) {
        if (!activityStatusManager.activityIsEnable(param, getActivityCode())) {
            return Boolean.FALSE;
        }
        log.info("newYear2022 积分处理 uid:{}, value:{}", param.getUid(), value);
        RLock lock = redissonClient.getLock(String.format(POINT_HANDLE_LOCK, param.getUid()));
        lock.lock();
        try {
            // 累计积分
            Double after = redisManager.hincr(POINT_DURING_ACTIVITY, param.getUid().toString(), value, RedisManager.ONE_DAY_SECONDS * 30);
            // 获取已处理积分
            Double history = Optional.ofNullable((Integer) redisManager.hget(POINT_DONE_DURING_ACTIVITY, param.getUid().toString())).orElse(0).doubleValue();
            // 计算待处理积分
            Double diff = after - history;
            log.info("newYear2022 积分处理 after:{}, history:{}, diff:{}", after, history, diff);
            // 基金处理
            if (diff >= 220) {
                // 累计基金值
                redisManager.hincr(USER_BONUS, param.getUid().toString(), Double.valueOf(diff.longValue() / 220), RedisManager.ONE_DAY_SECONDS * 30);
                // 累计已处理积分
                redisManager.hincr(POINT_DONE_DURING_ACTIVITY, param.getUid().toString(), diff, RedisManager.ONE_DAY_SECONDS * 30);
            }
        } finally {
            lock.unlock();
        }
        return Boolean.TRUE;
    }

    public Boolean coinHandle(BaseParam param, Long value) {
        if (!activityStatusManager.activityIsEnable(param, getActivityCode())) {
            return Boolean.FALSE;
        }
        log.info("newYear2022 金币处理 uid:{}, value:{}", param.getUid(), value);
        RLock lock = redissonClient.getLock(String.format(COIN_HANDLE_LOCK, param.getUid()));
        lock.lock();
        try {
            // 累计金币
            Double after = redisManager.hincr(COIN_DURING_ACTIVITY, param.getUid().toString(), value, RedisManager.ONE_DAY_SECONDS * 30);
            // 获取已处理金币
            Double history = Optional.ofNullable((Integer) redisManager.hget(COIN_DONE_DURING_ACTIVITY, param.getUid().toString())).orElse(0).doubleValue();
            // 计算待处理金币
            Double diff = after - history;
            log.info("newYear2022 金币处理 after:{}, history:{}, diff:{}", after, history, diff);
            // 基金处理
            if (diff >= 10) {
                // 累计基金值
                Double bAfter = redisManager.hincr(USER_BONUS, param.getUid().toString(), Double.valueOf(diff.longValue() / 10), RedisManager.ONE_DAY_SECONDS * 30);
                log.info("newYear2022 金币处理 bonusAfter:{}", bAfter);
                // 累计已处理金币
                redisManager.hincr(COIN_DONE_DURING_ACTIVITY, param.getUid().toString(), diff, RedisManager.ONE_DAY_SECONDS * 30);
            }
        } finally {
            lock.unlock();
        }
        return Boolean.TRUE;
    }

    public Long getBonus(Long uid) {
        Integer bonus = (Integer) redisManager.hget(USER_BONUS, uid.toString());
        if (Objects.isNull(bonus)) {
            redisManager.hset(USER_BONUS, uid.toString(), 0L);
            return 0L;
        }
        return bonus.longValue();
    }

    public void decrBonus(Long uid, Long value) {
        redisManager.hdecr(USER_BONUS, uid.toString(), value);
    }

    @Getter
    @AllArgsConstructor
    enum LoginNotifyTimeRange {

        RANGE1(LocalDateTime.parse("2022-12-24T20:00:00"), LocalDateTime.parse("2022-12-24T22:59:59")),
        RANGE2(LocalDateTime.parse("2022-12-26T20:00:00"), LocalDateTime.parse("2022-12-26T22:59:59")),
        RANGE3(LocalDateTime.parse("2022-12-28T20:00:00"), LocalDateTime.parse("2022-12-28T22:59:59")),
        RANGE4(LocalDateTime.parse("2022-12-30T20:00:00"), LocalDateTime.parse("2022-12-30T22:59:59")),
        RANGE5(LocalDateTime.parse("2022-01-01T20:00:00"), LocalDateTime.parse("2022-01-01T22:59:59")),
        RANGE6(LocalDateTime.parse("2022-01-02T20:00:00"), LocalDateTime.parse("2022-01-02T22:59:59")),
        ;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
    }

    public Boolean loginHandle(BaseParam param) {
        if (!activityStatusManager.activityIsEnable(param, getActivityCode())) {
            return Boolean.FALSE;
        }
        LocalDateTime now = ActivityTimeUtil.getNow(getActivityCode());
        for (NewYear2022BizManager.LoginNotifyTimeRange range : NewYear2022BizManager.LoginNotifyTimeRange.values()) {
            if (now.isAfter(range.getStartTime()) && now.isBefore(range.getEndTime()) && !hasBeenNotify(param)) {
                notifyComponent.npcNotify(param.getUid(), String.format("惊喜来袭！参与“新年上上签”有机会抽取价值1000元的黄金手链、海量积分和金币、绝版礼物等～ <a href=\"%s\" >点击查看</a>", getActivityUrl(param)));
                setNotifyLog(param);
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    private Boolean hasBeenNotify(BaseParam param) {
        return Objects.nonNull(redisManager.hget(String.format(LOGIN_NOTIFY_LOG, ActivityTimeUtil.getToday(getActivityCode())), param.getUid().toString()));
    }

    private void setNotifyLog(BaseParam param) {
        redisManager.hset(String.format(LOGIN_NOTIFY_LOG, ActivityTimeUtil.getToday(getActivityCode())), param.getUid().toString(), System.currentTimeMillis(), RedisManager.ONE_DAY_SECONDS * 30);
    }

    public String getActivityCode() {
        return ActivityCheckListEnum.NEW_YEAR_2022.getCode();
    }

    public String getActivityUrl(BaseParam param) {
        return ActivityUrlUtil.getH5BaseUrl(param.getUnionId(), env) + "new-year-sign?from=npc_talk";
    }

}
