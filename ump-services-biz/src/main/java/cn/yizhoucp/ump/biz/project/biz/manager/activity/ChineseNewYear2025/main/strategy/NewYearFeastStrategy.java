package cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.strategy;

import cn.hutool.core.collection.CollUtil;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.enums.UserPackageBizType;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.product.client.UserPackageFeignService;
import cn.yizhoucp.product.dto.PackageQueryConditionDTO;
import cn.yizhoucp.product.dto.UserPackageDetailDTO;
import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.common.MainChineseNewYear2025Constant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.common.MainChineseNewYear2025Enums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.common.MainChineseNewYear2025RedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.vo.NewYearFeastIndexVO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import com.alibaba.fastjson.JSON;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class NewYearFeastStrategy implements MainChineseNewYear2025Strategy {
    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    private MainChineseNewYear2025RedisManager mainChineseNewYear2025RedisManager;
    @Resource
    private UserPackageFeignService userPackageFeignService;

    @Override
    public NewYearFeastIndexVO execute(ButtonEventParam buttonEventParam) {
        BaseParam param = buttonEventParam.getBaseParam();
        return NewYearFeastIndexVO.builder()
                .ingredients(buildIngredients(param))
                .currentDish(buildCurrentDishes(param))
                .dishes(buildDishes())
                .build();
    }

    private List<NewYearFeastIndexVO.Dishes> buildDishes() {
        List<NewYearFeastIndexVO.Dishes> dishesCache = mainChineseNewYear2025RedisManager.getDishesCacheKey();
        if (CollUtil.isNotEmpty(dishesCache)) {
            return dishesCache;
        }
        List<NewYearFeastIndexVO.Dishes> dishes = new ArrayList<>();
        for (MainChineseNewYear2025Enums.DishsEnum dishsEnum : MainChineseNewYear2025Enums.DishsEnum.values()) {
            NewYearFeastIndexVO.Dishes dish = NewYearFeastIndexVO.Dishes.builder()
                    .dishKey(dishsEnum.getDishCode())
                    .dishName(dishsEnum.getDishName())
                    .cookingTime(dishsEnum.getCookingTime())
                    .requiredIngredients(buildRequiredIngredients(dishsEnum))
                    .build();
            dishes.add(dish);
        }
        mainChineseNewYear2025RedisManager.setDishesCacheKey(JSON.toJSONString(dishes));
        return dishes;
    }

    /**
     * 构建所需食材
     *
     * @param dishEnum 菜品枚举
     * @return 所需食材
     */
    private List<NewYearFeastIndexVO.Ingredients> buildRequiredIngredients(MainChineseNewYear2025Enums.DishsEnum dishEnum) {
        if (dishEnum == null) {
            return null;
        }
        List<ScenePrizeDO> scenePrizes = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), MainChineseNewYear2025Constant.ACTIVITY_CODE, dishEnum.getDishCode());
        if (scenePrizes == null || scenePrizes.isEmpty()) {
            return null;
        }
        return scenePrizes.stream().map(scenePrizeDO -> NewYearFeastIndexVO.Ingredients.builder()
                .ingredientKey(scenePrizeDO.getPrizeValue())
                .ingredientName(scenePrizeDO.getPrizeDesc())
                .quantity(scenePrizeDO.getPrizeNum())
                .build()).collect(Collectors.toList());
    }

    private NewYearFeastIndexVO.CurrentDish buildCurrentDishes(BaseParam param) {
        String dishKey = mainChineseNewYear2025RedisManager.getCurrentDish(param.getUid());
        Long countDown = mainChineseNewYear2025RedisManager.getCountDown(param.getUid());
        Integer status = getCurrentDishStatus(countDown);
        MainChineseNewYear2025Enums.DishsEnum dishsEnum = MainChineseNewYear2025Enums.DishsEnum.getByDishCode(dishKey);
        return NewYearFeastIndexVO.CurrentDish.builder()
                .dishKey(dishKey)
                .countDown(countDown)
                .requiredIngredients(buildRequiredIngredients(dishsEnum))
                .status(status)
                .build();
    }

    private Integer getCurrentDishStatus(Long countDown) {
        if (countDown == null) {
            return MainChineseNewYear2025Enums.CurrentDishStatusEnum.NOT_START.getStatus();
        }
        long currentTimeMillis = System.currentTimeMillis();
        boolean isDishFinished = countDown < currentTimeMillis;

        return isDishFinished ? MainChineseNewYear2025Enums.CurrentDishStatusEnum.FINISH.getStatus() : MainChineseNewYear2025Enums.CurrentDishStatusEnum.COOKING.getStatus();
    }


    private List<NewYearFeastIndexVO.Ingredients> buildIngredients(BaseParam param) {
        final MainChineseNewYear2025Enums.IngredientsEnum[] ingredientsEnums = MainChineseNewYear2025Enums.IngredientsEnum.values();

        List<String> ingredientCodes = Arrays.stream(ingredientsEnums)
                .map(MainChineseNewYear2025Enums.IngredientsEnum::getIngredientCode)
                .collect(Collectors.toList());

        PackageQueryConditionDTO queryParam = PackageQueryConditionDTO.builder()
                .uid(param.getUid())
                .bizIdList(ingredientCodes)
                .bizType(UserPackageBizType.GIFT.getCode())
                .build();

        Map<String, UserPackageDetailDTO> packageMap = userPackageFeignService
                .getPackageDetailMapByCondition(queryParam)
                .successData();

        return Arrays.stream(ingredientsEnums)
                .map(e -> createIngredientVO(e, packageMap))
                .collect(Collectors.toList());
    }

    // 6. 提取构建方法保持主逻辑清晰
    private NewYearFeastIndexVO.Ingredients createIngredientVO(
            MainChineseNewYear2025Enums.IngredientsEnum enumItem,
            Map<String, UserPackageDetailDTO> packageMap) {

        UserPackageDetailDTO dto = packageMap.get(enumItem.getIngredientCode());
        int quantity = (dto != null) ? dto.getAvailableNum().intValue() : 0;

        return NewYearFeastIndexVO.Ingredients.builder()
                .ingredientKey(enumItem.getIngredientCode())
                .ingredientName(enumItem.getIngredientName())
                .quantity(quantity)
                .build();
    }
}
