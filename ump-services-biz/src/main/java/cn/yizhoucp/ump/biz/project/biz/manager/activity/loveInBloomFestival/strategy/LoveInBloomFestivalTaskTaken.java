package cn.yizhoucp.ump.biz.project.biz.manager.activity.loveInBloomFestival.strategy;

import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.loveInBloomFestival.common.LoveInBloomFestivalRedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class LoveInBloomFestivalTaskTaken implements ExecutableStrategy {

    @Resource
    private LoveInBloomFestivalRedisManager loveInBloomFestivalRedisManager;

    @Override
    public Boolean execute(ButtonEventParam buttonEventParam) {
        String bizKey = buttonEventParam.getBizKey();
        Long uid = buttonEventParam.getBaseParam().getUid();
        //是否领取过
        ScenePrizeDO scenePrizeDO = loveInBloomFestivalRedisManager.getTaskScenePrizeDO(bizKey);
        if (scenePrizeDO == null) {
            return Boolean.FALSE;
        }
        String giftKey = scenePrizeDO.getPrizeValue();
        if (loveInBloomFestivalRedisManager.isTaskReceived(uid, giftKey)) {
            return Boolean.FALSE;
        }
        //领取任务
        loveInBloomFestivalRedisManager.setTaskReceived(uid, giftKey);
        return Boolean.TRUE;
    }
}
