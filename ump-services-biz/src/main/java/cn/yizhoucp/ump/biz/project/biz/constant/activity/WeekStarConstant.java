package cn.yizhoucp.ump.biz.project.biz.constant.activity;

import cn.yizhoucp.ump.api.vo.jimu.draw.PrizeItem;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw.inner.PrizeSubTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw.inner.PrizeTypeEnum;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * saka礼物周星活动
 *
 * @author: lianghu
 */
public class WeekStarConstant {

    /** 周星礼物缓存 redis-hash（周一日期） */
    public static final String WEEK_STAR_PRIZE = "ump:weekStart:prize_%s";
    /** 周星礼物1 hash-key */
    public static final String PRIZE1 = "prize1";
    /** 周星礼物2 hash-key */
    public static final String PRIZE2 = "prize2";
    /** 周星礼物配置 key */
    public static final String PRIZE_CONFIG_KEY = "weekPrizeConfig";
    /** 礼物1奖池 */
    public static final String DRAW_POOL_1 = "sakaWeekStarPrizePool";
    /** 礼物2奖池 */
    public static final String DRAW_POOL_2 = "sakaWeekStarPrizePool2";
    /** 魅力-礼物1-榜单 redis-zset（周一日期） */
    public static final String CHARM_PRIZE1_RANK = "ump:weekStart:charmPrize1Rank_%s";
    /** 魅力-礼物2-榜单 redis-zset（周一日期） */
    public static final String CHARM_PRIZE2_RANK = "ump:weekStart:charmPrize2Rank_%s";
    /** 财富-礼物1-榜单 redis-zset（周一日期） */
    public static final String RICH_PRIZE1_RANK = "ump:weekStart:richPrize1Rank_%s";
    /** 财富-礼物2-榜单 redis-zset（周一日期） */
    public static final String RICH_PRIZE2_RANK = "ump:weekStart:richPrize2Rank_%s";
    /** 魅力-礼物1-日榜榜单 redis-zset（当日日期） */
    public static final String CHARM_PRIZE1_DAILY_RANK = "ump:weekStart:charmPrize1DailyRank_%s";
    /** 魅力-礼物2-日榜榜单 redis-zset（当日日期） */
    public static final String CHARM_PRIZE2_DAILY_RANK = "ump:weekStart:charmPrize2DailyRank_%s";
    /** 财富-礼物1-日榜榜单 redis-zset（当日日期） */
    public static final String RICH_PRIZE1_DAILY_RANK = "ump:weekStart:richPrize1DailyRank_%s";
    /** 财富-礼物2-日榜榜单 redis-zset（当日日期） */
    public static final String RICH_PRIZE2_DAILY_RANK = "ump:weekStart:richPrize2DailyRank_%s";

    @AllArgsConstructor
    public enum Rank {
        charm,
        rich;

        public static List<List<PrizeItem>> getDefaultPrizeInfos(Rank rank) {
            switch (rank) {
                case charm:
                    return Lists.newArrayList(
                            Lists.newArrayList(
                                    PrizeItem.builder().prizeIcon("https://chatie-backend-cdn.myrightone.com/res/chatie_branch_test/head-frame/image/6441fa1086f4bb91.png").prizeName("Charm Star").prizeKey("wealth_star_head_frame").prizeType(PrizeTypeEnum.PRIZE_DRESS.getCode()).prizeSubType(PrizeSubTypeEnum.HEAD_FRAME.getCode()).build(),
                                    PrizeItem.builder().prizeIcon("https://design-platform-cdn.yizhoucp.cn/gift-image/2021-06/1624619364763738.png").prizeName("Wedding dress").prizeKey("CLHS_GIFT").prizeType(PrizeTypeEnum.PRIZE_GIFT.getCode()).fee(Boolean.TRUE).build()
                            ),
                            Lists.newArrayList(
                                    PrizeItem.builder().prizeIcon("https://res-cdn.nuan.chat/res/qa/head-frame/image/nl_im_head_fsfy_icon.png").prizeName("Floating Shadows").prizeKey("fanshifuying_head_frame").prizeType(PrizeTypeEnum.PRIZE_DRESS.getCode()).prizeSubType(PrizeSubTypeEnum.HEAD_FRAME.getCode()).build(),
                                    PrizeItem.builder().prizeIcon("https://chatie-backend-cdn.myrightone.com/res/chatie_test01/gift/image/63c7af0370ff9112.png").prizeName("Crystal Shoes").prizeKey("SJX_GIFT").prizeType(PrizeTypeEnum.PRIZE_GIFT.getCode()).fee(Boolean.TRUE).build()
                            ),
                            Lists.newArrayList(
                                    PrizeItem.builder().prizeIcon("https://res-cdn.nuan.chat/res/qa/head-frame/image/nl_im_head_fsfy_icon.png").prizeName("Floating Shadows").prizeKey("fanshifuying_head_frame").prizeType(PrizeTypeEnum.PRIZE_DRESS.getCode()).prizeSubType(PrizeSubTypeEnum.HEAD_FRAME.getCode()).build(),
                                    PrizeItem.builder().prizeIcon("https://chatie-backend-cdn.myrightone.com/res/chatie_branch_test/head-frame/image/6441fa51a3ef357b.png").prizeName("paper_plane").prizeKey("paper_plane_entry_special_effect").prizeType(PrizeTypeEnum.PRIZE_DRESS.getCode()).prizeSubType(PrizeSubTypeEnum.ENTRY_SPECIAL_EFFECT.getCode()).build()
                            )
                    );
                case rich:
                    return Lists.newArrayList(
                            Lists.newArrayList(
                                    PrizeItem.builder().prizeIcon("https://nuanchat.oss-cn-hangzhou.aliyuncs.com/activity/weekStar/freeCoin.png").prizeName("coins").prizeKey("500").prizeType(PrizeTypeEnum.PRIZE_COIN.getCode()).prizeSubType("free").build(),
                                    PrizeItem.builder().prizeIcon("https://chatie-backend-cdn.myrightone.com/res/chatie_branch_test/head-frame/image/6441fa339bcc29ad.png").prizeName("Rich Star").prizeKey("rich_star_head_frame").prizeType(PrizeTypeEnum.PRIZE_DRESS.getCode()).prizeSubType(PrizeSubTypeEnum.HEAD_FRAME.getCode()).build(),
                                    PrizeItem.builder().prizeIcon("https://chatie-backend-cdn.myrightone.com/res/chatie_branch_test/gift/image/643f5a9f90aa5c4a.png").prizeName("Spaceship").prizeKey("HJ_GIFT").prizeType(PrizeTypeEnum.PRIZE_GIFT.getCode()).fee(Boolean.TRUE).build()
                            ),
                            Lists.newArrayList(
                                    PrizeItem.builder().prizeIcon("https://res-cdn.nuan.chat/res/qa/head-frame/image/20211202-yhzl.png").prizeName("Eternal Love").prizeKey("yonghengzhilian_head_frame").prizeType(PrizeTypeEnum.PRIZE_DRESS.getCode()).prizeSubType(PrizeSubTypeEnum.HEAD_FRAME.getCode()).build(),
                                    PrizeItem.builder().prizeIcon("https://chatie-backend-cdn.myrightone.com/res/chatie_branch_test/gift/image/643f5ac6709d055d.png").prizeName("romantic lantern").prizeKey("romantic_lantern").prizeType(PrizeTypeEnum.PRIZE_GIFT.getCode()).fee(Boolean.TRUE).build()
                            ),
                            Lists.newArrayList(
                                    PrizeItem.builder().prizeIcon("https://res-cdn.nuan.chat/res/qa/head-frame/image/20211202-yhzl.png").prizeName("Eternal Love").prizeKey("yonghengzhilian_head_frame").prizeType(PrizeTypeEnum.PRIZE_DRESS.getCode()).prizeSubType(PrizeSubTypeEnum.HEAD_FRAME.getCode()).build(),
                                    PrizeItem.builder().prizeIcon("https://chatie-backend-cdn.myrightone.com/res/chatie_branch_test/head-frame/image/6441fa6d04d85470.png").prizeName("Supersonic").prizeKey("supersonic_entry_special_effect").prizeType(PrizeTypeEnum.PRIZE_DRESS.getCode()).prizeSubType(PrizeSubTypeEnum.ENTRY_SPECIAL_EFFECT.getCode()).build()
                            )
                    );
                default:
                    return Lists.newArrayList();
            }
        }

    }

}
