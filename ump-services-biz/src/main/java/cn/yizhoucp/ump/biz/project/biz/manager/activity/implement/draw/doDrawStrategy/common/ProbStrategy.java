package cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.doDrawStrategy.common;

import cn.yizhoucp.ms.core.base.util.WeightRandomUtil;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawContext;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.doDrawStrategy.DoDrawStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.util.Pair;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 概率匹配
 *
 * @author: lianghu
 */
@Slf4j
@Component
public class ProbStrategy implements DoDrawStrategy {

    public List<DrawPoolItemDTO> getDrawPoolItems(List<DrawPoolItemDO> drawPoolItemDOS, Integer times, Boolean useDoubleProb) {
        return getDrawPoolItems(DrawContext.builder()
                .drawParam(DrawParam.builder().times(times).build())
                .drawPoolItemDOS(drawPoolItemDOS)
                .drawPoolDO(DrawPoolDO.builder().build().setUseDoubleProb(useDoubleProb)).build());
    }

    @Override
    public List<DrawPoolItemDTO> getDrawPoolItems(DrawContext context) {
        // 获取概率配置
        Boolean useDoubleProb = Objects.nonNull(context) && Objects.nonNull(context.getDrawPoolDO()) ? context.getDrawPoolDO().getUseDoubleProb() : Boolean.FALSE;

        // 获取抽奖次数
        Integer times = context.getDrawParam().getTimes();

        // 获取奖池奖品信息
        List<DrawPoolItemDO> drawPoolItemList = context.getDrawPoolItemDOS();
        Map<String, DrawPoolItemDO> drawPoolItemDOMap = drawPoolItemList.stream().collect(Collectors.toMap(DrawPoolItemDO::getItemKey, v1 -> v1, (v1, v2) -> v1));

        // 根据概率匹配结果
        List<Pair> pairs = initPairs(useDoubleProb, drawPoolItemList).stream().filter(p ->
                Boolean.TRUE.equals(useDoubleProb) ? (Double) p.getValue() > 0d : (Integer) p.getValue() > 0).collect(Collectors.toList());
        log.debug("getDrawPoolItems pairs {} context {}", JSON.toJSONString(pairs), JSON.toJSONString(context));
        Map<String, DrawPoolItemDTO> giftMap = Maps.newHashMap();
        for (int i = 0; i < times; i++) {
            putDrawPrize(giftMap, drawPoolItemDOMap.get((String) new WeightRandomUtil(pairs).random()));
        }

        log.info("所获奖品:{}", JSONObject.toJSONString(giftMap));
        // 返回列表
        return giftMap.values().stream().collect(Collectors.toList());
    }

    private List<Pair> initPairs(Boolean useDoubleProb, List<DrawPoolItemDO> drawPoolItemDOS) {
        List<Pair> pairs = Lists.newArrayList();
        for (DrawPoolItemDO item : drawPoolItemDOS) {
            if (Boolean.TRUE.equals(useDoubleProb) && item.getProb() > 0d) {
                pairs.add(new Pair(item.getItemKey(), item.getProb()));
            } else if (Objects.nonNull(item.getProbInt()) && item.getProbInt() > 0) {
                pairs.add(new Pair(item.getItemKey(), item.getProbInt()));
            }
        }
        return pairs;
    }

    private void putDrawPrize(Map<String, DrawPoolItemDTO> giftMap, DrawPoolItemDO item) {
        DrawPoolItemDTO giftItem = giftMap.get(item.getItemKey());
        if (Objects.isNull(giftItem)) {
            giftMap.put(item.getItemKey(), DrawPoolItemDTO.builder()
                    .drawPoolItemDO(item)
                    .targetTimes(1).build());
        } else {
            giftItem.setTargetTimes(giftItem.getTargetTimes() + 1);
        }
    }

}
