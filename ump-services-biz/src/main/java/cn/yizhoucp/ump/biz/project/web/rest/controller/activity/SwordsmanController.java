package cn.yizhoucp.ump.biz.project.web.rest.controller.activity;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ms.core.vo.CommonListVO;
import cn.yizhoucp.ms.core.vo.CommonResultVO;
import cn.yizhoucp.ump.api.vo.GiftBaseInfoDTO;
import cn.yizhoucp.ump.api.vo.swordsman.*;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.swordsman.SwordsmanManager;
import com.alibaba.fastjson.JSONObject;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description 我在江湖当剑客
 * @createDate 2024/11/15
 */
@RequestMapping("/api/inner/activity/swordsman")
@RestController
public class SwordsmanController {

    @Resource
    private SwordsmanManager swordsmanManager;


    /**
     * 选择身份
     *
     * @param userId   用户 id
     * @param identity 用户身份
     * @return CommonResultVO
     */
    @GetMapping("/check-identity")
    public Result<CommonResultVO> checkIdentity(Long userId, Long appId, String identity) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> swordsmanManager.checkIdentity(userId, appId, identity));
    }

    /**
     * 切换身份
     * @param identity 身份
     * @return CommonResultVO
     */
    @GetMapping("/change-identity")
    public Result<CommonResultVO> changeIdentity(String identity) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> swordsmanManager.changeIdentity(identity));
    }

    /**
     * 获取用户身份
     * @return IdentityVO
     */
    @GetMapping("/obtain-identity")
    public Result<IdentityVO> obtainIdentity(Long userId) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> swordsmanManager.obtainIdentity(userId));
    }

    /**
     * 窃取列表
     * @return TheftListVO
     */
    @GetMapping("/theft-list")
    public Result<TheftListVO> theftList(Long userId, Long appId) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> swordsmanManager.theftList(userId, appId));
    }

    /**
     * 窃取主页
     * @param toUid 对方用户 id
     * @return TheftHomepageVO
     */
    @GetMapping("/theft-homepage")
    public Result<TheftHomepageVO> theftHomepage(Long toUid) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> swordsmanManager.theftHomepage(toUid));
    }


    /**
     * 窃取元宝
     * @param userId 用户 id
     * @param toUid 被窃取用户 id
     * @param theftCount 窃取数量
     * @return CommonResultVO
     */
    @GetMapping("/theft-ingot")
    public Result<CommonResultVO> theftIngot(Long userId, Long toUid, Long theftCount) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> swordsmanManager.theftIngot(userId, toUid, theftCount));
    }

    /**
     * 窃取记录
     * @return TheftRecordVO
     */
    @GetMapping("/theft-record")
    public Result<TheftRecordVO> theftRecord(Long userId) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> swordsmanManager.theftRecord(userId));
    }

    /**
     * 购买体力
     * @param physicalCount 购买数量
     * @return CommonResultVO
     */
    @GetMapping("/buy-physical")
    public Result<CommonResultVO> buyPhysical(Integer physicalCount) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> swordsmanManager.buyPhysical(physicalCount));
    }

    /**
     * 开启守护
     * @return CommonResultVO
     */
    @GetMapping("/open-guard")
    public Result<CommonResultVO> openGuard() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> swordsmanManager.openGuard());
    }

    /**
     * 任务列表
     * @param userId 用户 id
     * @param appId 应用 id
     * @return CommonListVO
     */
    @GetMapping("/mission-list")
    public Result<CommonListVO> missionList(Long userId, Long appId) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> swordsmanManager.missionList(userId, appId));
    }


    /**
     * 领取元宝
     * @param userId 用户 id
     * @return CommonResultVO
     */
    @GetMapping("/receive-ingot")
    public Result<CommonResultVO> receiveIngot(Long userId) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> swordsmanManager.receiveIngot(userId));
    }

    /**
     * 拍卖主页
     * @param auctionDate 拍卖日期
     * @return AuctionHomepageVO
     */
    @GetMapping("/auction-homepage")
    public Result<List<AuctionHomepageVO>> auctionHomepage(String auctionDate) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> swordsmanManager.auctionHomepage(auctionDate));
    }

    /**
     * 参与拍卖
     *
     * @param auctionId 拍品 id
     * @param auctionCount  拍卖数量
     * @return CommonResultVO
     */
    @GetMapping("/join-auction")
    public Result<CommonResultVO> joinAuction(Long auctionId, Long auctionCount) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> swordsmanManager.joinAuction(auctionId, auctionCount));
    }

    /**
     * 拍卖详情
     * @param auctionId 拍品 id
     * @return AuctionDetailVO
     */
    @GetMapping("/auction-detail")
    public Result<AuctionDetailVO> auctionDetail(Long auctionId) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> swordsmanManager.auctionDetail(auctionId));
    }

    /**
     * 古董主页
     * @return List<GiftBaseInfoDTO>
     */
    @GetMapping("/antique-homepage")
    public Result<List<GiftBaseInfoDTO>> antiqueHomepage() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> swordsmanManager.antiqueHomepage());
    }

    /**
     * 购买古董
     * @param goodsId 商品 id
     * @return CommonResultVO
     */
    @GetMapping("/buy-antique")
    public Result<CommonResultVO> buyAntique(Long goodsId) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> swordsmanManager.buyAntique(goodsId));
    }

    /**
     * 古董购买记录
     * @return JSONObject
     */
    @GetMapping("/antique-buy-record")
    public Result<JSONObject> antiqueBuyRecord() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> swordsmanManager.antiqueBuyRecord());
    }


    /**
     * 活动榜单
     * @return SwordsmanRankListVO
     */
    @GetMapping("/activity-list")
    public Result<SwordsmanRankListVO> activityList() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> swordsmanManager.activityList());
    }

}
