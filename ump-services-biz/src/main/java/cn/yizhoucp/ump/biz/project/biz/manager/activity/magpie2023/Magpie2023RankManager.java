package cn.yizhoucp.ump.biz.project.biz.manager.activity.magpie2023;

import cn.hutool.core.collection.CollUtil;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.enums.dressup.DressUpType;
import cn.yizhoucp.ms.core.base.util.AppUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.vo.jimu.rank.CpRankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.Magpie2023Constant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.AbstractRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityTimeUtil;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.Magpie2023Constant.COUPLE_BOARD_HIDE;

@Slf4j
@Service
public class Magpie2023RankManager extends AbstractRankManager {

    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private NotifyComponent notifyComponent;

    @Override
    protected void doPreProcess(RankContext rankContext) {
        // 设置榜单长度
        rankContext.setRankLen(10L);
        log.debug("rankContext:{}", JSON.toJSONString(rankContext));
    }

    @Override
    protected void postProcess(RankContext rankContext) {
        RankVO rankVO = rankContext.getRankVO();
        if (rankVO == null) {
            return;
        }

        List<CpRankItem> cpRankItemList = rankVO.getCpRankItemList();
        for (CpRankItem cpRankItem : cpRankItemList) {
            if (Boolean.TRUE.equals(redisManager.hasKey(String.format(COUPLE_BOARD_HIDE, AppUtil.splicUserId(cpRankItem.getMaleUid(), cpRankItem.getFemaleUid()))))) {
                cpRankItem.setHide(true);
            } else {
                cpRankItem.setHide(false);
            }
        }

        CpRankItem myCpRankItem = rankVO.getMyCpRankItem();
        if (myCpRankItem != null) {
            if (Boolean.TRUE.equals(redisManager.hasKey(String.format(COUPLE_BOARD_HIDE, AppUtil.splicUserId(myCpRankItem.getMaleUid(), myCpRankItem.getFemaleUid()))))) {
                myCpRankItem.setHide(true);
            } else {
                myCpRankItem.setHide(false);
            }
        }
    }

    public Boolean sendYesterdayPrize() {
        if (Boolean.TRUE.equals(redisManager.setnx(String.format("ump:magpie2023:sendPrizeIdempotent:%s", ActivityTimeUtil.getYesterdayStr(null)), System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND))) {
            log.info("sendYesterdayPrize");

            List<ScenePrizeDO> loveBoard = scenePrizeJpaDAO.getListBySceneCode(1L, Magpie2023Constant.ACTIVITY_CODE, "loveBoard");
            if (CollUtil.isNotEmpty(loveBoard)) {
                RankVO rankVO = this.getRank(
                        RankContext.builder()
                                .activityCode(Magpie2023Constant.ACTIVITY_CODE)
                                .rankKey(String.format(Magpie2023Constant.LOVE_BOARD, ActivityTimeUtil.getYesterdayStr(null)))
                                .type(RankContext.RankType.cp)
                                .build());
                List<CpRankItem> rankList = rankVO.getCpRankItemList();
                log.info("rankList {}", JSON.toJSONString(rankList));
                if (CollUtil.isNotEmpty(rankList)) {
                    for (CpRankItem cpRankItem : rankList) {
                        Long rank = cpRankItem.getRank();
                        List<ScenePrizeDO> scenePrizeDOS = loveBoard.stream().filter(item -> Objects.equals(rank, item.getPrizeBelongToRank())).collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(scenePrizeDOS)) {
                            log.info("maleUid {} femaleUid {} rank {} 没有奖励哦", cpRankItem.getMaleUid(), cpRankItem.getFemaleUid(), rank);
                            continue;
                        }
                        sendPrizeManager.sendPrize(BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(cpRankItem.getMaleUid()).build(),
                                scenePrizeDOS.stream().map(item -> SendPrizeDTO.of(item, cpRankItem.getFemaleUid())).collect(Collectors.toList()));
                        sendPrizeManager.sendPrize(BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(cpRankItem.getFemaleUid()).build(),
                                scenePrizeDOS.stream().map(item -> SendPrizeDTO.of(item, cpRankItem.getFemaleUid())).collect(Collectors.toList()));
                        for (ScenePrizeDO scenePrizeDO : scenePrizeDOS) {
                            notifyComponent.npcNotify(ServicesAppIdEnum.lanling.getUnionId(), cpRankItem.getMaleUid(), String.format("【银河之约鹊梦千年】您已获得【%s礼物】*1，可进入“我的背包”中查收", scenePrizeDO.getPrizeDesc()));
                            notifyComponent.npcNotify(ServicesAppIdEnum.lanling.getUnionId(), cpRankItem.getFemaleUid(), String.format("【银河之约鹊梦千年】您已获得【%s礼物】*1，可进入“我的背包”中查收", scenePrizeDO.getPrizeDesc()));
                        }
                    }
                }
            }

            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    public Boolean sendCoupleBoardPrize() {
        if (Boolean.TRUE.equals(redisManager.setnx("ump:magpie2023:sendCoupleBoardPrizeIdempotent", System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND))) {
            log.info("sendCoupleBoardPrize");

            List<ScenePrizeDO> loveBoard = scenePrizeJpaDAO.getListBySceneCode(1L, Magpie2023Constant.ACTIVITY_CODE, "coupleBoard");
            if (CollUtil.isNotEmpty(loveBoard)) {
                RankVO rankVO = this.getRank(
                        RankContext.builder()
                                .activityCode(Magpie2023Constant.ACTIVITY_CODE)
                                .rankKey(Magpie2023Constant.COUPLE_BOARD)
                                .type(RankContext.RankType.cp)
                                .build());
                List<CpRankItem> rankList = rankVO.getCpRankItemList();
                log.info("rankList {}", JSON.toJSONString(rankList));
                if (CollUtil.isNotEmpty(rankList)) {
                    for (CpRankItem cpRankItem : rankList) {
                        Long rank = cpRankItem.getRank();
                        List<ScenePrizeDO> scenePrizeDOS = loveBoard.stream().filter(item -> Objects.equals(rank, item.getPrizeBelongToRank())).collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(scenePrizeDOS)) {
                            log.info("maleUid {} femaleUid {} rank {} 没有奖励哦", cpRankItem.getMaleUid(), cpRankItem.getFemaleUid(), rank);
                            continue;
                        }
                        sendPrizeManager.sendPrize(BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(cpRankItem.getMaleUid()).build(),
                                scenePrizeDOS.stream().filter(item -> !"coin".equals(item.getPrizeType())).map(item -> SendPrizeDTO.of(item, cpRankItem.getMaleUid())).collect(Collectors.toList()));
                        sendPrizeManager.sendPrize(BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(cpRankItem.getFemaleUid()).build(),
                                scenePrizeDOS.stream().filter(item -> !"gift".equals(item.getPrizeType())).map(item -> SendPrizeDTO.of(item, cpRankItem.getFemaleUid())).collect(Collectors.toList()));
//                        for (ScenePrizeDO scenePrizeDO : scenePrizeDOS) {
//                            notifyComponent.npcNotify(ServicesAppIdEnum.lanling.getUnionId(), cpRankItem.getMaleUid(), String.format("【银河之约鹊梦千年】您已获得【%s礼物】*1，可进入“我的背包”中查收", scenePrizeDO.getPrizeDesc()));
//                            notifyComponent.npcNotify(ServicesAppIdEnum.lanling.getUnionId(), cpRankItem.getFemaleUid(), String.format("【银河之约鹊梦千年】您已获得【%s礼物】*1，可进入“我的背包”中查收", scenePrizeDO.getPrizeDesc()));
//                        }
                    }
                }
            }

            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    public Boolean sendSignPrize() {
        Set<String> set0 = new HashSet<>();
        Set<ZSetOperations.TypedTuple<Object>> typedTuples = redisManager.reverseRangeByScoreWithScores("ump:magpie2023:loveBoard_20230820", 777, Double.MAX_VALUE);
        if (typedTuples != null) {
            typedTuples.forEach(typedTuple -> set0.add(String.valueOf(typedTuple.getValue())));
        }
        log.info("set0: {}", JSON.toJSONString(set0));

        Set<String> set1 = new HashSet<>();
        typedTuples = redisManager.reverseRangeByScoreWithScores("ump:magpie2023:loveBoard_20230821", 777, Double.MAX_VALUE);
        if (typedTuples != null) {
            typedTuples.forEach(typedTuple -> set1.add(String.valueOf(typedTuple.getValue())));
        }
        log.info("set1: {}", JSON.toJSONString(set1));

        Set<String> set2 = new HashSet<>();
        typedTuples = redisManager.reverseRangeByScoreWithScores("ump:magpie2023:loveBoard_20230822", 777, Double.MAX_VALUE);
        if (typedTuples != null) {
            typedTuples.forEach(typedTuple -> set2.add(String.valueOf(typedTuple.getValue())));
        }
        log.info("set2: {}", JSON.toJSONString(set2));

        Set<String> set3 = new HashSet<>();
        typedTuples = redisManager.reverseRangeByScoreWithScores("ump:magpie2023:loveBoard_20230823", 777, Double.MAX_VALUE);
        if (typedTuples != null) {
            typedTuples.forEach(typedTuple -> set3.add(String.valueOf(typedTuple.getValue())));
        }
        log.info("set3: {}", JSON.toJSONString(set3));

        Set<String> set4 = new HashSet<>();
        typedTuples = redisManager.reverseRangeByScoreWithScores("ump:magpie2023:loveBoard_20230824", 777, Double.MAX_VALUE);
        if (typedTuples != null) {
            typedTuples.forEach(typedTuple -> set4.add(String.valueOf(typedTuple.getValue())));
        }
        log.info("set4: {}", JSON.toJSONString(set4));

        Set<String> set5 = new HashSet<>();
        typedTuples = redisManager.reverseRangeByScoreWithScores("ump:magpie2023:loveBoard_20230825", 777, Double.MAX_VALUE);
        if (typedTuples != null) {
            typedTuples.forEach(typedTuple -> set5.add(String.valueOf(typedTuple.getValue())));
        }
        log.info("set5: {}", JSON.toJSONString(set5));

        Set<String> set6 = new HashSet<>();
        typedTuples = redisManager.reverseRangeByScoreWithScores("ump:magpie2023:loveBoard_20230826", 777, Double.MAX_VALUE);
        if (typedTuples != null) {
            typedTuples.forEach(typedTuple -> set6.add(String.valueOf(typedTuple.getValue())));
        }
        log.info("set6: {}", JSON.toJSONString(set6));

        set0.retainAll(set1);
        set0.retainAll(set2);
        set0.retainAll(set3);
        set0.retainAll(set4);
        set0.retainAll(set5);
        set0.retainAll(set6);
        log.info("set0: {}", JSON.toJSONString(set0));

        set0.forEach(splitUserId -> {
            List<Long> userIds = AppUtil.openSplicUserId(splitUserId);
            sendPrizeManager.sendGift(ServicesAppIdEnum.lanling.getAppId(), "XDPF_GIFT", userIds.get(0), 1L, 7, Magpie2023Constant.ACTIVITY_CODE, false, true);
            sendPrizeManager.sendGift(ServicesAppIdEnum.lanling.getAppId(), "XDPF_GIFT", userIds.get(1), 1L, 7, Magpie2023Constant.ACTIVITY_CODE, false, true);
            sendPrizeManager.sendGift(ServicesAppIdEnum.lanling.getAppId(), "LLZ_GIFT", userIds.get(0), 1L, 7, Magpie2023Constant.ACTIVITY_CODE, false, true);
            sendPrizeManager.sendGift(ServicesAppIdEnum.lanling.getAppId(), "LLZ_GIFT", userIds.get(1), 1L, 7, Magpie2023Constant.ACTIVITY_CODE, false, true);
            sendPrizeManager.sendDressUp(ServicesAppIdEnum.lanling.getAppId(), userIds.get(0), DressUpType.mount, "zhuanshudingshehuachuan_mount", 7, 1);
            sendPrizeManager.sendDressUp(ServicesAppIdEnum.lanling.getAppId(), userIds.get(1), DressUpType.mount, "zhuanshudingshehuachuan_mount", 7, 1);
        });

        return Boolean.TRUE;
    }

}
