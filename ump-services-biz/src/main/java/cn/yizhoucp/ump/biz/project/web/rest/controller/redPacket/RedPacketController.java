package cn.yizhoucp.ump.biz.project.web.rest.controller.redPacket;

import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.biz.project.biz.manager.redPacket.RedPacketManager;
import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.auth.AuthConstant;
import cn.yizhoucp.ms.core.base.auth.Authorize;
import cn.yizhoucp.ms.core.base.enums.RedPacketType;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ms.core.vo.coinservices.*;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.yizhoucp.ump.api.vo.redPacket.OpenRedPacketResult;

import javax.annotation.Resource;
import java.util.List;

/**
 * 红包
 * <p>
 * 20220322 coin-services 迁移至项目
 *
 * @author: lianghu
 */
@Deprecated
@RestController
public class RedPacketController {

    @Resource
    private RedPacketManager redPacketManager;
    @Resource
    private TransactionTemplate transactionTemplate;

    /**
     * 获取用户发送过的次数
     *
     * @return
     */
    @RequestMapping("/api/inner/redPacket/user-send-red-packet-num")
    Result<Integer> userSendRedPacketNum(Long userId, Integer type) {
        return RestBusinessTemplate.execute(() -> {
            return redPacketManager.userSendRedPacketNum(userId, type);
        }, transactionTemplate);
    }


//    /**
//     * 发红包
//     *
//     * @param type             红包类型   1-家族红包 2-广场红包
//     * @param title            红包主题
//     * @param num              红包总个数
//     * @param coinAmount       红包金额
//     * @param toOtherId        发送场景id，可以是familyId,userId
//     * @param canGrabGroupType 可抢人群类型，all - 所有人可抢 follow - 关注
//     * @param canGrabTimeType  可抢时间类型，immediately - 立即可抢 delay - 延迟可抢
//     * @param canGrabTimestamp 可抢时间戳
//     * @return
//     */
//    @RequestMapping("/api/coin/redPacket/send-red-packet")
//    public Result<SendRedPacketResult> sendRedPacket(Integer type, String title, Integer num, Integer coinAmount
//            , Long toOtherId, @RequestParam(value = "canGrabGroupType", required = false) String canGrabGroupType
//            , @RequestParam(value = "canGrabTimeType", required = false) String canGrabTimeType
//            , @RequestParam(value = "canGrabTimestamp", required = false) Long canGrabTimestamp) {
//
//        return RestBusinessTemplate.execute(() -> {
//            return redPacketManager.redPacketBasicPrompt(type, title, num, coinAmount, toOtherId, canGrabGroupType, canGrabTimeType, canGrabTimestamp, null, null);
//        }, transactionTemplate);
//
//    }

    /**
     * 判断红包状态
     *
     * @param redPacketId 红包id
     * @return
     */
    @RequestMapping("/api/coin/redPacket/check-red-packet")
    public Result<CheckRedPacketResult> checkRedPacket(Long redPacketId) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            return redPacketManager.checkRedPacket(redPacketId);
        });
    }

    /**
     * 开红包
     *
     * @param redPacketId 红包id
     * @param toOtherId   发送场景id，可以是familyId,userId
     * @return
     */
    @RequestMapping("/api/coin/redPacket/open-red-packet")
    public Result<OpenRedPacketResult> openRedPacket(Long redPacketId, Long toOtherId) {
        return Result.successResult(redPacketManager.openRedPacket(BaseParam.ofMDC(), redPacketId, toOtherId, null));
    }

    /**
     * 查询语音房的红包房间 延迟红包 进行中 语音房的
     *
     * @param type            红包类型
     * @param status          1-进行中 2-已抢完 3-过期
     * @param canGrabTimeType 可抢的时间类型是delay还是立即抢
     * @return List<RedPacketInfoVO>
     * @see cn.yizhoucp.ms.core.base.enums.RedPacketType
     * @see cn.yizhoucp.ms.core.base.enums.RedPacketCanGrabGroupType
     */
    @RequestMapping("/api/inner/redPacket/voice-room-red-packet")
    public Result<List<RedPacketInfoVO>> voiceRoomRedPacket(Integer type, Integer status, String canGrabTimeType) {
        return RestBusinessTemplate.executeWithoutTransaction(
                () -> redPacketManager.batchRedPacketListOfVoiceRoom(type, status, canGrabTimeType)
        );
    }


    /**
     * 获取红包明细
     *
     * @param redPacketId
     * @return
     */
    @RequestMapping("/api/coin/redPacket/record-details")
    public Result<List<RedPacketRecordVO>> redPacketRecordDetails(Long redPacketId) {
        return RestBusinessTemplate.executeWithoutTransaction(
                () -> redPacketManager.redPacketRecordDetails(redPacketId)
        );
    }

    /**
     * 获取单个红包的VO
     *
     * @param redPacketId
     * @return
     */
    @RequestMapping("/api/coin/redPacket/info")
    public Result<RedPacketInfoVO> getRedPacketInfoVO(Long redPacketId) {
        return RestBusinessTemplate.executeWithoutTransaction(
                () -> redPacketManager.getRedPacketInfoVO(redPacketId)
        );
    }

    /**
     * 获取多长时间以前的红包数量
     *
     * @param type      红包类型
     * @param toOtherId 场景id
     * @param time      多少时间之前的数量(毫秒)
     * @return
     * @see RedPacketType
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @RequestMapping("/api/inner/redPacket/get-num-before-time")
    public Result<Integer> getNumBeforeTime(Integer type, Long toOtherId, Long time) {
        return RestBusinessTemplate.executeWithoutTransaction(
                () -> redPacketManager.getNumBeforeTime(type, toOtherId, time)
        );
    }

    /**
     * 红包列表查询 - 目前只有语音房使用
     *
     * @param status           1-进行中 2-已抢完 3-过期
     * @param type             红包类型 @see cn.yizhoucp.ms.core.base.enums.RedPacketType
     * @param toOtherId        家族id、用户id、语音房id
     * @param canGrabTimeType  可抢时间类型 @see RedPacketCanGrabTimeType
     * @param canGrabTimestamp 可抢时间戳（毫秒）
     * @return
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @RequestMapping("/api/inner/redPacket/red-packet-list")
    public Result<List<RedPacketInfoVO>> redPacketList(Integer status, Integer type, Long toOtherId, String canGrabTimeType, Long canGrabTimestamp) {
        return RestBusinessTemplate.executeWithoutTransaction(
                () -> redPacketManager.redPacketList(status, type, toOtherId, canGrabTimeType, canGrabTimestamp)
        );
    }

    /**
     * 红包列表 根据otherId、type、status查询
     *
     * @param otherId 家族id、用户id、语音房id
     * @param type    红包类型 @see cn.yizhoucp.ms.core.base.enums.RedPacketType
     * @param status  1-进行中 2-已抢完 3-过期
     * @return List<RedPacketInfoVO>
     */
    @RequestMapping("/api/inner/redPacket/red-packet-list-other-id-type-status")
    public Result<List<RedPacketInfoVO>> redPacketListByOtherIdAndTypeAndStatus(Long otherId, Integer type, Integer status) {
        return RestBusinessTemplate.executeWithoutTransaction(
                () -> redPacketManager.redPacketListByOtherIdAndTypeAndStatus(otherId, type, status)
        );
    }

    /**
     * 批量获取红包列表 根据 otherIds、type、status查询
     *
     * @param otherIds 家族id、用户id、语音房id(JSON格式)
     * @param type     红包类型 @see cn.yizhoucp.ms.core.base.enums.RedPacketType
     * @param status   1-进行中 2-已抢完 3-过期
     * @return List<RedPacketInfoVO>
     */
    @RequestMapping("/api/inner/redPacket/batch-red-packet-list")
    public Result<List<RedPacketInfoVO>> batchRedPacketList(String otherIds, Integer type, Integer status, String canGrabTimeType, Long canGrabTimestamp) {
        return RestBusinessTemplate.executeWithoutTransaction(
                () -> redPacketManager.batchRedPacketList(otherIds, type, status, canGrabTimeType, canGrabTimestamp)
        );
    }

    /**
     * 红包列表 根据type、status查询
     *
     * @param type   红包类型 @see cn.yizhoucp.ms.core.base.enums.RedPacketType
     * @param status 1-进行中 2-已抢完 3-过期
     * @return List<RedPacketInfoVO>
     */
    @RequestMapping("/api/inner/redPacket/red-packet-list-type-status")
    public Result<List<RedPacketInfoVO>> redPacketListByTypeAndStatus(Integer type, Integer status, String canGrabTimeType, Long canGrabTimestamp) {
        return RestBusinessTemplate.executeWithoutTransaction(
                () -> redPacketManager.redPacketListByTypeAndStatus(type, status, canGrabTimeType, canGrabTimestamp)
        );
    }

    /**
     * 通过 ${redPacketId} 获取红包
     *
     * @param redPacketId
     * @return
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @RequestMapping("/api/inner/redPacket/get-by-id")
    public Result<RedPacketInfoVO> getById(Long redPacketId) {
        return RestBusinessTemplate.executeWithoutTransaction(
                () -> redPacketManager.getById(redPacketId)
        );
    }

}
