package cn.yizhoucp.ump.biz.project.common.checker.component.mission;

import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.ump.biz.project.common.checker.BaseChecker;
import cn.yizhoucp.ump.biz.project.common.checker.CheckerContext;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;

@Slf4j
@Component("sendGiftChecker")
public class SendGiftChecker extends BaseChecker {

    @Override
    protected Boolean doCheck(CheckerContext context, JSONObject param) {
        log.debug("sendGiftChecker context:{}, param:{}", JSON.toJSONString(context), JSON.toJSONString(param));
        JSONObject missionExt = context.getMissionExt();
        if (Objects.isNull(missionExt)) {
            return Boolean.TRUE;
        }
        Set<String> limitKeySet = getLimitKeySet(missionExt);
        Set<String> limitSceneSet = getLimitSceneSet(missionExt);
        if (Objects.isNull(limitKeySet) && Objects.isNull(limitSceneSet)) {
            return Boolean.TRUE;
        }
        List<CoinGiftGivedModel> gifts = param.getJSONArray("coinGiftGivedModels").toJavaList(CoinGiftGivedModel.class);
        for (CoinGiftGivedModel gift : gifts) {
            if (giftKeyCheck(limitKeySet, gift.getGiftKey()) && sceneCheck(limitSceneSet, gift.getFrom())) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    private Set<String> getLimitKeySet(JSONObject missionExt) {
        String giftKey = missionExt.getString("giftKey");
        if (StringUtils.isBlank(giftKey)) {
            return null;
        }
        return Sets.newHashSet(Arrays.asList(giftKey.split(",")));
    }

    private Set<String> getLimitSceneSet(JSONObject missionExt) {
        String scene = missionExt.getString("scene");
        if (StringUtils.isBlank(scene)) {
            return null;
        }
        return Sets.newHashSet(Arrays.asList(scene.split(",")));
    }

    private Boolean giftKeyCheck(Set<String> limit, String giftKey) {
        log.debug("limit:{}, giftKey:{}", JSON.toJSONString(limit), giftKey);
        if (Objects.isNull(limit)) {
            return Boolean.TRUE;
        }
        return limit.contains(giftKey);
    }

    private Boolean sceneCheck(Set<String> limit, String from) {
        log.debug("limit:{}, giftKey:{}", JSON.toJSONString(limit), from);
        if (Objects.isNull(limit)) {
            return Boolean.TRUE;
        }
        return limit.contains(from);
    }


}
