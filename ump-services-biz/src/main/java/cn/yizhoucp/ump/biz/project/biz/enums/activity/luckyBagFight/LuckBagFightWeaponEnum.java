package cn.yizhoucp.ump.biz.project.biz.enums.activity.luckyBagFight;

import org.apache.commons.lang3.tuple.Pair;

/**
 * <AUTHOR>
 * @Description 武器枚举
 * @date 2022-12-09 16:51
 */
public enum LuckBagFightWeaponEnum {

    /**
     * 武器枚举
     */

    mujian("mujian", "木剑", "normal", "https://res-cdn.nuan.chat/res/activity/luckyBag/style/fight/icon/mujian.png","https://res-cdn.nuan.chat/res/activity/luckyBag/style/fight/btn/mujian.png", 2, Pair.of(2, 3), 1, 3, "木剑"),
    sheng<PERSON>u_tiejian("shengxiu_tiejian", "生锈的铁剑", "normal", "https://res-cdn.nuan.chat/res/activity/luckyBag/style/fight/icon/shengxiutiejian.png","https://res-cdn.nuan.chat/res/activity/luckyBag/style/fight/btn/shengxiutiejian.png", 5, Pair.of(4, 7), 1, 10, "生锈的铁剑"),
    tie<PERSON><PERSON>("tiejian", "铁剑", "normal", "https://res-cdn.nuan.chat/res/activity/luckyBag/style/fight/icon/tiejian.png","https://res-cdn.nuan.chat/res/activity/luckyBag/style/fight/btn/tiejian.png", 20, Pair.of(17, 27), 10, 40, "铁剑"),
    tiechui("tiechui", "铁锤", "normal", "https://res-cdn.nuan.chat/res/activity/luckyBag/style/fight/icon/tiechui.png","https://res-cdn.nuan.chat/res/activity/luckyBag/style/fight/btn/tiechui.png", 25, Pair.of(21, 34), 14, 60, "铁锤"),
    tieqiang("tieqiang", "铁枪", "normal", "https://res-cdn.nuan.chat/res/activity/luckyBag/style/fight/icon/tieqiang.png","https://res-cdn.nuan.chat/res/activity/luckyBag/style/fight/btn/tieqiang.png", 30, Pair.of(25, 41), 21, 70, "铁枪"),
    tiefu("tiefu", "铁斧", "normal", "https://res-cdn.nuan.chat/res/activity/luckyBag/style/fight/icon/tiefu.png","https://res-cdn.nuan.chat/res/activity/luckyBag/style/fight/btn/tiefu.png", 35, Pair.of(29, 48), 30, 80, "铁斧"),
    qingyunjian("qingyunjian", "青云剑", "high", "https://res-cdn.nuan.chat/res/activity/luckyBag/style/fight/icon/qingyunjian.png","https://res-cdn.nuan.chat/res/activity/luckyBag/style/fight/btn/qingyunjian.png", 60, Pair.of(50, 82), 300, 180, "青云剑"),
    kaitianchui("kaitianchui", "开天锤", "high", "https://res-cdn.nuan.chat/res/activity/luckyBag/style/fight/icon/kaitianchui.png","https://res-cdn.nuan.chat/res/activity/luckyBag/style/fight/btn/kaitianchui.png", 65, Pair.of(55, 89), 350, 190, "开天锤"),
    hongyingqiang("hongyingqiang", "红缨枪", "high", "https://res-cdn.nuan.chat/res/activity/luckyBag/style/fight/icon/hongyingqiang.png","https://res-cdn.nuan.chat/res/activity/luckyBag/style/fight/btn/hongyingqiang.png", 70, Pair.of(59, 96), 400, 200, "红缨枪"),
    jingleifu("jingleifu", "惊雷斧", "high", "https://res-cdn.nuan.chat/res/activity/luckyBag/style/fight/icon/jingleifu.png","https://res-cdn.nuan.chat/res/activity/luckyBag/style/fight/btn/jingleifu.png", 75, Pair.of(63, 102), 450, 210, "惊雷斧"),

    ;

    /** 武器码 */
    private String code;

    /** 名称 */
    private String name;

    /** 品阶类型 normal/high */
    private String gradeType;

    /** 图片 */
    private String icon;

    private String btnIcon;

    /** 攻击力 */
    private Integer attackValue;

    /** 伤害范围 */
    private Pair<Integer,Integer> hurtValueRange;

    /** 需要碎片数量 */
    private Integer needSplinterNum;

    /** 最大次数 */
    private Integer maxTimes;

    //描述
    private String desc;

    public static LuckBagFightWeaponEnum getByCode(String code){
        for (LuckBagFightWeaponEnum item: LuckBagFightWeaponEnum.values()){
            if (item.code.equals(code)){
                return item;
            }
        }
        return null;
    }

    LuckBagFightWeaponEnum(String code, String name, String gradeType, String icon, String btnIcon, Integer attackValue, Pair<Integer, Integer> hurtValueRange, Integer needSplinterNum, Integer maxTimes, String desc) {
        this.code = code;
        this.name = name;
        this.gradeType = gradeType;
        this.icon = icon;
        this.btnIcon = btnIcon;
        this.attackValue = attackValue;
        this.hurtValueRange = hurtValueRange;
        this.needSplinterNum = needSplinterNum;
        this.maxTimes = maxTimes;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getIcon() {
        return icon;
    }

    public Pair<Integer, Integer> getHurtValueRange() {
        return hurtValueRange;
    }

    public Integer getNeedSplinterNum() {
        return needSplinterNum;
    }

    public Integer getMaxTimes() {
        return maxTimes;
    }

    public String getDesc() {
        return desc;
    }

    public String getGradeType() {
        return gradeType;
    }

    public Integer getAttackValue() {
        return attackValue;
    }

    public String getBtnIcon() {
        return btnIcon;
    }
}
