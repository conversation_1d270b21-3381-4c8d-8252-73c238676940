package cn.yizhoucp.ump.biz.project.dto.adSpace;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 浮标业务信息
 *
 * @author: lianghu
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BuoyInfoDTO {

    /** 角标数字信息 */
    private Long showNum;
    /** 倒计时截止时间戳 */
    private Long countDownTime;
    /** 倒计时颜色 */
    private String countDownTimeColor;
    /** 倒计时边框颜色 */
    private String countDownTimeBorderColor;
    /** 倒计时文字大小 */
    private Integer countDownTimeTextSize;
    /** 跳转地址 */
    private String routerUrl;
    /** 浮标动效 KEY */
    private String relationKey;
    /** 更新浮标图片 */
    private String activityIcon;
}
