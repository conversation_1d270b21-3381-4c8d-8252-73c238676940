package cn.yizhoucp.ump.biz.project.biz.manager.jimu;

import cn.yizhoucp.family.api.client.FamilyFeignService;
import cn.yizhoucp.family.api.dto.family.FamilyInfoDTO;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.roomservices.RoomVO;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.remoteService.FeignRoomService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.FeignUserService;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2023/3/10 12:01
 * @Version 1.0
 */
@Deprecated
@Slf4j
public abstract class RankTemplate implements RankManager {

    @Resource
    private RedisManager redisManager;
    @Resource
    private FeignUserService feignUserService;
    @Resource
    private FamilyFeignService familyFeignService;
    @Resource
    private FeignRoomService feignRoomService;

    @Override
    public void incrRankValue(Long uid, Long incrVal, String rankKey) {

    }

    @Override
    public RankVO getRank(RankContext context) {

        preProcess(context);

        // 1、设置榜单长度
        setRankLen(context);
        // 1.1 支持计算差值
        isSupportDiff(context);

        // 2、查榜单
        getNativeRank(context);

        // 3、获取个人排名
        getSelfRank(context);


        // 3、填充和上一名的差值
        if (Boolean.TRUE.equals(context.getSupportDiff())) {
            calculationDiff(context);
        }

        // 4、扩展处理
        handler(context);

        return context.getRankVO();
    }

    @Override
    public Boolean sendPrize(RankContext rankContext) {
        return Boolean.TRUE;
    }

    public abstract void setRankLen(RankContext context);

    public abstract void isSupportDiff(RankContext context);

    /**
     * 通过榜单key 获取榜单类型 eg: 家族榜单、个人榜单、房间榜单
     * @return
     */
    public abstract RankContext.RankType getRankTypeByRankKey(String key);

    private void getNativeRank(RankContext context) {
        List<RankItem> rank = new ArrayList<>();
        log.debug("getNativeRank context {}", JSON.toJSONString(context));
        Set<ZSetOperations.TypedTuple<Object>> typedTuples = redisManager.reverseRangeByScoreWithScores(context.getRankKey(),
                0, Double.MAX_VALUE, context.getOffset(), context.getRankLen());
        log.debug(" 榜单详情 key:{}, rank:{}", context.getRankKey(), JSON.toJSONString(typedTuples));
        if (CollectionUtils.isEmpty(typedTuples)) {
            context.setRankVO(RankVO.builder().rankList(Lists.newArrayList()).myselfRank(RankItem.builder().rank(-1L).build()).build());
        }
        Long index = 0L;
        for (ZSetOperations.TypedTuple<Object> typedTuple : typedTuples) {
            if (Objects.isNull(typedTuple.getValue()) ||
                    Objects.isNull(typedTuple.getScore())) {
                continue;
            }
            // 补充排名、数值信息
            Long score = typedTuple.getScore().longValue();
            RankItem rankItem = getRankItem(context, Long.valueOf(typedTuple.getValue().toString()));
            if (Objects.isNull(rankItem)) {
                continue;
            }
            ++index;
            rankItem.setRank(index);
            rankItem.setValue(score);
            rank.add(rankItem);
        }
        context.setRankVO(RankVO.builder().rankList(rank).build());
    }

    private RankItem getSelfRank(RankContext context) {
        BaseParam param = context.getParam();
        log.info(" 获取个人 排名  param {}", JSON.toJSONString(param));
        Long id = getIdByRankType(context);
        if (Objects.isNull(id)) {
            context.getRankVO().setMyselfRank(RankItem.builder().rank(-1L).value(0L).build());
            return RankItem.builder().rank(-1L).value(0L).build();
        }
        log.debug("getSelfRank rankKey {} id {}", context.getRankKey(), id);
        Long rank = redisManager.reverseRank(context.getRankKey(), String.valueOf(id));
        log.debug("rank {}", rank);
        if (Objects.isNull(rank) || rank > (context.getRankLen() - 1)) {
            RankItem rankItem = getRankItem(context, id);
            log.debug("getSelfRank rankItem {}", JSON.toJSONString(rankItem));
            if (Objects.isNull(rankItem)) {
                log.debug("getRankItem is null");
                rankItem = RankItem.builder().rank(-1L).value(0L).build();
                context.getRankVO().setMyselfRank(rankItem);
                return rankItem;
            }
            rankItem.setRank(-1L);
            if (Objects.isNull(rank)) {
                rankItem.setValue(0L);
            } else {
                Double score = Optional.ofNullable(redisManager.score(context.getRankKey(), String.valueOf(id))).orElse(0d);
                rankItem.setValue(score.longValue());
            }
            context.getRankVO().setMyselfRank(rankItem);
            return rankItem;
        }
        rank += 1;
        RankItem rankItem = getRankItem(context, id);
        if (Objects.isNull(rankItem)) {
            log.debug("getRankItem is null");
            rankItem = RankItem.builder().rank(-1L).value(0L).build();
            context.getRankVO().setMyselfRank(rankItem);
            return rankItem;
        }
        Double score = Optional.ofNullable(redisManager.score(context.getRankKey(), String.valueOf(rankItem.getId()))).orElse(0d);
        log.debug("getSelfRank rankKey {} rank {} score {} rankItem {}", context.getRankKey(), rank, score, JSON.toJSONString(rankItem));
        rankItem.setRank(rank);
        rankItem.setValue(score.longValue());
        context.getRankVO().setMyselfRank(rankItem);
        return rankItem;
    }

    /**
     * 计算和前一名的差值
     * @param context
     */
    private void calculationDiff(RankContext context) {
        getDiffFromLastOne(context.getRankVO().getMyselfRank(), context.getRankKey());
    }

    private RankItem getDiffFromLastOne(RankItem rank, String key) {
        log.debug("getDiffFromLastOne rank {}", rank);
        Long diff = -1L;
        Set<ZSetOperations.TypedTuple<Object>> typedTuples = redisManager.reverseRangeByScoreWithScores(key, rank.getValue(), Double.MAX_VALUE, rank.getRank() - 2, 1);
        if (!CollectionUtils.isEmpty(typedTuples)) {
            for (ZSetOperations.TypedTuple<Object> item : typedTuples) {
                if (null == item || null == item.getValue() || null == item.getScore()) {
                    break;
                }
                diff = item.getScore().longValue();
                log.info("getDiffFromLastOne lastOne {}", diff);
                rank.setDiffVal(diff.intValue() - rank.getValue());
            }
        }
        return rank;
    }

    private Long getIdByRankType(RankContext context) {
        RankContext.RankType type = getRankTypeByRankKey(context.getRankKey());
        if (Objects.isNull(type)) {
            throw new ServiceException(ErrorCode.SERVER_EXCEPTION);
        }
        switch (type) {
            case user:
                return context.getParam().getUid();
            case family:
                FamilyInfoDTO familyInfoDTO = familyFeignService.findFamilyInfoByUid(context.getParam().getUid(), context.getParam().getAppId()).successData();
                if (Objects.isNull(familyInfoDTO)) {
                    return null;
                }
                return familyInfoDTO.getId();
            default:
                throw new ServiceException(ErrorCode.SERVER_EXCEPTION);
        }
    }

    private RankItem getRankItem(RankContext context, Long id) {
        RankContext.RankType type = getRankTypeByRankKey(context.getRankKey());
        log.debug("getRankItem type {} id {}", type, id);
        switch (type) {
            case user:
                return buildByUid(context.getParam().getAppId(), id);
            case family:
                return buildByFamilyId(context.getParam().getAppId(), id);
            case room:
                return buildByRoomId(context.getParam().getAppId(), id);
            default:
                return null;
        }

    }

    private RankItem buildByUid(Long appId, Long uid) {
        log.debug("buildByUid appId {} uid {}", appId, uid);
        UserVO userVO = feignUserService.getBasic(uid, appId).successData();
        if (Objects.isNull(userVO)) {
            return null;
        }
        return RankItem.builder().id(uid).name(userVO.getName()).icon(userVO.getAvatar()).build();
    }


    private RankItem buildByFamilyId(Long appId, Long familyId) {
        log.debug("buildByFamilyId appId {} familyId {}", appId, familyId);
        FamilyInfoDTO familyInfoDTO = familyFeignService.findFamilyInfoById(familyId, appId).successData();
        if (Objects.isNull(familyInfoDTO)) {
            return null;
        }
        return RankItem.builder().id(familyId).icon(familyInfoDTO.getAvatar()).name(familyInfoDTO.getName()).build();
    }

    private RankItem buildByRoomId(Long appId, Long roomId) {
        log.debug("buildByRoomId appId {} roomId {}", appId, roomId);
        RoomVO roomVO = feignRoomService.getRoomInfoByRoomId(roomId, appId).successData();
        if (Objects.isNull(roomVO)) {
            return null;
        }
        return RankItem.builder().id(roomId).name(roomVO.getRoomName()).icon(roomVO.getRoomIcon()).build();
    }

}
