package cn.yizhoucp.ump.biz.project.biz.mq;

import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.rushSky.RushSkyBizManager;
import cn.yizhoucp.ms.core.base.TopicConstant;
import cn.yizhoucp.ms.core.base.TopicTagEnum;
import cn.yizhoucp.ms.core.base.env.DynamicEnvManager;
import cn.yizhoucp.ms.core.base.util.RocketMqUtil;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 接受处理 任务相关消息
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@RocketMQMessageListener(topic = "ump_topic", consumerGroup = "${rocketmq.consumer.name}" + "${rocketmq.consumer.global}")
public class ApacheMqConsumer implements RocketMQListener<MessageExt> {

    @Resource
    private RushSkyBizManager rushSkyBizManager;
    @Resource
    @Lazy
    private DynamicEnvManager dynamicEnvManager;
    @Resource
    private Environment env;
    @Value("${spring.profiles.active}")
    private String envProfile;

    @Override
    public void onMessage(MessageExt message) {
        String tag = message.getTags();
        String mqBody = new String(message.getBody());
        String jsonBody;
        try {
            // 包了一层 JSONObject，保存 traceId
            log.info("msgTag: {}, mqBody : {}", tag, mqBody);
            jsonBody = RocketMqUtil.resolveFromBody(mqBody);
            // 动态环境 开发/测试 校验是否消费处理 false就不消费
            if (!dynamicEnvManager.checkMQNeedConsume(envProfile, mqBody, env.getProperty(DynamicEnvManager.GLOBAL_ENV_NAME), DynamicEnvManager.UMP_CACHE_PREFIX)) {
                return;
            }
        } catch (Exception e) {
            log.info("resolve mq params error", e);
            return;
        }
        if (StringUtils.isBlank(jsonBody)) {
            return;
        }
        try {
            consume(tag, jsonBody);
        } catch (Exception e) {
            log.error("mq 消费失败", e);
        }
    }

    private boolean consume(String tag, String jsonBody) {
        boolean result = false;
        TopicTagEnum tagEnum = TopicTagEnum.findByCode(TopicConstant.TOPIC_UMP, tag);
        if (Objects.isNull(tagEnum)) {
            log.warn("tagEnum 为空 tag:{}, json:{}", tag, JSON.toJSONString(jsonBody));
            return false;
        }
        // 任务中立事件处理
        switch (tagEnum) {
            case TOPIC_UMP_GIVE_GIFT:
                result = this.giveGift(BaseParam.ofMDC(), jsonBody);
                break;
            default:
                log.info("未找到对应 tag {}", tagEnum.getTagKey());
        }
        log.info("mq process action: {}, result: {}", tag, result ? "success" : "fail");
        return result;
    }

    /**
     * 送礼
     *
     * @param jsonBody 请求数据
     * @return boolean
     */
    private boolean giveGift(BaseParam param, String jsonBody) {
        List<CoinGiftGivedModel> coinGiftGivedModels = JSON.parseArray(jsonBody, CoinGiftGivedModel.class);
        rushSkyBizManager.giveGiftHandle(param, coinGiftGivedModels);
        return true;
    }

}
