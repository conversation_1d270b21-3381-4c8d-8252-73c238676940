package cn.yizhoucp.ump.biz.project.biz.manager.activity.girlSDay;

import cn.yizhoucp.ms.core.base.util.AppUtil;
import cn.yizhoucp.ms.core.base.util.FunctionUtil;
import cn.yizhoucp.ump.biz.project.dal.mp.dao.GirlSDayGuardInfoDAO;
import cn.yizhoucp.ump.biz.project.dal.mp.dataobject.GirlSDayGuardInfoDO;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 只负责数据存储/拉取，不负责业务逻辑
 */
@Slf4j
@Service
public class GirlSDayGuardManager {
    @Resource
    private GirlSDayGuardInfoDAO girlSDayGuardInfoDAO;

    @Resource
    private RedissonClient redissonClient;

    public GirlSDayGuardInfoDO incrGuardValue(Long fromUid, Long toUid, Long addValue) {
        GirlSDayGuardInfoDO guardInfo = this.getGuardInfo(fromUid, toUid, true);
        girlSDayGuardInfoDAO.incrGuardValue(guardInfo.getId(), addValue);
        return guardInfo;
    }

    public void incrGuardValueById(Long id, Integer addValue) {
        girlSDayGuardInfoDAO.incrOnlyGuardValue(id, addValue);
    }

    public void incrFairyValueById(Long id, Integer addValue) {
        girlSDayGuardInfoDAO.incrOnlyFairyValue(id, addValue);
    }


    public List<GirlSDayGuardInfoDO> listMyGuard(Long uid, boolean guard) {
        return girlSDayGuardInfoDAO.listMyGuard(uid, guard);
    }

    public GirlSDayGuardInfoDO upLevel(Long fromUid, Long toUid, Integer objectLevel, boolean guard) {
        RLock lock = redissonClient.getLock("ump:girlsDay:guardInfo:uplevellock:" + AppUtil.splicUserId(fromUid, toUid));
        try {
            lock.lock(6, TimeUnit.SECONDS);
            GirlSDayGuardInfoDO guardInfo = this.getGuardInfo(fromUid, toUid, true);
            if (guard) {
                if (objectLevel <= guardInfo.getGuardLevel()) {
                    return null;
                }
                guardInfo.setGuardLevel(objectLevel);
            } else {
                if (objectLevel <= guardInfo.getFairyLevel()) {
                    return null;
                }
                guardInfo.setFairyLevel(objectLevel);
            }
            log.info("upLevel guardInfo:{} objectLevel: {}", guardInfo, objectLevel);
            girlSDayGuardInfoDAO.updateById(guardInfo);
            return guardInfo;
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.forceUnlock();
            }
        }
    }

    public Integer getMaxGuardLevel(Long uid) {
        return FunctionUtil.getOrNull(girlSDayGuardInfoDAO.getTopGuardLevelByFromUid(uid), GirlSDayGuardInfoDO::getGuardLevel);
    }

    public GirlSDayGuardInfoDO getBySplicId(Long fromUid, Long toUid) {
        return girlSDayGuardInfoDAO.getBySplicId(fromUid, toUid);
    }


    public GirlSDayGuardInfoDO getGuardInfo(Long fromUid, Long toUid, boolean createIfNotExist) {
        GirlSDayGuardInfoDO girlSDayGuardInfoDO = girlSDayGuardInfoDAO.getBySplicId(fromUid, toUid);
        if (girlSDayGuardInfoDO == null && createIfNotExist) {
            RLock lock = redissonClient.getLock("ump:girlsDay:guardInfo:newlock:" + AppUtil.splicUserId(fromUid, toUid));
            try {
                lock.lock(6, TimeUnit.SECONDS);
                girlSDayGuardInfoDO = girlSDayGuardInfoDAO.getBySplicId(fromUid, toUid);
                if (girlSDayGuardInfoDO == null) {
                    girlSDayGuardInfoDO = GirlSDayGuardInfoDO.builder()
                            .fromUid(fromUid)
                            .toUid(toUid)
                            .splicUid(AppUtil.splicUserId(fromUid, toUid))
                            .guardLevel(0)
                            .fairyLevel(0)
                            .fairyValue(0L)
                            .build();
                    girlSDayGuardInfoDAO.save(girlSDayGuardInfoDO);
                }
            } finally {
                if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.forceUnlock();
                }
            }
        }
        return girlSDayGuardInfoDO;
    }
}
