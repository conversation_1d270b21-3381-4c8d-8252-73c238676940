package cn.yizhoucp.ump.biz.project.dal.jpa.dataobject;

import cn.yizhoucp.ump.biz.project.biz.util.JsonUtil;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/6/1 17:50
 * @Version 1.0
 */
@Slf4j
@EntityListeners(value = AuditingEntityListener.class)
@Table(name = "activity_user_cache")
@Entity
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActivityUserCacheDO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 应用 ID
     */
    private Long appId;

    private String unionId;

    /**
     * 用户 ID
     */
    private Long userId;

    /**
     * 缓存 json
     */
    private String bizCache;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public Map<String, Object> getBizCacheMap() {
        if (StringUtils.isBlank(bizCache)) {
            return Maps.newHashMap();
        }
        Map<String, Object> result;
        try {
            result = (Map<String, Object>) JsonUtil.fromJson(bizCache, Map.class);
        } catch (Exception e) {
            log.error("反序列化失败，e: {}");
            return Maps.newHashMap();
        }
        return result;
    }

}
