package cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main;

import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.common.MainChineseNewYear2025Constant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.common.MainChineseNewYear2025RedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.vo.MainChineseNewYear2025IndexVO;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.IndexManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.dal.service.DrawPoolItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 2025新春活动正式活动首页
 *
 * <AUTHOR>
 * @version V1.0
 * @since 15:06 2025/1/19
 */
@Slf4j
@Service
public class MainChineseNewYear2025IndexManager implements IndexManager {

    @Resource
    private MainChineseNewYear2025RedisManager mainChineseNewYear2025RedisManager;
    @Resource
    private DrawPoolItemService drawPoolItemService;

    @Override
    public MainChineseNewYear2025IndexVO getIndex(BaseParam param, Long toUid, String extData) {
        Long uid = param.getUid();
        return MainChineseNewYear2025IndexVO.builder()
                .drawItems(mainChineseNewYear2025RedisManager.getDrawItems(uid))
                .poolItems(buildPoolItems())
                .build();
    }

    /**
     * 构建奖池
     *
     * @return List<MainChineseNewYear2025IndexVO.PoolItem>
     */
    private List<MainChineseNewYear2025IndexVO.PoolItem> buildPoolItems() {
        List<DrawPoolItemDO> drawPoolItemDOList = drawPoolItemService.getByPoolCode(MainChineseNewYear2025Constant.POOL_CODE);
        if (drawPoolItemDOList == null || drawPoolItemDOList.isEmpty()) {
            log.error("MainChineseNewYear2025IndexManager#drawPoolItemDOList is empty");
            return null;
        }
        return drawPoolItemDOList.stream().map(drawPoolItemDO -> MainChineseNewYear2025IndexVO.PoolItem.builder()
                .giftName(drawPoolItemDO.getItemName())
                .giftIcon(drawPoolItemDO.getItemIcon())
                .giftValue(drawPoolItemDO.getItemValueGold())
                .giftKey(drawPoolItemDO.getItemKey())
                .build()).collect(Collectors.toList());
    }

    @Override
    public String getTemplateType() {
        return "";
    }

    @Override
    public String getActivityCode() {
        return MainChineseNewYear2025Constant.ACTIVITY_CODE;
    }
}
