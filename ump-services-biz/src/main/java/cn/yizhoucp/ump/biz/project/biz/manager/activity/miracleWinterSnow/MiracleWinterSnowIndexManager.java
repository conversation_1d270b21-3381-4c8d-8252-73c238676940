package cn.yizhoucp.ump.biz.project.biz.manager.activity.miracleWinterSnow;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.activity.miracleWinterSnow.MiracleWinterSnowIndexVO;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.IndexManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.FeignUserService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.manager.activity.miracleWinterSnow.MiracleWinterSnowConstant.*;


/**
 * 奇迹降临的冬雪夜主界面
 */
@Service
@Slf4j
public class MiracleWinterSnowIndexManager implements IndexManager {

    @Resource
    private RedisManager redisManager;

    @Resource
    private MiracleWinterSnowConstant constant;

    @Resource
    private FeignUserService feignUserService;

    @Resource
    private MiracleWinterSnowRankManager rankManager;

    @Override
    @ActivityCheck(activityCode = ACTIVITY_CODE)
    public MiracleWinterSnowIndexVO getIndex(BaseParam param, Long toUid, String extData) {
        if (param.getUid() == null || param.getAppId() == null || param.getUnionId() == null) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        log.debug("MiracleWinterSnow getIndex param:{}, toUid:{}", JSON.toJSONString(param), toUid);
        MiracleWinterSnowIndexVO snowIndexVO = new MiracleWinterSnowIndexVO();
        snowIndexVO.setGingerbreadNum(constant.getGingerBreadNum(param.getUid()));

        // 任务
        snowIndexVO.setTask(taskList(param.getUid()));
        // 碎片
        snowIndexVO.setShard(shardList(param.getUid()));
        // 榜单
        snowIndexVO.setRank(buildRank(param));
        return snowIndexVO;
    }

    /**
     * 任务
     * @return
     */
    public List<MiracleWinterSnowIndexVO.Task> taskList(Long uid) {
        return Arrays.stream(MiracleWinterSnowConstant.TaskEnum.values()).map(taskEnum -> {
            return MiracleWinterSnowIndexVO.Task.builder()
                    .taskKey(taskEnum.getTaskKey())
                    .taskStatus(constant.getTaskStatus(uid, taskEnum.getTaskKey(), taskEnum.getQualify()))
                    .build();
        }).collect(Collectors.toList());
    }

    /**
     * 碎片
     * @return
     */
    public List<MiracleWinterSnowIndexVO.Shard> shardList(Long uid) {
        return SHARD_LIST.stream().map(shardKey -> {
            return MiracleWinterSnowIndexVO.Shard.builder()
                    .shardKey(shardKey)
                    .shardNum(constant.getShardNum(uid, shardKey))
                    .build();
        }).collect(Collectors.toList());
    }

    /**
     * 榜单
     * @return
     */
    private RankVO buildRank(BaseParam param) {
        RankVO rank = rankManager.getRank(RankContext.builder()
                .param(param)
                .activityCode(ACTIVITY_CODE)
                .rankKey(RANK_KEY)
                .rankLen(10L)
                .supportDiff(Boolean.TRUE)
                .type(RankContext.RankType.user)
                .build());

        List<RankItem> rankList = rank.getRankList();
        int currentSize = rankList.size();
        if (currentSize < 10) {
            Long lastRank = rankList.isEmpty() ? 0 : rankList.get(currentSize - 1).getRank();
            for (int i = currentSize; i < 10; i++) {
                RankItem newItem = new RankItem();
                newItem.setRank(lastRank + 1);
                rankList.add(newItem);
                lastRank = newItem.getRank();
            }
        }

        return rank;
    }


    @Override
    public String getTemplateType() {
        return null;
    }

    @Override
    public String getActivityCode() {
        return MiracleWinterSnowConstant.ACTIVITY_CODE;
    }
}
