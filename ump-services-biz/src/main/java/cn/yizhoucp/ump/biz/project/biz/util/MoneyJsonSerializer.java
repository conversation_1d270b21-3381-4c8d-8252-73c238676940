package cn.yizhoucp.ump.biz.project.biz.util;

import cn.yizhoucp.ms.core.base.model.Money;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;

/**
 * Created by 张晋 on 2019/9/4.
 */
public class MoneyJsonSerializer extends JsonSerializer<Money> {
    public MoneyJsonSerializer() {
    }

    public void serialize(Money value, JsonGenerator gen, SerializerProvider serializers) throws IOException, JsonProcessingException {
        if (value != null) {
            gen.writeNumber(Double.parseDouble(value.toString()));
        } else {
            gen.writeNumber(0);
        }

    }
}

