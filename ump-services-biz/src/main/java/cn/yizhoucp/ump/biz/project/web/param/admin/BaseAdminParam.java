package cn.yizhoucp.ump.biz.project.web.param.admin;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import lombok.Data;

import java.util.Objects;

/**
 * 查询积累
 *
 * @author: lianghu
 */
@Data
public class BaseAdminParam {

    /** 应用编号 */
    private Long appId;
    /** 页面大小 */
    private Integer pageSize;
    /** 页码 */
    private Integer pageNum;

    private static final Integer MIN_PAGE_NUM = 1;
    private static final Integer MAX_PAGE_SIZE = 100;

    public void check() {
        if (Objects.isNull(appId) || Objects.isNull(pageNum) || Objects.isNull(pageNum)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        if (pageNum < MIN_PAGE_NUM || pageSize > MAX_PAGE_SIZE) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
    }

}
