package cn.yizhoucp.ump.biz.project.biz.manager.activity.labourDay2024;

import cn.yizhoucp.depth.api.client.DepthFeignService;
import cn.yizhoucp.depth.api.vo.CardiacRelationVO;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.annotation.NoRepeatSubmit;
import cn.yizhoucp.ms.core.base.config.risk.RiskConfiguration;
import cn.yizhoucp.ms.core.base.enums.*;
import cn.yizhoucp.ms.core.base.util.AppUtil;
import cn.yizhoucp.ms.core.base.util.RiskUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.coinservices.UserAccountVO;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.risk.api.client.RiskFeignService;
import cn.yizhoucp.risk.api.constant.RiskParam;
import cn.yizhoucp.risk.api.constant.RiskScene;
import cn.yizhoucp.risk.api.dto.RiskParamDTO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.activity.labourDay2024.BarrageVO;
import cn.yizhoucp.ump.api.vo.activity.labourDay2024.IncomeCallWarnVO;
import cn.yizhoucp.ump.api.vo.activity.labourDay2024.IndexVO;
import cn.yizhoucp.ump.api.vo.activity.labourDay2024.PuzzleVO;
import cn.yizhoucp.ump.api.vo.activity.labourDay2024.RomanticTourVO;
import cn.yizhoucp.ump.api.vo.activity.labourDay2024.TicketVO;
import cn.yizhoucp.ump.api.vo.activity.labourDay2024.UrgentLoveVO;
import cn.yizhoucp.ump.api.vo.jimu.task.TaskItem;
import cn.yizhoucp.ump.api.vo.jimu.task.TaskVO;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.IndexManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.biz.manager.userActivity.ActivityManager;
import cn.yizhoucp.ump.biz.project.biz.manager.userCoinAccount.UserCoinAccountManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.FeignUserService;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ActivityDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dal.service.ScenePrizeService;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Random;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.ACTIVITY_CODE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.BARRAGE_LIST_KEY;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.BROADCAST;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.FLIP_TIMES;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.GIFT_VALUE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.GREAT_MOVE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.HEAD_FRAME_RECEIVE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.INCOME_CALL_WARN_FRIEND;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.LIFE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.LUCKY_NUMBER;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.MAN_RANK;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.MY_NUMBER;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.TASK_CUR_FINISH_TIMES;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.TASK_TAKE_PRIZE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.TICKET;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.TOUR_STAGE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.TOUR_TICKET_RECEIVE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.TOUR_VALUE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.WOMAN_RANK;

@Service
@Slf4j
public class LabourDay2024IndexManager implements IndexManager {

    private static final Random RANDOM = new Random();
    @Resource
    private RedisManager redisManager;
    @Resource
    private ActivityManager activityManager;
    @Resource
    private UserCoinAccountManager userCoinAccountManager;
    @Autowired
    private RiskConfiguration riskConfig;
    @Resource
    private DepthFeignService depthFeignService;
    @Resource
    private FeignUserService feignUserService;
    @Resource
    private LabourDay2024RankManager labourDay2024RankManager;
    @Resource
    private LabourDay2024BizManager labourDay2024BizManager;
    @Resource
    private ScenePrizeService scenePrizeService;
    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private NotifyComponent notifyComponent;
    @Resource
    private LabourDay2024TrackManager labourDay2024TrackManager;
    @Resource
    private RiskFeignService riskFeignService;

    @Override
    public IndexVO getIndex(BaseParam param, Long toUid, String extData) {
        if (toUid == null) {
            toUid = this.getMaxIntimacyOppositeSexCloseFriend(param);
        }

        IndexVO indexVO = new IndexVO();
        indexVO.setWomanRank(labourDay2024RankManager.getRank(
                RankContext.builder()
                        .activityCode(ACTIVITY_CODE)
                        .rankKey(String.format(WOMAN_RANK, labourDay2024BizManager.stage()))
                        .type(RankContext.RankType.user)
                        .param(param)
                        .supportDiff(Boolean.TRUE)
                        .build()));
        indexVO.setManRank(labourDay2024RankManager.getRank(
                RankContext.builder()
                        .activityCode(ACTIVITY_CODE)
                        .rankKey(String.format(MAN_RANK, labourDay2024BizManager.stage()))
                        .type(RankContext.RankType.user)
                        .param(param)
                        .supportDiff(Boolean.TRUE)
                        .build()));
        List<Object> barrageList = redisManager.lGet(BARRAGE_LIST_KEY, 0, 100);
        indexVO.setBarrageList(barrageList.stream().map(barrage -> {
            BarrageVO barrageVO = JSONObject.parseObject(String.valueOf(barrage), BarrageVO.class);
            barrageVO.setBelongToMe(param.getUid().equals(barrageVO.getToUid()));
            return barrageVO;
        }).collect(Collectors.toList()));
        indexVO.setTicket(Optional.ofNullable(redisManager.getInteger(String.format(TICKET, param.getUid()))).orElse(0));
        indexVO.setBroadcast(Optional.ofNullable(redisManager.lGet(BROADCAST, 0, 20)).orElse(Collections.emptyList()));
        indexVO.setTask(TaskVO.builder().taskItemList(new ArrayList<>()).build());
        for (LabourDay2024Constant.Task task : LabourDay2024Constant.Task.values()) {
            TaskItem taskItem = new TaskItem();
            taskItem.setTaskCode(task.name());
            taskItem.setCurFinishTimes(Math.min(task.getFinishTimes(), Optional.ofNullable(redisManager.getInteger(String.format(TASK_CUR_FINISH_TIMES, param.getUid(), DateUtil.getNowYyyyMMdd(), task.name()))).orElse(0)));
            int buttonStatus;
            if (Boolean.TRUE.equals(redisManager.hasKey(String.format(TASK_TAKE_PRIZE, param.getUid(), DateUtil.getNowYyyyMMdd(), taskItem.getTaskCode())))) {
                buttonStatus = -1;
            } else if (taskItem.getCurFinishTimes() < task.getFinishTimes()) {
                buttonStatus = 0;
            } else {
                buttonStatus = 1;
            }
            taskItem.setButtonStatus(buttonStatus);
            indexVO.getTask().getTaskItemList().add(taskItem);
        }
        indexVO.setRomanticTour(romanticTourChooseCloseFriend(param, toUid, "index"));
        indexVO.setUrgentLove(buildUrgentLove(param));
        Long incomeCallWarnFriend = redisManager.getLong(String.format(INCOME_CALL_WARN_FRIEND, param.getUid()));
        if (incomeCallWarnFriend != null) {
            toUid = incomeCallWarnFriend;
        } else {
            toUid = this.getMaxIntimacyOppositeSexCloseFriend(param);
        }
        indexVO.setIncomeCallWarnVO(buildIncomeCallWarn(param, toUid));

        return indexVO;
    }

    @NoRepeatSubmit(time = 3)
    public Boolean send(BaseParam param, Long toUid, String text) {
        if (toUid == null || text == null) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        log.debug("send,param:{},toUid:{},text:{}",param,toUid,text);
        Map<String, Object> featureMap = Maps.newHashMapWithExpectedSize(1);
        featureMap.put("signature", text);
        Map<String, Object> detectionMap = RiskUtil.getDetectionMap(text, null, null, null, null, null);
        RiskParamDTO build = RiskParamDTO.builder().scene(RiskScene.USER_DETAIL_SIGNATURE)
                .services(ServicesNameEnum.ump_services)
                .appId(param.getAppId())
                .baseUrl(RiskParam.baseRiskUrl).basePath(RiskParam.syncScanUrl)
                .userId(param.getUid())
                .graphId(GraphIdEnum.USER_DETAIL_SIGNATURE)
                .feature(featureMap)
                .detection(detectionMap)
                .build();
        log.debug("sendBuild -> {}",build);
        String resultString = riskFeignService.sendRisk(build).successData();
//        String resultString = RiskUtil.getRiskResultString(riskConfig.getBaseRiskUrl() + riskConfig.getSyncScanUrl(), param.getAppId(), param.getUid(), GraphIdEnum.USER_DETAIL_SIGNATURE.getGraphId(), null, text, null, null, null, null, featureMap);
        if (!Boolean.TRUE.equals(RiskUtil.getResultBooleanByResultString(resultString))) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "不能发送违规言论噢~");
        }

        boolean reduceCoinSuccess = userCoinAccountManager.reduceCoin(param.getUnionId(), param.getAppId(), param.getUid(), toUid, AppScene.activity, AppFunctionEnum.activity_labour_day_2024.getCode(),
                52L, 0L, AccountReduceType.adapt, "labour_day_2024_love_confess",
                CoinBusinessType.DEDUCT_COIN.getCode(), "2024五一活动-恋爱告白");
        if (!reduceCoinSuccess) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "金币不足");
        }

        BarrageVO barrageVO = new BarrageVO();
        barrageVO.setText(text);
        barrageVO.setToUid(toUid);
        redisManager.listLpush(BARRAGE_LIST_KEY, barrageVO, DateUtil.ONE_MONTH_SECOND);

        return Boolean.TRUE;
    }

    public RomanticTourVO romanticTourChooseCloseFriend(BaseParam param, Long toUid, String from) {
        if (toUid != null) {
            Boolean cardiac = depthFeignService.hasUserCardiacRelationship(param.getUid(), toUid).successData();
            if (!Boolean.TRUE.equals(cardiac)) {
                toUid = null;
            }
        }

        UserVO user = feignUserService.getBasic(param.getUid(), param.getAppId()).successData();
        UserVO toUser = feignUserService.getBasic(toUid, param.getAppId()).successData();
        if (toUser != null && Objects.equals(user.getSex(), toUser.getSex())) {
            if ("index".equals(from)) {
                toUid = null;
            } else {
                throw new ServiceException(ErrorCode.INVALID_PARAM, "请选择异性密友噢~");
            }
        }

        RomanticTourVO romanticTourVO = new RomanticTourVO();
        romanticTourVO.setUser(user);
        romanticTourVO.setToUserVO(toUser);
        romanticTourVO.setTourValue(Optional.ofNullable(redisManager.getInteger(String.format(TOUR_VALUE, DateUtil.getNowYyyyMMdd(), AppUtil.splicUserId(param.getUid(), toUid)))).orElse(0));
        romanticTourVO.setStage(Optional.ofNullable(redisManager.getInteger(String.format(TOUR_STAGE, DateUtil.getNowYyyyMMdd(), AppUtil.splicUserId(param.getUid(), toUid)))).orElse(0));

        List<PuzzleVO> puzzleList = new ArrayList<>();
        PuzzleVO puzzleVO = new PuzzleVO();
        puzzleVO.setTicketList(new ArrayList<>());
        puzzleVO.setPiece(0);
        Set<Object> tourTicketReceive = Optional.ofNullable(redisManager.sGet(String.format(TOUR_TICKET_RECEIVE, DateUtil.getNowYyyyMMdd(), AppUtil.splicUserId(param.getUid(), toUid)))).orElse(Collections.emptySet());
        for (LabourDay2024Constant.Tour tour : LabourDay2024Constant.Tour.values()) {
            TicketVO ticketVO = new TicketVO();
            ticketVO.setNum(tour.getTicket());
            if (tourTicketReceive.contains(tour.ordinal())) {
                ticketVO.setStatus(2);
            } else if (tour.getTourValue() <= romanticTourVO.getTourValue()) {
                ticketVO.setStatus(1);
            } else {
                ticketVO.setStatus(0);
            }
            puzzleVO.getTicketList().add(ticketVO);
            if (romanticTourVO.getTourValue() >= tour.getTourValue()) {
                puzzleVO.setPiece(puzzleVO.getPiece() + 1);
            }
        }
        if (Boolean.TRUE.equals(redisManager.hasKey(String.format(HEAD_FRAME_RECEIVE, AppUtil.splicUserId(param.getUid(), toUid), romanticTourVO.getStage())))) {
            puzzleVO.setHeadFrameStatus(2);
        } else if (puzzleVO.getPiece() >= 8) {
            puzzleVO.setHeadFrameStatus(1);
        } else {
            puzzleVO.setHeadFrameStatus(0);
        }
        puzzleList.add(puzzleVO);

        if (romanticTourVO.getStage() == 0) {
            puzzleList.add(emptyPuzzle(param, toUid));
        } else {
            puzzleList.add(0, fullPuzzle(param, toUid));
        }

        romanticTourVO.setPuzzle(puzzleList);

        return romanticTourVO;
    }

    public RomanticTourVO receive(BaseParam param, Long toUid, Integer puzzleIndex, Integer ticketIndex, Boolean headFrame) {
        if (toUid == null) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }

        if (Boolean.TRUE.equals(headFrame)) {
            if (Boolean.TRUE.equals(redisManager.setnx(String.format(HEAD_FRAME_RECEIVE, AppUtil.splicUserId(param.getUid(), toUid), puzzleIndex), System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND))) {
                List<ScenePrizeDO> scenePrizeDOList = scenePrizeService.getListBySceneCode(ACTIVITY_CODE, "head_frame_" + puzzleIndex);
                sendPrizeManager.sendPrize(
                        BaseParam.builder().appId(param.getAppId()).unionId(param.getUnionId()).uid(param.getUid()).build(),
                        scenePrizeDOList.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, param.getUid())).collect(Collectors.toList())
                );
                sendPrizeManager.sendPrize(
                        BaseParam.builder().appId(param.getAppId()).unionId(param.getUnionId()).uid(toUid).build(),
                        scenePrizeDOList.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, param.getUid())).collect(Collectors.toList())
                );
                for (ScenePrizeDO scenePrizeDO : scenePrizeDOList) {
                    labourDay2024TrackManager.allActivityReceiveAward("romantic_round_get", scenePrizeDO.getPrizeValue(), scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeNum(), null, param.getUid());
                    labourDay2024TrackManager.allActivityReceiveAward("romantic_round_get", scenePrizeDO.getPrizeValue(), scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeNum(), null, toUid);
                }
            } else {
                throw new ServiceException(ErrorCode.INVALID_PARAM);
            }
        } else {
            int tourValue = Optional.ofNullable(redisManager.getInteger(String.format(TOUR_VALUE, DateUtil.getNowYyyyMMdd(), AppUtil.splicUserId(param.getUid(), toUid)))).orElse(0);
            Set<Object> tourTicketReceive = Optional.ofNullable(redisManager.sGet(String.format(TOUR_TICKET_RECEIVE, DateUtil.getNowYyyyMMdd(), AppUtil.splicUserId(param.getUid(), toUid)))).orElse(Collections.emptySet());
            if (!tourTicketReceive.contains(ticketIndex)) {
                LabourDay2024Constant.Tour[] values = LabourDay2024Constant.Tour.values();
                for (int i = 0; i < values.length; i++) {
                    if (i == ticketIndex) {
                        if (tourValue >= values[i].getTourValue()) {
                            redisManager.sSetExpire(String.format(TOUR_TICKET_RECEIVE, DateUtil.getNowYyyyMMdd(), AppUtil.splicUserId(param.getUid(), toUid)), DateUtil.ONE_MONTH_SECOND, ticketIndex);
                            redisManager.incrLong(String.format(TICKET, param.getUid()), values[i].getTicket(), DateUtil.ONE_MONTH_SECOND);
                            redisManager.incrLong(String.format(TICKET, toUid), values[i].getTicket(), DateUtil.ONE_MONTH_SECOND);
                            notifyComponent.npcNotify(
                                    ServicesAppIdEnum.lanling.getUnionId(),
                                    toUid,
                                    String.format("恭喜获得热恋券%s张，快去抽奖吧~", values[i].getTicket())
                            );
                            labourDay2024TrackManager.allActivityReceiveAward("romantic_round_get", null, null, null, values[i].getTicket(), param.getUid());
                            labourDay2024TrackManager.allActivityReceiveAward("romantic_round_get", null, null, null, values[i].getTicket(), toUid);
                            int tourStage = Optional.ofNullable(redisManager.getInteger(String.format(TOUR_STAGE, DateUtil.getNowYyyyMMdd(), AppUtil.splicUserId(param.getUid(), toUid)))).orElse(0);
                            if (tourStage == 0 && tourTicketReceive.size() == 7) {
                                redisManager.delete(String.format(TOUR_TICKET_RECEIVE, DateUtil.getNowYyyyMMdd(), AppUtil.splicUserId(param.getUid(), toUid)));
                                redisManager.delete(String.format(TOUR_VALUE, DateUtil.getNowYyyyMMdd(), AppUtil.splicUserId(param.getUid(), toUid)));
                                redisManager.incrLong(String.format(TOUR_STAGE, DateUtil.getNowYyyyMMdd(), AppUtil.splicUserId(param.getUid(), toUid)), 1L, DateUtil.ONE_MONTH_SECOND);
                            }
                        } else {
                            throw new ServiceException(ErrorCode.INVALID_PARAM);
                        }
                    }
                }
            } else {
                throw new ServiceException(ErrorCode.INVALID_PARAM);
            }
        }

        return romanticTourChooseCloseFriend(param, toUid, null);
    }

    public Long giveGift(BaseParam param, Long toUid, Integer productCount) {
        return redisManager.incrLong(String.format(FLIP_TIMES, param.getUid()), productCount, DateUtil.ONE_MONTH_SECOND);
    }

    public IncomeCallWarnVO urgentLoveChooseCloseFriend(BaseParam param, Long toUid) {
        redisManager.set(String.format(INCOME_CALL_WARN_FRIEND, param.getUid()), toUid, DateUtil.ONE_MONTH_SECOND);
        return buildIncomeCallWarn(param, toUid);
    }

    public UrgentLoveVO flip(BaseParam param) {
        Integer flipTimes = Optional.ofNullable(redisManager.getInteger(String.format(FLIP_TIMES, param.getUid()))).orElse(0);
        if (flipTimes <= 0) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "还未获得卡牌哦~");
        }

        UrgentLoveVO urgentLoveVO = new UrgentLoveVO();
        urgentLoveVO.setFlipTimes(Math.toIntExact(Optional.ofNullable(redisManager.decrLong(String.format(FLIP_TIMES, param.getUid()), 1L, DateUtil.ONE_MONTH_SECOND)).orElse(0L)));
        ActivityDO activityDO = activityManager.getActivityInfo(param, this.getActivityCode());
        LocalDateTime startTime = activityDO.getStartTime().truncatedTo(ChronoUnit.DAYS);
        LocalDateTime now = LocalDateTime.now();
        log.debug("startTime {} now {}", startTime, now);
        long between = ChronoUnit.DAYS.between(startTime, now);
        urgentLoveVO.setLuckyNumber(LUCKY_NUMBER.get(Math.toIntExact(between)));
        urgentLoveVO.setMyNumber(RANDOM.nextInt(9) + 1);
        redisManager.set(String.format(MY_NUMBER, DateUtil.getNowYyyyMMdd(), param.getUid()), urgentLoveVO.getMyNumber(), DateUtil.ONE_MONTH_SECOND);
        if (urgentLoveVO.getLuckyNumber().equals(urgentLoveVO.getMyNumber())) {
            redisManager.incrLong(String.format(TICKET, param.getUid()), 1L, DateUtil.ONE_MONTH_SECOND);
        }
        UserAccountVO userAccount = feignUserService.getUserAccountByUid(param.getAppId(), param.getUid()).successData();
        urgentLoveVO.setCoin(userAccount == null ? 0L : userAccount.getBalance() + userAccount.getFreeBalance());
        return urgentLoveVO;
    }

    private UrgentLoveVO buildUrgentLove(BaseParam param) {
        UrgentLoveVO urgentLoveVO = new UrgentLoveVO();
        urgentLoveVO.setFlipTimes(Optional.ofNullable(redisManager.getInteger(String.format(FLIP_TIMES, param.getUid()))).orElse(0));
        ActivityDO activityDO = activityManager.getActivityInfo(param, this.getActivityCode());
        LocalDateTime startTime = activityDO.getStartTime().truncatedTo(ChronoUnit.DAYS);
        LocalDateTime now = LocalDateTime.now();
        log.debug("startTime {} now {}", startTime, now);
        long between = ChronoUnit.DAYS.between(startTime, now);
        urgentLoveVO.setLuckyNumber(LUCKY_NUMBER.get(Math.toIntExact(between)));
        urgentLoveVO.setMyNumber(redisManager.getInteger(String.format(MY_NUMBER, DateUtil.getNowYyyyMMdd(), param.getUid())));
        UserAccountVO userAccount = feignUserService.getUserAccountByUid(param.getAppId(), param.getUid()).successData();
        urgentLoveVO.setCoin(userAccount == null ? 0L : userAccount.getBalance() + userAccount.getFreeBalance());
        return urgentLoveVO;
    }

    @NoRepeatSubmit(time = 3)
    public IncomeCallWarnVO greatMove(BaseParam param, Long toUid, Integer index) {
        int giftValue = Optional.ofNullable(redisManager.getInteger(String.format(GIFT_VALUE, DateUtil.getNowYyyyMMdd(), AppUtil.splicUserId(param.getUid(), toUid)))).orElse(0);
        for (int i = 0; i < LabourDay2024Constant.GreatMove.values().length; i++) {
            if (i == index && LabourDay2024Constant.GreatMove.values()[i].getGiftValue() > giftValue) {
                throw new ServiceException(ErrorCode.INVALID_PARAM);
            }
        }

        Set<Object> greatMove = Optional.ofNullable(redisManager.sGet(String.format(GREAT_MOVE, DateUtil.getNowYyyyMMdd(), AppUtil.splicUserId(param.getUid(), toUid)))).orElse(Collections.emptySet());
        if (greatMove.contains(index)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        redisManager.sSetExpire(String.format(GREAT_MOVE, DateUtil.getNowYyyyMMdd(), AppUtil.splicUserId(param.getUid(), toUid)), DateUtil.ONE_MONTH_SECOND, index);

        IncomeCallWarnVO incomeCallWarnVO = new IncomeCallWarnVO();
        incomeCallWarnVO.setUser(feignUserService.getBasic(param.getUid(), param.getAppId()).successData());
        incomeCallWarnVO.setToUserVO(feignUserService.getBasic(toUid, param.getAppId()).successData());
        incomeCallWarnVO.setTimeStamp(System.currentTimeMillis());
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 20);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        incomeCallWarnVO.setStartTimeStamp(calendar.getTimeInMillis());
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        incomeCallWarnVO.setEndSimeStamp(calendar.getTimeInMillis());
        incomeCallWarnVO.setLife(Math.toIntExact(4 - redisManager.incrLong(String.format(LIFE, DateUtil.getNowYyyyMMdd(), AppUtil.splicUserId(param.getUid(), toUid)), 1L, DateUtil.ONE_MONTH_SECOND)));
        incomeCallWarnVO.setGiftValue(giftValue);
        List<Integer> greatMoveStatus = Lists.newArrayListWithCapacity(4);
        for (int i = 0; i < LabourDay2024Constant.GreatMove.values().length; i++) {
            if (greatMove.contains(i) || i == index) {
                greatMoveStatus.add(2);
            } else if (incomeCallWarnVO.getGiftValue() >= LabourDay2024Constant.GreatMove.values()[i].getGiftValue()) {
                greatMoveStatus.add(1);
            } else {
                greatMoveStatus.add(0);
            }
        }
        incomeCallWarnVO.setGreatMoveStatus(greatMoveStatus);
        incomeCallWarnVO.setFinish(Boolean.TRUE);
        for (Integer status: incomeCallWarnVO.getGreatMoveStatus()) {
            if (status != 2) {
                incomeCallWarnVO.setFinish(Boolean.FALSE);
                break;
            }
        }

        if (incomeCallWarnVO.getFinish()) {
            redisManager.incrLong(String.format(TICKET, param.getUid()), 10L, cn.yizhoucp.ms.core.base.util.DateUtil.ONE_MONTH_SECOND);
            redisManager.incrLong(String.format(TICKET, toUid), 10L, cn.yizhoucp.ms.core.base.util.DateUtil.ONE_MONTH_SECOND);
            notifyComponent.npcNotify(
                    ServicesAppIdEnum.lanling.getUnionId(),
                    toUid,
                    "恭喜在【来电预警】中pk胜利，获得10张【热恋券】，快去抽奖吧~"
            );
            labourDay2024TrackManager.allActivityReceiveAward("luxifa_task", null, null, null, 10, param.getUid());
            labourDay2024TrackManager.allActivityReceiveAward("luxifa_task", null, null, null, 10, toUid);
        }

        for (int i = 0; i < LabourDay2024Constant.GreatMove.values().length; i++) {
            if (i == index) {
                labourDay2024TrackManager.allActivityTaskFinish(LabourDay2024Constant.GreatMove.values()[i].getTaskType() , null, null, null, param.getUid());
                labourDay2024TrackManager.allActivityTaskFinish(LabourDay2024Constant.GreatMove.values()[i].getTaskType() , null, null, null, toUid);
            }
        }

        return incomeCallWarnVO;
    }

    public Long getMaxIntimacyOppositeSexCloseFriend(BaseParam param) {
        UserVO user = feignUserService.getBasic(param.getUid(), param.getAppId()).successData();
        if (user == null) {
            return null;
        }

        Map<Long, CardiacRelationVO> allUserDepth = depthFeignService.getAllUserDepth(param.getUid()).successData();
        if (allUserDepth == null || allUserDepth.isEmpty()) {
            return null;
        }

        Map<Long, UserVO> idUserMap = feignUserService.acquireUsersInBulkPost2(new ArrayList<>(allUserDepth.keySet())).successData();
        if (idUserMap == null || idUserMap.isEmpty()) {
            return null;
        }

        List<CardiacRelationVO> cardiacRelationList = allUserDepth.values().stream().sorted((c1, c2) -> c2.getCurrentValue().intValue() - c1.getCurrentValue().intValue()).collect(Collectors.toList());
        for (CardiacRelationVO cardiacRelationVO : cardiacRelationList) {
            UserVO toUser = idUserMap.get(cardiacRelationVO.getToUid());
            if (toUser != null && !Objects.equals(user.getSex(), toUser.getSex())) {
                return toUser.getId();
            }
        }

        return null;
    }

    @Override
    public String getActivityCode() {
        return ACTIVITY_CODE;
    }

    @Override
    public String getTemplateType() {
        return null;
    }

    private IncomeCallWarnVO buildIncomeCallWarn(BaseParam param, Long toUid) {
        IncomeCallWarnVO incomeCallWarnVO = new IncomeCallWarnVO();
        incomeCallWarnVO.setUser(feignUserService.getBasic(param.getUid(), param.getAppId()).successData());
        incomeCallWarnVO.setToUserVO(feignUserService.getBasic(toUid, param.getAppId()).successData());
        incomeCallWarnVO.setTimeStamp(System.currentTimeMillis());

        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 20);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        incomeCallWarnVO.setStartTimeStamp(calendar.getTimeInMillis());
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        incomeCallWarnVO.setEndSimeStamp(calendar.getTimeInMillis());

        incomeCallWarnVO.setLife(4 - Optional.ofNullable(redisManager.getInteger(String.format(LIFE, DateUtil.getNowYyyyMMdd(), AppUtil.splicUserId(param.getUid(), toUid)))).orElse(0));
        incomeCallWarnVO.setGiftValue(Optional.ofNullable(redisManager.getInteger(String.format(GIFT_VALUE, DateUtil.getNowYyyyMMdd(), AppUtil.splicUserId(param.getUid(), toUid)))).orElse(0));
        List<Integer> greatMoveStatus = Lists.newArrayListWithCapacity(4);
        Set<Object> greatMove = Optional.ofNullable(redisManager.sGet(String.format(GREAT_MOVE, DateUtil.getNowYyyyMMdd(), AppUtil.splicUserId(param.getUid(), toUid)))).orElse(Collections.emptySet());
        for (int i = 0; i < LabourDay2024Constant.GreatMove.values().length; i++) {
            if (greatMove.contains(i)) {
                greatMoveStatus.add(2);
            } else if (incomeCallWarnVO.getGiftValue() >= LabourDay2024Constant.GreatMove.values()[i].getGiftValue()) {
                greatMoveStatus.add(1);
            } else {
                greatMoveStatus.add(0);
            }
        }
        incomeCallWarnVO.setGreatMoveStatus(greatMoveStatus);
        incomeCallWarnVO.setFinish(Boolean.TRUE);
        for (Integer status: incomeCallWarnVO.getGreatMoveStatus()) {
            if (status != 2) {
                incomeCallWarnVO.setFinish(Boolean.FALSE);
                break;
            }
        }

        return incomeCallWarnVO;
    }

    private PuzzleVO fullPuzzle(BaseParam param, Long toUid) {
        PuzzleVO puzzleVO = new PuzzleVO();
        puzzleVO.setTicketList(new ArrayList<>());
        puzzleVO.setPiece(8);
        for (LabourDay2024Constant.Tour tour : LabourDay2024Constant.Tour.values()) {
            TicketVO ticketVO = new TicketVO();
            ticketVO.setNum(tour.getTicket());
            ticketVO.setStatus(2);
            puzzleVO.getTicketList().add(ticketVO);
        }
        if (Boolean.TRUE.equals(redisManager.hasKey(String.format(HEAD_FRAME_RECEIVE, AppUtil.splicUserId(param.getUid(), toUid), 0)))) {
            puzzleVO.setHeadFrameStatus(2);
        } else {
            puzzleVO.setHeadFrameStatus(1);
        }
        return puzzleVO;
    }

    private PuzzleVO emptyPuzzle(BaseParam param, Long toUid) {
        PuzzleVO puzzleVO = new PuzzleVO();
        puzzleVO.setTicketList(new ArrayList<>());
        puzzleVO.setPiece(0);
        for (LabourDay2024Constant.Tour tour : LabourDay2024Constant.Tour.values()) {
            TicketVO ticketVO = new TicketVO();
            ticketVO.setNum(tour.getTicket());
            ticketVO.setStatus(0);
            puzzleVO.getTicketList().add(ticketVO);
        }
        if (Boolean.TRUE.equals(redisManager.hasKey(String.format(HEAD_FRAME_RECEIVE, AppUtil.splicUserId(param.getUid(), toUid), 1)))) {
            puzzleVO.setHeadFrameStatus(2);
        } else {
            puzzleVO.setHeadFrameStatus(0);
        }
        return puzzleVO;
    }

}
