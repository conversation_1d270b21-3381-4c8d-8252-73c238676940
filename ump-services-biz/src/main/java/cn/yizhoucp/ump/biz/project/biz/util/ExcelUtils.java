package cn.yizhoucp.ump.biz.project.biz.util;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2023/2/27 11:48
 * @Version 1.0
 */
public class ExcelUtils {


    protected static final Logger logger = LoggerFactory.getLogger(ExcelUtils.class);

    /**
     * @param sheetName
     * @param title     表头
     * @param values    数据
     * @param wb        工作簿
     */
    public static HSSFWorkbook getHSSFWorkbook(String sheetName, String[] title, String[][] values, HSSFWorkbook wb) {
        if (Objects.isNull(title) || title.length == 0) {
            return wb;
        }
        if (Objects.isNull(wb)) {
            wb = new HSSFWorkbook();
        }
        HSSFSheet sheet = wb.createSheet(StringUtils.isBlank(sheetName) ? System.currentTimeMillis() + "" : sheetName);
        HSSFRow row = sheet.createRow(0);
        HSSFCellStyle cellStyle = wb.createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        HSSFCell cell;
        // 每一列的标题
        for (int i = 0; i < title.length; i++) {
            cell = row.createCell(i);
            cell.setCellStyle(cellStyle);
            cell.setCellValue(title[i]);
        }
        // 填充每一行的内容
        if (Objects.isNull(values) || values.length == 0) {
            return wb;
        }
        for (int i = 0; i < values.length; i++) {
            row = sheet.createRow(i + 1);
            for (int j = 0; j < values[i].length; j++) {
                row.createCell(j).setCellValue(values[i][j]);
            }
        }
        return wb;
    }

    /**
     * 设置 http 响应头
     *
     * @param response
     * @param fileName
     */
    public static void setResponseHeader(HttpServletResponse response, String fileName) {
        try {
            // 设置响应头
            response.reset();
            //设置响应头
            response.setContentType("application/x-download");
            fileName = "activityRank" + System.currentTimeMillis() + ".xls";
            fileName = new String(fileName.getBytes(), "ISO-8859-1");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        } catch (Exception e) {
            logger.error("setResponseHeader e {}", e);
        }
    }

    /**
     * 设置excel下载响应头属性
     */
    public static void setExcelRespProp(HttpServletResponse response, String rawFileName) throws UnsupportedEncodingException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode(rawFileName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xls");
    }

}
