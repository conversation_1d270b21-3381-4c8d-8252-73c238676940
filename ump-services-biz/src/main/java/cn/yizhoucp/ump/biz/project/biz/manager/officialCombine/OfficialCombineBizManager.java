package cn.yizhoucp.ump.biz.project.biz.manager.officialCombine;

import cn.yizhoucp.coupon.api.enums.CouponType;
import cn.yizhoucp.coupon.api.feign.UserCouponFeignService;
import cn.yizhoucp.coupon.api.vo.UserCouponVO;
import cn.yizhoucp.family.api.client.FamilyInfoFeignService;
import cn.yizhoucp.family.api.client.FamilyMemberFeignService;
import cn.yizhoucp.family.api.dto.family.FamilyInfoDTO;
import cn.yizhoucp.family.api.dto.family.FamilyMemberDTO;
import cn.yizhoucp.family.api.dto.family.query.FamilyMemberQuery;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.TopicConstant;
import cn.yizhoucp.ms.core.base.TopicTagEnum;
import cn.yizhoucp.ms.core.base.auth.SecurityUser;
import cn.yizhoucp.ms.core.base.convert.SexType;
import cn.yizhoucp.ms.core.base.enums.*;
import cn.yizhoucp.ms.core.base.env.DynamicEnvManager;
import cn.yizhoucp.ms.core.base.util.AppUtil;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.coinservices.GiftOrderQueryVO;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.ms.core.vo.productServices.PackageProductVO;
import cn.yizhoucp.ms.core.vo.roomservices.RoomVO;
import cn.yizhoucp.ms.core.vo.userservices.PropagandaUserVO;
import cn.yizhoucp.ms.core.vo.userservices.UserBaseVO;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.order.api.client.NormalOrderFeignService;
import cn.yizhoucp.order.api.enums.OrderStatusEnum;
import cn.yizhoucp.order.api.enums.OrderTypeEnum;
import cn.yizhoucp.order.api.param.ProductItem;
import cn.yizhoucp.order.api.param.addOrder.OfficialCombineParam;
import cn.yizhoucp.order.api.vo.OrderDetailVO;
import cn.yizhoucp.order.api.vo.OrderVO;
import cn.yizhoucp.product.client.UserPackageFeignService;
import cn.yizhoucp.product.dto.BatchUsePackageDetailDTO;
import cn.yizhoucp.product.dto.BatchUsePackageResultDTO;
import cn.yizhoucp.product.enums.PackageUseScene;
import cn.yizhoucp.starter.cassandra.base.enums.MqDelayLevelEnum;
import cn.yizhoucp.starter.cassandra.base.kafka.YzKafkaProducerManager;
import cn.yizhoucp.starter.cassandra.base.rocketmq.RocketmqProducerManager;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.officialCombine.GiftListVO;
import cn.yizhoucp.ump.api.vo.officialCombine.OfficialCombineCancelCheckVO;
import cn.yizhoucp.ump.api.vo.officialCombine.OfficialCombineUserVO;
import cn.yizhoucp.ump.api.vo.officialCombine.enums.CombineStatusEnum;
import cn.yizhoucp.ump.api.vo.officialCombine.enums.GiftBagStatusEnum;
import cn.yizhoucp.ump.api.vo.officialCombine.inner.GiftItem;
import cn.yizhoucp.ump.api.vo.officialCombine.inner.ShoppingCart;
import cn.yizhoucp.ump.api.vo.officialCombine.param.ConfirmCartParam;
import cn.yizhoucp.ump.biz.project.biz.constant.OfficialCombineConstant;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.girlsDay.GirlSDayEveryDayTaskEnum;
import cn.yizhoucp.ump.biz.project.biz.enums.officialCombine.CabinTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.enums.officialCombine.PageIndexEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.astrology.thumpLover.ThumpLoverBizManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.girlSDay.GirlSBizManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.mayConfession.MayConfessionBizManager;
import cn.yizhoucp.ump.biz.project.biz.manager.lovepromise.LovePromiseManager;
import cn.yizhoucp.ump.biz.project.biz.manager.userCoinAccount.UserCoinAccountManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.FeignImService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.FeignRoomService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.coin.FeignCoinService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.lanling.FeignLanlingService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.lanling.official.OldOfficialRemoteService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.order.AddOrderRemoteService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.product.FeignProductService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.product.PackageProductRemoteService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.UserRemoteService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.userAccount.UserAccountRemoteService;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityUrlUtil;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.biz.util.PushTimeUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.OfficialCombineUserJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.OfficialCombineUserDO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bytedance.tester.utils.MapUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static cn.yizhoucp.ms.core.base.ErrorCode.*;
import static cn.yizhoucp.ump.biz.project.biz.constant.OfficialCombineConstant.*;
import static cn.yizhoucp.ump.biz.project.biz.enums.officialCombine.NpcNotifyEnum.*;

/**
 * 官宣房
 *
 * @author: lianghu
 */
@Slf4j
@Service
public class OfficialCombineBizManager {

    @Resource
    private MayConfessionBizManager mayConfessionBizManager;
    @Resource
    private OfficialCombineCommonManager officialCombineCommonManager;
    @Resource
    private OfficialCombineUserJpaDAO officialCombineUserJpaDAO;
    @Resource
    private OfficialCombineV2Manager officialCombineV2Manager;
    @Resource
    private RedisManager redisManager;
    @Resource
    private UserRemoteService userRemoteService;
    @Resource
    private UserAccountRemoteService userAccountRemoteService;
    @Resource
    private FeignRoomService feignRoomService;
    @Resource
    private AddOrderRemoteService addOrderRemoteService;
    @Resource
    private FeignImService feignImService;
    @Resource
    private FeignProductService feignProductService;
    @Resource
    private FeignLanlingService feignLanlingService;
    @Resource
    private OldOfficialRemoteService oldOfficialRemoteService;
    @Value("${spring.profiles.active}")
    private String env;
    @Resource
    private UserCouponFeignService userCouponFeignService;
    @Resource
    private NotifyComponent notifyComponent;
    @Resource
    private FamilyMemberFeignService familyMemberFeignService;
    @Resource
    private FamilyInfoFeignService familyInfoFeignService;
    @Resource
    private PackageProductRemoteService packageProductRemoteService;
    @Resource
    private UserPackageFeignService userPackageFeignService;
    @Resource
    private OfficialCombineAsyncManager officialCombineAsyncManager;
    @Resource
    private UserCoinAccountManager userCoinAccountManager;
    @Resource
    private NormalOrderFeignService normalOrderFeignService;
    @Resource
    private OfficialCombinePushManager officialCombinePushManager;
    @Resource
    private OfficialCombineUnprecedentedLoveManager officialCombineUnprecedentedLoveManager;
    @Resource
    private YzKafkaProducerManager yzKafkaProducerManager;
    @Resource
    private RocketmqProducerManager rocketmqProducerManager;
    @Resource
    private FeignCoinService feignCoinService;
    @Resource
    private LovePromiseManager lovePromiseManager;

    @Resource
    private GirlSBizManager girlSBizManager;

    @Resource
    private ThumpLoverBizManager thumpLoverBizManager;

    @Resource
    private Environment environment;

    @Resource
    private SweetCabinAsyncManager sweetCabinAsyncManager;

    /** 支付定金幂等 redis-string（string） */
    public static final String PAY_DEPOSIT_LOG = "ump:oc:payDepositLog_%s";
    /** 取消官宣失败提示 */
    public static final String CANCEL_CHECK_FAILED = "结成官宣3天后，才可以取消官宣哦~";
    /** 取消官宣结果提示 */
    public static final String CANCEL_OFFICIAL_RESULT = "官宣已取消或已结束~";

    /** 处理的记录 */
    public static final String OFF_EXPIRED_HANDLE_KEY = "ump:offcial:expired:%s";

    /**
     * 官宣增量收益埋点上报
     *
     * @param param
     * @param modelList
     * @return
     */
    public Boolean sendGiftHandle(BaseParam param, List<CoinGiftGivedModel> modelList) {
        for (CoinGiftGivedModel item : modelList) {
            if (OC_PRODUCT_SET.contains(item.getGiftKey())) {
                log.debug("oc 送礼处理 item:{}", JSON.toJSONString(item));
                // 延迟处理
                rocketmqProducerManager.sendDelayMessage(TopicConstant.TOPIC_TRADE_EVENT.getTopicKey(), TopicTagEnum.TOPIC_GIVE_GIFT_LIST_DELAY_EVENT.getTagKey(), JSON.toJSONString(modelList), System.currentTimeMillis() + "", MqDelayLevelEnum.TWENTY_MINUTE);
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 官宣增量收益埋点上报
     *
     * @param param
     * @param modelList
     * @return
     */
    public Boolean sendGiftDelayHandle(BaseParam param, List<CoinGiftGivedModel> modelList) {
        for (CoinGiftGivedModel item : modelList) {
            // 确定统计时间范围
            LocalDateTime now = LocalDateTime.now();
            long startTime = now.minusMinutes(35).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
            long endTime = now.minusMinutes(5).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
            // 查询送礼消耗（排除福袋）
            Map<String, Long> sendGiftValue = feignCoinService.getSendGiftValue(item.getFromUid(), item.getToUid(), startTime, endTime).successData();
            log.debug("oc 送礼延迟处理 item:{}, gift:{}", JSON.toJSONString(item), JSON.toJSONString(sendGiftValue));
            if (MapUtils.isEmpty(sendGiftValue)) {
                return Boolean.FALSE;
            }
            // 上报埋点
            Map<String, Object> params = new HashMap<>();
            params.put("pay_count", Optional.ofNullable(sendGiftValue.get("feeCoin")).orElse(0L));
            params.put("free_count", Optional.ofNullable(sendGiftValue.get("freeCoin")).orElse(0L));
            params.put("amount", Optional.ofNullable(sendGiftValue.get("feeCoin") + sendGiftValue.get("freeCoin")).orElse(0L));
            params.put("to_user_id", Optional.ofNullable(item.getToUid()).orElse(-1L));
            params.put("from_user_id", Optional.ofNullable(item.getFromUid()).orElse(-1L));
            params.put("splic_user_id", Optional.ofNullable(AppUtil.splicUserId(item.getFromUid(), item.getToUid())).orElse(""));
            yzKafkaProducerManager.dataRangerTrack(param.getAppId(), param.getUid(), "official_announcement_1V1_incremental_consumption", params, ServicesNameEnum.ump_services.getCode());

            // 查询送礼消耗（仅面板礼物）
            Integer freeTotal = feignCoinService.getGiftOrderAmount(GiftOrderQueryVO.builder()
                    .fromUid(item.getFromUid())
                    .toUid(item.getToUid())
                    .giftWay("normal")
                    .minCreateTime(startTime)
                    .maxCreateTime(endTime)
                    .fee(Boolean.FALSE)
                    .build()).successData();
            Integer feeTotal = feignCoinService.getGiftOrderAmount(GiftOrderQueryVO.builder()
                    .fromUid(item.getFromUid())
                    .toUid(item.getToUid())
                    .giftWay("normal")
                    .minCreateTime(startTime)
                    .maxCreateTime(endTime)
                    .fee(Boolean.TRUE)
                    .build()).successData();
            // 上报埋点
            Map<String, Object> params2 = new HashMap<>();
            params2.put("pay_count", Optional.ofNullable(feeTotal).orElse(0));
            params2.put("free_count", Optional.ofNullable(freeTotal).orElse(0));
            params2.put("amount", Optional.ofNullable(feeTotal + freeTotal).orElse(0));
            params2.put("to_user_id", Optional.ofNullable(item.getToUid()).orElse(-1L));
            params2.put("from_user_id", Optional.ofNullable(item.getFromUid()).orElse(-1L));
            params2.put("splic_user_id", Optional.ofNullable(AppUtil.splicUserId(item.getFromUid(), item.getToUid())).orElse(""));
            params2.put("from_scenes", item.getFrom());
            yzKafkaProducerManager.dataRangerTrack(param.getAppId(), param.getUid(), "official_announcement_incremental_consumption", params2, ServicesNameEnum.ump_services.getCode());
        }
        return Boolean.TRUE;
    }

    /**
     * 激活福利礼包
     *
     * @param param
     * @param toUid
     * @return
     */
    public Boolean activeGiftBag(BaseParam param, Long toUid) {
        OfficialCombineUserDO combine = officialCombineCommonManager.getCurrentCombineByToUid(param, toUid);
        if (Objects.isNull(combine) || !CombineStatusEnum.COMBINE_STARTED.getCode().equals(combine.getStatus())) {
            return Boolean.FALSE;
        }
        if (!officialCombineCommonManager.everPayFullCheck(param)) {
            return Boolean.FALSE;
        }
        // 发送系统通知
        notifyComponent.chatNotify(param.getUid(), toUid, String.format("恭喜获得官宣福利礼包，立即官宣享受超级福利 <a href=\"%s\">去看看</a>", getActivityUrl() + "?from=single_chat_notify"));

        // 修改福利礼包状态
        combine.setGiftBagStatus(GiftBagStatusEnum.ACTIVATED.getCode());
        combine.setGiftBagCountDown(LocalDateTime.now().plusDays(2).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        officialCombineUserJpaDAO.save(combine);
        return Boolean.TRUE;
    }

    /**
     * 支付定金
     *
     * @param toUid
     * @return
     */
    public Boolean payDeposit(BaseParam param, Long toUid) {
        if (Objects.isNull(toUid)) {
            throw new ServiceException(ErrorCode.MISS_PARAM);
        }
        
        // 流程验证
        UserVO user = userRemoteService.getBasic(param.getUid(), param.getAppId(), Boolean.TRUE);
        UserVO toUser = userRemoteService.getBasic(toUid, param.getAppId(), Boolean.TRUE);
        if (Objects.isNull(user) || Objects.isNull(toUser)) {
            return Boolean.FALSE;
        }
        OfficialCombineUserDO userCombine = officialCombineCommonManager.getCurrentCombineByStatus(param, user, CombineStatusEnum.COMBINE_STARTED);
        OfficialCombineUserDO toUserCombine = officialCombineCommonManager.getCurrentCombineByStatus(BaseParam.builder()
                .appId(param.getAppId())
                .unionId(param.getUnionId())
                .uid(toUid).build(), toUser, CombineStatusEnum.COMBINE_STARTED);
        // 双方任意一方存在进行中流程 => 终止
        if (Objects.nonNull(userCombine) || Objects.nonNull(toUserCombine)) {
            log.info("一方正在流程中 userCombine:{}, toUserCombine:{}", JSON.toJSONString(userCombine), JSON.toJSONString(toUserCombine));
            throw new ServiceException(ErrorCode.OFFICIAL_BINE_ERROR_181001);
        }

        // 双方之间存在已结成未完结流程 => 终止
        OfficialCombineUserDO combinedProcess = officialCombineCommonManager.getCurrentCombineByStatusAndToUid(param, toUid, CombineStatusEnum.COMBINE_COMPLETED);
        if (Objects.nonNull(combinedProcess)) {
            log.info("双方之间存在未完结流程 combine:{}", JSON.toJSONString(combinedProcess));
            throw new ServiceException(ErrorCode.OFFICIAL_BINE_ERROR_181001);
        }
        // 双方之间存在旧官宣已结成流程 => 终止
        if (LocalDateTime.now().isBefore(OfficialCombineConstant.OLD_COMBINE_EXPIRED_TIME)) {
            PropagandaUserVO oldCombine = oldOfficialRemoteService.getPropagandaUserVO(param.getAppId(), param.getUid());
            if (Objects.nonNull(oldCombine) && Boolean.TRUE.equals(oldCombine.getStatus())
                    && Objects.nonNull(oldCombine.getWomanUser()) && Objects.nonNull(oldCombine.getManUser())) {
                if ((SexType.MAN.equals(user.getSex()) && toUid.equals(oldCombine.getWomanUser().getId()))
                        || (SexType.WOMAN.equals(user.getSex()) && toUid.equals(oldCombine.getManUser().getId()))) {
                    log.info("双方之间存在未结束的旧官宣流程 combine:{}", JSON.toJSONString(oldCombine));
                    throw new ServiceException(ErrorCode.OFFICIAL_BINE_ERROR_181001);
                }
            }
        }

        // 查询商品信息
        List<PackageProductVO> productVOList = feignProductService.getListByIds(param.getAppId(), param.getUnionId(), JSON.toJSONString(Lists.newArrayList(OfficialCombineConstant.DEPOSIT_PRODUCT_ID))).successData();
        if (CollectionUtils.isEmpty(productVOList)) {
            log.error("商品信息为空 uid:{}", param.getUid());
            throw new ServiceException(ErrorCode.ORDER_ERROR_NO_PRODUCT);
        }
        PackageProductVO product = productVOList.get(0);

        // 防止重复
        if (!redisManager.setnx(String.format(PAY_DEPOSIT_LOG, AppUtil.splicUserId(param.getUid(), toUid)), System.currentTimeMillis(), RedisManager.ONE_MINUTE_SECONDS * 10)) {
            log.warn("oc 重复处理 uid:{}, toUid:{}", param.getUid(), toUid);
            throw new ServiceException(MISS_PARAM, "定金已被支付");
        }

        // 创建订单
        List<ProductItem> productItems = Lists.newArrayList(ProductItem.builder().productId(OfficialCombineConstant.DEPOSIT_PRODUCT_ID).num(1).build());
        OrderVO orderVO = addOrderRemoteService.addOfficialCombineOrder(OfficialCombineParam.builder()
                .appId(param.getAppId())
                .unionId(param.getUnionId())
                .uid(param.getUid())
                .orderType(OrderTypeEnum.OFFICIAL_COMBINE_PRE_ORDER.getType())
                .productItemList(productItems)
                .appScene(AppScene.platform)
                .appFunction(AppFunctionEnum.buy_official_publicity_gift)
                .memo("支付官宣定金").build(), null);
        if (Objects.isNull(orderVO)) {
            redisManager.delete(String.format(PAY_DEPOSIT_LOG, AppUtil.splicUserId(param.getUid(), toUid)));
            throw new ServiceException(OFFICIAL_BINE_ERROR_181007);
        }

        // 保存官宣信息
        Long familyId = getFamilyIdByUid(param.getUid());
        Long toFamilyId = getFamilyIdByUid(toUid);
        OfficialCombineUserDO combine = officialCombineUserJpaDAO.save(getDefault(param, toUid, orderVO.getOrderId(), familyId, toFamilyId));

        officialCombineAsyncManager.payDeposit(param.getAppId(), param.getUid(), combine.getId());

        // 通知被发起方
        notifyComponent.npcNotify(toUid, String.format(PAY_DEPOSIT.getMsg(), user.getName(), getActivityUrl() + "?from=npc_talk"));
        // 通知双方家族长
        FamilyInfoDTO family = familyInfoFeignService.findById(familyId).successData();
        FamilyInfoDTO toFamily = familyInfoFeignService.findById(toFamilyId).successData();
        String familyText = Optional.ofNullable(family).map(f -> f.getName() + " 家族的").orElse("");
        String toFamilyText = Optional.ofNullable(toFamily).map(f -> f.getName() + " 家族的").orElse("");
        if (Objects.nonNull(family) && Objects.nonNull(family.getOwnerUid())) {
            notifyComponent.npcNotify(family.getOwnerUid(), String.format(PAY_DEPOSIT_FAMILY.getMsg(), familyText, "app://router.nuan.chat/user/profile?uid=" + user.getId(), user.getName(), toFamilyText, "app://router.nuan.chat/user/profile?uid=" + toUser.getId(), toUser.getName()));
        }
        if (Objects.nonNull(toFamily) && Objects.nonNull(toFamily.getOwnerUid())) {
            notifyComponent.npcNotify(toFamily.getOwnerUid(), String.format(PAY_DEPOSIT_FAMILY.getMsg(), familyText, "app://router.nuan.chat/user/profile?uid=" + user.getId(), user.getName(), toFamilyText, "app://router.nuan.chat/user/profile?uid=" + toUser.getId(), toUser.getName()));
        }
        notifyComponent.npcNotify(toUid, String.format(PAY_DEPOSIT_EXPIRED_DAY.getMsg(), user.getName(), 3, getActivityUrl() + "?from=npc_talk&toUid=" + user.getId()));
        notifyComponent.npcNotify(user.getId(), String.format(PAY_DEPOSIT_EXPIRED_DAY.getMsg(), toUser.getName(), 3, getActivityUrl() + "?from=npc_talk&toUid=" + toUser.getId()));

        // 发送私聊系统通知
        notifyComponent.chatNotify(param.getUid(), toUid, String.format("官宣定金已支付，快去挑选你们的官宣礼物吧 <a href=\"%s\">去看看</a>", getActivityUrl() + "?from=single_chat_notify"));
        officialCombinePushManager.pushInitiateOfficialCombinePop(param.getAppId(), param.getUid(), toUid);
        log.debug("发送发起官宣卡片");
        // todo @六月 2023/4/19 旷世恋人
        officialCombineUnprecedentedLoveManager.payDepositNotify(param);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("type", "initiate");
        jsonObject.put("fromUid", param.getUid());
        jsonObject.put("toUid", toUid);
        rocketmqProducerManager.sendNormalMessage(TopicConstant.TOPIC_SOCIAL_NETWORK.getTopicKey(), TopicTagEnum.INITIATE_FORM_OFFICIAL_COMBINE.getTagKey(), jsonObject.toJSONString(), null);

        return Boolean.TRUE;
    }

    /**
     * 提交商品
     *
     * @param
     * @return
     */
    public Boolean confirmProduct(BaseParam param, Long toUid, Long productId) {
        OfficialCombineUserDO combine = officialCombineCommonManager.getCurrentCombineByToUid(param, toUid);
        if (Objects.isNull(combine) || !CombineStatusEnum.COMBINE_STARTED.getCode().equals(combine.getStatus())) {
            throw new ServiceException(OFFICIAL_BINE_ERROR_181004);
        }

        // 1. 页面索引验证
        if (!PageIndexEnum.PRODUCT_LIST_PAGE.getIndex().equals(combine.getPageIndex())
                && !PageIndexEnum.SKU_PAGE.getIndex().equals(combine.getPageIndex())) {
            throw new ServiceException(OFFICIAL_BINE_ERROR_181003);
        }

        // 2. 查询商品详情
        List<PackageProductVO> productVOList = feignProductService.getListByIds(param.getAppId(), param.getUnionId(), JSON.toJSONString(Lists.newArrayList(productId))).successData();
        if (CollectionUtils.isEmpty(productVOList)) {
            throw new ServiceException(ORDER_ERROR_NO_PRODUCT);
        }

        // 3. 更新官宣信息
        if (PageIndexEnum.PRODUCT_LIST_PAGE.getIndex().equals(combine.getPageIndex())) {
            combine.pushPageIndex();
        }
        combine.setLoveTokenId(productId);
        combine.setLoveTokenIcon(productVOList.get(0).getIcon());
        officialCombineUserJpaDAO.save(combine);
        return Boolean.TRUE;
    }


    /**
     * 提交购物车
     *
     * @param
     * @return
     */
    public Boolean commitSkuInfo(ConfirmCartParam cartParam) {
        log.info("oc 提交购物车 param:{}", JSON.toJSONString(cartParam));
        OfficialCombineUserDO combine = officialCombineCommonManager.getCurrentCombineByToUid(cartParam, cartParam.getToUid());
        if (Objects.isNull(combine) || !CombineStatusEnum.COMBINE_STARTED.getCode().equals(combine.getStatus())) {
            throw new ServiceException(OFFICIAL_BINE_ERROR_181004);
        }
        if (!PageIndexEnum.SKU_PAGE.getIndex().equals(combine.getPageIndex())) {
            throw new ServiceException(MISS_PARAM);
        }

        // 更新购物车（放缓存）
        redisManager.set(String.format(OfficialCombineConstant.OFFICIAL_COMBINE_CART, cartParam.getUid() + "_" + cartParam.getToUid()), JSON.toJSONString(cartParam), RedisManager.ONE_DAY_SECONDS * 28);

        if (Boolean.TRUE.equals(cartParam.getConfirm())) {
            // 根据双方购物车组合商品列表
            List<ProductItem> productItems = getProductItemListOfBothCart(cartParam.getUid(), cartParam.getToUid(), combine.getLoveTokenId());
            // 查询优惠券
            Long couponId = null;
            UserCouponVO userCouponVO = userCouponFeignService.getMaxPriceCouponByType(cartParam.getAppId(), cartParam.getUnionId(), cartParam.getVestChannel(), cartParam.getUid(),
                    StringUtils.join(Lists.newArrayList(CouponType.OC_COIN_COUPON.getCode(), CouponType.OC_COUPON_LIMIT_USER.getCode()), ","), cartParam.getToUid()).successData();
            if (Objects.nonNull(userCouponVO)) {
                couponId = userCouponVO.getUserCouponId();
            }
            // 创建订单
            OrderVO order = addOrderRemoteService.addOfficialCombineOrder(OfficialCombineParam.builder()
                            .appId(cartParam.getAppId())
                            .unionId(cartParam.getUnionId())
                            .uid(cartParam.getUid())
                            .toUid(cartParam.getToUid())
                            .couponId(couponId)
                            .orderType(OrderTypeEnum.OFFICIAL_COMBINE_ORDER.getType())
                            .productItemList(productItems)
                            .appScene(AppScene.platform)
                            .appFunction(AppFunctionEnum.buy_official_publicity_gift)
                            .memo("支付官宣订单").build(),
                    () -> new ServiceException(OFFICIAL_BINE_ERROR_181002));

            // 推进页面索引
            combine.setOrderId(order.getOrderId());
            officialCombineUserJpaDAO.save(combine.pushPageIndex());

            officialCombineAsyncManager.cartProductPush(cartParam.getAppId(), cartParam.getUid(), combine.getId(), combine.getLoveTokenId());
            officialCombineAsyncManager.cartGiftPush(cartParam.getAppId(), cartParam.getUid(), combine.getId(), productItems);
        }
        return Boolean.TRUE;
    }

    /**
     * 更改清单 这时候从结算 退回到 选择库存
     *
     * @param param
     * @param toUid
     * @return
     */
    public Boolean changeCart(BaseParam param, Long toUid) {
        OfficialCombineUserDO combine = officialCombineCommonManager.getCurrentCombineByToUid(param, toUid);
        if (Objects.isNull(combine) || !CombineStatusEnum.COMBINE_STARTED.getCode().equals(combine.getStatus())) {
            throw new ServiceException(OFFICIAL_BINE_ERROR_181004);
        }
        // 页面索引验证
        if (!PageIndexEnum.SETTLE_PAGE.getIndex().equals(combine.getPageIndex())) {
            throw new ServiceException(OFFICIAL_BINE_ERROR_181003);
        }
        //取消订单
        Boolean aBoolean = normalOrderFeignService.cannelOrder(combine.getOrderId()).successData();
        if (!aBoolean) {
            return Boolean.FALSE;
        }

        //设置会退
        combine.setOrderId(null);
        combine.setPageIndex(PageIndexEnum.SKU_PAGE.getIndex());
        officialCombineUserJpaDAO.save(combine);
        return Boolean.TRUE;
    }


    /**
     * 支付订单
     *
     * @param
     * @return
     */
    public Boolean payOrder(BaseParam param, Long toUid) {
        OfficialCombineUserDO combine = officialCombineCommonManager.getCurrentCombineByToUid(param, toUid);
        if (Objects.isNull(combine) || !CombineStatusEnum.COMBINE_STARTED.getCode().equals(combine.getStatus())) {
            throw new ServiceException(OFFICIAL_BINE_ERROR_181004);
        }

        // 页面索引验证
        if (!PageIndexEnum.SETTLE_PAGE.getIndex().equals(combine.getPageIndex())) {
            throw new ServiceException(OFFICIAL_BINE_ERROR_181003);
        }

        // 订单验证
        Long orderId = combine.getOrderId();
        if (Objects.isNull(orderId)) {
            throw new ServiceException(OFFICIAL_BINE_ERROR_181009);
        }
        OrderVO orderVO = normalOrderFeignService.getOrder(orderId).successData();
        if (Objects.isNull(orderVO)) {
            throw new ServiceException(OFFICIAL_BINE_ERROR_181009);
        }

        // 订单流转
        addOrderRemoteService.addOfficialCombineOrder(OfficialCombineParam.builder()
                        .appId(param.getAppId())
                        .unionId(param.getUnionId())
                        .uid(param.getUid())
                        .orderId(orderId)
                        .orderType(OrderTypeEnum.OFFICIAL_COMBINE_ORDER.getType())
                        .appScene(AppScene.platform)
                        .appFunction(AppFunctionEnum.buy_official_publicity_gift)
                        .memo("支付官宣订单").build(),
                () -> new ServiceException(OFFICIAL_BINE_ERROR_181007));

        // 更新 ownerId，推进页面索引
        combine.setOwnerId(param.getUid());
        combine.setExpiredTime(LocalDateTime.now().plusDays(14));
        combine.setGiftBagStatus(GiftBagStatusEnum.CANNOT_BE_ACTIVATED.getCode());
        officialCombineUserJpaDAO.save(combine.pushPageIndex());

        // 清空购物车
        redisManager.delete(String.format(OfficialCombineConstant.OFFICIAL_COMBINE_CART, param.getUid() + "_" + toUid));
        redisManager.delete(String.format(OfficialCombineConstant.OFFICIAL_COMBINE_CART, toUid + "_" + param.getUid()));

        officialCombineAsyncManager.payOrder(param.getAppId(), param.getUid(), combine.getId(), combine.getLoveTokenId(), orderVO.getOrderPayPrice());

        // 2024在一起活动，倒计时
//        togetherIn2024BizManager.addCountDownTimes(param.getUid(), toUid);
        //五月告白日 替换2024
        mayConfessionBizManager.sendPrizeByOrderPayPrice(param.getUid(), toUid, combine.getOrderId());
        mayConfessionBizManager.addCountDownTimes(param.getUid(), toUid);

        // 小助手通知
        UserVO user = userRemoteService.getBasic(param.getUid(), param.getAppId(), Boolean.TRUE);
        UserVO toUser = userRemoteService.getBasic(toUid, param.getAppId(), Boolean.TRUE);
        UserVO maleUser = user;
        UserVO femaleUser = toUser;
        if (SexType.WOMAN.equals(user.getSex())) {
            maleUser = toUser;
            femaleUser = user;
        }
        if (Objects.nonNull(user)) {
            String orderDesc = getOrderDesc(orderVO);
            notifyComponent.npcNotify(param.getUid(), String.format(PAY_ORDER.getMsg(), orderDesc, orderVO.getOrderPrice(), getActivityUrl() + "?from=npc_talk"));
            notifyComponent.npcNotify(toUid, String.format(PAY_ORDER_TO.getMsg(), user.getName(), orderDesc, orderVO.getOrderPrice(), getActivityUrl() + "?from=npc_talk"));
            // 通知双方家族长
            FamilyInfoDTO maleFamily = familyInfoFeignService.findById(combine.getMaleFamilyId()).successData();
            FamilyInfoDTO femaleFamily = familyInfoFeignService.findById(combine.getFemaleFamilyId()).successData();
            String maleFamilyText = Optional.ofNullable(maleFamily).map(f -> f.getName() + " 家族的").orElse("");
            String femaleFamilyText = Optional.ofNullable(femaleFamily).map(f -> f.getName() + " 家族的").orElse("");
            if (Objects.nonNull(maleFamily) && Objects.nonNull(maleFamily.getOwnerUid())) {
                notifyComponent.npcNotify(maleFamily.getOwnerUid(), String.format(PAY_ORDER_FAMILY.getMsg(), maleFamilyText, getActivityUrl() + "?from=npc_talk", maleUser.getName(), femaleFamilyText, getActivityUrl() + "?from=npc_talk", femaleUser.getName(), orderDesc, orderVO.getOrderPrice()));
            }
            if (Objects.nonNull(femaleFamily) && Objects.nonNull(femaleFamily.getOwnerUid())) {
                notifyComponent.npcNotify(femaleFamily.getOwnerUid(), String.format(PAY_ORDER_FAMILY.getMsg(), maleFamilyText, getActivityUrl() + "?from=npc_talk", maleUser.getName(), femaleFamilyText, getActivityUrl() + "?from=npc_talk", femaleUser.getName(), orderDesc, orderVO.getOrderPrice()));
            }
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("type", "form");
        jsonObject.put("fromUid", param.getUid());
        jsonObject.put("toUid", toUid);
        rocketmqProducerManager.sendNormalMessage(TopicConstant.TOPIC_SOCIAL_NETWORK.getTopicKey(), TopicTagEnum.INITIATE_FORM_OFFICIAL_COMBINE.getTagKey(), jsonObject.toJSONString(), null);

        return Boolean.TRUE;
    }


    private String getOrderDesc(OrderVO orderVO) {
        List<String> orderDesc = Lists.newArrayList();
        List<OrderDetailVO> orderDetails = orderVO.getOrderDetails();
        if (CollectionUtils.isEmpty(orderDetails)) {
            return "";
        }
        for (OrderDetailVO item : orderDetails) {
            PackageProductVO product = JSONObject.parseObject(item.getProductSnapshot(), PackageProductVO.class);
            orderDesc.add(product.getName() + "*" + item.getNum());
        }
        return StringUtils.join(orderDesc, "、");
    }

    /**
     * 预约 & 编辑官宣时间
     *
     * @param toUid
     * @return
     */
    public Boolean reserveDateTime(BaseParam param, Long toUid, String dateTime) {
        OfficialCombineUserDO combine = officialCombineCommonManager.getCurrentCombineByToUid(param, toUid);
        if (Objects.isNull(combine) || !CombineStatusEnum.COMBINE_STARTED.getCode().equals(combine.getStatus())) {
            throw new ServiceException(OFFICIAL_BINE_ERROR_181004);
        }

        if (Objects.isNull(combine.getLeftEditTimes()) || combine.getLeftEditTimes() < 1) {
            throw new ServiceException(OFFICIAL_BINE_ERROR_181006);
        }

        // 更新官宣信息
        combine.setLeftEditTimes(combine.getLeftEditTimes() - 1);
        combine.setStartTime(LocalDateTime.parse(dateTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        officialCombineUserJpaDAO.save(combine);

        if (combine.getLeftEditTimes().equals(1)) {
            officialCombineAsyncManager.reserveTime(param.getAppId(), param.getUid(), combine.getId());
        } else {
            officialCombineAsyncManager.editTime(param.getAppId(), param.getUid(), combine.getId());
        }

        // 小助手通知
        UserVO user = userRemoteService.getBasic(param.getUid(), param.getAppId(), Boolean.TRUE);
        UserVO toUser = userRemoteService.getBasic(toUid, param.getAppId(), Boolean.TRUE);
        UserVO maleUser = user;
        UserVO femaleUser = toUser;
        if (SexType.WOMAN.equals(user.getSex())) {
            maleUser = toUser;
            femaleUser = user;
        }
        if (combine.getLeftEditTimes().equals(1)) {
            notifyComponent.npcNotify(param.getUid(), String.format(RESERVE_DATE.getMsg(), dateTime));
            notifyComponent.npcNotify(toUid, String.format(RESERVE_DATE.getMsg(), dateTime));
            // 通知双方家族长
            FamilyInfoDTO maleFamily = familyInfoFeignService.findById(combine.getMaleFamilyId()).successData();
            FamilyInfoDTO femaleFamily = familyInfoFeignService.findById(combine.getFemaleFamilyId()).successData();
            String maleFamilyText = Optional.ofNullable(maleFamily).map(f -> f.getName() + " 家族的").orElse("");
            String femaleFamilyText = Optional.ofNullable(femaleFamily).map(f -> f.getName() + " 家族的").orElse("");
            if (Objects.nonNull(maleFamily) && Objects.nonNull(maleFamily.getOwnerUid())) {
                notifyComponent.npcNotify(maleFamily.getOwnerUid(), String.format(RESERVE_DATE_FAMILY.getMsg(), maleFamilyText, maleUser.getName(), femaleFamilyText, femaleUser.getName(), dateTime));
            }
            if (Objects.nonNull(femaleFamily) && Objects.nonNull(femaleFamily.getOwnerUid())) {
                notifyComponent.npcNotify(femaleFamily.getOwnerUid(), String.format(RESERVE_DATE_FAMILY.getMsg(), maleFamilyText, maleUser.getName(), femaleFamilyText, femaleUser.getName(), dateTime));
            }
        } else {
            notifyComponent.npcNotify(param.getUid(), String.format(MODIFY_DATE.getMsg(), dateTime));
            notifyComponent.npcNotify(toUid, String.format(MODIFY_DATE.getMsg(), dateTime));
            // 通知双方家族长
            FamilyInfoDTO maleFamily = familyInfoFeignService.findById(combine.getMaleFamilyId()).successData();
            FamilyInfoDTO femaleFamily = familyInfoFeignService.findById(combine.getFemaleFamilyId()).successData();
            String maleFamilyText = Optional.ofNullable(maleFamily).map(f -> f.getName() + " 家族的").orElse("");
            String femaleFamilyText = Optional.ofNullable(femaleFamily).map(f -> f.getName() + " 家族的").orElse("");
            if (Objects.nonNull(maleFamily) && Objects.nonNull(maleFamily.getOwnerUid())) {
                notifyComponent.npcNotify(maleFamily.getOwnerUid(), String.format(MODIFY_DATE_FAMILY.getMsg(), maleFamilyText, maleUser.getName(), femaleFamilyText, femaleUser.getName(), dateTime));
            }
            if (Objects.nonNull(femaleFamily) && Objects.nonNull(femaleFamily.getOwnerUid())) {
                notifyComponent.npcNotify(femaleFamily.getOwnerUid(), String.format(MODIFY_DATE_FAMILY.getMsg(), maleFamilyText, maleUser.getName(), femaleFamilyText, femaleUser.getName(), dateTime));
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 开启 & 进入官宣房
     *
     * @param toUid
     * @return
     */
    public OfficialCombineUserVO enterRoom(BaseParam param, Long toUid) {
        OfficialCombineUserDO combine = officialCombineCommonManager.getCurrentCombineByToUid(param, toUid);
        if (Objects.isNull(combine) || !CombineStatusEnum.COMBINE_STARTED.getCode().equals(combine.getStatus())) {
            throw new ServiceException(OFFICIAL_BINE_ERROR_181004);
        }

        // 页面索引验证
        if (!PageIndexEnum.COMBINE_PAGE.getIndex().equals(combine.getPageIndex())) {
            throw new ServiceException(MISS_PARAM);
        }

        // 开启官宣房
        Long superAdminId = combine.getOwnerId().equals(param.getUid()) ? toUid : param.getUid();
        log.debug("enterRoom uid{} toUid{} super{}", param.getUid(), toUid, superAdminId);
        RoomVO roomVO = feignRoomService.serverOperationRoom(param.getAppId(), combine.getOwnerId(), superAdminId, null, "ROMANTIC_WEDDING", "官宣房间", "官宣房间", "https://newsns.myrightone.com/res/activity/officialCombine/oc-bg.png", "MIC_MODE_FREEDOM", null, "ROOM_SOURCE_PERSONAL", "WEDDING_GAME", 11).successData();
        if (Objects.isNull(roomVO)) {
            log.error("开启官宣房失败 uid:{}, toUid:{}", param.getUid(), toUid);
            throw new ServiceException(OFFICIAL_BINE_ERROR_181005);
        }

        // 更新官宣信息
        combine.setRoomId(roomVO.getRoomId());
        officialCombineUserJpaDAO.save(combine);

        // 扑通love恢复官宣关系
        thumpLoverBizManager.resumeRelations(param.getUid(), toUid);

        return combine.convert2VO();
    }

    /**
     * 完成官宣
     *
     * @param toUid
     * @return
     */
    public Boolean completeCombine(BaseParam param, Long toUid) {
        OfficialCombineUserDO combine = officialCombineCommonManager.getCurrentCombineByToUid(param, toUid);
        if (Objects.isNull(combine) || !CombineStatusEnum.COMBINE_STARTED.getCode().equals(combine.getStatus())) {
            throw new ServiceException(OFFICIAL_BINE_ERROR_181004);
        }

        // 调整官宣信息，保存女生信息快照
        UserBaseVO user = userRemoteService.getBasicAll(param.getAppId(), param.getUid(), Boolean.FALSE);
        UserBaseVO toUser = userRemoteService.getBasicAll(param.getAppId(), toUid, Boolean.FALSE);
        UserBaseVO female = toUser;
        if (SexType.WOMAN.equals(user.getSex())) {
            female = user;
        }
        combine.setFemaleName(female.getName());
        combine.setFemaleIcon(female.getAvatar());
        combine.setStatus(CombineStatusEnum.COMBINE_COMPLETED.getCode());
        combine.setEndTime(LocalDateTime.now());
        combine.setExpiredTime(LocalDateTime.now().plusDays(30));
        combine.setConfessStatus(true);
        officialCombineUserJpaDAO.save(combine.pushPageIndex());

        // 关闭官宣房
        Boolean close = feignRoomService.closeFreeRoom(param.getAppId(), combine.getRoomId()).successData();
        if (Objects.isNull(close) || Boolean.FALSE.equals(close)) {
            log.error("关闭官宣房失败 roomId:{}", combine.getRoomId());
        }

        // 埋点上报
        officialCombineAsyncManager.completed(param.getAppId(), param.getUid(), combine);

        // 下发积分
        if (!redisManager.setnx(String.format(OfficialCombineConstant.OC_COMPLETED_SEND_KEY, param.getUid()), System.currentTimeMillis(), 10L)) {
            throw new ServiceException(OFFICIAL_BINE_ERROR_181013);
        }
        // 下发定金积分 给 付定金的对方 exp: a付定金 b付全款 定金积分下发给 b
        if (Objects.isNull(combine.getPreOrderId())) {
            log.error("没有付定金 combine{}", JSONObject.toJSONString(combine));
        }
        OrderVO orderVO = normalOrderFeignService.getOrder(combine.getPreOrderId()).successData();
        if (Objects.isNull(orderVO) || Objects.isNull(orderVO.getUid())) {
            log.debug("completeCombine order{} ", JSONObject.toJSONString(orderVO));
            throw new ServiceException(ErrorCode.ORDER_ERROR_NO_PRODUCT);
        }
        Long to = combine.getMaleUid().toString().equals(orderVO.getUid().toString()) ? combine.getFemaleUid() : combine.getMaleUid();
        Boolean returnResult = userCoinAccountManager.sendPoint(param.getUnionId(), param.getAppId(), to,
                AppScene.platform, AppFunctionEnum.complete_official_combine_send_point.getCode(),
                22000L, 0L, "", "", "完成官宣下发积分");
        if (Boolean.FALSE.equals(returnResult)) {
            throw new ServiceException(ErrorCode.COIN_ORDER_ADD_ERROR);
        }
        log.debug("下发积分结果{}", returnResult);

        // 小助手通知
        notifyComponent.npcNotify(param.getUid(), String.format(COMBINE_COMPLETE.getMsg(), getActivityUrl() + "?from=npc_talk&toUid=" + toUid));
        notifyComponent.npcNotify(toUid, String.format(COMBINE_COMPLETE.getMsg(), getActivityUrl() + "?from=npc_talk&toUid=" + param.getUid()));
        officialCombineUnprecedentedLoveManager.completeOfficialCombine(combine);

        // 2024在一起活动，梯度奖励
//        togetherIn2024BizManager.sendPrizeByOrderPayPrice(param.getUid(), toUid, combine.getOrderId());

        girlSBizManager.finishDailyTask(combine.getMaleUid(), combine.getFemaleUid(), GirlSDayEveryDayTaskEnum.confession, 1);

        lovePromiseManager.notifyUserNewCardOfficialCombineCompleted(param.getAppId(), param.getUid(), toUid);

        // 官宣 扑通love解除关系
        thumpLoverBizManager.terminationOfRelationship(param.getUid(), toUid);

        // 初始化小屋
        sweetCabinAsyncManager.initCabinAsync(param.getAppId(), combine.getOwnerId(), combine.getOwnerId().equals(param.getUid()) ? toUid : param.getUid(), CabinTypeEnum.officialCombine.getCode());

        return Boolean.TRUE;
    }

    /**
     * 取消官宣
     *
     * @param toUid
     * @return
     */
    public OfficialCombineCancelCheckVO cancelCombine(BaseParam param, Long toUid) {
        if (!redisManager.setnx(String.format(OfficialCombineConstant.OC_CANCEL_ORDER_PROCESS_KEY, param.getUid()), System.currentTimeMillis(), 10L)) {
            throw new ServiceException(OFFICIAL_BINE_ERROR_181013);
        }
        OfficialCombineUserDO combine = officialCombineCommonManager.getCurrentCombineByToUid(param, toUid);
        if (Objects.isNull(combine) && LocalDateTime.now().isBefore(OfficialCombineConstant.OLD_COMBINE_EXPIRED_TIME)) {
            PropagandaUserVO oldCombine = oldOfficialRemoteService.getPropagandaUserVO(MDCUtil.getCurAppIdByMdc(), param.getUid());
            if (Objects.isNull(oldCombine) || Boolean.FALSE.equals(oldCombine.getStatus())) {
                throw new ServiceException(OFFICIAL_BINE_ERROR_181004);
            }
            // 旧官宣关系直接取消
            Boolean edit = oldOfficialRemoteService.editPropagandaUser(param.getAppId(), oldCombine.getId(), oldCombine.getManUser().getId(), oldCombine.getWomanUser().getId(), CommonStatus.disable.getCode(), oldCombine.getRingKey());
            if (Objects.isNull(edit) || Boolean.FALSE.equals(edit)) {
                throw new ServiceException(OFFICIAL_BINE_ERROR_181012);
            }
            officialCombineAsyncManager.cancelOrder(param.getAppId(), param.getUid(), oldCombine.getId());
            return OfficialCombineCancelCheckVO.builder().checkResult(Boolean.TRUE).build();
        }
        if (Objects.isNull(combine)) {
            return OfficialCombineCancelCheckVO.builder().checkResult(Boolean.FALSE).failedMsg(CANCEL_OFFICIAL_RESULT).build();
        }
        // 非付款方 取消 结成状态官宣 资格检查
        if (!Objects.equals(param.getUid(), combine.getOwnerId()) && Objects.nonNull(combine.getEndTime())) {
            boolean compareRes = LocalDateTime.now().isBefore(combine.getEndTime().plusDays(4).with(LocalTime.MIN));
            if (CombineStatusEnum.COMBINE_COMPLETED.getCode().equals(combine.getStatus()) && compareRes) {
                log.error("取消官宣失败 uid:{}, owner:{}", param.getUid(), combine.getOwnerId());
                return OfficialCombineCancelCheckVO.builder().checkResult(Boolean.FALSE).failedMsg(CANCEL_CHECK_FAILED).build();
            }
        }

        OrderVO orderVO = null;
        if (Objects.nonNull(combine.getOrderId())) {
            orderVO = normalOrderFeignService.getOrder(combine.getOrderId()).successData();
        }

        // 已支付订单 && 未结成
        if (CombineStatusEnum.COMBINE_STARTED.getCode().equals(combine.getStatus()) && Objects.nonNull(orderVO) && OrderStatusEnum.ORDER_PAID.getCode().equals(orderVO.getStatus())) {
            // 逆向处理
            cancelHandle(param, combine);
            // 小助手通知
            UserVO user = userRemoteService.getBasic(param.getUid(), param.getAppId(), Boolean.TRUE);
            UserVO toUser = userRemoteService.getBasic(toUid, param.getAppId(), Boolean.TRUE);
            notifyComponent.npcNotify(param.getUid(), PAY_ORDER_CANCEL.getMsg());
            if (Objects.nonNull(toUser)) {
                notifyComponent.npcNotify(toUid, String.format(PAY_ORDER_CANCEL_TO.getMsg(), user.getName()));
            }
            officialCombineAsyncManager.cancelOrder(param.getAppId(), param.getUid(), combine.getId());
        } else if (CombineStatusEnum.COMBINE_STARTED.getCode().equals(combine.getStatus())) {
            // 已支付定金且未支付全款
            if (Objects.isNull(orderVO) || (Objects.nonNull(orderVO) && OrderStatusEnum.ORDER_CREATED.getCode().equals(orderVO.getStatus()))) {
                //定金回退
                cancelDepositHandle(param, combine);
                // 小助手通知
                UserVO user = userRemoteService.getBasic(param.getUid(), param.getAppId(), Boolean.TRUE);
                UserVO toUser = userRemoteService.getBasic(toUid, param.getAppId(), Boolean.TRUE);
                Long owner = combine.getOwnerId().equals(param.getUid()) ? param.getUid() : toUid;
                UserVO nonOwner = !combine.getOwnerId().equals(toUid) ? toUser : user;
                notifyComponent.npcNotify(owner, String.format(PAY_DEPOSIT_OWNER_CANCEL.getMsg(), nonOwner.getName()));
                notifyComponent.npcNotify(param.getUid(), PAY_DEPOSIT_CANCEL.getMsg());
                notifyComponent.npcNotify(toUid, String.format(PAY_DEPOSIT_CANCEL_TO.getMsg(), user.getName()));
                // 通知双方家族长
                if (Objects.nonNull(combine.getMaleFamilyId())) {
                    FamilyInfoDTO maleFamily = familyInfoFeignService.findById(combine.getMaleFamilyId()).successData();
                    if (Objects.nonNull(maleFamily) && Objects.nonNull(maleFamily.getOwnerUid())) {
                        notifyComponent.npcNotify(maleFamily.getOwnerUid(), String.format(PAY_ORDER_CANCEL_FAMILY.getMsg(), user.getName()));
                    }
                }
                if (Objects.nonNull(combine.getFemaleFamilyId())) {
                    FamilyInfoDTO femaleFamily = familyInfoFeignService.findById(combine.getMaleFamilyId()).successData();
                    if (Objects.nonNull(femaleFamily) && Objects.nonNull(femaleFamily.getOwnerUid())) {
                        notifyComponent.npcNotify(femaleFamily.getOwnerUid(), String.format(PAY_ORDER_CANCEL_FAMILY.getMsg(), user.getName()));
                    }
                }
                officialCombineAsyncManager.cancelDeposit(param.getAppId(), param.getUid(), combine.getId());
            }
        } else {
            // 已结成
            // 小助手通知
            UserVO user = userRemoteService.getBasic(param.getUid(), param.getAppId(), Boolean.TRUE);
            notifyComponent.npcNotify(param.getUid(), PAY_DEPOSIT_CANCEL.getMsg());
            notifyComponent.npcNotify(toUid, String.format(PAY_DEPOSIT_CANCEL_TO.getMsg(), user.getName()));
            // 通知双方家族长
            if (Objects.nonNull(combine.getMaleFamilyId())) {
                FamilyInfoDTO maleFamily = familyInfoFeignService.findById(combine.getMaleFamilyId()).successData();
                if (Objects.nonNull(maleFamily) && Objects.nonNull(maleFamily.getOwnerUid())) {
                    notifyComponent.npcNotify(maleFamily.getOwnerUid(), String.format(PAY_ORDER_CANCEL_FAMILY.getMsg(), user.getName()));
                }
            }
            if (Objects.nonNull(combine.getFemaleFamilyId())) {
                FamilyInfoDTO femaleFamily = familyInfoFeignService.findById(combine.getMaleFamilyId()).successData();
                if (Objects.nonNull(femaleFamily) && Objects.nonNull(femaleFamily.getOwnerUid())) {
                    notifyComponent.npcNotify(femaleFamily.getOwnerUid(), String.format(PAY_ORDER_CANCEL_FAMILY.getMsg(), user.getName()));
                }
            }
            officialCombineAsyncManager.cancelDeposit(param.getAppId(), param.getUid(), combine.getId());
        }
        commonCancelHandle(combine);
        return OfficialCombineCancelCheckVO.builder().checkResult(Boolean.TRUE).build();
    }

    private Boolean cancelHandle(BaseParam param, OfficialCombineUserDO combine) {
        OrderVO order = normalOrderFeignService.getOrder(combine.getOrderId()).successData();
        if (Objects.nonNull(order) && OrderStatusEnum.ORDER_PAID.getCode().equals(order.getStatus()) && !CollectionUtils.isEmpty(order.getOrderDetails())) {
            // 获取礼物清单
            Map<String, Long> productPriceMap = new HashMap<>();
            List<BatchUsePackageDetailDTO> packageList = Lists.newArrayList();
            for (OrderDetailVO item : order.getOrderDetails()) {
                PackageProductVO product = JSONObject.parseObject(item.getProductSnapshot(), PackageProductVO.class);
                if (Objects.nonNull(product.getType()) && ProductType.oc_package_gift.getCode().equals(product.getType())) {
                    List<PackageProductVO> productList = packageProductRemoteService.getDetailById(param.getAppId(), combine.getUnionId(), product.getId());
                    if (!CollectionUtils.isEmpty(productList)) {
                        packageList.addAll(productList.stream().map(p -> BatchUsePackageDetailDTO.builder().bizId(p.getKey()).useNum(item.getNum() * p.getNum()).build()).collect(Collectors.toList()));
                        for (PackageProductVO p : productList) {
                            productPriceMap.put(p.getKey(), p.getPrice());
                        }
                    }
                } else {
                    packageList.add(BatchUsePackageDetailDTO.builder().bizId(product.getKey()).useNum(item.getNum()).build());
                    productPriceMap.put(product.getKey(), product.getPrice());
                }
            }
            // 回收礼物
            if (!CollectionUtils.isEmpty(packageList)) {
                BatchUsePackageResultDTO result = userPackageFeignService.batchUsePackage(param.getAppId(), combine.getUnionId(), param.getUid(), packageList, PackageUseScene.cancelOfficialCombine.getCode(), Boolean.TRUE);
                if (Objects.nonNull(result) && !CollectionUtils.isEmpty(result.getDetailList())) {
                    Long returnPrice = result.getDetailList().stream().mapToLong(r -> productPriceMap.get(r.getBizId())).sum() * 7 / 10;
                    Boolean returnResult = userCoinAccountManager.coinBack(combine.getUnionId(), param.getAppId(), param.getUid(),
                            AppScene.platform, AppFunctionEnum.cancel_official_publicity_return.getCode(),
                            returnPrice, 0L, "", "", "官宣订单退款", null, null);
                    if (Boolean.FALSE.equals(returnResult)) {
                        throw new ServiceException(MISS_PARAM);
                    }
                }
            }
            // 回退优惠券
//            if (Objects.nonNull(order.getCouponId())) {
//                Boolean result = userCouponFeignService.cancelDeduct(order.getCouponId()).successData();
//                if (Objects.isNull(result) || Boolean.FALSE.equals(result)) {
//                    log.error("回退优惠券失败 couponId:{}", order.getCouponId());
//                }
//            }
        }
        return Boolean.TRUE;
    }

    private Boolean cancelDepositHandle(BaseParam param, OfficialCombineUserDO combine) {
        OrderVO order = normalOrderFeignService.getOrder(combine.getPreOrderId()).successData();
        if (Objects.nonNull(order) && OrderStatusEnum.ORDER_COMPLETED.getCode().equals(order.getStatus())) {
            Boolean returnResult = userCoinAccountManager.coinBack(param.getUnionId(), param.getAppId(), combine.getOwnerId(),
                    AppScene.platform, AppFunctionEnum.cancel_official_deposit_publicity_return.getCode(),
                    1000L, 0L, "", "", "官宣预订单退款", null, null);
            if (Boolean.FALSE.equals(returnResult)) {
                throw new ServiceException(MISS_PARAM);
            }
        }
        log.info("官宣预订单退款 combine{}", JSONObject.toJSONString(combine));
        return Boolean.TRUE;
    }

    /**
     * 查看清单
     *
     * @param toUid
     * @return
     */
    public GiftListVO combineGiftInfo(BaseParam param, Long toUid) {
        OfficialCombineUserDO combine = officialCombineCommonManager.getCurrentCombineByToUid(param, toUid);
        if (Objects.isNull(combine)) {
            throw new ServiceException(OFFICIAL_BINE_ERROR_181004);
        }

        // 查询订单解析商品
        OrderVO orderVO = normalOrderFeignService.getOrder(combine.getOrderId()).successData();
        if (Objects.isNull(orderVO)) {
            throw new ServiceException(MISS_PARAM);
        }
        List<OrderDetailVO> orderDetails = orderVO.getOrderDetails();
        if (CollectionUtils.isEmpty(orderDetails)) {
            return GiftListVO.builder().totalCoin(orderVO.getOrderPrice()).giftItemList(Lists.newArrayList()).build();
        }

        // 数据结构转换
        List<GiftItem> result = Lists.newArrayList();
        for (OrderDetailVO detail : orderDetails) {
            if (StringUtils.isBlank(detail.getProductSnapshot())) {
                continue;
            }
            PackageProductVO product = JSONObject.parseObject(detail.getProductSnapshot(), PackageProductVO.class);
            result.add(GiftItem.builder().id(product.getId()).name(product.getName()).icon(product.getIcon()).gold(product.getPrice()).num(detail.getNum()).build());
        }
        return GiftListVO.builder().totalCoin(orderVO.getOrderPrice()).giftItemList(result).build();
    }

    public String getActivityUrl() {
//        return ActivityUrlUtil.getH5BaseUrl(MDCUtil.getCurUnionIdByMdc(), env, Boolean.TRUE, environment.getProperty(DynamicEnvManager.GLOBAL_ENV_NAME)) + "confession";
        return ActivityUrlUtil.getH5BaseUrl(MDCUtil.getCurUnionIdByMdc(), env, Boolean.TRUE, environment.getProperty(DynamicEnvManager.GLOBAL_ENV_NAME)) + OfficialCombineV2Manager.H5_INDEX_URI;
    }

    public Boolean autoExpired() {
        List<OfficialCombineUserDO> combines = officialCombineUserJpaDAO.getByExpiredTime(ServicesAppIdEnum.lanling.getAppId(), Lists.newArrayList(CombineStatusEnum.COMBINE_STARTED.getCode(), CombineStatusEnum.COMBINE_COMPLETED.getCode()));
        if (CollectionUtils.isEmpty(combines)) {
            return Boolean.FALSE;
        }

        LocalDateTime now = LocalDateTime.now();
        for (OfficialCombineUserDO combine : combines) {
            if (!StringUtils.isBlank(combine.getBagType())) {
                String splicUserId = AppUtil.splicUserId(combine.getMaleUid(), combine.getFemaleUid());
                log.info("splicUserId {} expire {} status {}", splicUserId, now.isAfter(combine.getExpiredTime()), combine.getStatus());
                //不为null说明是新版本的官宣  不进行自动任务处理
                if (CombineStatusEnum.COMBINE_STARTED.getCode().equals(combine.getStatus()) && now.isAfter(combine.getExpiredTime())) {
                    combine.setStatus(CombineStatusEnum.COMBINE_EXPIRED.getCode());
                    officialCombineUserJpaDAO.save(combine);
                    Long ownerId = combine.getOwnerId();
                    Long otherUid = combine.getOwnerId().equals(combine.getMaleUid()) ? combine.getFemaleUid() : combine.getMaleUid();
                    notifyComponent.chatNotify(otherUid, ownerId, "系统提醒：亲爱的用户，您当前购买的礼包还未赠送哦~已经过了72h时效哦~");
                    officialCombineV2Manager.cancelDeposit(combine.getAppId(),ownerId,otherUid,combine.getId(),true, true);
                } else if (CombineStatusEnum.COMBINE_STARTED.getCode().equals(combine.getStatus())
                        && now.isAfter(combine.getExpiredTime().minusDays(1))
                        && now.isBefore(combine.getExpiredTime().minusDays(1).plusMinutes(1))) {
                    //如果还有一个小时就要进行结束
                    Long ownerId = combine.getOwnerId();
                    Long otherUid = combine.getOwnerId().equals(combine.getMaleUid()) ? combine.getFemaleUid() : combine.getMaleUid();
                    notifyComponent.chatNotifyOneWay(otherUid, ownerId, "系统消息：亲爱的用户，您购买的礼包还未赠送哦~还有24小时失效哦~快去赠送给心上人吧");
                    notifyComponent.chatNotifyOneWay(ownerId, otherUid, "系统消息：亲爱的用户，与您官宣的有情人还未赠送官宣礼物给您哦~礼包还有24小时失效哦~快去找他确认关系吧");
                } else if (CombineStatusEnum.COMBINE_STARTED.getCode().equals(combine.getStatus())
                        && now.isAfter(combine.getExpiredTime().minusHours(1))
                        && now.isBefore(combine.getExpiredTime().minusHours(1).plusMinutes(1))) {
                    Long ownerId = combine.getOwnerId();
                    Long otherUid = combine.getOwnerId().equals(combine.getMaleUid()) ? combine.getFemaleUid() : combine.getMaleUid();
                    notifyComponent.chatNotifyOneWay(otherUid, ownerId, "系统消息：亲爱的用户，您购买的礼包还未赠送哦~还有1小时失效哦~快去赠送给心上人吧");
                    notifyComponent.chatNotifyOneWay(ownerId, otherUid, "系统消息：亲爱的用户，与您官宣的有情人还未赠送官宣礼物给您哦~礼包还有1小时失效哦~快去找他确认关系吧");
                }
                if (now.isAfter(combine.getExpiredTime()) && CombineStatusEnum.COMBINE_COMPLETED.getCode().equals(combine.getStatus())) {
                    log.info("官宣过期 {} {}", combine.getMaleUid(), combine.getFemaleUid());
                    combine.setStatus(CombineStatusEnum.COMBINE_EXPIRED.getCode());
                    officialCombineUserJpaDAO.save(combine);

                    // 回收小屋
                    sweetCabinAsyncManager.dissolutionOfficialCombine(combine.getAppId(), combine.getMaleUid(), combine.getFemaleUid());
                    try {
                        //添加埋点
                        long daysBetween = Math.abs(ChronoUnit.DAYS.between(combine.getCreateTime(), LocalDateTime.now()));
                        Map<String, Object> params = Maps.newHashMap();
                        params.put("uid", combine.getMaleUid());
                        params.put("type", "end");
                        params.put("officialId", combine.getId());
                        params.put("splic_uid", splicUserId);
                        params.put("continuance", daysBetween);
                        yzKafkaProducerManager.dataRangerTrack(combine.getAppId(), combine.getMaleUid(), "official_announcement_end", params, ServicesNameEnum.ump_services.getCode());
                        params.put("uid", combine.getFemaleUid());
                        yzKafkaProducerManager.dataRangerTrack(combine.getAppId(), combine.getFemaleUid(), "official_announcement_end", params, ServicesNameEnum.ump_services.getCode());

                    } catch (Exception e) {
                        log.info("recordEndTimeerror {}", JSON.toJSONString(combine),e);
                    }
                }
                continue;
            }

            log.info("官宣定时任务处理流程 combine:{}", JSON.toJSONString(combine));
            SecurityUser curSecurityUser = new SecurityUser();
            curSecurityUser.setAppId(ServicesAppIdEnum.lanling.getAppId());
            curSecurityUser.setUnionId(ServicesAppIdEnum.lanling.getUnionId());
            curSecurityUser.setUserId(combine.getOwnerId());
            curSecurityUser.setVersionCode(0);
            curSecurityUser.setUserPlatform(PlatformEnum.android);
            MDC.put("securityUser", JSONObject.toJSONString(curSecurityUser));

            // 福利礼包过期通知
            if (GiftBagStatusEnum.ACTIVATED.getCode().equals(combine.getGiftBagStatus())) {
                // 有效期不足 1 小时通知
                if (LocalDateTime.now().plusHours(1).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() >= combine.getGiftBagCountDown()
                        && redisManager.setnx(String.format(OC_1HOUR_EXPIRED_NOTIFY, combine.getId()), System.currentTimeMillis(), RedisManager.ONE_DAY_SECONDS * 60)) {
                    notifyComponent.chatNotify(combine.getFemaleUid(), combine.getMaleUid(), String.format("你的官宣福利礼包将在 1 小时后过期 <a href=\"%s\">去看看</a>", getActivityUrl() + "?from=single_chat_notify"));
                }
                // 有效期不足 12 小时通知
                if (LocalDateTime.now().plusHours(12).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() >= combine.getGiftBagCountDown()
                        && redisManager.setnx(String.format(OC_12HOUR_EXPIRED_NOTIFY, combine.getId()), System.currentTimeMillis(), RedisManager.ONE_DAY_SECONDS * 60)) {
                    notifyComponent.chatNotify(combine.getFemaleUid(), combine.getMaleUid(), String.format("恭喜获得官宣福利礼包，立即官宣享受超级福利 <a href=\"%s\">去看看</a>", getActivityUrl() + "?from=single_chat_notify"));
                }
                // 有效期不足 24 小时通知
                if (LocalDateTime.now().plusHours(24).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() >= combine.getGiftBagCountDown()
                        && redisManager.setnx(String.format(OC_24HOUR_EXPIRED_NOTIFY, combine.getId()), System.currentTimeMillis(), RedisManager.ONE_DAY_SECONDS * 60)) {
                    notifyComponent.chatNotify(combine.getFemaleUid(), combine.getMaleUid(), String.format("恭喜获得官宣福利礼包，立即官宣享受超级福利 <a href=\"%s\">去看看</a>", getActivityUrl() + "?from=single_chat_notify"));
                }
                // 有效期不足 36 小时通知
                if (LocalDateTime.now().plusHours(36).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() >= combine.getGiftBagCountDown()
                        && redisManager.setnx(String.format(OC_36HOUR_EXPIRED_NOTIFY, combine.getId()), System.currentTimeMillis(), RedisManager.ONE_DAY_SECONDS * 60)) {
                    notifyComponent.chatNotify(combine.getFemaleUid(), combine.getMaleUid(), String.format("恭喜获得官宣福利礼包，立即官宣享受超级福利 <a href=\"%s\">去看看</a>", getActivityUrl() + "?from=single_chat_notify"));
                }
            }

            // 官宣流程超过过期时间处理
            if (now.isAfter(combine.getExpiredTime())) {
                log.info("官宣过期处理 {} {}", combine.getMaleUid(), combine.getFemaleUid());
                // 订单未支付 => 官宣过期
                if (Objects.isNull(combine.getOrderId())) {
                    log.info("定金过期 {} {}", combine.getMaleUid(), combine.getFemaleUid());
                    depositAutoExpire(combine);
                    commonCancelHandle(combine);
                    notifyComponent.npcNotify(combine.getMaleUid(), PAY_DEPOSIT_CANCEL_ALERT.getMsg());
                    notifyComponent.npcNotify(combine.getFemaleUid(), PAY_DEPOSIT_CANCEL_ALERT.getMsg());
                    continue;
                }
                OrderVO orderVO = normalOrderFeignService.getOrder(combine.getOrderId()).successData();
                if (Objects.isNull(orderVO)) {
                    log.error("订单为空 orderId:{}", combine.getOrderId());
                    continue;
                }
                if (OrderStatusEnum.ORDER_CREATED.getCode().equals(orderVO.getStatus())) {
                    log.info("定金过期 {} {}", combine.getMaleUid(), combine.getFemaleUid());
                    depositAutoExpire(combine);
                    commonCancelHandle(combine);
                    notifyComponent.npcNotify(combine.getMaleUid(), PAY_DEPOSIT_CANCEL_ALERT.getMsg());
                    notifyComponent.npcNotify(combine.getFemaleUid(), PAY_DEPOSIT_CANCEL_ALERT.getMsg());
                    continue;
                }
                // 订单已支付 && 未预约官宣时间 => 官宣过期、退款、退券
                if (OrderStatusEnum.ORDER_PAID.getCode().equals(orderVO.getStatus()) && Objects.isNull(combine.getStartTime())) {
                    log.info("订单过期 {} {}", combine.getMaleUid(), combine.getFemaleUid());
                    cancelHandle(BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(combine.getOwnerId()).build(), combine);
                    commonCancelHandle(combine);
                    officialCombineAsyncManager.autoCancel(curSecurityUser.getAppId(), curSecurityUser.getUserId(), combine.getId());
                    continue;
                }
                // 官宣仍在进行 && 订单已支付 && 已预约官宣时间 => 官宣完成
                if (CombineStatusEnum.COMBINE_STARTED.getCode().equals(combine.getStatus()) && OrderStatusEnum.ORDER_PAID.getCode().equals(orderVO.getStatus()) && Objects.nonNull(combine.getStartTime())) {
                    log.info("订单自动完成 {} {}", combine.getMaleUid(), combine.getFemaleUid());
                    // 调整官宣信息
                    combine.setStatus(CombineStatusEnum.COMBINE_COMPLETED.getCode());
                    combine.setEndTime(LocalDateTime.now());
                    combine.setExpiredTime(LocalDateTime.now().plusDays(30));
                    officialCombineUserJpaDAO.save(combine.pushPageIndex());
                    officialCombineAsyncManager.autoCompleted(curSecurityUser.getAppId(), curSecurityUser.getUserId(), combine.getId());
                    continue;
                }
                // 结成超过 30 天
                if (CombineStatusEnum.COMBINE_COMPLETED.getCode().equals(combine.getStatus())) {
                    log.info("流程完结 {} {}", combine.getMaleUid(), combine.getFemaleUid());
                    combine.setStatus(CombineStatusEnum.COMBINE_EXPIRED.getCode());
                    officialCombineUserJpaDAO.save(combine);
                    // 回收小屋
                    sweetCabinAsyncManager.dissolutionOfficialCombine(combine.getAppId(), combine.getMaleUid(), combine.getFemaleUid());
                }
            } else if (Objects.nonNull(combine.getStartTime()) && LocalDateTime.now().isAfter(combine.getStartTime())) {
                log.info("官宣倒计时结束判定 {} {}", combine.getMaleUid(), combine.getFemaleUid());
                if (Objects.nonNull(redisManager.hget(OfficialCombineConstant.OC_TIME_DONE_NOTIFY_LOG, combine.getId().toString()))) {
                    continue;
                }
                log.info("官宣倒计时结束处理 {} {}", combine.getMaleUid(), combine.getFemaleUid());
                // 通知
                notifyComponent.npcNotify(combine.getMaleUid(), String.format(COMBINE_TIME.getMsg(), getActivityUrl() + "?from=npc_talk&toUid=" + combine.getFemaleUid()));
                notifyComponent.npcNotify(combine.getFemaleUid(), String.format(COMBINE_TIME.getMsg(), getActivityUrl() + "?from=npc_talk&toUid=" + combine.getMaleUid()));
                // 通知双方家族长
                UserVO maleUser = userRemoteService.getBasic(combine.getMaleUid(), curSecurityUser.getAppId(), Boolean.TRUE);
                UserVO femaleUser = userRemoteService.getBasic(combine.getFemaleUid(), curSecurityUser.getAppId(), Boolean.TRUE);
                FamilyInfoDTO maleFamily = familyInfoFeignService.findById(combine.getMaleFamilyId()).successData();
                FamilyInfoDTO femaleFamily = familyInfoFeignService.findById(combine.getFemaleFamilyId()).successData();
                String maleFamilyText = Optional.ofNullable(maleFamily).map(f -> f.getName() + " 家族的").orElse("");
                String femaleFamilyText = Optional.ofNullable(femaleFamily).map(f -> f.getName() + " 家族的").orElse("");
                if (Objects.nonNull(maleFamily) && Objects.nonNull(maleFamily.getOwnerUid())) {
                    notifyComponent.npcNotify(maleFamily.getOwnerUid(), String.format(COMBINE_TIME_FAMILY.getMsg(), maleFamilyText, maleUser.getName(), femaleFamilyText, femaleUser.getName()));
                }
                if (Objects.nonNull(femaleFamily) && Objects.nonNull(femaleFamily.getOwnerUid())) {
                    notifyComponent.npcNotify(femaleFamily.getOwnerUid(), String.format(COMBINE_TIME_FAMILY.getMsg(), maleFamilyText, maleUser.getName(), femaleFamilyText, femaleUser.getName()));
                }
                redisManager.hset(OfficialCombineConstant.OC_TIME_DONE_NOTIFY_LOG, combine.getId().toString(), System.currentTimeMillis(), RedisManager.ONE_DAY_SECONDS * 30);
            } else if (Objects.nonNull(combine.getPreOrderId()) && Objects.nonNull(combine.getExpiredTime())
                    && LocalDateTime.now().isBefore(combine.getExpiredTime())) {
                //这个地方处理下 一天 两天 三天处理
                log.info("官宣倒计时提醒 {} {}", combine.getMaleUid(), combine.getFemaleUid());
                Duration between = Duration.between(LocalDateTime.now(), combine.getExpiredTime());
                long diffMillis = between.toMillis();
                //一分钟内处理
                boolean alert = diffMillis % PushTimeUtil.WHOLE_DAY <= 60000;
                log.debug("官宣倒计时提醒 {} {} alert {} between {}", combine.getMaleUid(), combine.getFemaleUid(), alert, diffMillis);
                if (!alert) {
                    continue;
                }
                long day = diffMillis / PushTimeUtil.WHOLE_DAY;
                log.debug("官宣倒计时提醒 {} {} day {}", combine.getMaleUid(), combine.getFemaleUid(), day);
                if (day < 1) {
                    //过期的一分钟 不处理了
                    log.debug("官宣倒计时提醒 最后一次不提醒 {} {} day {}", combine.getMaleUid(), combine.getFemaleUid(), day);
                    continue;
                }

                UserVO maleUser = userRemoteService.getBasic(combine.getMaleUid(), curSecurityUser.getAppId(), Boolean.TRUE);
                UserVO femaleUser = userRemoteService.getBasic(combine.getFemaleUid(), curSecurityUser.getAppId(), Boolean.TRUE);
                log.info("定金即将过期判定 {} {}", combine.getMaleUid(), combine.getFemaleUid());
//                if (Objects.nonNull(redisManager.hget(OC_DEPOSIT_EXPIRED_NOTIFY_LOG, combine.getId().toString()))) {
//                    continue;
//                }
                log.info("定金即将过期处理 {} {} ", combine.getMaleUid(), combine.getFemaleUid());
                notifyComponent.npcNotify(combine.getMaleUid(), String.format(PAY_DEPOSIT_EXPIRED_DAY.getMsg(), femaleUser.getName(), day, getActivityUrl() + "?from=npc_talk&toUid=" + combine.getFemaleUid()));
                notifyComponent.npcNotify(combine.getFemaleUid(), String.format(PAY_DEPOSIT_EXPIRED_DAY.getMsg(), maleUser.getName(), day, getActivityUrl() + "?from=npc_talk&toUid=" + combine.getMaleUid()));
                redisManager.hset(OfficialCombineConstant.OC_DEPOSIT_EXPIRED_NOTIFY_LOG, combine.getId().toString(), System.currentTimeMillis(), RedisManager.ONE_DAY_SECONDS * 30);
            } else if (Objects.nonNull(combine.getOrderId()) && Objects.isNull(combine.getEndTime()) && Objects.nonNull(combine.getExpiredTime())
                    && LocalDateTime.now().isBefore(combine.getExpiredTime()) && LocalDateTime.now().plusDays(1).isAfter(combine.getExpiredTime())) {
                log.info("订单即将过期处理 {} {}", combine.getMaleUid(), combine.getFemaleUid());
                if (Objects.nonNull(redisManager.hget(OfficialCombineConstant.OC_ORDER_EXPIRED_NOTIFY_LOG, combine.getId().toString()))) {
                    continue;
                }
                log.info("订单即将过期处理 {} {}", combine.getMaleUid(), combine.getFemaleUid());
                notifyComponent.npcNotify(combine.getMaleUid(), String.format(PAY_ORDER_EXPIRED.getMsg(), getActivityUrl() + "?from=npc_talk&toUid=" + combine.getFemaleUid()));
                notifyComponent.npcNotify(combine.getFemaleUid(), String.format(PAY_ORDER_EXPIRED.getMsg(), getActivityUrl() + "?from=npc_talk&toUid=" + combine.getMaleUid()));
                redisManager.hset(OfficialCombineConstant.OC_ORDER_EXPIRED_NOTIFY_LOG, combine.getId().toString(), System.currentTimeMillis(), RedisManager.ONE_DAY_SECONDS * 30);
            }
        }
        return Boolean.FALSE;
    }

    private void depositAutoExpire(OfficialCombineUserDO combine) {
        // 预订单已支付，自动过期 => 退还定金
        if (Objects.nonNull(combine.getPreOrderId())) {
            log.info("定金过期 {} {}", combine.getMaleUid(), combine.getFemaleUid());
            try {
                // 官宣过期 定金返还
                BaseParam baseParam = BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).uid(combine.getOwnerId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).build();
                cancelDepositHandle(baseParam, combine);
                // 小助手通知
                Long uid = combine.getOwnerId().equals(combine.getMaleUid()) ? combine.getMaleUid() : combine.getFemaleUid();
                Long toUid  = combine.getOwnerId().equals(combine.getFemaleUid()) ? combine.getMaleUid() : combine.getFemaleUid();
                UserBaseVO user = userRemoteService.getBasicAll(combine.getAppId(), uid, Boolean.FALSE);
                UserBaseVO toUser = userRemoteService.getBasicAll(combine.getAppId(), toUid, Boolean.FALSE);
                if (Objects.nonNull(user) && Objects.nonNull(toUser) && Objects.nonNull(toUser.getName())) {
                    notifyComponent.npcNotify(uid, String.format(PAY_DEPOSIT_OWNER_CANCEL.getMsg(), toUser.getName()));
                    notifyComponent.npcNotify(uid, PAY_DEPOSIT_CANCEL.getMsg());
                    notifyComponent.npcNotify(toUid, String.format(PAY_DEPOSIT_CANCEL_TO.getMsg(), user.getName()));
                }
            } catch (Exception e) {
                log.error("定金返还失败 combine{}", combine, e);
                throw new ServiceException(INVALID_PARAM);
            }


        }
    }

    private Boolean commonCancelHandle(OfficialCombineUserDO combine) {
        combine.setStatus(CombineStatusEnum.COMBINE_CANCELED.getCode());
        officialCombineUserJpaDAO.save(combine);

        // 清空购物车
        redisManager.delete(String.format(OfficialCombineConstant.OFFICIAL_COMBINE_CART, combine.getMaleUid() + "_" + combine.getFemaleUid()));
        redisManager.delete(String.format(OfficialCombineConstant.OFFICIAL_COMBINE_CART, combine.getFemaleUid() + "_" + combine.getMaleUid()));

        return Boolean.TRUE;
    }

    private OfficialCombineUserDO getDefault(BaseParam param, Long toUid, Long orderId, Long familyId, Long toFamilyId) {
        UserBaseVO user = userRemoteService.getBasicAll(param.getAppId(), param.getUid(), Boolean.TRUE);
        UserBaseVO toUser = userRemoteService.getBasicAll(param.getAppId(), toUid, Boolean.TRUE);

        UserBaseVO male = user;
        Long maleFamilyId = familyId;
        UserBaseVO female = toUser;
        Long femaleFamilyId = toFamilyId;
        if (SexType.WOMAN.equals(user.getSex())) {
            female = user;
            femaleFamilyId = familyId;
            male = toUser;
            maleFamilyId = toFamilyId;
        }

        return OfficialCombineUserDO.builder()
                .appId(param.getAppId())
                .unionId(param.getUnionId())
                .maleUid(male.getId())
                .maleFamilyId(maleFamilyId)
                .femaleUid(female.getId())
                .femaleFamilyId(femaleFamilyId)
                .ownerId(param.getUid())
                .preOrderId(orderId)
                .leftEditTimes(2)
                .expiredTime(LocalDateTime.now().plusDays(3))
                .pageIndex(PageIndexEnum.PRODUCT_LIST_PAGE.getIndex())
                .status(CombineStatusEnum.COMBINE_STARTED.getCode())
                .build();
    }

    private List<ProductItem> getProductItemListOfBothCart(Long uid, Long toUid, Long loveTokenId) {
        List<ProductItem> productItemList = Lists.newArrayList();
        productItemList.add(ProductItem.builder().productId(loveTokenId).num(1).build());

        String myCartStr = (String) redisManager.get(String.format(OfficialCombineConstant.OFFICIAL_COMBINE_CART, uid + "_" + toUid));
        ShoppingCart myCart = StringUtils.isBlank(myCartStr) ? officialCombineCommonManager.getShoppingCard(null) : officialCombineCommonManager.getShoppingCard(JSONObject.parseObject(myCartStr, ConfirmCartParam.class));
        String otherCartStr = (String) redisManager.get(String.format(OfficialCombineConstant.OFFICIAL_COMBINE_CART, toUid + "_" + uid));
        ShoppingCart otherCart = StringUtils.isBlank(otherCartStr) ? officialCombineCommonManager.getShoppingCard(null) : officialCombineCommonManager.getShoppingCard(JSONObject.parseObject(otherCartStr, ConfirmCartParam.class));

        if (Objects.nonNull(myCart) && !CollectionUtils.isEmpty(myCart.getGiftList())) {
            productItemList.addAll(Lists.transform(myCart.getGiftList(), p -> ProductItem.builder().productId(p.getId()).num(p.getNum()).build()));
        }
        if (Objects.nonNull(otherCart) && !CollectionUtils.isEmpty(otherCart.getGiftList())) {
            productItemList.addAll(Lists.transform(otherCart.getGiftList(), p -> ProductItem.builder().productId(p.getId()).num(p.getNum()).build()));
        }

        // productId 去重
        Map<Long, Integer> productMap = new HashMap<>();
        for (ProductItem item : productItemList) {
            if (Objects.isNull(productMap.get(item.getProductId()))) {
                productMap.put(item.getProductId(), item.getNum());
            } else {
                productMap.put(item.getProductId(), productMap.get(item.getProductId()) + item.getNum());
            }
        }
        return productMap.entrySet().stream().map(o -> ProductItem.builder().productId(o.getKey()).num(o.getValue()).build()).collect(Collectors.toList());
    }

    private Long getFamilyIdByUid(Long uid) {
        List<FamilyMemberDTO> familyList = familyMemberFeignService.findByUidAndRoleNotAndStatus(FamilyMemberQuery.builder().uid(uid).role(FamilyRole.guest.getCode()).status("common").build()).successData();
        if (CollectionUtils.isEmpty(familyList)) {
            return -1L;
        }
        return familyList.get(0).getFamilyId();
    }



    public void addHistoryCombineTrack() {
        Long startId = 0L;
        Integer limit = 20;
        List<OfficialCombineUserDO> list;
        do {
            log.info("startId:{}", startId);
            list = officialCombineUserJpaDAO.findByIdGreaterThanLimit(startId, limit);
            if (list.isEmpty()) {
                break;
            }
            for (OfficialCombineUserDO official : list) {
                if (!CombineStatusEnum.COMBINE_FINISHED.getCode().equals(official.getStatus())
                    && !CombineStatusEnum.COMBINE_COMPLETED.getCode().equals(official.getStatus())) {
                    continue;
                }
                log.info("符合条件， 上报埋点 -> {}", official);
                Map<String, Object> params = new HashMap<>();
                params.put("splic_id", AppUtil.splicUserId(official.getMaleUid(), official.getFemaleUid()));
                params.put("user_type", official.getMaleUid().equals(official.getOwnerId()) ? "initiator" : "recipient");
                yzKafkaProducerManager.dataRangerTrack(official.getAppId(), official.getMaleUid(), "official_announcement_confession_finish_both", params, ServicesNameEnum.ump_services.getCode());
                params.put("user_type", official.getFemaleUid().equals(official.getOwnerId()) ? "initiator" : "recipient");
                yzKafkaProducerManager.dataRangerTrack(official.getAppId(), official.getFemaleUid(), "official_announcement_confession_finish_both", params, ServicesNameEnum.ump_services.getCode());
            }

            startId = list.get(list.size() - 1).getId();
         } while(list.size() <= limit);
        log.info("上报埋点结束");
    }
    /**
     * 获取付全款的金额
     * @param idList
     * @return
     */
    public Map<Long, Long> getCombineOrderPrice(List<Long> idList) {
        // idList->orderIdList
        if (CollectionUtils.isEmpty(idList)) {
            return Maps.newHashMap();
        }
        log.info("弹幕官宣列表:{}",idList);
        List<OfficialCombineUserDO> allById = officialCombineUserJpaDAO.findAllById(idList);
        if (CollectionUtils.isEmpty(allById)) {
            return Maps.newHashMap();
        }
        log.info("获取付全款的订单列表:{}", JSONObject.toJSONString(allById));

        List<Long> orderIdList = allById.stream().map(OfficialCombineUserDO::getOrderId).filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, Long> combineIdMapOrderId = allById.stream().filter(o -> Objects.nonNull(o.getOrderId())).collect(Collectors.toMap(OfficialCombineUserDO::getOrderId, OfficialCombineUserDO::getId));
        if (CollectionUtils.isEmpty(orderIdList) || CollectionUtils.isEmpty(combineIdMapOrderId)) {
            return Maps.newHashMap();
        }
        log.info("获取付全款的处理订单列表:{}:{}", JSONObject.toJSONString(orderIdList),JSONObject.toJSONString(combineIdMapOrderId));

        List<OrderVO> orderVOS = normalOrderFeignService.batchQueryOrder(JSONObject.toJSONString(orderIdList)).successData();
        if (CollectionUtils.isEmpty(orderVOS)) {
            return Maps.newHashMap();
        }
        log.info("获取付全款的金额列表:{}", JSONObject.toJSONString(orderVOS));
        Map<Long , Long> map = new HashMap<>();
        for (OrderVO orderVO : orderVOS) {
            Long orderId = orderVO.getId();
            Long combineId = combineIdMapOrderId.get(orderId);
            if (Objects.nonNull(combineId)) {
                Long price = orderVO.getOrderPayPrice();
                if (Objects.nonNull(price)) {
                    map.put(combineId, price);
                }
            }
        }
        log.info("获取付全款的金额列表:{}", JSONObject.toJSONString(map));
        return map;

    }
}
