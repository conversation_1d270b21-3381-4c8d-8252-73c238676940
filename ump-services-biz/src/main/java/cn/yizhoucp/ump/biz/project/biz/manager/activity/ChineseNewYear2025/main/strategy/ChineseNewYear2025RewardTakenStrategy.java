package cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.strategy;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.annotation.NoRepeatSubmit;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.util.AppUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.MainChineseNewYear2025TrackManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.common.MainChineseNewYear2025Constant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.common.MainChineseNewYear2025Enums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.common.MainChineseNewYear2025RedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.vo.RewardResponseVO;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ChineseNewYear2025RewardTakenStrategy implements MainChineseNewYear2025Strategy {

    @Resource
    ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    MainChineseNewYear2025RedisManager mainChineseNewYear2025RedisManager;
    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private MainChineseNewYear2025TrackManager mainChineseNewYear2025TrackManager;

    @NoRepeatSubmit(time = 3)
    @Override
    public Object execute(ButtonEventParam buttonEventParam) {
        // 奖励领取事件
        BaseParam param = buttonEventParam.getBaseParam();
        Long uid = param.getUid();
        String taskCode = buttonEventParam.getBizKey();
        MainChineseNewYear2025Enums.CardTaskEnum taskEnum = MainChineseNewYear2025Enums.CardTaskEnum.getByTaskCode(taskCode);
        MainChineseNewYear2025Enums.DishTaskEnum disEnum = MainChineseNewYear2025Enums.DishTaskEnum.getByTaskCode(taskCode);
        MainChineseNewYear2025Enums.NodeEnum nodeEnum = MainChineseNewYear2025Enums.NodeEnum.getByNodeCode(taskCode);
        String sceneCode = "";
        if (taskEnum != null) {
            sceneCode = taskEnum.getRewardCode();
            mainChineseNewYear2025TrackManager.allActivityTaskFinish(uid, taskEnum.getTrackCode());
        } else if (disEnum != null) {
            sceneCode = disEnum.getRewardCode();
            mainChineseNewYear2025TrackManager.allActivityTaskFinish(uid, disEnum.getTrackCode());
        } else if (nodeEnum != null) {
            Long otherId = buttonEventParam.getOtherId();
            if (otherId == null) {
                return null;
            }
            String bindId = AppUtil.splicUserId(uid, otherId);
            Long waterNum = mainChineseNewYear2025RedisManager.getWateringNumByUid(bindId, uid, nodeEnum.getNodeCode());
            Long otherWaterNum = mainChineseNewYear2025RedisManager.getWateringNumByUid(bindId, otherId, nodeEnum.getNodeCode());
            if (waterNum < nodeEnum.getWateringRequired() || otherWaterNum < nodeEnum.getWateringRequired()) {
                throw new ServiceException(ErrorCode.INVALID_PARAM, "未完成浇灌任务");
            }
            Boolean status=mainChineseNewYear2025RedisManager.getRewardNodeStatus(AppUtil.splicUserId(uid, otherId), nodeEnum.getNodeCode());
            if(Boolean.TRUE.equals(status)){
                throw new ServiceException(ErrorCode.INVALID_PARAM,"已领取 ");
            }
            //增加万能卡
            mainChineseNewYear2025RedisManager.incrementDrawItems(buttonEventParam.getBaseParam().getUid(), nodeEnum.getRewardCard().longValue());
            mainChineseNewYear2025RedisManager.incrementDrawItems(otherId, nodeEnum.getRewardCard().longValue());
            MainChineseNewYear2025Enums.CardEnum cardEnum = MainChineseNewYear2025Enums.CardEnum.CARD_6;
            List<RewardResponseVO.Gift> rewardList = Collections.singletonList(RewardResponseVO.Gift.builder()
                    .giftIcon("https://nuanchat.oss-cn-hangzhou.aliyuncs.com/nuanliao-fronted/activity/new-year-2025/WNK_GIFT.png")
                    .giftKey(MainChineseNewYear2025Enums.CardEnum.CARD_6.getCardCode())
                    .giftName(cardEnum.getCardName())
                    .giftNum(nodeEnum.getRewardCard())
                    .build());
            mainChineseNewYear2025RedisManager.setRewardNodeStatus(AppUtil.splicUserId(uid, otherId), nodeEnum.getNodeCode());
            //埋点
            mainChineseNewYear2025TrackManager.allActivityReceiveAward("water_money_tree", MainChineseNewYear2025Enums.CardEnum.CARD_6.getCardCode(), 0L, nodeEnum.getRewardCard(), uid);
            return RewardResponseVO.builder()
                    .rewardList(rewardList)
                    .build();
        }
        List<ScenePrizeDO> rewardPrizeDOS = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), MainChineseNewYear2025Constant.ACTIVITY_CODE, sceneCode);
        if (rewardPrizeDOS == null || rewardPrizeDOS.isEmpty()) {
            log.error("奖励不存在,taskCode:{}", sceneCode);
            return null;
        }
        log.info("RewardTakenEvent execute uid:{},taskCode:{},rewardList:{}", uid, taskCode, rewardPrizeDOS);
        for (ScenePrizeDO scenePrizeDO : rewardPrizeDOS) {
            log.info("uid:{} scenePrizeDO:{}", uid, scenePrizeDO);
            if (MainChineseNewYear2025Constant.CARD_TYPE.equals(scenePrizeDO.getPrizeType())) {
                mainChineseNewYear2025RedisManager.incrementDrawItems(uid, scenePrizeDO.getPrizeNum().longValue());
                continue;
            }
            sendPrizeManager.sendPrize(
                    param,
                    Collections.singletonList(SendPrizeDTO.of(scenePrizeDO, param.getUid()))
            );
        }
        // 奖励领取逻辑
        mainChineseNewYear2025RedisManager.setRewardClaimed(uid, sceneCode);
        List<RewardResponseVO.Gift> rewardList = rewardPrizeDOS.stream().map(scenePrizeDO -> RewardResponseVO.Gift.builder()
                .giftIcon(scenePrizeDO.getPrizeIcon())
                .giftName(scenePrizeDO.getPrizeDesc())
                .giftKey(scenePrizeDO.getPrizeValue())
                .giftValue(scenePrizeDO.getPrizeValueGold())
                .giftNum(scenePrizeDO.getPrizeNum())
                .build()).collect(Collectors.toList());
        return RewardResponseVO.builder()
                .rewardList(rewardList)
                .build();
    }
}
