package cn.yizhoucp.ump.biz.project.biz.manager.activity.component;

import cn.yizhoucp.ump.biz.project.biz.manager.MqTransitManager;
import cn.yizhoucp.ump.biz.project.biz.manager.clientPush.ServerPushManager;
import cn.yizhoucp.ump.biz.project.biz.manager.userCoinAccount.UserCoinAccountManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.FeignAvService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.coin.FeignCoinService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.FeignImService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.FeignRoomService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.lanling.FeignLanlingService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.product.PackageProductRemoteService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.FeignUserService;
import cn.yizhoucp.ump.biz.project.dal.service.ScenePrizeService;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;

/**
 * 活动组件抽象类
 *
 * @author: lianghu
 */
@Slf4j
@Deprecated
public class AbstractComponent {

    @Value("${spring.profiles.active}")
    protected String env;
    @Resource
    protected RedisManager redisManager;
    @Resource
    protected RedissonClient redissonClient;
    @Resource
    protected ServerPushManager serverPushManager;
    @Resource
    protected FeignCoinService feignCoinService;
    @Resource
    protected MqTransitManager mqTransitManager;
    @Resource
    protected ScenePrizeService scenePrizeService;
    @Resource
    protected FeignLanlingService feignLanlingService;
    @Resource
    protected FeignImService feignImService;
    @Resource
    protected FeignAvService feignAvService;
    @Resource
    protected FeignRoomService feignRoomService;
    @Resource
    protected FeignUserService feignUserService;
    @Resource
    protected UserCoinAccountManager userCoinAccountManager;
    @Resource
    protected PackageProductRemoteService packageProductRemoteService;

    protected static final String FULL_ACTIVITY_CODE = "%s_activity";

}
