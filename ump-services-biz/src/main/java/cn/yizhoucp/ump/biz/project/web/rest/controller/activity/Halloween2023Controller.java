package cn.yizhoucp.ump.biz.project.web.rest.controller.activity;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.annotation.NoRepeatSubmit;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.ump.api.vo.activity.magpie2023.IndexVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.PrizeItem;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.hallloween2023.Halloween2023IndexManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.magpie2023.Magpie2023PageManager;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
public class Halloween2023Controller {

    @Resource
    private Halloween2023IndexManager halloween2023IndexManager;

    @RequestMapping("/api/inner/activity/halloween2023/receive-today-candy")
    public Result<Long> receiveTodayCandy(BaseParam param) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> halloween2023IndexManager.receiveTodayCandy(param));
    }

    @RequestMapping("/api/inner/activity/halloween2023/exchange-candy-card")
    @NoRepeatSubmit()
    public Result<Long> exchangeCandyCard(BaseParam param) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> halloween2023IndexManager.exchangeCandyCard(param));
    }

    @RequestMapping("/api/inner/activity/halloween2023/receive-rewards")
    public Result<List<Integer>> receiveRewards(BaseParam param, Integer castleLevel) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> halloween2023IndexManager.receiveRewards(param, castleLevel));
    }

    @RequestMapping("/api/inner/activity/halloween2023/confirm-partener")
    public Result<UserVO> confirmPartener(BaseParam param, Long toUid) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> halloween2023IndexManager.confirmPartener(param, toUid));
    }

    @RequestMapping("/api/inner/activity/halloween2023/mission-receive")
    public Result<Boolean> missionReceive(BaseParam param, Integer mission) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> halloween2023IndexManager.missionReceive(param, mission));
    }

}
