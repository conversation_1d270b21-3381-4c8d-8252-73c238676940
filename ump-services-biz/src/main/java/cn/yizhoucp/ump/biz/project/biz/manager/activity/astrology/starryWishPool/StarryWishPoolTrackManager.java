package cn.yizhoucp.ump.biz.project.biz.manager.activity.astrology.starryWishPool;

import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.enums.ServicesNameEnum;
import cn.yizhoucp.starter.cassandra.base.kafka.YzKafkaProducerManager;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

@Service
public class StarryWishPoolTrackManager {

    @Resource
    private YzKafkaProducerManager yzKafkaProducerManager;

    public void allActivityLottery(String poolCode, String awardKey, Long awardAmount, Long uid) {
        Map<String, Object> params = Maps.newHashMapWithExpectedSize(4);
        params.put("activity_type", "star_words_wish");
        params.put("pool_code", poolCode);
        params.put("award_key", awardKey);
        params.put("award_amount", awardAmount);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_lottery", params, ServicesNameEnum.ump_services.getCode());
    }

    public void allActivityTaskFinish(String starWordsWishResult, String taskType, String award, Integer awardCount, Long uid) {
        Map<String, Object> params = Maps.newHashMapWithExpectedSize(6);
        params.put("activity_type", "star_words_wish");
        params.put("attribute_type", "astrology_activity");
        params.put("star_words_wish_result", starWordsWishResult);
        params.put("task_type", taskType);
        params.put("award", award);
        params.put("award_count", awardCount);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_task_finish", params, ServicesNameEnum.ump_services.getCode());
    }

    public void allActivityReceiveAward(String taskType, String awardKey, Long awardAmount, Integer awardCount, Long uid) {
        Map<String, Object> params = Maps.newHashMapWithExpectedSize(7);
        params.put("activity_type", "star_words_wish");
        params.put("attribute_type", "astrology_activity");
        params.put("task_type", taskType);
        params.put("award_key", awardKey);
        params.put("award_amount", awardAmount);
        params.put("award_count", awardCount);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_receive_award", params, ServicesNameEnum.ump_services.getCode());
    }

}
