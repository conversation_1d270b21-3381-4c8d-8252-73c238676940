package cn.yizhoucp.ump.biz.project.biz.manager.activity.journeyToTheWest;

import cn.yizhoucp.ms.core.base.enums.GiftFrom;
import cn.yizhoucp.ms.core.base.enums.GiftTypeEnum;
import cn.yizhoucp.ms.core.base.enums.GiftWay;
import cn.yizhoucp.ms.core.base.util.AppUtil;
import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.activity.journeyToTheWest.ThemeLevel;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.common.ActivityComponent;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class JourneyToTheWestBizManager implements ActivityComponent {

    @Resource
    private JourneyToTheWestConstant journeyToTheWestConstant;
    @Resource
    private JourneyToTheWestRankManager journeyToTheWestRankManager;
    @Resource
    private RedisManager redisManager;
    @Resource
    private JourneyToTheWestIndexManager journeyToTheWestIndexManager;
    @Resource
    private JourneyToTheWestTrackManager journeyToTheWestTrackManager;


    @Override
    public String getActivityCode() {
        return "";
    }

    @ActivityCheck(activityCode = JourneyToTheWestConstant.ACTIVITY_CODE, isThrowException = false)
    public Boolean sendGiftHandle(BaseParam param, List<CoinGiftGivedModel> coinGiftGivedModelList) {
        for (CoinGiftGivedModel coinGiftGivedModel : coinGiftGivedModelList) {
            if (!checkGiftFrom(coinGiftGivedModel)) {
                continue;
            }
            //梦英雄闯关
            heroicDreamPrize(param, coinGiftGivedModel);
            //雨霖铃
            rainBellPrize(param, coinGiftGivedModel);
            //望城楼
            towerViewPrize(param, coinGiftGivedModel);
        }
        return Boolean.TRUE;
    }

    private Boolean checkGiftFrom(CoinGiftGivedModel coinGiftGivedModel) {
        // 面板礼物
        if (StringUtils.isEmpty(coinGiftGivedModel.getGiftWay()) || !StringUtils.equalsIgnoreCase(coinGiftGivedModel.getGiftWay(), GiftWay.NORMAL.getCode())) {
            return Boolean.FALSE;
        }

/*
        // 聊天室礼物不统计
        if (StringUtils.isEmpty(coinGiftGivedModel.getFrom()) ||
                StringUtils.equalsIgnoreCase(coinGiftGivedModel.getFrom(), GiftFrom.room.getCode()) || StringUtils.equalsIgnoreCase(coinGiftGivedModel.getFrom(), GiftFrom.live_room.getCode())) {
            return Boolean.FALSE;
        }
        //家族场景不统计
        if (StringUtils.equalsIgnoreCase(coinGiftGivedModel.getFrom(), GiftFrom.family_gift.getCode())
                || StringUtils.equalsIgnoreCase(coinGiftGivedModel.getFrom(), GiftFrom.gift_in_return_family.getCode())) {
            return Boolean.FALSE;
        }
*/
        return Boolean.TRUE;
    }

    private void towerViewPrize(BaseParam param, CoinGiftGivedModel coinGiftGivedModel) {
        String giftKey = coinGiftGivedModel.getGiftKey();
        Long uid = param.getUid();
        for (JourneyToTheWestConstant.TaskCodeEnum taskCodeEnum : JourneyToTheWestConstant.TaskCodeEnum.values()) {
            Boolean isComplete = journeyToTheWestConstant.getToViewTaskIsComplete(uid, taskCodeEnum);
            if (!isComplete) {
                Boolean isGift = taskCodeEnum.getTaskGiftKey().contains(giftKey);
                if (isGift) {
                    journeyToTheWestConstant.incrementTaskProgress(taskCodeEnum.getTaskId(), uid, coinGiftGivedModel.getProductCount());
                    taskCompleteTrack(param, taskCodeEnum);
                    return;
                }
                return;
            }
        }
    }
    private void taskCompleteTrack(BaseParam param, JourneyToTheWestConstant.TaskCodeEnum taskCodeEnum) {
        Boolean isComplete = journeyToTheWestConstant.getToViewTaskIsComplete(param.getUid(), taskCodeEnum);
        if(isComplete){
            journeyToTheWestTrackManager.allActivityTaskFinish(param.getUid(),taskCodeEnum.getTaskType(),taskCodeEnum.getRewardNum().intValue());
        }
    }

    private void rainBellPrize(BaseParam param, CoinGiftGivedModel coinGiftGivedModel) {
        if (!GiftTypeEnum.relation.getCode().equals(coinGiftGivedModel.getGiftType())) {
            return;
        }
        if (!GiftFrom.chat.getCode().equals(coinGiftGivedModel.getFrom())) {
            return;
        }
        String bindKey = AppUtil.splicUserId(param.getUid(), coinGiftGivedModel.getToUid());
        redisManager.setnx(String.format(JourneyToTheWestConstant.BIND_FRIEND_KEY, bindKey), System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND);
        Long score = journeyToTheWestConstant.incrementRainBellScore(bindKey, coinGiftGivedModel.getCoin());
        if (score >= JourneyToTheWestConstant.RAIN_BELL_MAX_SCORE) {
            journeyToTheWestTrackManager.allActivityTaskFinish(param.getUid(), "rain_bell_52000", 0);
        }
        //排行榜
        journeyToTheWestRankManager.incrRankValue(bindKey, coinGiftGivedModel.getCoin(), JourneyToTheWestConstant.CP_RANK_KEY);
    }

    private void heroicDreamPrize(BaseParam param, CoinGiftGivedModel coinGiftGivedModel) {
        //主题
        String giftKey = coinGiftGivedModel.getGiftKey();
        Long uid = param.getUid();
        for (JourneyToTheWestConstant.ThemesEnum themesEnum : JourneyToTheWestConstant.ThemesEnum.values()) {
            if (themesEnum.getTaskGiftKey().contains(giftKey)) {
                List<ThemeLevel> levels = themesEnum.getLevel();
                for (ThemeLevel level : levels) {
                    //关卡是否开启
                    if (!journeyToTheWestIndexManager.getThemeIsEnabled(themesEnum)) {
                        continue;
                    }
                    //关卡是否完成
                    if (journeyToTheWestConstant.getThemeLevelIsComplete(param.getUid(), themesEnum, JourneyToTheWestConstant.ThemeLevelEnum.getThemeLevelEnumByLevelId(level.getLevel()))) {
                        continue;
                    }
                    Integer progress = journeyToTheWestConstant.incrementThemeLevelProgress(param.getUid(), themesEnum, level.getLevel(), giftKey);
                    if (progress >= level.getLevel()) {
                        journeyToTheWestConstant.setThemeLevelIsComplete(uid, themesEnum, JourneyToTheWestConstant.ThemeLevelEnum.getThemeLevelEnumByLevelId(level.getLevel()));
                        //主题完成埋点
                        allLevelCompleteTrack(param, themesEnum,levels);
                    }
                    return;
                }
            }
        }
    }
    private void allLevelCompleteTrack(BaseParam param, JourneyToTheWestConstant.ThemesEnum themesEnum,List<ThemeLevel> levels){
        for(ThemeLevel themeLevel: levels){
            Boolean allComplete = Boolean.TRUE;
            if(!journeyToTheWestConstant.getThemeLevelIsComplete(param.getUid(), themesEnum, JourneyToTheWestConstant.ThemeLevelEnum.getThemeLevelEnumByLevelId(themeLevel.getLevel()))) {
                allComplete=Boolean.FALSE;
            }
            if(allComplete){
                journeyToTheWestTrackManager.allActivityTaskFinish(param.getUid(), themesEnum.getTaskType(), 0);
            }
        }
    }
}
