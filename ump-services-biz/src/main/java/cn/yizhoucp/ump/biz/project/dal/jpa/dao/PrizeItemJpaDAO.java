package cn.yizhoucp.ump.biz.project.dal.jpa.dao;

import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.PrizeItemDO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.List;

@Repository
public interface PrizeItemJpaDAO extends JpaRepository<PrizeItemDO, Long>, JpaSpecificationExecutor<PrizeItemDO>, CrudRepository<PrizeItemDO, Long> {

    Page<PrizeItemDO> getByMissionId(Long missionId, Pageable pageable);

    List<PrizeItemDO> getByMissionId(Long missionId);

    List<PrizeItemDO> getByMissionIdIn(List<Long> missionIdList);

    @Transactional(rollbackOn = Exception.class)
    @Modifying
    @Query(value = "delete from prize_item where id in ?1", nativeQuery = true)
    void deleteByIds(List<Long> ids);

}
