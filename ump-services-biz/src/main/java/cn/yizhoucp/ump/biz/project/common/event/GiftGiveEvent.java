package cn.yizhoucp.ump.biz.project.common.event;

import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * 送礼事件
 */
@Getter
@Setter
public class GiftGiveEvent extends ApplicationEvent {

    private BaseParam baseParam;
    private List<CoinGiftGivedModel> coinGiftGivedModels;

    public GiftGiveEvent(Object source, BaseParam baseParam, List<CoinGiftGivedModel> coinGiftGivedModels) {
        super(source);
        this.baseParam = baseParam;
        this.coinGiftGivedModels = coinGiftGivedModels;
    }
}
