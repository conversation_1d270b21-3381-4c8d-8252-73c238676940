package cn.yizhoucp.ump.biz.project.biz.manager.activity.lovestory;

import cn.hutool.core.bean.BeanUtil;
import cn.yizhoucp.ump.api.vo.activity.lovestory.LoveStoryVO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 爱情故事常量
 */
@Component
public class LoveStoryConstant {

    public static final List<String> PRESENT_KEY_LIST =
            Arrays.asList(Gift1.GIFT_1.gift<PERSON>ey, Gift1.GIFT_2.giftKey, Gift1.GIFT_3.giftKey, Gift1.GIFT_4.giftKey);

    public static final String ACTIVITY_CODE = "love_story_2";

    public static final String PRIZE_SCENE_CODE = "love_wall_task";

    /**
     * 排行榜消息key
     */
    public static final String LOVE_STORY_RANK_MESSAGE_KEY = "love_story_rank_message_key";


    private static final String SPECIAL_TAG = "";

    /**
     * 爱意满墙礼物 行数
     */
    private static final Integer ROW = 4;

    /**
     * 爱意满墙礼物 列数
     */
    private static final Integer COL = 4;

    //埋点award_key
    public static final String AWARD_TYPE_1 = "rank";

    public static final String AWARD_TYPE_2 = "love_wall";


    public static String rankKey() {
        return String.format("activity:%s:rank-key", ACTIVITY_CODE);
    }






    /**
     * 普通奖励 存储状态和数据 initNormalRewardData
     *
     * @return
     */
    public static String wallItemRewardKey() {
        return "normal-reward";
    }

    /**
     * 礼物 存储每个用户的礼物状态 value initGiftData
     *
     * @return
     */
    public static String wallItemGiftKey() {
        return "gift";
    }

    /**
     * 最终任务奖励
     *
     * @return
     */
    public static String wallItemUltimateRewardKey() {
        return "ultimate-reward";
    }


    public static List<LoveStoryVO.WallData> initNormalRewardData() {
        return Arrays.stream(Reward.values()).filter(item -> item.getType().equals(0))
                .map(item -> {
                    LoveStoryVO.WallData wallData = new LoveStoryVO.WallData();
                    wallData.setCol(item.getCol());
                    wallData.setName(item.getItemName());
                    wallData.setRow(item.getRow());
                    wallData.setIcon(item.getIcon());
                    wallData.setItemKey(item.getItemKey());
//                    wallData.setIdx(LoveStoryConstant.getGiftDataIdx(item.getRow(), item.getCol()));
                    wallData.setType(1);
                    wallData.setStatus(0);
                    return wallData;
                }).collect(Collectors.toList());
    }


    /**
     * 获取「普通」奖励所需要的礼物 下标
     * 不需要存redis
     * key 奖励 itemKey
     *
     * @return
     */
    public static Map<String, List<Point>> getRewardItem(String itemKey) {
        Map<String, List<Point>> res = Maps.newHashMap();
        Stream<Reward> stream = Arrays.stream(Reward.values());
        List<Reward> rewards = new ArrayList<>();
        if (StringUtils.isBlank(itemKey)) {
            rewards = stream.collect(Collectors.toList());
        } else {
            rewards = stream.filter(i -> itemKey.equals(i.getItemKey())).collect(Collectors.toList());
        }
        for (Reward reward : rewards) {
            List<Gift2> gift2 = reward.getGift2();
            if (!CollectionUtils.isEmpty(gift2)) {
                List<Point> points = gift2.stream().map(i -> {
                    Point point = new Point();
                    point.setX(i.getRow());
                    point.setY(i.getCol());
                    return point;
                }).collect(Collectors.toList());
                res.put(reward.getItemKey(), points);
            }
        }
        return res;
    }


    /**
     * 初始化爱意满墙数据  [{0,giftData},{},{},{}  ]
     */
//    public static List<GiftInfo> initWallData() {
//        //初始化用户
//        List<GiftInfo> data = Lists.newArrayList();
//        for (Gift2 gift2 : Gift2.values()) {
//            data.set(gift2.getRow() * COL + gift2.getCol(), new GiftInfo(0, gift2.getGiftKey()));
//        }
//        return data;
//    }
    public static List<LoveStoryVO.WallData> initGiftData() {
        List<LoveStoryVO.WallData> data = Lists.newArrayList(Arrays.stream(Gift2.values())
                .map(i -> new LoveStoryVO.WallData()).collect(Collectors.toList()));
        for (Gift2 gift2 : Gift2.values()) {
            LoveStoryVO.WallData giftData = new LoveStoryVO.WallData();
            BeanUtil.copyProperties(gift2, giftData);
            int idx = getGiftDataIdx(gift2.getRow(), gift2.getCol());
//            giftData.setIdx(idx);
            giftData.setType(0);
            giftData.setStatus(0);
            giftData.setItemKey(gift2.getGiftKey());
            data.set(idx, giftData);
        }
        return data;
    }

    public static int getGiftDataIdx(Integer row, Integer col) {
        return row * COL + col;
    }


    /**
     * 爱情故事礼物
     */
    @Getter
    @AllArgsConstructor
    public enum Gift1 {
        GIFT_1("PP_GIFT", "琵琶", "520"),
        GIFT_2("LG_GIFT", "铃鼓", "520"),
        GIFT_3("JL_GIFT", "金莲", "520"),

        //特殊礼物
        GIFT_4("QXQY_GIFT", "秋夕祈月", "3344");

        private final String giftKey;

        private final String giftName;

        private final String giftPrice;


    }

    /**
     * 爱意满墙
     */
    @Getter
    @AllArgsConstructor
    public enum Gift2 {

        GIFT_1(0, 0, "XDBD_GIFT", "心动爆灯", "https://res-cdn.nuan.chat/res/prod/gift/image/gift_icon_xdbd.png", 10),
        GIFT_2(0, 1, "GBQKL_GIFT", "告白巧克力", "https://res-cdn.nuan.chat/gift-image/2022-01/1642058479065662.png", 52),
        GIFT_3(0, 2, "FTXZ_GIFT", "飞天小猪", "https://res-cdn.nuan.chat/gift-image/2021-09/1632369663123774.png", 188),
        GIFT_4(0, 3, "WAN_GIFT", "我爱你", "https://res-cdn.nuan.chat/res/prod/gift/image/gift_icon_wan.png", 520),
        GIFT_5(1, 0, "WAXY_GIFT", "晚安亲爱的", "https://res-cdn.nuan.chat/res/prod/gift/image/gift_icon_waxy.png", 888),
        GIFT_6(1, 1, "ZNMG_GIFT", "赠你玫瑰", "https://res-cdn.nuan.chat/gift-image/2022-01/1643099679110093.png", 520),
        GIFT_7(1, 2, "ZDQC_GIFT", "纸短情长", "https://res-cdn.nuan.chat/gift-image/2022-04/1650353482409804.png", 1314),
        GIFT_8(1, 3, "XBAN_GIFT", "宣布爱你", "https://res-cdn.nuan.chat/gift-image/2021-09/1632972509753666.png", 3344),
        GIFT_9(2, 0, "YJQX_GIFT", "一见倾心", "https://res-cdn.nuan.chat/gift-image/2021-06/1622629390146736.png", 25),
        GIFT_10(2, 1, "TLMS_GIFT", "提拉米苏", "https://res-cdn.nuan.chat/res/prod/gift/image/62403b814fdc2f5d.png", 77),
        GIFT_11(2, 2, "YSZA_GIFT", "一生挚爱", "https://res-cdn.nuan.chat/gift-image/2022-01/1643193053211311.png", 1314),
        GIFT_12(2, 3, "YYX_GIFT", "姻缘线", "https://res-cdn.nuan.chat/gift-image/2021-08/1628235597270306.png", 177),
        GIFT_13(3, 0, "ZAYH_GIFT", "真爱永恒", "https://res-cdn.nuan.chat/gift-image/2021-10/1634887201180013.png", 5200),
        GIFT_14(3, 1, "YNRH_GIFT", "拥你入怀", "https://res-cdn.nuan.chat/gift-image/2021-09/1631181249433650.png", 999),
        GIFT_15(3, 2, "GZC_GIFT", "公主床", "https://res-cdn.nuan.chat/res/prod/gift/image/nl_im_gift_pressine_bed_icon.png", 5200),
        GIFT_16(3, 3, "YWDQ_GIFT", "一吻定情", "https://res-cdn.nuan.chat/gift-image/2021-08/1630377917413432.png", 3344);

        private final Integer row;

        private final Integer col;
        private final String giftKey;

        private final String name;

        private final String icon;

        private final Integer price;


    }

    //todo icon
    @AllArgsConstructor
    @Getter
    public enum Reward {

        REWORD_1(0, 4, 0, "yishengsuoai_head_frame", "一生所爱", Arrays.asList(Gift2.GIFT_1, Gift2.GIFT_2, Gift2.GIFT_3, Gift2.GIFT_4), "https://res-cdn.nuan.chat/admin-v2/files/dev/2024-08/323c3734d9b46ad68f312a3bba2b9353.png", null),
        REWORD_2(1, 4, 0, "jiuxiaoshenhui_head_frame", "九宵神辉", Arrays.asList(Gift2.GIFT_5, Gift2.GIFT_6, Gift2.GIFT_7, Gift2.GIFT_8), "https://res-cdn.nuan.chat/res/branch:test-luckdraw20220830/gift/image/6316c49267be91df.png", null),
        REWORD_3(2, 4, 0, "xingtuyaolan_mount", "星兔摇篮", Arrays.asList(Gift2.GIFT_9, Gift2.GIFT_10, Gift2.GIFT_11, Gift2.GIFT_12), "https://res-cdn.nuan.chat/res/test/mount/image/huiye.png", null),
        REWORD_4(3, 4, 0, "LAFL_GIFT", "幸运风铃", Arrays.asList(Gift2.GIFT_13, Gift2.GIFT_14, Gift2.GIFT_15, Gift2.GIFT_16), "https://res-cdn.nuan.chat/res/prod/gift/image/gift_icon_lafl.png", 66),
        REWORD_5(4, 0, 0, "mengjinghuahai_mount", "梦境花海", Arrays.asList(Gift2.GIFT_1, Gift2.GIFT_5, Gift2.GIFT_9, Gift2.GIFT_13), "https://res-cdn.nuan.chat/res/test/mount/image/nl_im_welcome_mengjinghuahai0206_icon.png.png", null),
        REWORD_6(4, 1, 0, "langmanxinghe_entry_special_effect", "浪漫星河", Arrays.asList(Gift2.GIFT_2, Gift2.GIFT_6, Gift2.GIFT_10, Gift2.GIFT_14), "https://res-cdn.nuan.chat/res/qa/entry_special_effect/image/icon211124.png", null),
        REWORD_7(4, 2, 0, "duijiudangge_entry_special_effect", "对酒当歌", Arrays.asList(Gift2.GIFT_3, Gift2.GIFT_7, Gift2.GIFT_11, Gift2.GIFT_15), "https://res-cdn.nuan.chat/res/qa/entry_special_effect/image/djdg_icon.png", null),
        REWORD_8(4, 3, 0, "fengxiangyutian_head_frame", "凤翔于天", Arrays.asList(Gift2.GIFT_4, Gift2.GIFT_8, Gift2.GIFT_12, Gift2.GIFT_16), "https://res-cdn.nuan.chat/res/test01/gift/image/62c3d8265191ae39.png", null),
        //终极奖励
        REWORD_9(4, 4, 1, "XFXC_GIFT", "幸福小船", null, "https://res-cdn.nuan.chat/gift-image/2023-05/1684653913255465.png", 520);

        private final Integer row;

        private final Integer col;

        private final Integer type;

        private final String itemKey;

        private final String itemName;

        private final List<Gift2> gift2;
        private final String icon;

        private final Integer price;
    }

    @Data
    public static class Point {
        private Integer x;

        private Integer y;
    }

    @Data
    @AllArgsConstructor
    public static class GiftInfo {
        private Integer status;

        private String giftKey;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LoveStorySendGiftParams {
        @NotNull
        private Long toUid;

        @NotBlank
        private String itemKey;

        @NotNull
        private Long productCount;
    }


}
