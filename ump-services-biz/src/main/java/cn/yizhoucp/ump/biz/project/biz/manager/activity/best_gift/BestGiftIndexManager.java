package cn.yizhoucp.ump.biz.project.biz.manager.activity.best_gift;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.best_gift.common.BestGiftConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.best_gift.common.BestGiftRedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.best_gift.vo.BestGiftIndexVO;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.IndexManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.dal.service.DrawPoolItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 最后的礼物首页
 *
 * <AUTHOR>
 * @version V1.0
 * @since 23:15 2025/5/25
 */
@Slf4j
@Service
public class BestGiftIndexManager implements IndexManager {
    @Resource
    private BestGiftRedisManager bestGiftRedisManager;
    @Resource
    private DrawPoolItemService drawPoolItemService;

    @Override
    public BestGiftIndexVO getIndex(BaseParam param, Long toUid, String extData) {
        Long uid = param.getUid();
        return BestGiftIndexVO.builder()
                .giftPool(buildGiftPool())
                .lotteryItems(bestGiftRedisManager.getLotteryItems(uid).intValue())
                .build();
    }

    private List<BestGiftIndexVO.GiftItem> buildGiftPool() {
        List<DrawPoolItemDO> drawPoolItemDOList = drawPoolItemService.getByPoolCode(BestGiftConstant.LOTTERY_POOL_CODE);
        if (CollectionUtils.isEmpty(drawPoolItemDOList)) {
            log.error("目标奖池不存在 poolCode:{}", BestGiftConstant.LOTTERY_POOL_CODE);
            throw new ServiceException(ErrorCode.MISS_PARAM, "奖池列表为空");
        }
        return drawPoolItemDOList.stream().map(drawPoolItemDO -> BestGiftIndexVO.GiftItem.builder()
                .giftIcon(drawPoolItemDO.getItemIcon())
                .giftName(drawPoolItemDO.getItemName())
                .giftValue(drawPoolItemDO.getItemValueGold())
                .giftKey(drawPoolItemDO.getItemKey())
                .build()).collect(Collectors.toList());
    }

    @Override
    public String getTemplateType() {
        return "";
    }

    @Override
    public String getActivityCode() {
        return "";
    }
}
