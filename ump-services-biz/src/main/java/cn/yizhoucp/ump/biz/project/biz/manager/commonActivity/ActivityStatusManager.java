package cn.yizhoucp.ump.biz.project.biz.manager.commonActivity;


import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.biz.project.biz.manager.userActivity.UserActivityManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ActivityJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.UserActivityJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ActivityDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.UserActivityDO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Example;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 判断用户是否加入活动
 *
 * @author: lianghu
 */
@Slf4j
@Service
public class ActivityStatusManager {

    @Resource
    private ActivityJpaDAO activityJpaDAO;
    @Resource
    private UserActivityJpaDAO userActivityJpaDAO;
    @Resource
    private UserActivityManager userActivityManager;

    /**
     * 全服类型活动 API
     *
     * @param param
     * @param activityCode
     * @return
     */
    public Boolean activityIsEnable(BaseParam param, String activityCode) {
        log.debug("查询活动 param:{}, activityCode:{}", JSON.toJSONString(param), activityCode);
        if (StringUtils.isBlank(activityCode)) {
            return Boolean.FALSE;
        }
        Optional<ActivityDO> opt = activityJpaDAO.findOne(Example.of(ActivityDO.builder()
                .unionId(param.getUnionId())
                .activityCode(activityCode).build()));
        if (!opt.isPresent()) {
            log.warn("活动不存在 param:{}, activityCode:{}", JSON.toJSONString(param), activityCode);
            return Boolean.FALSE;
        }
        return opt.get().getStatus() > 0;
    }

    /**
     * 用户类型活动 API
     *
     * @param param
     * @param activityCode
     * @return
     */
    public Boolean hasJoinedActivity(BaseParam param, String activityCode) {
        Optional<UserActivityDO> opt = userActivityJpaDAO.findOne(Example.of(UserActivityDO.builder()
                .appId(param.getAppId())
                .unionId(param.getUnionId())
                .userId(param.getUid())
                .status("enable")
                .activityCode(activityCode).build()));
        if (!opt.isPresent()) {
            return Boolean.FALSE;
        }
        // 退出活动判定
        // todo: 接入超时中心后移除
        UserActivityDO userActivityDO = opt.get();
        LocalDateTime now = LocalDateTime.now();
        if (userActivityDO.getEndTime().isBefore(now)) {
            log.info("过期判定成功 uid:{}, activityDO:{}", param.getUid(), userActivityDO);
            userActivityManager.exitUserActivity(param.getAppId(), param.getUnionId(), param.getUid(), userActivityDO);
        }
        return Boolean.TRUE;
    }

    /**
     * 通过活动code 查询活动是否开启
     *
     * @param activityCode
     * @return
     */
    @Deprecated
    public Boolean checkActivityIsEnableByActivityCode(String activityCode) {
        log.debug("查询活动 checkActivityIsEnableByActivityCode  activityCode:{}", activityCode);
        Optional<ActivityDO> opt = activityJpaDAO.findOne(Example.of(ActivityDO.builder()
                .activityCode(activityCode).build()));
        if (!opt.isPresent()) {
            log.warn("活动不存在 checkActivityIsEnableByActivityCode  activityCode:{}", activityCode);
            return Boolean.FALSE;
        }
        return opt.get().getStatus() > 0;
    }

}
