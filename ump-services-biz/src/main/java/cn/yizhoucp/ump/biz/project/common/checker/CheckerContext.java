package cn.yizhoucp.ump.biz.project.common.checker;

import cn.yizhoucp.ump.biz.project.biz.manager.goddessTrain.GoddessTrainAppointmentManager;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static cn.yizhoucp.ump.biz.project.common.checker.CheckerChainEnum.*;


/**
 * <AUTHOR>
 * @Date 2022/8/9 14:54
 * @Version 1.0
 * checker上下文
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CheckerContext {

    /** 链枚举 */
    private CheckerChainEnum chainEnum;
    /** 性别 */
    private String sex;
    /** AB 实验 key */
    private String abTestKey;
    /** AB 实验分组组 */
    private String abTestExperiment;
    /** 分群 ID */
    private Long cohortId;
    /** 参与活动验证 */
    private String userActivity;
    /** 参与活动模板验证 */
    private String userActivityTemplate;
    /** 参与同模板活动数量限制 */
    private String joinTemplateLimit;
    /** 加入活动业务验证 */
    private Boolean joinActivityBizCheck;
    /** 查询资源位业务验证 */
    private Boolean queryAdSpaceBizCheck;
    /** iOS 审核开关验证 */
    private Boolean iOSInReviewCheck;
    /** 幂等验证编号 */
    private String idempotentCode;
    /** 幂等验证持续时间 */
    private Integer idempotentDuring;
    /** 平台 */
    private String platform;
    /** 开始时间 */
    private String timeRangeStart;
    /** 结束时间 */
    private String timeRangeEnd;
    /** 星期验证 */
    private List<String> dayOfWeek;
    /** 星期 & 时间混合验证 */
    private Map<String, List<String>> dateTimeLimit;
    /** 任务拓展参数 */
    private JSONObject missionExt;
    /** 豪气值验证 */
    private Long haughtyValue;
    /** 主播房间验证 */
    private Boolean anchorRoomCheck;
    /** 版本号验证 */
    private String yumoVersion;
    private String nuanliaoVersion;
    private String huayuanVersion;
    private String lianainiangVersion;
    private String lianainiangIOSVersion;
    private Boolean checkRandomGameShow;

    private Boolean hideAd;
    private Boolean allShow;

    /** 是否需要校验甜蜜小屋 */
    private Boolean sweetCabin;

    private String putOnChannelNot;

    /** 注册时长验证 */
    private Integer registrationDuration;

    /***************************** 业务回调类型参数，不可复用，谨慎拓展 *************************************/

    /** 要求当下为本轮周期的第一周 {@link GoddessTrainAppointmentManager} */
    private Boolean needFirstWeek;

    public static CheckerContext getJoinActivityChainContext(String config) {
        if (StringUtils.isBlank(config)) {
            return CheckerContext.builder()
                    .chainEnum(JOIN_USER_ACTIVITY).build();
        }
        JSONObject param = JSONObject.parseObject(config).getJSONObject("checkerContext");
        if (Objects.isNull(param)) {
            return CheckerContext.builder()
                    .chainEnum(JOIN_USER_ACTIVITY).build();
        }
        CheckerContext context = param.getObject(JOIN_USER_ACTIVITY.getConfigKey(), CheckerContext.class);
        context.setChainEnum(JOIN_USER_ACTIVITY);
        return context;
    }

    public static CheckerContext getGetAdSpaceChainContext(String config) {
        if (StringUtils.isBlank(config)) {
            return CheckerContext.builder()
                    .chainEnum(GET_AD_SPACE_LIST).build();
        }
        JSONObject param = JSONObject.parseObject(config).getJSONObject("checkerContext");
        if (Objects.isNull(param)) {
            return CheckerContext.builder()
                    .chainEnum(GET_AD_SPACE_LIST).build();
        }
        CheckerContext context = param.getObject(GET_AD_SPACE_LIST.getConfigKey(), CheckerContext.class);
        context.setChainEnum(GET_AD_SPACE_LIST);
        return context;
    }

    public static CheckerContext getPushPopMsgChainContext(String config) {
        if (StringUtils.isBlank(config)) {
            return CheckerContext.builder()
                    .chainEnum(PUSH_POP_MSG).build();
        }
        JSONObject param = JSONObject.parseObject(config).getJSONObject("checkerContext");
        if (Objects.isNull(param)) {
            return CheckerContext.builder()
                    .chainEnum(PUSH_POP_MSG).build();
        }
        CheckerContext context = param.getObject(PUSH_POP_MSG.getConfigKey(), CheckerContext.class);
        context.setChainEnum(PUSH_POP_MSG);
        return context;
    }

}
