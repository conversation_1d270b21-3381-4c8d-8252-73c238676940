package cn.yizhoucp.ump.biz.project.biz.manager.activity.loveInProgress.strategy;

import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 恋爱进行时策略类Demo
 *
 * <AUTHOR>
 * @version V1.0
 * @since 14:49 2025/2/14
 */
@Component
@Slf4j
public class LoveInProgressDemoStrategy implements ExecutableStrategy {


    @Override
    public Object execute(ButtonEventParam buttonEventParam) {
        return null;
    }
}
