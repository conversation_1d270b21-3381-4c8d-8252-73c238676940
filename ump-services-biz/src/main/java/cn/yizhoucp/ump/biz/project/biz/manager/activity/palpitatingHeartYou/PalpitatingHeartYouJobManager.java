package cn.yizhoucp.ump.biz.project.biz.manager.activity.palpitatingHeartYou;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 定时任务发放奖励
 */
@Service
@Slf4j
public class PalpitatingHeartYouJobManager {

    @Resource
    private PalpitatingHeartYouRankManager rankManager;

    /**
     * 发放陪伴榜奖励
     *
     * @return
     */
    public Boolean sendAccompanyLeaderboard() {
        return rankManager.sendAccompanyLeaderboard();
    }

    /**
     * 发放心动榜奖励
     *
     * @return
     */
    public Boolean sendHeartbeatLeaderboard() {
        return rankManager.sendHeartbeatLeaderboard();
    }
}
