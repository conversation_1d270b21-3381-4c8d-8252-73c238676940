package cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.strategy;

import cn.yizhoucp.ms.core.base.enums.UserPackageBizType;
import cn.yizhoucp.product.client.UserPackageFeignService;
import cn.yizhoucp.product.dto.PackageQueryConditionDTO;
import cn.yizhoucp.product.dto.UserPackageDetailDTO;
import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.common.MainChineseNewYear2025Enums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.common.MainChineseNewYear2025RedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.vo.CollectCardsForLuckVO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class CollectCardsForLuckStrategy implements MainChineseNewYear2025Strategy {

    @Resource
    private MainChineseNewYear2025RedisManager mainChineseNewYear2025RedisManager;
    @Resource
    private UserPackageFeignService userPackageFeignService;


    @Override
    public CollectCardsForLuckVO execute(ButtonEventParam buttonEventParam) {
        Long uid = buttonEventParam.getBaseParam().getUid();
        return CollectCardsForLuckVO.builder()
                .cards(buildCardVO(uid))
                .specialCard(buildSpecialCard(uid))
                .build();
    }


    private CollectCardsForLuckVO.CardVO buildSpecialCard(Long uid) {
        MainChineseNewYear2025Enums.CardEnum cardEnum = MainChineseNewYear2025Enums.CardEnum.CARD_6;
        Long quantity = mainChineseNewYear2025RedisManager.getDrawItems(uid);
        return CollectCardsForLuckVO.CardVO.builder()
                .cardKey(cardEnum.getCardCode())
                .cardName(cardEnum.getCardName())
                .quantity(quantity.intValue())
                .build();
    }


    public List<CollectCardsForLuckVO.CardVO> buildCardVO(Long uid) {
        final MainChineseNewYear2025Enums.CardEnum[] cardEnums = MainChineseNewYear2025Enums.CardEnum.getNorMalCard();

        List<String> cards = Arrays.stream(cardEnums)
                .map(MainChineseNewYear2025Enums.CardEnum::getCardCode)
                .collect(Collectors.toList());

        PackageQueryConditionDTO queryParam = PackageQueryConditionDTO.builder()
                .uid(uid)
                .bizIdList(cards)
                .bizType(UserPackageBizType.GIFT.getCode())
                .build();

        Map<String, UserPackageDetailDTO> packageMap = userPackageFeignService
                .getPackageDetailMapByCondition(queryParam)
                .successData();

        return Arrays.stream(cardEnums)
                .map(e -> createCardVO(e, packageMap))
                .collect(Collectors.toList());
    }

    private CollectCardsForLuckVO.CardVO createCardVO(MainChineseNewYear2025Enums.CardEnum e, Map<String, UserPackageDetailDTO> packageMap) {
        UserPackageDetailDTO dto = packageMap.get(e.getCardCode());
        int quantity = (dto != null) ? dto.getAvailableNum().intValue() : 0;

        return CollectCardsForLuckVO.CardVO.builder()
                .cardKey(e.getCardCode())
                .cardName(e.getCardName())
                .quantity(quantity)
                .build();
    }
}
