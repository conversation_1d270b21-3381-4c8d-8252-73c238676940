package cn.yizhoucp.ump.biz.project.biz.manager.activity.loveInProgress.strategy;

import cn.hutool.core.collection.CollUtil;
import cn.yizhoucp.ms.core.base.convert.SexType;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.loveInProgress.common.LoveInProgressConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.loveInProgress.common.LoveInProgressEnums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.loveInProgress.common.LoveInProgressRedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.moonAboveTheSea.vo.MoonAboveTheSeaTaskVO;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.user.manager.UserFeignManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LoveInProgressTaskInfoStrategy implements ExecutableStrategy {


    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    private LoveInProgressRedisManager loveInProgressRedisManager;
    @Resource
    private UserFeignManager userFeignManager;

    public String getActivityCode() {
        return LoveInProgressConstant.ACTIVITY_CODE;
    }

    @Override
    public Object execute(ButtonEventParam buttonEventParam) {
        Long uid = buttonEventParam.getBaseParam().getUid();
        List<MoonAboveTheSeaTaskVO> tasks = CollUtil.newArrayList();
        List<String> taskScene = CollUtil.newArrayList();
        List<String> rewardScene = CollUtil.newArrayList();
        for (LoveInProgressEnums.TaskEnum dailyTaskEnum : LoveInProgressEnums.TaskEnum.values()) {
            taskScene.add(dailyTaskEnum.getTaskCode());
            rewardScene.add(dailyTaskEnum.getRewardCode());
        }
        List<ScenePrizeDO> scenePrizeDOS = scenePrizeJpaDAO.getListBySceneCodeList(ServicesAppIdEnum.lanling.getAppId(), getActivityCode(), taskScene);
        List<ScenePrizeDO> rewardPrizeDOS = scenePrizeJpaDAO.getListBySceneCodeList(ServicesAppIdEnum.lanling.getAppId(), getActivityCode(), rewardScene);
        Map<String, ScenePrizeDO> taskPrizeDOMap = scenePrizeDOS.stream().collect(Collectors.toMap(ScenePrizeDO::getSceneCode, scenePrizeDO -> scenePrizeDO));
        Map<String, List<ScenePrizeDO>> rewardPrizeDOMap = rewardPrizeDOS.stream().collect(Collectors.groupingBy(ScenePrizeDO::getSceneCode));
        for (LoveInProgressEnums.TaskEnum dailyTaskEnum : LoveInProgressEnums.TaskEnum.values()) {
            MoonAboveTheSeaTaskVO task = new MoonAboveTheSeaTaskVO();
            ScenePrizeDO taskPrize = taskPrizeDOMap.get(dailyTaskEnum.getTaskCode());
            List<ScenePrizeDO> rewardPrize = rewardPrizeDOMap.get(dailyTaskEnum.getRewardCode());
            if (taskPrize == null || rewardPrize == null) {
                log.error("任务或奖励不存在,taskCode:{},rewardCode:{}", dailyTaskEnum.getTaskCode(), dailyTaskEnum.getRewardCode());
                continue;
            }
            task.setTaskCode(dailyTaskEnum.getTaskCode());
            task.setTotalProgress(dailyTaskEnum.getTotalProgress());
            Long progress = loveInProgressRedisManager.getTaskProgress(uid, dailyTaskEnum.getTaskCode());
            progress = progress > dailyTaskEnum.getTotalProgress() ? dailyTaskEnum.getTotalProgress() : progress;
            task.setCurrentProgress(progress);
            UserVO userVO = userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), uid);
            if (userVO == null) {
                log.error("用户信息不存在,uid:{}", uid);
                continue;
            }
            String taskDesc;
            if (userVO.getSex().getCode().equals(SexType.MAN.getCode())) {
                taskDesc = String.format(LoveInProgressConstant.MAN_TASK_DESC, taskPrize.getPrizeDesc(), dailyTaskEnum.getTotalProgress());
            } else {
                taskDesc = String.format(LoveInProgressConstant.WOMAN_TASK_DESC, taskPrize.getPrizeDesc(), dailyTaskEnum.getTotalProgress());
            }
            task.setTaskDesc(taskDesc);
            String taskRewardDesc = rewardPrize.stream().map(scenePrizeDO -> String.format(LoveInProgressConstant.TASK_REWARD_DESC, scenePrizeDO.getPrizeNum())).collect(Collectors.joining(","));
            task.setTaskRewardDesc(taskRewardDesc);
            task.setClaimButtonStatus(getClaimButtonStatus(uid, dailyTaskEnum));
            task.setTaskIcon(taskPrize.getPrizeIcon());
            tasks.add(task);
        }
        return tasks;
    }

    private int getClaimButtonStatus(Long uid, LoveInProgressEnums.TaskEnum dailyTaskEnum) {
        boolean taskClaimed = loveInProgressRedisManager.getTaskClaimed(uid, dailyTaskEnum.getTaskCode());
        if (!taskClaimed) {
            return LoveInProgressEnums.TaskStatusEnum.NOT_TAKEN.getCode();
        }
        Long progress = loveInProgressRedisManager.getTaskProgress(uid, dailyTaskEnum.getTaskCode());
        boolean taskHasFinished = progress >= dailyTaskEnum.getTotalProgress();
        boolean rewardClaimed = loveInProgressRedisManager.getRewardClaimed(uid, dailyTaskEnum.getRewardCode());
        if (!taskHasFinished) {
            return LoveInProgressEnums.TaskStatusEnum.TAKEN.getCode();
        }
        if (!rewardClaimed) {
            return LoveInProgressEnums.TaskStatusEnum.FINISHED.getCode();
        }
        return LoveInProgressEnums.TaskStatusEnum.REWARD_TAKEN.getCode();
    }

}
