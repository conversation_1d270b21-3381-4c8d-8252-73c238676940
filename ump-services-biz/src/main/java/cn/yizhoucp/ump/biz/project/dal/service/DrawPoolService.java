package cn.yizhoucp.ump.biz.project.dal.service;

import cn.yizhoucp.ump.biz.project.dal.jpa.dao.DrawPoolJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolDO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 奖池
 *
 * @author: lianghu
 */
@Service
public class DrawPoolService {

    @Resource
    private DrawPoolJpaDAO drawPoolJpaDAO;


    public DrawPoolDO save(DrawPoolDO drawPoolDO) {
        return drawPoolJpaDAO.save(drawPoolDO);
    }

    /**
     * 根据活动与时间查询奖池
     *
     * @param activityCode
     * @param now
     * @return api.project.dal.jpa.dataobject.ActivityDO
     */
    public DrawPoolDO getByActivityCodeAndTime(String activityCode, String bizKey, LocalDateTime now) {
        return drawPoolJpaDAO.getByActivityCodeAndTime(activityCode, bizKey, now);
    }

    /**
     * 查询数据
     *
     * @param unionId
     * @param activityCode
     * @param status
     * @return
     */
    public List<DrawPoolDO> getActivityLuckBagList(String unionId, String activityCode, Integer status) {
        List<DrawPoolDO> result = null;
        if (StringUtils.isEmpty(activityCode) && null == status) {
            result = drawPoolJpaDAO.findByUnionId(unionId);
        } else if (StringUtils.isEmpty(activityCode)) {
            result = drawPoolJpaDAO.findByUnionIdAndStatus(unionId, status);
        } else if (null == status) {
            result = drawPoolJpaDAO.findByUnionIdAndActivityCode(unionId, activityCode);
        } else {
            result = drawPoolJpaDAO.findByUnionIdAndActivityCodeAndStatus(unionId, activityCode, status);
        }
        return result;
    }

    /**
     * 根据活动与业务KEY查询
     *
     * @param activityCode
     * @param bizKey
     * @return api.project.dal.jpa.dataobject.ActivityDO
     */
    public List<DrawPoolDO> getByActivityCodeAndBizKey(String activityCode, String bizKey) {
        return drawPoolJpaDAO.getByActivityCodeAndBizKey(activityCode, bizKey);
    }

    public List<DrawPoolDO> getByCode(String poolCode) {
        return drawPoolJpaDAO.getByCode(poolCode);
    }

    public void deleteById(Long id) {
        drawPoolJpaDAO.deleteById(id);
    }

    /**
     * 根据业务KEY查询
     *
     * @param activityCode
     * @param poolCode
     * @return api.project.dal.jpa.dataobject.DrawPoolDO
     */
    public DrawPoolDO getByActivityCodeAndPoolCodeAndEnable(String activityCode, String poolCode) {
        return drawPoolJpaDAO.getByActivityCodeAndPoolCode(activityCode, poolCode);
    }

    public DrawPoolDO findByActivityCodeAndPoolCode(String activityCode, String poolCode) {
        return drawPoolJpaDAO.findByActivityCodeAndPoolCode(activityCode, poolCode);
    }

    public DrawPoolDO getById(Long id) {
        Optional<DrawPoolDO> byId = drawPoolJpaDAO.findById(id);
        return byId.isPresent() ? byId.get() : null;
    }

    public List<String> findAllPoolCode() {
        return drawPoolJpaDAO.findAllPoolCode();
    }

    public DrawPoolDO findByPoolCode(String poolCode) {
        return drawPoolJpaDAO.findByPoolCode(poolCode);
    }

    public List<DrawPoolDO> findByActivityCode(String activityCode) {
        return drawPoolJpaDAO.findByActivityCode(activityCode);
    }
}
