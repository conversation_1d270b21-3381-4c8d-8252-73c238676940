package cn.yizhoucp.ump.biz.project.biz.manager.activity.doubleEgg2023;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.convert.SexType;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.enums.coin.TreasurePoolEnum;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.jimu.draw.inner.DrawLogItem;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.Magpie2023Constant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.log.LogComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.AbstractDrawTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawConfig;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawContext;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawLogParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.doDrawStrategy.common.ProbStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.FeignUserService;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawLogDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.DoubleEgg2023Constant.ACTIVITY_CODE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.DoubleEgg2023Constant.CAROUSEL;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.DoubleEgg2023Constant.DRAW_TICKET_KEY;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.DoubleEgg2023Constant.MH_GIFT_LIGHT_KEY;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.DoubleEgg2023Constant.MH_GIFT_LIGHT_POOL_CODE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.Magpie2023Constant.LOVE_POOL_DRAW_TIME;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.MidAutumn2023Constant.DRAW_HORN;

@Service
@Slf4j
public class DoubleEgg2023DrawManager extends AbstractDrawTemplate {

    @Resource
    private ProbStrategy probStrategy;
    @Resource
    private RedisManager redisManager;
    @Resource
    private NotifyComponent notifyComponent;
    @Resource
    private LogComponent logComponent;
    @Resource
    private FeignUserService feignUserService;
    @Resource
    private DoubleEgg2023BizManager doubleEgg2023BizManager;

    @Override
    protected void resourceCheck(DrawContext context) {
        DrawParam drawParam = context.getDrawParam();
        String poolCode = drawParam.getPoolCode();

        if ("SDMH_GIFT".equals(poolCode) || "YDMH_GIFT".equals(poolCode)) {
            // 不下发礼物
            drawParam.setNoSendPrize(Boolean.TRUE);
        }
        if ("TCHX_DRAW_POOL".equals(poolCode)) {
            Integer drawTicket = redisManager.getInteger(String.format(DRAW_TICKET_KEY, drawParam.getUid()));
            log.info("drawTicket {}", drawTicket);
            if (drawTicket == null || drawTicket <= 0) {
                throw new ServiceException(ErrorCode.MISS_PARAM, "抽奖券不足");
            }

            redisManager.decrLong(String.format(DRAW_TICKET_KEY, drawParam.getUid()), context.getDrawParam().getTimes(), DateUtil.ONE_MONTH_SECOND);
        }
    }

    @Override
    protected void draw(DrawContext context) {
        List<DrawPoolItemDTO> drawPoolItemDTOList = probStrategy.getDrawPoolItems(context.getDrawPoolItemDOS(), context.getDrawParam().getTimes(), Boolean.TRUE);
        context.setPrizeItemList(drawPoolItemDTOList);
    }

    @Override
    protected void deductResource(DrawContext context) {
    }

    @Override
    protected void doCallback(DrawContext context) {
        DrawParam drawParam = context.getDrawParam();
        String poolCode = drawParam.getPoolCode();

        if ("SDMH_GIFT".equals(poolCode) || "YDMH_GIFT".equals(poolCode)) {
            String key = String.format(MH_GIFT_LIGHT_KEY, poolCode, context.getDrawParam().getUid());
            int times = 1;
            if (context.getDrawParam().getTimes() == 1) {
                redisManager.sSetExpire(key, DateUtil.ONE_MONTH_SECOND, context.getPrizeItemList().get(0).getDrawPoolItemDO().getItemKey());
            } else {
                int min = Integer.MAX_VALUE;
                Set<Object> set = redisManager.sGet(key);
                for (DrawPoolItemDTO drawPoolItemDTO : context.getPrizeItemList()) {
                    String itemKey = drawPoolItemDTO.getDrawPoolItemDO().getItemKey();
                    redisManager.sSetExpire(key, DateUtil.ONE_MONTH_SECOND, itemKey);
                    min = Math.min(min, (set.contains(itemKey) ? 1 : 0) + drawPoolItemDTO.getTargetTimes());
                }
                if (context.getPrizeItemList().size() == 5) {
                    times = min;
                }
            }
            Long setSize = Optional.ofNullable(redisManager.sGetSetSize(key)).orElse(0L);
            if (setSize == 5) {
                this.sendLightPrize(poolCode, times, drawParam);
                redisManager.delete(key);
            }
        }

        if ("TCHX_DRAW_POOL".equals(poolCode)) {
            logComponent.putDrawLog(drawParam.getBaseParam(), drawParam.getActivityCode(), context.getPrizeItemList());

            UserVO userVO = feignUserService.getBasic(context.getDrawParam().getUid(), context.getDrawParam().getAppId()).successData();
            if (userVO != null) {
                for (DrawPoolItemDTO drawPoolItemDTO : context.getPrizeItemList()) {
                    redisManager.listLpush(CAROUSEL, String.format("恭喜 %s 抽出 %s", userVO.getName(), drawPoolItemDTO.getDrawPoolItemDO().getItemName()), DateUtil.ONE_MONTH_SECOND);
                }
                redisManager.listTrim(CAROUSEL, 0, 60);
            }

            notifyComponent.npcNotify(drawParam.getUnionId(), drawParam.getUid(), String.format("恭喜您在“双旦送暖迎新岁”活动“糖彩贺新”中成功抽奖1次并获得1个%s金币的礼物“%s”，礼物已下发至您的背包，请注意查收~", context.getPrizeItemList().get(0).getDrawPoolItemDO().getItemValueGold(), context.getPrizeItemList().get(0).getDrawPoolItemDO().getItemName()));

            context.setDrawLeft(redisManager.getInteger(String.format(DRAW_TICKET_KEY, drawParam.getUid())));
        }

        List<DrawPoolItemDTO> prizeItemList = context.getPrizeItemList();
        if (CollectionUtils.isEmpty(prizeItemList)) {
            return;
        }
        for (DrawPoolItemDTO item : prizeItemList) {
            doubleEgg2023BizManager.allActivityReceiveAwardTrack("sugar_colored_lottery", null, item.getDrawPoolItemDO().getItemKey(), item.getDrawPoolItemDO().getItemValueGold(), item.getTargetTimes(), drawParam.getUid());
        }
    }

    @Override
    protected List<DrawLogItem> drawLogWrapper(DrawLogParam param, List<DrawLogDO> drawLogDOList) {
//        drawLogDOList.sort(Comparator.comparingLong(d -> d.getCreateTime().toInstant(ZoneOffset.UTC).toEpochMilli()));
        List<DrawLogItem> drawLogItemList = drawLogDOList.stream().map(drawLogDO -> {
            DrawPoolItemDO drawPoolItemDO = JSON.parseObject(drawLogDO.getLogJson(), DrawPoolItemDTO.class).getDrawPoolItemDO();
            return DrawLogItem.builder()
                    .icon(drawPoolItemDO.getItemIcon())
                    .valueGold(drawPoolItemDO.getItemValueGold())
                    .text(drawPoolItemDO.getItemName())
                    .itemNum(drawPoolItemDO.getItemNum())
                    .time(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(drawLogDO.getCreateTime())).build();
        }).collect(Collectors.toList());
        log.debug("drawLogItemList {}", JSON.toJSONString(drawLogItemList));
        Collections.reverse(drawLogItemList);

        return drawLogItemList;
    }

    private void sendLightPrize(String poolCode, Integer times, DrawParam drawParam) {
        List<DrawPoolItemDO> drawPoolItemDOList = drawPoolItemService.getByPoolCode(String.format(MH_GIFT_LIGHT_POOL_CODE, poolCode));
        List<DrawPoolItemDTO> drawPoolItemDTOList = probStrategy.getDrawPoolItems(drawPoolItemDOList, times, Boolean.TRUE);
        List<SendPrizeDTO> sendPrizeDTOList = drawPoolItemDTOList.stream().map(drawPoolItemDTO -> SendPrizeDTO.of(drawPoolItemDTO, ACTIVITY_CODE)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(sendPrizeDTOList)) {
            sendPrizeManager.sendPrize(drawParam.getBaseParam(), sendPrizeDTOList);
            for (SendPrizeDTO sendPrizeDTO : sendPrizeDTOList) {
                notifyComponent.npcNotify(drawParam.getUnionId(), drawParam.getUid(), String.format("恭喜您在“双旦送暖迎新岁”活动中成功点亮%s次“%s”，您可以额外获得%s个%s金币的礼物“%s”，礼物已下发至您的背包，请注意查收~", sendPrizeDTO.getPrizeNum(), Optional.ofNullable(TreasurePoolEnum.getByName(poolCode)).map(TreasurePoolEnum::getName).orElse(""), sendPrizeDTO.getPrizeNum(), sendPrizeDTO.getValueGold(), sendPrizeDTO.getPrizeName()));
            }
        }

        for (SendPrizeDTO sendPrizeDTO : sendPrizeDTOList) {
            doubleEgg2023BizManager.allActivityReceiveAwardTrack("SDMH_GIFT".equals(poolCode) ? "christmas_box_lighten" : "new_years_day_lighten", null, sendPrizeDTO.getPrizeValue(), sendPrizeDTO.getValueGold(), sendPrizeDTO.getPrizeNum(), drawParam.getUid());
        }
    }

}
