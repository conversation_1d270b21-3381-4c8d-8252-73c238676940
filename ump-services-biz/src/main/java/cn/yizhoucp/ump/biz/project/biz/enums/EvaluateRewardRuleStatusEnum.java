package cn.yizhoucp.ump.biz.project.biz.enums;

import lombok.Getter;

public enum EvaluateRewardRuleStatusEnum {
    SEND_MESSAGE("已发送小助手消息"),

    PRE_MARK_SCORE("已预评分"),

    UPLOAD_SUCCESS("上传评价成功"),

    SUCCESS("审核通过，奖励发放"),

    PRE_MARK_FAIL("预评分过低"),

    FAIL("审核未通过")
    ;

    @Getter
    private String desc;

    EvaluateRewardRuleStatusEnum(String desc) {
        this.desc = desc;
    }
}
