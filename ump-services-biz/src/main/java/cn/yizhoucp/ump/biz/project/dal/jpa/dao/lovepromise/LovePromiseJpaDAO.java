package cn.yizhoucp.ump.biz.project.dal.jpa.dao.lovepromise;

import cn.yizhoucp.enums.lovepromise.LovePromiseStatusEnum;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.lovepromise.LovePromiseDO;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface LovePromiseJpaDAO extends CrudRepository<LovePromiseDO, Long> {

    List<LovePromiseDO> findByApplyTimeAfterAndStatusIn(Date startTime, List<LovePromiseStatusEnum> statusList);

    List<LovePromiseDO> findByCompleteTimeAfterAndStatusIn(Date  startTime, List<LovePromiseStatusEnum> statusList);

    @Query(value = "select * from love_promise where (( from_uid = ?1 and to_uid = ?2) or ( from_uid = ?2 and to_uid = ?1 )) and `status` != 'cancel' order by apply_time desc limit 1", nativeQuery = true)
    LovePromiseDO findLatestApplyByUid1AndUid2AndNotCancel(Long uid, Long toUid);

    @Query(value = "select * from love_promise where (from_uid = ?1 or to_uid = ?1) and `status` != 'cancel' order by apply_time desc", nativeQuery = true)
    List<LovePromiseDO> findByUidAndStatusNotCancel(Long toUid);

    @Query(value = "select * from love_promise where (( from_uid = ?1 and to_uid = ?2) or ( from_uid = ?2 and to_uid = ?1 ))", nativeQuery = true)
    LovePromiseDO findOneByUid1AndUid2(Long uid1, Long uid2);

    List<LovePromiseDO> findByRoomIdInAndStatus(List<Long> roomIds, LovePromiseStatusEnum ceremony);

    @Query(value = "select * from love_promise where status ='apply' or status = 'ceremony'", nativeQuery = true)
    List<LovePromiseDO> findByLovePromiseStatusEnum();

    @Query(value ="select * from love_promise where from_uid = ?1" ,nativeQuery = true)
    List<LovePromiseDO> findByUid(Long uid1);
}
