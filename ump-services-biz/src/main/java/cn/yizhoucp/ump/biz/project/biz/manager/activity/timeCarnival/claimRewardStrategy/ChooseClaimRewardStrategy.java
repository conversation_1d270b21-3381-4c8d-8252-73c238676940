package cn.yizhoucp.ump.biz.project.biz.manager.activity.timeCarnival.claimRewardStrategy;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

@Service
public class ChooseClaimRewardStrategy {

    @Resource
    private Map<String,ClaimRewardStrategy> claimRewardStrategyMap;

    public ClaimRewardStrategy getStrategy(String strategyName){
        String beanName=ClaimRewardStrategyEnum.getBeanNameByName(strategyName);
        return claimRewardStrategyMap.get(beanName);
    }
}
