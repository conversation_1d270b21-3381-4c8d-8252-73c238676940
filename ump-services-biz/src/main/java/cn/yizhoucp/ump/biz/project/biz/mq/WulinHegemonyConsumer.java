package cn.yizhoucp.ump.biz.project.biz.mq;

import cn.yizhoucp.ms.core.base.TopicConstant;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.starter.cassandra.base.enums.MqDelayLevelEnum;
import cn.yizhoucp.starter.cassandra.base.rocketmq.RocketmqAbstractConsumer;
import cn.yizhoucp.starter.cassandra.base.rocketmq.RocketmqProducerManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.wlzb.WuLinHegemonyBizManager;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Set;

import static cn.yizhoucp.ms.core.base.TopicTagEnum.*;

@Slf4j
@Component
@RocketMQMessageListener(topic = "wlzb_event_topic", consumerGroup = "GID_UMP_WLZB_EVENT_GROUP")
public class WulinHegemonyConsumer extends RocketmqAbstractConsumer {

    @Resource
    private RocketmqProducerManager rocketmqProducerManager;
    @Resource
    private WuLinHegemonyBizManager wuLinHegemonyBizManager;

    private static final Set<String> TOPIC_TAG_ENUMS = Sets.newHashSet(TOPIC_WLZB_LOCK_GAME.getTagKey(),
            TOPIC_WLZB_DRAW_GAME.getTagKey(),
            TOPIC_WLZB_FINISH_GAME.getTagKey(),
            TOPIC_WLZB_COMPUTE_TASK.getTagKey());

    @Override
    protected Boolean consume(String unionId, Long userId, String tag, String json) {
        if (TOPIC_WLZB_LOCK_GAME.getTagKey().equals(tag)) {
            return lockGameHandle(BaseParam.ofMDC(), json);
        } else if (TOPIC_WLZB_DRAW_GAME.getTagKey().equals(tag)) {
            return drawHandle(BaseParam.ofMDC(), json);
        } else if (TOPIC_WLZB_FINISH_GAME.getTagKey().equals(tag)) {
            return finishHandle(BaseParam.ofMDC(), json);
        } else if (TOPIC_WLZB_COMPUTE_TASK.getTagKey().equals(tag)) {
            return wuLinHegemonyBizManager.settleDown(BaseParam.ofMDC(), JSON.parseObject(json));
        }
        return Boolean.TRUE;
    }

    @Override
    protected String getTopic() {
        return TopicConstant.TOPIC_WLZB_EVENT.getTopicKey();
    }

    @Override
    protected Set<String> getTags() {
        return TOPIC_TAG_ENUMS;
    }

    private Boolean lockGameHandle(BaseParam param, String json) {
        JSONObject mqParam = JSON.parseObject(json);
        Integer currentTimes = mqParam.getInteger("times");
        // 30s(times0) => 10s(times1) => 10s(times2)
        if (Objects.nonNull(currentTimes) && currentTimes >= 2) {
            log.info("wlzb 消费锁定比赛消息");
            return wuLinHegemonyBizManager.lockGame(param, mqParam);
        }
        mqParam.put("times", currentTimes + 1);
        rocketmqProducerManager.sendDelayMessage(TopicConstant.TOPIC_WLZB_EVENT.getTopicKey(), TOPIC_WLZB_LOCK_GAME.getTagKey(), JSON.toJSONString(mqParam), System.currentTimeMillis() + "", MqDelayLevelEnum.TEN_SECOND);
        return Boolean.TRUE;
    }

    private Boolean drawHandle(BaseParam param, String json) {
        return wuLinHegemonyBizManager.drawGame(param, JSON.parseObject(json));
    }

    private Boolean finishHandle(BaseParam param, String json) {
        return wuLinHegemonyBizManager.finishGame(param, JSON.parseObject(json));
    }
}
