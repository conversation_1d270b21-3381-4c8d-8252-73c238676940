package cn.yizhoucp.ump.biz.project.biz.enums.activity.doubleEgg;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.LocalDateTime;

/**
 * 活动日期节点
 *
 * @author: lianghu
 */
@Getter
@AllArgsConstructor
public enum DateFrameEnum {

    BEFORE_CHRISTMAS_EVE("beforeChristmasEve", LocalDateTime.parse("2021-12-10T00:00:00"), LocalDateTime.parse("2021-12-24T00:00:00")),
    CHRISTMAS_EVE("christmasEve", LocalDateTime.parse("2021-12-24T00:00:00"), LocalDateTime.parse("2021-12-25T00:00:00")),
    CHRISTMAS("christmas", LocalDateTime.parse("2021-12-25T00:00:00"), LocalDateTime.parse("2021-12-26T00:00:00")),
    AFTER_CHRISTMAS("afterChristmas", LocalDateTime.parse("2021-12-26T00:00:00"), LocalDateTime.parse("2021-12-29T00:00:00")),
    BEFORE_NEW_YEAR("beforeNewYear", LocalDateTime.parse("2021-12-29T00:00:00"), LocalDateTime.parse("2022-01-01T00:00:00")),
    NEW_YEAR("newYear", LocalDateTime.parse("2022-01-01T00:00:00"), LocalDateTime.parse("2022-03-01T00:00:00")),
    ;

    private String code;
    private LocalDateTime start;
    private LocalDateTime end;


    public static String getDateCode(LocalDateTime time) {
        for (DateFrameEnum item : values()) {
            if (time.isAfter(item.getStart()) && time.isBefore(item.getEnd())) {
                return item.getCode();
            }
        }
        return null;
    }

}
