package cn.yizhoucp.ump.biz.project.biz.manager.activity.the_glowing_of_spring;

import cn.yizhoucp.ms.core.base.config.ThreadPoolConfig;
import cn.yizhoucp.ms.core.base.util.AppUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.the_glowing_of_spring.common.TheGlowingOfSpringConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.the_glowing_of_spring.common.TheGlowingOfSpringEnums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.the_glowing_of_spring.common.TheGlowingOfSpringRedisManager;
import cn.yizhoucp.ump.biz.project.common.ActivityComponent;
import cn.yizhoucp.ump.biz.project.common.event.GiftGiveEvent;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 春日灼灼业务类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 16:40 2025/3/7
 */
@Slf4j
@Service
public class TheGlowingOfSpringBizManager implements ActivityComponent {

    @Resource
    private TheGlowingOfSpringRankManager theGlowingOfSpringRankManager;
    @Resource
    private TheGlowingOfSpringRedisManager theGlowingOfSpringRedisManager;

    @Override
    public String getActivityCode() {
        return TheGlowingOfSpringConstant.ACTIVITY_CODE;
    }

    @Async(ThreadPoolConfig.ASYNC_EXECUTOR_NAME)
    @EventListener(GiftGiveEvent.class)
    @ActivityCheck(activityCode = TheGlowingOfSpringConstant.ACTIVITY_CODE, isThrowException = false)
    public void eventSendGiftHandle(GiftGiveEvent event) {
        log.info("sendGiftHandle event:{}", event);
        sendGiftHandle(event.getBaseParam(), event.getCoinGiftGivedModels());
    }

    public Boolean sendGiftHandle(BaseParam param, List<CoinGiftGivedModel> coinGiftGivedModelList) {
        log.info("sendGiftHandle param:{},coinGiftGivedModelList:{}", param, coinGiftGivedModelList);
        for (CoinGiftGivedModel coinGiftGivedModel : coinGiftGivedModelList) {
            //排行榜解锁处理
            unLockRank(param, coinGiftGivedModel);
            //任务处理
            taskHandler(param, coinGiftGivedModel);
            //桃花仙任务处理
            peachBlossomFairyTaskHandler(param, coinGiftGivedModel);
            //排行榜处理
            rankingListHandler(param, coinGiftGivedModel);
        }
        return Boolean.TRUE;
    }

    private void unLockRank(BaseParam param, CoinGiftGivedModel coinGiftGivedModel) {
        ScenePrizeDO scenePrizeDO = theGlowingOfSpringRedisManager.getUnLockRankScenePrize(coinGiftGivedModel.getGiftKey());
        if (scenePrizeDO == null) {
            return;
        }
        theGlowingOfSpringRedisManager.setUnLockRank(param.getUid(), coinGiftGivedModel.getToUid());
    }

    private void rankingListHandler(BaseParam param, CoinGiftGivedModel coinGiftGivedModel) {
        Boolean isUnLockRank = theGlowingOfSpringRedisManager.getUnLockRank(param.getUid(), coinGiftGivedModel.getToUid());
        if (Boolean.FALSE.equals(isUnLockRank)) {
            return;
        }
        //桃花夭夭礼物
        ScenePrizeDO peachBlossomFairyPrize = theGlowingOfSpringRedisManager.getPeachBlossomFairyScenePrize(coinGiftGivedModel.getGiftKey());
        //任务礼盒
        ScenePrizeDO taskPrize = theGlowingOfSpringRedisManager.getTaskScenePrize(coinGiftGivedModel.getLotteryGiftKey());
        if (peachBlossomFairyPrize == null && taskPrize == null) {
            return;
        }
        String bindId = AppUtil.splicUserId(param.getUid(), coinGiftGivedModel.getToUid());
        Long uid = param.getUid();
        Long toUid = coinGiftGivedModel.getToUid();
        Long value = coinGiftGivedModel.getCoin() * TheGlowingOfSpringConstant.BASE_REWARD_COUNT;
        theGlowingOfSpringRankManager.incrRankValue(bindId, value, theGlowingOfSpringRedisManager.getRankKey());
        theGlowingOfSpringRedisManager.incrementMyRank(uid, toUid, value);
        theGlowingOfSpringRedisManager.incrementMyRank(toUid, uid, value);
    }

    private void peachBlossomFairyTaskHandler(BaseParam param, CoinGiftGivedModel coinGiftGivedModel) {
        String giftKey = coinGiftGivedModel.getGiftKey();
        ScenePrizeDO peachBlossomFairyPrize = theGlowingOfSpringRedisManager.getPeachBlossomFairyScenePrize(giftKey);
        if (peachBlossomFairyPrize == null) {
            return;
        }
        Long rewardCount = getRewardCount(peachBlossomFairyPrize.getExtData());
        Arrays.stream(TheGlowingOfSpringEnums.CollectItemEnum.values()).forEach(item -> theGlowingOfSpringRedisManager.incrementCollectItemCount(param.getUid(), item.getItemId(), coinGiftGivedModel.getProductCount() * rewardCount));
    }

    private Long getRewardCount(String extData) {
        JSONObject jsonObject = JSON.parseObject(extData);
        if (jsonObject == null) {
            return 0L;
        }
        if (jsonObject.containsKey("rewardCount")) {
            return jsonObject.getLong("rewardCount");
        }
        return 0L;
    }

    private void taskHandler(BaseParam param, CoinGiftGivedModel coinGiftGivedModel) {
        String giftKey = coinGiftGivedModel.getLotteryGiftKey();
        ScenePrizeDO taskPrize = theGlowingOfSpringRedisManager.getTaskScenePrize(giftKey);
        if (taskPrize == null) {
            return;
        }
        Long uid = param.getUid();
        Long toUid = coinGiftGivedModel.getToUid();
        if(Boolean.TRUE.equals(theGlowingOfSpringRedisManager.getTaskReceive(uid,taskPrize.getSceneCode()))){
            theGlowingOfSpringRedisManager.incrementTaskProgress(param.getUid(), taskPrize.getSceneCode(), coinGiftGivedModel.getProductCount());
        }
        if(Boolean.TRUE.equals(theGlowingOfSpringRedisManager.getTaskReceive(toUid,taskPrize.getSceneCode()))){
            theGlowingOfSpringRedisManager.incrementTaskProgress(toUid, taskPrize.getSceneCode(), coinGiftGivedModel.getProductCount());
        }

    }

}