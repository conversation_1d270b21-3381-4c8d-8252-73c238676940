package cn.yizhoucp.ump.biz.project.biz.manager.activity.thePrincessDiaries;

import cn.hutool.core.bean.BeanUtil;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.log.LogComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.AbstractDrawTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawContext;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.loveInBloomFestival.common.LoveInBloomFestivalConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.thePrincessDiaries.common.ThePrincessDiariesConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.thePrincessDiaries.common.ThePrincessDiariesRedisManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 公主日记抽奖类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 11:34 2025/3/3
 */
@Slf4j
@Service
public class ThePrincessDiariesDrawManager extends AbstractDrawTemplate {

    @Resource
    private ThePrincessDiariesRedisManager thePrincessDiariesRedisManager;
    @Resource
    private NotifyComponent notifyComponent;
    @Resource
    private ThePrincessDiariesTrackManager thePrincessDiariesTrackManager;
    @Resource
    private LogComponent logComponent;

    @Override
    protected void resourceCheck(DrawContext context) {
        Long uid = context.getDrawParam().getUid();
        Long drawItem = thePrincessDiariesRedisManager.getDrawItemCount(uid);
        Long times = context.getDrawParam().getTimes().longValue();
        if (drawItem < times * ThePrincessDiariesConstant.DRAW_BASE_VALUE) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "抽奖次数不足!");
        }
    }

    @Override
    protected void deductResource(DrawContext context) {
        Long uid = context.getDrawParam().getUid();
        Long times = context.getDrawParam().getTimes().longValue();
        times = times * ThePrincessDiariesConstant.DRAW_BASE_VALUE;
        thePrincessDiariesRedisManager.decrementDrawItemCount(uid, times);
    }

    @Override
    protected void doCallback(DrawContext context) {
        BaseParam param = context.getDrawParam().getBaseParam();
        List<DrawPoolItemDTO> prizeItems = context.getPrizeItemList();
        //埋点
        for(DrawPoolItemDTO prizeItem : prizeItems){
            thePrincessDiariesTrackManager.allActivityLottery(param.getUid(), ThePrincessDiariesConstant.POOL_CODE,prizeItem.getDrawPoolItemDO().getItemKey(),prizeItem.getDrawPoolItemDO().getItemValueGold(),prizeItem.getTargetTimes().longValue());
        }
        // 拼接中奖信息
        StringBuilder msgBuilder = new StringBuilder("恭喜在「公主日记」中抽中：");
        for (int i = 0; i < prizeItems.size(); i++) {
            DrawPoolItemDTO prizeItem = prizeItems.get(i);
            String prizeName = prizeItem.getDrawPoolItemDO().getItemName(); // 假设 DrawPoolItemDTO 有 getPrizeName() 方法
            int prizeCount = prizeItem.getTargetTimes(); // 假设 DrawPoolItemDTO 有 getPrizeCount() 方法
            msgBuilder.append(prizeName).append(" * ").append(prizeCount);
            if (i < prizeItems.size() - 1) {
                msgBuilder.append("，");
            }
        }
        msgBuilder.append("，奖励已经发放至背包，快去查看吧！");

        String msg = msgBuilder.toString();
        notifyComponent.npcNotify(
                ServicesAppIdEnum.lanling.getUnionId(),
                param.getUid(),
                msg
        );
    }

    @Override
    protected void recordDrawLog(DrawContext context, DrawParam drawParam) {
        List<DrawPoolItemDTO> drawPoolItemDTOS = recordDrawLogOverride(context.getPrizeItemList());
        logComponent.putDrawLog(drawParam.getBaseParam(), drawParam.getActivityCode(), drawPoolItemDTOS);
    }

    private List<DrawPoolItemDTO> recordDrawLogOverride(List<DrawPoolItemDTO> prizeItemList) {
        List<DrawPoolItemDTO> newList = new ArrayList<>();
        for (DrawPoolItemDTO originalItem : prizeItemList) { // 遍历原始列表
            // 使用 BeanUtil 复制对象（浅拷贝）
            DrawPoolItemDTO copyItem = BeanUtil.copyProperties(originalItem, DrawPoolItemDTO.class);

            // 手动深拷贝嵌套对象：DrawPoolItemDO
            if (originalItem.getDrawPoolItemDO() != null) {
                DrawPoolItemDO originalDo = originalItem.getDrawPoolItemDO();
                DrawPoolItemDO copyDo = BeanUtil.copyProperties(originalDo, DrawPoolItemDO.class);
                copyItem.setDrawPoolItemDO(copyDo);
            }

            // 修改拷贝后的对象
            Long times = copyItem.getTargetTimes().longValue();
            String itemName = copyItem.getDrawPoolItemDO().getItemName();
            String msg = String.format(LoveInBloomFestivalConstant.DRAW_RECORD_MSG, itemName, times);
            copyItem.getDrawPoolItemDO().setItemName(msg);

            newList.add(copyItem);
        }
        return newList;
    }

}
