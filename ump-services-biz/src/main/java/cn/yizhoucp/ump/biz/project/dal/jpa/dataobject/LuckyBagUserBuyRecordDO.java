package cn.yizhoucp.ump.biz.project.dal.jpa.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 用户购买记录
 *
 * @author: lianghu
 */
@Table(name = "lucky_bag_user_buy_record")
@Entity
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LuckyBagUserBuyRecordDO {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "lucky_bag_user_buy_record_generator")
    @SequenceGenerator(name = "lucky_bag_user_buy_record_generator",sequenceName = "lucky_bag_user_buy_record_seq", allocationSize = 10)
    private Long id;
    /** 全局应用ID */
    private String unionId;
    /** 应用ID */
    private Long appId;
    /** 用户ID */
    private Long userId;
    /** 购买数量 */
    private Long buyNum;
    /** 消耗金币 */
    private Long totalCoin;
    /** 创建时间 */
    private LocalDateTime createTime;
    /** 更新时间 */
    private LocalDateTime updateTime;

}
