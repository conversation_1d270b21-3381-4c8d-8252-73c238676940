package cn.yizhoucp.ump.biz.project.biz.manager.activity.component.barrage;


import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.AbstractComponent;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 弹幕管理
 *
 * @author: lianghu
 */
@Slf4j
@Component
public class BarrageComponent extends AbstractComponent {

    /**
     * 添加普通弹幕
     *
     * @param key
     * @param msg
     * @return java.lang.Boolean
     */
    @Deprecated
    public Boolean putBarrage(String key, String msg) {
        redisManager.listLpush(key, msg, DateUtil.ONE_MONTH_SECOND);
        return true;
    }

    public Boolean putBarrage(String key, String msg, int targetSize) {
        redisManager.listLpush(key, msg, DateUtil.ONE_MONTH_SECOND);
        redisManager.listTrim(key, 0, targetSize - 1);
        return Boolean.TRUE;
    }

    /**
     * 获取普通弹幕
     *
     * @param key
     * @param targetSize
     * @return java.util.List<java.lang.String>
     */
    public List<String> getBarrageList(String key, Long targetSize) {
        long size = redisManager.lGetListSize(key);
        if (size == 0 || targetSize == 0) {
            return null;
        }
        long start = 0;
        long end = size < targetSize ? size - 1 : targetSize - 1;
        return (List<String>) (List) redisManager.lGet(key, start, end);
    }

    /**
     * 添加对象弹幕
     *
     * @param key
     * @param obj
     * @return java.lang.Boolean
     */
    public Boolean putObjectBarrage(String key, Object obj) {
        redisManager.listLpush(key, JSONObject.toJSONString(obj),DateUtil.ONE_MONTH_SECOND);
        return true;
    }

    /**
     * 获取对象弹幕
     *
     * @param key
     * @param targetSize
     * @param typeClass
     * @return java.util.List<T>
     */
    public <T> List<T> getObjectBarrageList(String key, Long targetSize, Class<T> typeClass) {
        List<T> result = new ArrayList<>();
        List<String> barrageList = getBarrageList(key, targetSize);
        if (CollectionUtils.isEmpty(barrageList)) {
            return null;
        }
        for (String item : barrageList) {
            result.add(JSONObject.parseObject(item, typeClass));
        }
        return result;
    }

}
