package cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.inner;

import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.LoginPopDO;
import cn.yizhoucp.ms.core.base.enums.ServerPushRenderType;
import cn.yizhoucp.ms.core.vo.imservices.BottomButtonVO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 客户端弹窗参数
 *
 * @author: lianghu
 */
@Accessors(chain = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WindowParam {

    private Long toUid;
    private String type;
    private String from;
    private String backgroundImage;
    private String actionImage;
    private String actionUrl;
    private String title;
    private String contentMsg;
    private String renderType;
    private Integer priority;
    private Boolean needCloseIcon;
    private List<String> listPayloads;
    private BottomButtonVO bottomButton;

    public static WindowParam of(Long toUid, LoginPopDO popDO) {
        // 拓展按钮信息
        BottomButtonVO btnInfo = null;
        if (StringUtils.isNotBlank(popDO.getExtData())) {
            JSONObject ext = JSON.parseObject(popDO.getExtData());
            btnInfo = BottomButtonVO.builder()
                    .leftButtonImg(ext.getString("leftBtnImg"))
                    .leftButtonActionUrl(ext.getString("leftBtnUrl"))
                    .rightButtonImg(ext.getString("rightBtnImg"))
                    .rightButtonActionUrl(ext.getString("rightBtnUrl")).build();
        }
        return WindowParam.builder()
                .toUid(toUid)
                .backgroundImage(popDO.getBackgroundImage())
                .actionImage(popDO.getActionImage())
                .actionUrl(popDO.getActionUrl())
                .title(popDO.getTitle())
                .contentMsg(popDO.getContentMsg())
                .renderType(popDO.getRenderType())
                .priority(popDO.getPriority().intValue())
                .needCloseIcon(popDO.getNeedCloseIcon())
                .bottomButton(btnInfo).build();
    }

    public static WindowParam getDefault(Long toUid) {
        WindowParam param = new WindowParam();
        param.setToUid(toUid);
        param.setPriority(80);
        param.setNeedCloseIcon(Boolean.TRUE);
        param.setRenderType(ServerPushRenderType.fixed.getCode());
        return param;
    }

    @Deprecated
    public static WindowParam getInstance(Long toUid, String bgUrl, String acUrl) {
        return WindowParam.builder()
                .toUid(toUid)
                .backgroundImage(bgUrl)
                .actionImage(acUrl)
                .actionUrl(null)
                .title(null)
                .contentMsg(null)
                .renderType(ServerPushRenderType.fixed.getCode())
                .priority(80)
                .build();
    }

    @Deprecated
    public static WindowParam getInstance(Long toUid, String bgUrl, String acUrl, String acHref) {
        return WindowParam.builder()
                .toUid(toUid)
                .backgroundImage(bgUrl)
                .actionImage(acUrl)
                .actionUrl(acHref)
                .title(null)
                .contentMsg(null)
                .renderType(ServerPushRenderType.fixed.getCode())
                .priority(80)
                .build();
    }
}
