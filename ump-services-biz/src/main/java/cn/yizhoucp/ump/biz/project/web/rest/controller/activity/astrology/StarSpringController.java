package cn.yizhoucp.ump.biz.project.web.rest.controller.activity.astrology;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.astrology.starSpring.StarSpringBizManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.astrology.starWishCardCollectionGame.StarWishCardCollectionGameIndexManager;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
public class StarSpringController {

    @Resource
    private StarSpringBizManager starSpringBizManager;

    @GetMapping("/api/inner/activity/star_spring/task_1_finish")
    public Result<Boolean> task1Finsh(BaseParam param) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> starSpringBizManager.task1Finish(param));
    }

}
