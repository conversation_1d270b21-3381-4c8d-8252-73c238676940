package cn.yizhoucp.ump.biz.project.biz.manager.activity.fools_day_2025.common;

import cn.yizhoucp.ump.biz.project.biz.manager.activity.fools_day_2025.strategy.FoolsDay2025Proceed;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.fools_day_2025.strategy.FoolsDayTaskTaken;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.StrategyEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 2025愚人节枚举类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 11:51 2025/3/28
 */
public class FoolsDay2025Enums {

    @AllArgsConstructor
    @Getter
    public enum ButtonEnum implements StrategyEnum {
        PROCEED("proceed", "foolsDay2025Proceed", FoolsDay2025Proceed.class),
        TASK_TAKEN("task_taken", "foolsDayTaskTaken", FoolsDayTaskTaken.class);
        private final String strategyName;
        private final String beanName;
        private final Class<? extends ExecutableStrategy> strategyClass;
    }

    @AllArgsConstructor
    @Getter
    public enum NodeEnum {
        NODE_1(1),
        NODE_2(2),
        NODE_3(3),
        NODE_4(4),
        NODE_5(5),
        NODE_6(6),
        NODE_7(7),
        NODE_8(8),
        NODE_9(9),
        NODE_10(10),
        NODE_11(11),
        NODE_12(12),
        NODE_13(13),
        NODE_14(14),
        NODE_15(15);
        private final Integer nodeId;

        public static List<String> getNodeCodes() {
            return Arrays.stream(NodeEnum.values()).map(Enum::name).collect(Collectors.toList());
        }

        public static String getNodeCodeByNodeId(Integer nodeId) {
            for (NodeEnum nodeEnum : NodeEnum.values()) {
                if (nodeEnum.getNodeId().equals(nodeId)) {
                    return nodeEnum.name();
                }
            }
            return null;
        }
    }

    @AllArgsConstructor
    @Getter
    public enum TaskEnum{
        TASK_1("task_1"),
        TASK_2("task_2"),
        TASK_3("task_3"),
        TASK_4("task_4"),
        TASK_5("task_5"),
        TASK_6("task_6");
        private final String taskCode;

        public static List<String> getTaskCodes() {
            return Stream.of(TaskEnum.values()).map(TaskEnum::getTaskCode).collect(Collectors.toList());
        }

        public static TaskEnum getTaskByCode(String bizKey) {
            for(TaskEnum taskEnum : TaskEnum.values()) {
                if(taskEnum.getTaskCode().equals(bizKey)) {
                    return taskEnum;
                }
            }
            return null;
        }
    }

}
