package cn.yizhoucp.ump.biz.project.biz.manager.activity.thePrincessDiaries.strategy;

import cn.hutool.core.util.ObjectUtil;
import cn.yizhoucp.depth.api.client.DepthFeignService;
import cn.yizhoucp.depth.api.vo.CardiacRelationVO;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.thePrincessDiaries.vo.ThePrincessDiariesFriendVO;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import cn.yizhoucp.user.manager.UserFeignManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class ThePrincessDiariesGetFriendList implements ExecutableStrategy {
    @Resource
    private DepthFeignService depthFeignService;
    @Resource
    private UserFeignManager userFeignManager;

    @Override
    public List<ThePrincessDiariesFriendVO> execute(ButtonEventParam buttonEventParam) {
        BaseParam baseParam = buttonEventParam.getBaseParam();
        // 只获取密友
        Long uid = baseParam.getUid();
        UserVO userVO = userFeignManager.getBasicWithCache(MDCUtil.getCurAppIdByMdc(), uid);
        Map<Long, CardiacRelationVO> allUserDepth = depthFeignService.getAllUserDepth(uid).successData();
        if (allUserDepth == null || allUserDepth.isEmpty()) {
            return null;
        }
        ArrayList<ThePrincessDiariesFriendVO> list = new ArrayList<>();
        allUserDepth.forEach((k, v) -> {
            UserVO friendUserVO = userFeignManager.getBasicWithCache(MDCUtil.getCurAppIdByMdc(), k);
            if (friendUserVO != null && ObjectUtil.notEqual(userVO.getSex().getCode(), friendUserVO.getSex().getCode())) {
                list.add(ThePrincessDiariesFriendVO.builder()
                        .userId(friendUserVO.getId())
                        .userAvatar(friendUserVO.getAvatar())
                        .userName(friendUserVO.getName())
                        .build());
            }
        });
        return list;
    }
}
