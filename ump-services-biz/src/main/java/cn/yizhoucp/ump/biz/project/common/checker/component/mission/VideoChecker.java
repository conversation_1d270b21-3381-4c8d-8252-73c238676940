package cn.yizhoucp.ump.biz.project.common.checker.component.mission;


import cn.yizhoucp.ump.biz.project.biz.constant.activity.NewYear2022Constant;
import cn.yizhoucp.ump.biz.project.common.checker.BaseChecker;
import cn.yizhoucp.ump.biz.project.common.checker.CheckerContext;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 视频类型任务检查
 * <p>
 * 相关参数 durationSeconds
 *
 * @author: lianghu
 */
@Slf4j
@Component("videoChecker")
public class VideoChecker extends BaseChecker {

    @Override
    protected Boolean doCheck(CheckerContext context, JSONObject param) {
        log.debug("videoChecker context:{}, param:{}", JSON.toJSONString(context), JSON.toJSONString(param));
        JSONObject missionExt = context.getMissionExt();
        if (Objects.isNull(missionExt)) {
            return Boolean.TRUE;
        }
        Integer limit = missionExt.getInteger("durationSeconds");
        if (Objects.isNull(limit)) {
            return Boolean.TRUE;
        }
        Integer durationSeconds = param.getInteger("durationSeconds");
        if (Objects.isNull(durationSeconds) || durationSeconds < limit) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

}
