package cn.yizhoucp.ump.biz.project.web.rest.controller.activity;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.auth.SecurityUser;
import cn.yizhoucp.ms.core.base.auth.SecurityUtils;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ump.api.vo.activity.dreamingIntoTheGalaxy.DreamingIntoTheGalaxyHomePageVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.dreamingIntoTheGalaxy.DreamingIntoTheGalaxyIndexManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.dreamingIntoTheGalaxy.DreamingIntoTheGalaxyRankManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
public class DreamingIntoTheGalaxyController {

    @Resource
    private DreamingIntoTheGalaxyRankManager dreamingIntoTheGalaxyRankManager;

    @Resource
    private DreamingIntoTheGalaxyIndexManager dreamingIntoTheGalaxyIndexManager;

    @GetMapping("/api/inner/activity/dreaming_into_the_galaxy/get-rank-by-date")
    public Result<DreamingIntoTheGalaxyHomePageVO.Rank> getRankByDate(@RequestParam("date") String date) {
        SecurityUser user = SecurityUtils.getCurrentUser();
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                dreamingIntoTheGalaxyRankManager.getRankByDate(user.getUserId(), date)
        );
    }

    /**
     * 兑换
     */
    @GetMapping("/api/inner/activity/dreaming_into_the_galaxy/exchange-gift")
    public Result<DreamingIntoTheGalaxyHomePageVO.GiftExchangeItem> exchangeGift(@RequestParam("giftKey") String giftKey) {
        SecurityUser user = SecurityUtils.getCurrentUser();
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                dreamingIntoTheGalaxyIndexManager.exchangeGift(user.getUserId(), giftKey)
        );
    }

}
