package cn.yizhoucp.ump.biz.project.biz.manager.activity.patRoom;

import cn.hutool.core.util.ObjectUtil;
import cn.yizhoucp.ms.core.base.enums.AuctionProcessStatus;
import cn.yizhoucp.ms.core.base.enums.GiftFrom;
import cn.yizhoucp.ms.core.base.enums.GiftWay;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.landingservices.AuctionResultingUserInfoVO;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.patRoom.internal.PatRoomConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.patRoom.internal.PatRoomEnums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.patRoom.internal.PatRoomRedisManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.lanling.FeignLanlingService;
import cn.yizhoucp.ump.biz.project.common.ActivityComponent;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.RoomActivityProcessDO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class PatRoomBizManager implements ActivityComponent {

    @Resource
    private FeignLanlingService lanlingService;


    @Resource
    private PatRoomRedisManager patRoomRedisManager;
    @Autowired
    private RedisManager redisManager;

    @Override
    public String getActivityCode() {
        return "";
    }

    @ActivityCheck(activityCode = PatRoomConstant.ACTIVITY_CODE, isThrowException = false)
    public Boolean sendGiftHandle(BaseParam param, List<CoinGiftGivedModel> coinGiftGivedModelList) {
        for (CoinGiftGivedModel coinGiftGivedModel : coinGiftGivedModelList) {
            if (!checkGift(param, coinGiftGivedModel)) {
                continue;
            }
            finishTask(param, coinGiftGivedModel);
        }
        return Boolean.TRUE;
    }

    private void finishTask(BaseParam param, CoinGiftGivedModel coinGiftGivedModel) {
        PatRoomEnums.PatRoomTask taskEnum = PatRoomEnums.PatRoomTask.getByGiftCode(coinGiftGivedModel.getGiftKey());
        if (taskEnum == null) {
            return;
        }
        //增加任务进度
        patRoomRedisManager.incrementTaskProgress(taskEnum.name(), coinGiftGivedModel.getProductCount(), coinGiftGivedModel.getToUid());
    }

    private Boolean checkGift(BaseParam param, CoinGiftGivedModel coinGiftGivedModel) {
        // 面板礼物
        if (StringUtils.isEmpty(coinGiftGivedModel.getGiftWay()) || !StringUtils.equalsIgnoreCase(coinGiftGivedModel.getGiftWay(), GiftWay.NORMAL.getCode())) {
            return Boolean.FALSE;
        }

        // 拍拍房
        if (StringUtils.isEmpty(coinGiftGivedModel.getFrom()) ||
                !StringUtils.equalsIgnoreCase(coinGiftGivedModel.getFrom(), GiftFrom.room.getCode())
        ) {
            return Boolean.FALSE;
        }
        //关系认证阶段送礼
        if (!checkRoomActivityProcess(param, coinGiftGivedModel)) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }



    private Boolean checkRoomActivityProcess(BaseParam param, CoinGiftGivedModel coinGiftGivedModel) {
        Long relationId = coinGiftGivedModel.getRelationId();
        RoomActivityProcessDO roomActivityProcessDO = lanlingService.getAuctionProgress(relationId).getData();
        if (roomActivityProcessDO == null) {
            return Boolean.FALSE;
        }
        if (!AuctionProcessStatus.resulting.equals(roomActivityProcessDO.getStep())) {
            return Boolean.FALSE;
        }
        AuctionResultingUserInfoVO info = lanlingService.getAuctionResultUserInfo(relationId).getData();
        //是拍卖礼物
        PatRoomEnums.PatRoomBox boxEnum = PatRoomEnums.PatRoomBox.getByGiftCode(coinGiftGivedModel.getGiftKey());
        if(boxEnum == null){
            return Boolean.FALSE;
        }
        if(!ObjectUtil.equals(boxEnum.getBoxKey(),info.getTargetGiftKey())){
            return Boolean.FALSE;
        }
        //是认证关系的两人
        if (ObjectUtil.equals(param.getUid(), info.getWinnerUserId()) && ObjectUtil.equals(coinGiftGivedModel.getToUid(), info.getAuctionedUserId())) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

}
