package cn.yizhoucp.ump.biz.project.biz.mq.rocketMQ;

import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.starter.cassandra.base.rocketmq.RocketmqAbstractConsumer;
import cn.yizhoucp.ump.biz.project.common.event.GiftGiveEvent;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

import static cn.yizhoucp.ms.core.base.TopicConstant.TOPIC_TRADE_EVENT;
import static cn.yizhoucp.ms.core.base.TopicTagEnum.TOPIC_GIVE_GIFT_LIST_EVENT;

/**
 * 新mq, 交易事件消费
 *
 * @author: aliang
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = "topic_trade_event", consumerGroup = "GID_UMP_TRADE_EVENT_GROUP_NEW")
public class NewTradeEventConsumer extends RocketmqAbstractConsumer {

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    /**
     * 关注 tag 列表
     */
//    private static final Set<String> TOPIC_TAG_ENUMS = Sets.newHashSet(TOPIC_GIVE_GIFT_LIST_EVENT.getTagKey(),
//            TOPIC_SEND_AIRDROP.getTagKey(), TOPIC_ROB_AIRDROP.getTagKey(), TOPIC_SEND_RED_PACKET.getTagKey(),
//            TOPIC_ROB_RED_PACKET.getTagKey(), TOPIC_GIVE_GIFT_LIST_DELAY_EVENT.getTagKey());

    private static final Set<String> TOPIC_TAG_ENUMS = Sets.newHashSet(TOPIC_GIVE_GIFT_LIST_EVENT.getTagKey());


    @Override
    protected Boolean consume(String unionId, Long userId, String tag, String param) {
        log.info("TradeEvent-consume userId : {} tag : {} param : {}", userId, JSON.toJSONString(tag), param);
        if (TOPIC_GIVE_GIFT_LIST_EVENT.getTagKey().equals(tag)) {
            this.giveGift(param);
            return Boolean.TRUE;
        } else {
            return Boolean.TRUE;
        }
    }

    @Override
    protected String getTopic() {
        return TOPIC_TRADE_EVENT.getTopicKey();
    }

    @Override
    protected Set<String> getTags() {
        return TOPIC_TAG_ENUMS;
    }

    @Override
    protected String getConsumerGroup() {
        return "GID_UMP_TRADE_EVENT_GROUP";
    }


    /**
     * 送礼
     *
     * @param jsonBody 请求数据
     * @return boolean
     */
    private boolean giveGift(String jsonBody) {
        log.info("giveGift-start param : {} ", JSONObject.toJSONString(jsonBody));
        if (StringUtils.isEmpty(jsonBody)) {
            return true;
        }
        BaseParam baseParam = BaseParam.ofMDC();
        List<CoinGiftGivedModel> coinGiftGivedModels = JSON.parseArray(jsonBody, CoinGiftGivedModel.class);
        applicationEventPublisher.publishEvent(new GiftGiveEvent(this, baseParam, coinGiftGivedModels));
        return true;
    }

}
