package cn.yizhoucp.ump.biz.project.dal.service;

import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.AppResourceJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.AppResourceDO;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * App 装修资源
 *
 * @author: lianghu
 */
@Service
public class AppResourceService {

    @Resource
    private RedisManager redisManager;
    @Resource
    private AppResourceJpaDAO appResourceJpaDAO;

    private static final String CACHE = "app_resource_cache_%s";

    public AppResourceDO getByActivityCode(Long appId, String activityCode) {
        AppResourceDO result;
        String cache = (String) redisManager.get(String.format(CACHE, appId + "_" + activityCode));
        if (StringUtils.isNotBlank(cache)) {
            result = JSONObject.parseObject(cache, AppResourceDO.class);
        } else {
            result = appResourceJpaDAO.getByActivityCode(appId, activityCode);
            if (Objects.nonNull(result)) {
                redisManager.set(String.format(CACHE, appId + "_" + activityCode), JSONObject.toJSONString(result), DateUtil.ONE_HOUR_SECOND * 2);
            }
        }
        return result;
    }

}
