package cn.yizhoucp.ump.biz.project.common.checker.component;

import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.biz.project.common.checker.BaseChecker;
import cn.yizhoucp.ump.biz.project.common.checker.CheckerContext;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 幂等 checker
 * <p>
 * 相关参数 uid
 * 该 checker
 *
 * @author: lianghu
 */
@Slf4j
@Component("idempotentChecker")
public class IdempotentChecker extends BaseChecker {

    @Resource
    private RedisManager redisManager;

    /** 幂等 redis-key（业务 code、uid） */
    private static final String IDEMPOTENT_CHECKER_KEY = "ump:idempotentChecker:%s_%s";

    @Override
    protected Boolean doCheck(CheckerContext context, JSONObject param) {
        if (StringUtils.isBlank(context.getIdempotentCode()) || Objects.isNull(context.getIdempotentDuring()) || context.getIdempotentDuring() <= 0 || Objects.isNull(param.getLong("uid"))) {
            log.debug("参数错误 context:{}", JSON.toJSONString(context));
            return Boolean.TRUE;
        }
        // 有效期最长时间调整
        Integer idempotentDuring = context.getIdempotentDuring() >= 60 ? 60 : context.getIdempotentDuring();
        // 幂等检查
        if (!redisManager.setnx(String.format(IDEMPOTENT_CHECKER_KEY, context.getIdempotentCode(), param.getLong("uid")), System.currentTimeMillis(), RedisManager.ONE_DAY_SECONDS * idempotentDuring)) {
            log.warn("idempotentChecker 验证失败 uid:{}", MDCUtil.getCurUserIdByMdc());
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

}
