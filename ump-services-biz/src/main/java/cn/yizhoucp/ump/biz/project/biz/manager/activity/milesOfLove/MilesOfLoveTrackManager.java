package cn.yizhoucp.ump.biz.project.biz.manager.activity.milesOfLove;

import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.enums.ServicesNameEnum;
import cn.yizhoucp.starter.cassandra.base.kafka.YzKafkaProducerManager;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 埋点
 */
@AllArgsConstructor
@Data
@Service
public class MilesOfLoveTrackManager {

    @Resource
    private YzKafkaProducerManager yzKafkaProducerManager;


    /**
     * 通用埋点_活动抽奖
     *
     * @param uid
     * @param awardCount  获得奖励的个数
     * @param awardAmount 奖品对应的金币价值
     */
    public void allActivityLottery(Long uid, String poolCode, String awardKey, Long awardAmount, Integer awardCount) {
        Map<String, Object> params = new HashMap<>();
        params.put("activity_type", "the_course_of_love");
        params.put("attribute_type", "platform_activity");
        params.put("lottery_type", "wonderful_halloween_night");
        params.put("pool_code", poolCode);
        params.put("award_key", awardKey);
        params.put("award_amount", awardAmount);
        params.put("award_count", awardCount);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_lottery", params, ServicesNameEnum.ump_services.getCode());
    }

    /**
     * 通用埋点_活动任务完成
     *
     * @param uid
     *
     */
    public void allActivityTaskFinish(Long uid, String taskType) {
        Map<String, Object> params = Maps.newHashMapWithExpectedSize(6);
        params.put("activity_type", "the_course_of_love");
        params.put("attribute_type", "platform_activity");
        params.put("task_type", taskType);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_task_finish", params, ServicesNameEnum.ump_services.getCode());
    }

    /**
     * 通用埋点_ 活动领取奖励
     */
    public void allActivityReceiveAward(Long uid, String awardType, String awardKey, Long awardAmount, Integer awardCount) {
        Map<String, Object> params = Maps.newHashMapWithExpectedSize(6);
        params.put("activity_type", "the_course_of_love");
        params.put("attribute_type", "platform_activity");
        params.put("award_type", awardType);
        if (!StringUtils.isBlank(awardKey)) {
            params.put("award_key", awardKey);
        }
        params.put("award_amount", awardAmount);
        params.put("award_count", awardCount);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_receive_award", params, ServicesNameEnum.ump_services.getCode());
    }
}
