package cn.yizhoucp.ump.biz.project.biz.enums.activity.activitySkin;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum ActivityAdminEvnEnum {
    DEV("开发环境", "https://admin-v2.myrightone.com/app/warm-chat-dev"),
    TEST("测试环境", "https://admin-v2.myrightone.com/app/warm-chat-qa"),
    PRE_PROD("预发环境", "https://admin-v2.myrightone.com/app/warm-chat-qa"),
    PROD("正式环境", "https://admin-v2.myrightone.com/app/warm-chat");

    private final String name;
    private final String host;
}
