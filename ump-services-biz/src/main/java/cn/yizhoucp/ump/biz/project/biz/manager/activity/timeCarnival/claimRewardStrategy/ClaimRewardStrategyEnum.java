package cn.yizhoucp.ump.biz.project.biz.manager.activity.timeCarnival.claimRewardStrategy;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum ClaimRewardStrategyEnum {
    MODERNFAMILY("modernFamily", "modernFamilyClaimRewardStrategy"),
    NEONSYMPHONY("neonSymphony", "neonSymphonyClaimRewardStrategy"),
    NOSTALGIAKARAOKE("nostalgiaKaraoke","nostalgiaKaraokeClaimRewardStrategy");
    private final String name;
    private final String beanName;

    public static String getBeanNameByName(String name) {
        if (StrUtil.isBlank(name)) {
            return StrUtil.EMPTY;
        }
        for (ClaimRewardStrategyEnum value : ClaimRewardStrategyEnum.values()) {
            if (name.equals(value.name)) {
                return value.beanName;
            }
        }
        return StrUtil.EMPTY;
    }
}
