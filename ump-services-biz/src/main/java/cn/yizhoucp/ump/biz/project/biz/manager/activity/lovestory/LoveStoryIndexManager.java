package cn.yizhoucp.ump.biz.project.biz.manager.activity.lovestory;

import cn.hutool.core.bean.BeanUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.activity.lovestory.LoveStoryVO;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.IndexManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class LoveStoryIndexManager implements IndexManager {

    @Resource
    RedisManager redisManager;

    @Resource
    LoveStoryRankManager loveStoryRankManager;

    @Resource
    LoveStoryRedisKeyBuilder loveStoryRedisKeyBuilder;

    @ActivityCheck(activityCode = LoveStoryConstant.ACTIVITY_CODE, isThrowException = false)
    @Override
    public LoveStoryVO getIndex(BaseParam param, Long toUid, String extData) {
        if (Objects.isNull(param) || Objects.isNull(param.getAppId()) || Objects.isNull(param.getUid())) {
            log.warn("love_story getIndex 参数未传");
            return LoveStoryVO.builder().build();
        }
        log.info("love_story getIndex param :{}", JSONObject.toJSONString(param));
        Long uid = param.getUid();
        initData(uid);
        return LoveStoryVO.builder().dataList(buildWallData(uid)).rankVO(buildRankVO(param)).build();
    }

    @Override
    public String getActivityCode() {
        return LoveStoryConstant.ACTIVITY_CODE;
    }

    @Override
    public String getTemplateType() {
        return null;
    }

    /**
     * 初始化 爱意满墙数据
     *
     * @param uid
     */
    public void initData(Long uid) {
        if (Objects.isNull(uid)) {
            return;
        }
        String key = loveStoryRedisKeyBuilder.wallKey(uid);
        if (!redisManager.hasKey(key)) {
            String itemKey1 = LoveStoryConstant.wallItemGiftKey();
            String itemKey2 = LoveStoryConstant.wallItemRewardKey();
            String itemKey3 = LoveStoryConstant.wallItemUltimateRewardKey();
            Map<Object, Object> map = new HashMap<>(4);
            map.put(itemKey1, JSONObject.toJSONString(LoveStoryConstant.initGiftData()));
            map.put(itemKey2, JSONObject.toJSONString(LoveStoryConstant.initNormalRewardData()));
            map.put(itemKey3, JSONObject.toJSONString(Arrays.stream(LoveStoryConstant.Reward.values()).filter(item -> item.getType().equals(0)).map(LoveStoryConstant.Reward::getItemKey).collect(Collectors.toList())));
            redisManager.hmset(key, map, DateUtil.ONE_MONTH_SECOND);
            log.info("initData uid :{} key :{} map :{}", uid, key, JSONObject.toJSONString(map));
        }
    }

    /**
     * build所有礼物和奖励数据 并排序返回
     *
     * @param uid
     * @return
     */
    private List<LoveStoryVO.WallData> buildWallData(Long uid) {
        List<LoveStoryVO.WallData> list = new LinkedList<>();
        list.addAll(buildGiftData(uid));
        list.addAll(buildRewardData(uid));
        List<LoveStoryVO.WallData> resList = list.stream().sorted((r, c) -> r.getRow().equals(c.getRow()) ? r.getCol() - c.getCol() :
                r.getRow() - c.getRow()).collect(Collectors.toList());
        log.info("love_story buildWallData uid :{} resList :{}", uid, JSONObject.toJSONString(resList));
        return resList;
    }


    /**
     * 爱意满墙-礼物数据
     *
     * @param uid
     * @return
     */
    private List<LoveStoryVO.WallData> buildGiftData(Long uid) {
        if (Objects.isNull(uid)) {
            return Collections.emptyList();
        }
        String key = loveStoryRedisKeyBuilder.wallKey(uid);
        return JSONArray.parseArray(redisManager.hget(key, LoveStoryConstant.wallItemGiftKey()).toString(), LoveStoryVO.WallData.class);
    }

    /**
     * 爱意满墙-奖励数据
     *
     * @param uid
     * @return
     */
    private List<LoveStoryVO.WallData> buildRewardData(Long uid) {
        if (Objects.isNull(uid)) {
            return Collections.emptyList();
        }
        //处理普通奖励
        String key = loveStoryRedisKeyBuilder.wallKey(uid);
        List<LoveStoryVO.WallData> rewardData = JSONArray.parseArray(redisManager.hget(key, LoveStoryConstant.wallItemRewardKey()).toString(), LoveStoryVO.WallData.class);
        List<String> names = JSONArray.parseArray(redisManager.hget(key, LoveStoryConstant.wallItemUltimateRewardKey()).toString(), String.class);

        //处理最终奖励
        List<LoveStoryConstant.Reward> ultimateReward = Arrays.stream(LoveStoryConstant.Reward.values()).filter(item -> item.getType().equals(1)).collect(Collectors.toList());
        for (LoveStoryConstant.Reward reward : ultimateReward) {
            LoveStoryVO.WallData ultimateRewardData = new LoveStoryVO.WallData();
            BeanUtil.copyProperties(reward, ultimateRewardData);
            Boolean isTake = redisManager.hasKey(loveStoryRedisKeyBuilder.takeUltimatePrizeRecord(uid));
            ultimateRewardData.setStatus((isTake && names.size() == 0) ? 2 : ((!isTake && names.size() == 0) ? 1 : 0));
            ultimateRewardData.setName(reward.getItemName());
            rewardData.add(ultimateRewardData);
            log.info("love_story buildRewardData uid : {} 终极奖励 : {}", uid, JSONObject.toJSONString(rewardData));
        }
        return rewardData;
    }

    private RankVO buildRankVO(BaseParam param) {
        return loveStoryRankManager.getRank(RankContext.builder()
                .param(param).activityCode(LoveStoryConstant.ACTIVITY_CODE)
                .rankKey(loveStoryRedisKeyBuilder.rankKey())
                .rankLen(10L)
                .supportDiff(Boolean.TRUE)
                .type(RankContext.RankType.user)
                .build());
    }


}
