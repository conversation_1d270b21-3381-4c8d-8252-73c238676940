package cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 情感纽带活动定时任务管理类
 */
@Service
@Slf4j
public class EmotionalBondsJobManager {

    @Resource
    private EmotionalBondsReminderManager emotionalBondsReminderManager;

    /**
     * 处理即将过期的关系提醒
     * 查找3天内即将过期的关系，并发送提醒消息
     * @return 处理的关系数量
     */
    public int sendExpirationReminders() {
        log.info("开始执行情感纽带关系过期提醒任务");
        try {
            int count = emotionalBondsReminderManager.processExpiringRelationships();
            log.info("情感纽带关系过期提醒任务执行完成，处理了 {} 个关系", count);
            return count;
        } catch (Exception e) {
            log.error("执行情感纽带关系过期提醒任务时发生错误", e);
            return 0;
        }
    }
}
