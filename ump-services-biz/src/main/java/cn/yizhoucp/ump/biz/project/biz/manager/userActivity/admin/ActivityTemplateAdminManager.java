package cn.yizhoucp.ump.biz.project.biz.manager.userActivity.admin;

import cn.hutool.extra.pinyin.PinyinUtil;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.enums.CommonStatus;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.util.BeanUtil;
import cn.yizhoucp.ms.core.dto.central.AdminPageParamsDTO;
import cn.yizhoucp.ms.core.vo.centralservices.AdminPageResult;
import cn.yizhoucp.ms.core.vo.productServices.CoinGiftProductVO;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw.inner.ExpiredTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw.inner.PrizeSubTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw.inner.PrizeTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.remoteService.product.FeignProductService;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ActivityJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ActivityMissionJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.PrizeItemJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ActivityDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ActivityMissionDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.PrizeItemDO;
import cn.yizhoucp.ump.biz.project.web.vo.ActivityMissionAdminVO;
import cn.yizhoucp.ump.biz.project.web.vo.ActivityTemplateAdminVO;
import cn.yizhoucp.ump.biz.project.web.vo.PrizeItemAdminVO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ActivityTemplateAdminManager {

    @Resource
    private ActivityJpaDAO activityJpaDAO;

    @Resource
    private ActivityMissionJpaDAO activityMissionJpaDAO;

    @Resource
    private PrizeItemJpaDAO prizeItemJpaDAO;

    @Resource
    private FeignProductService feignProductService;

    public AdminPageResult<ActivityTemplateAdminVO> page(Long appId, String templateCode, String activityName, AdminPageParamsDTO adminPageParamsDTO) {
        // TODO
        appId = ServicesAppIdEnum.lanling.getAppId();
//        templateCode = "tc@guard-constellation";
        activityName = "守护";

        Page<ActivityDO> page = null;
        if (StringUtils.isBlank(activityName)) {
            page = activityJpaDAO.getByAppIdAndTemplateCode(appId, templateCode, PageRequest.of(adminPageParamsDTO.getPage() - 1, adminPageParamsDTO.getPerPage()));
        } else {
            page = activityJpaDAO.getByAppIdAndActivityNameLike(appId, activityName, PageRequest.of(adminPageParamsDTO.getPage() - 1, adminPageParamsDTO.getPerPage()));
        }

        AdminPageResult<ActivityTemplateAdminVO> adminPageResult = new AdminPageResult<>();
        adminPageResult.setTotal(Math.toIntExact(page.getTotalElements()));
        adminPageResult.setItems(page.getContent().stream().map(this::do2vo).collect(Collectors.toList()));
        return adminPageResult;
    }

    public Boolean saveOrUpdate(ActivityTemplateAdminVO activityTemplateAdminVO) {
        if (activityTemplateAdminVO == null) {
            return false;
        }

        // TODO
        activityTemplateAdminVO.setAppId(ServicesAppIdEnum.lanling.getAppId());
//        activityTemplateAdminVO.setTemplateCode("tc@guard-constellation");

        if (activityTemplateAdminVO.getId() != null) {
            Optional<ActivityDO> optional = activityJpaDAO.findById(activityTemplateAdminVO.getId());
            if (!optional.isPresent()) {
                return false;
            }

            ActivityDO activityDO = optional.get();
            activityDO.setActivityName(activityTemplateAdminVO.getActivityName());
            activityDO.setStartTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(activityTemplateAdminVO.getStartTime()), ZoneId.systemDefault()));
            activityDO.setEndTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(activityTemplateAdminVO.getEndTime()), ZoneId.systemDefault()));
            activityDO.setUnionId(activityTemplateAdminVO.getUnionId());
            activityJpaDAO.save(activityDO);
            return true;
        }

        ActivityDO source = activityJpaDAO.findTopByAppIdAndTemplateCodeOrderByIdDesc(activityTemplateAdminVO.getAppId(), activityTemplateAdminVO.getTemplateCode());
        if (source == null) {
            return false;
        }

        ActivityDO target = new ActivityDO();
        BeanUtil.copyBeanIgnoreNullProperties(source, target);

        target.setId(null);
        target.setActivityName(activityTemplateAdminVO.getActivityName());
        target.setStartTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(activityTemplateAdminVO.getStartTime()), ZoneId.systemDefault()));
        target.setEndTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(activityTemplateAdminVO.getEndTime()), ZoneId.systemDefault()));
        target.setUnionId(activityTemplateAdminVO.getUnionId());

        if (!Objects.equals(target.getActivityCode(), activityTemplateAdminVO.getActivityCode())) {
            target.setActivityCode(activityTemplateAdminVO.getActivityCode());

            String config = target.getConfig();
            JSONObject jsonObject = JSON.parseObject(config);
            jsonObject.put("rankKey", String.format("ump:twins:%s", activityTemplateAdminVO.getActivityCode()));

            List<Integer> constellation = JSON.parseArray(jsonObject.get("constellation").toString(), Integer.class);
            constellation.add(constellation.get(constellation.size() - 1) + 1);
            jsonObject.put("constellation", constellation);

            target.setConfig(jsonObject.toJSONString());
        }
        activityJpaDAO.save(target);

        return true;
    }

    public String activityCode(Long appId, String unionId, String activityName) {
        if (StringUtils.isBlank(unionId)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }

        // TODO
        appId = ServicesAppIdEnum.lanling.getAppId();

        String pinyin = DateUtil.format(new Date(), "yyyyMM") + PinyinUtil.getPinyin(activityName).replace(" ", "");
        List<ActivityDO> activityDOList = activityJpaDAO.getByAppIdAndUnionIdAndActivityCodeLike(appId, unionId, pinyin);
        if (CollectionUtils.isEmpty(activityDOList)) {
            return pinyin;
        }

        List<String> activityCodeList = activityDOList.stream().map(ActivityDO::getActivityCode).collect(Collectors.toList());
        for (int i = 1; ; i++) {
            if (!activityCodeList.contains(pinyin + i)) {
                return pinyin + i;
            }
        }
    }

    public AdminPageResult<ActivityMissionAdminVO> missionPage(Long appId, String activityCode, AdminPageParamsDTO adminPageParamsDTO) {
        // TODO
        appId = ServicesAppIdEnum.lanling.getAppId();

        Page<ActivityMissionDO> page = activityMissionJpaDAO.getByAppIdAndBelongActivityCode(appId, activityCode, PageRequest.of(adminPageParamsDTO.getPage() - 1, adminPageParamsDTO.getPerPage()));

        AdminPageResult<ActivityMissionAdminVO> adminPageResult = new AdminPageResult<>();
        adminPageResult.setTotal(Math.toIntExact(page.getTotalElements()));
        adminPageResult.setItems(page.getContent().stream().map(this::do2vo).collect(Collectors.toList()));
        return adminPageResult;
    }

    public Boolean missionSaveOrUpdate(ActivityMissionAdminVO activityMissionAdminVO) {
        if (activityMissionAdminVO == null) {
            return false;
        }

        if (activityMissionAdminVO.getId() != null) {
            Optional<ActivityMissionDO> optional = activityMissionJpaDAO.findById(activityMissionAdminVO.getId());
            if (optional.isPresent()) {
                ActivityMissionDO activityMissionDO = optional.get();
                activityMissionDO.setActivityPlate(activityMissionAdminVO.getActivityPlate());
                activityMissionDO.setTitle(activityMissionAdminVO.getTitle());
                activityMissionDO.setCode(activityMissionAdminVO.getCode());
                activityMissionDO.setMissionDesc(activityMissionAdminVO.getMissionDesc());
                activityMissionDO.setPriority(activityMissionAdminVO.getPriority());
                activityMissionDO.setFinishType(activityMissionAdminVO.getFinishType());

                JSONObject jsonObject = Optional.ofNullable(JSON.parseObject(activityMissionDO.getExtData())).orElse(new JSONObject());
                if (activityMissionAdminVO.getGiftKey() != null) {
                    jsonObject.put("giftKey", activityMissionAdminVO.getGiftKey());
                }
                if (activityMissionAdminVO.getGiftCount() != null) {
                    jsonObject.put("giftCount", activityMissionAdminVO.getGiftCount());
                }
                activityMissionDO.setExtData(jsonObject.isEmpty() ? null : jsonObject.toJSONString());

                activityMissionDO.setCycleType(activityMissionAdminVO.getCycleType());
                activityMissionDO.setLimitTimes(activityMissionAdminVO.getLimitTimes());
                activityMissionJpaDAO.save(activityMissionDO);
                return true;
            }
        }

        ActivityMissionDO activityMissionDO = new ActivityMissionDO();
        // TODO
        activityMissionDO.setAppId(ServicesAppIdEnum.lanling.getAppId());
        activityMissionDO.setUnionId(ServicesAppIdEnum.nuanliao.getUnionId());

        activityMissionDO.setCode(activityMissionAdminVO.getCode());
        activityMissionDO.setBelongActivityCode(activityMissionAdminVO.getBelongActivityCode());
        activityMissionDO.setTitle(activityMissionAdminVO.getTitle());
        activityMissionDO.setMissionDesc(activityMissionAdminVO.getMissionDesc());

        if (activityMissionAdminVO.getIcon() == null) {
            String giftKey = activityMissionAdminVO.getGiftKey();
            if (StringUtils.isNotBlank(giftKey)) {
                CoinGiftProductVO coinGiftProductVO = feignProductService.getGiftByGiftKey(activityMissionAdminVO.getAppId(), giftKey).successData();
                if (coinGiftProductVO != null) {
                    activityMissionAdminVO.setIcon(coinGiftProductVO.getIcon());
                }
            }
        }

        activityMissionDO.setLimitTimes(activityMissionAdminVO.getLimitTimes());

        JSONObject jsonObject = new JSONObject();
        if (activityMissionAdminVO.getGiftKey() != null) {
            jsonObject.put("giftKey", activityMissionAdminVO.getGiftKey());
        }
        if (activityMissionAdminVO.getGiftCount() != null) {
            jsonObject.put("giftCount", activityMissionAdminVO.getGiftCount());
        }
        activityMissionDO.setExtData(jsonObject.isEmpty() ? null : jsonObject.toJSONString());

        activityMissionDO.setGroupId("default");
        activityMissionDO.setPriority(Optional.ofNullable(activityMissionAdminVO.getPriority()).orElse(10));
        activityMissionDO.setStatus(CommonStatus.enable.getCode());
        activityMissionDO.setActivityPlate(activityMissionAdminVO.getActivityPlate());
        activityMissionDO.setType(activityMissionAdminVO.getType());
        activityMissionDO.setFinishType(activityMissionAdminVO.getFinishType());
        activityMissionDO.setDistributeType(activityMissionAdminVO.getDistributeType());
        activityMissionDO.setCycleType(activityMissionAdminVO.getCycleType());
        activityMissionJpaDAO.save(activityMissionDO);

        return true;
    }

    public Boolean missionDelete(String ids) {
        if (StringUtils.isBlank(ids)) {
            return false;
        }

        String[] idArray = ids.split(",");
        List<Long> idList = Arrays.stream(idArray).map(Long::valueOf).collect(Collectors.toList());
        activityMissionJpaDAO.deleteByIds(idList);

        return true;
    }

    public AdminPageResult<PrizeItemAdminVO> prizePage(Long missionId, AdminPageParamsDTO adminPageParamsDTO) {
        Page<PrizeItemDO> page = prizeItemJpaDAO.getByMissionId(missionId, PageRequest.of(adminPageParamsDTO.getPage() - 1, adminPageParamsDTO.getPerPage()));

        AdminPageResult<PrizeItemAdminVO> adminPageResult = new AdminPageResult<>();
        adminPageResult.setTotal(Math.toIntExact(page.getTotalElements()));
        adminPageResult.setItems(page.getContent().stream().map(this::do2vo).collect(Collectors.toList()));
        adminPageResult.getItems().forEach(prizeItemAdminVO -> prizeItemAdminVO.setMissionId(missionId));
        return adminPageResult;
    }

    public Boolean prizeSaveOrUpdate(PrizeItemAdminVO prizeItemAdminVO) {
        if (prizeItemAdminVO == null) {
            return false;
        }
        // 特殊处理
        if (PrizeSubTypeEnum.MOUNT.getCode().equals(prizeItemAdminVO.getPrizeType())
                || PrizeSubTypeEnum.HEAD_FRAME.getCode().equals(prizeItemAdminVO.getPrizeType())
                || PrizeSubTypeEnum.ENTRY_SPECIAL_EFFECT.getCode().equals(prizeItemAdminVO.getPrizeType())) {
            prizeItemAdminVO.setPrizeSubType(prizeItemAdminVO.getPrizeType());
            prizeItemAdminVO.setPrizeType(PrizeTypeEnum.PRIZE_DRESS.getCode());
        }

        PrizeItemDO prizeItemDO = PrizeItemDO.of(prizeItemAdminVO);
        prizeItemDO.setExpiredType(ExpiredTypeEnum.EFFECTIVE_DAYS.getCode());
        prizeItemDO.setFee(false);
        prizeItemJpaDAO.save(prizeItemDO);

        return true;
    }

    public Boolean prizeDelete(String ids) {
        if (StringUtils.isBlank(ids)) {
            return false;
        }

        String[] idArray = ids.split(",");
        List<Long> idList = Arrays.stream(idArray).map(Long::valueOf).collect(Collectors.toList());
        prizeItemJpaDAO.deleteByIds(idList);

        return true;
    }

    public Boolean missionSync(List<ActivityMissionAdminVO> activityMissionAdminVOList) {
        if (CollectionUtils.isEmpty(activityMissionAdminVOList)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }

        activityMissionJpaDAO.deleteByIds(activityMissionAdminVOList.stream().map(ActivityMissionAdminVO::getId).collect(Collectors.toList()));
        for (ActivityMissionAdminVO activityMissionAdminVO : activityMissionAdminVOList) {
            this.missionSaveOrUpdate(activityMissionAdminVO);
            prizeItemJpaDAO.deleteByIds(activityMissionAdminVO.getPrizeItemAdminVOList().stream().map(PrizeItemAdminVO::getId).collect(Collectors.toList()));
            for (PrizeItemAdminVO prizeItemAdminVO : activityMissionAdminVO.getPrizeItemAdminVOList()) {
                this.prizeSaveOrUpdate(prizeItemAdminVO);
            }
        }

        return Boolean.TRUE;
    }

    private ActivityTemplateAdminVO do2vo(ActivityDO activityDO) {
        if (activityDO == null) {
            return null;
        }

        ActivityTemplateAdminVO activityTemplateAdminVO = new ActivityTemplateAdminVO();
        activityTemplateAdminVO.setId(activityDO.getId());
        activityTemplateAdminVO.setActivityCode(activityDO.getActivityCode());
        activityTemplateAdminVO.setActivityName(activityDO.getActivityName());
        if (activityDO.getStartTime() != null) {
            activityTemplateAdminVO.setStartTime(activityDO.getStartTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        }
        if (activityDO.getEndTime() != null) {
            activityTemplateAdminVO.setEndTime(activityDO.getEndTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        }
        activityTemplateAdminVO.setStatus(activityDO.getStatus());
        activityTemplateAdminVO.setUnionId(activityDO.getUnionId());
        return activityTemplateAdminVO;
    }

    private ActivityMissionAdminVO do2vo(ActivityMissionDO activityMissionDO) {
        if (activityMissionDO == null) {
            return null;
        }

        ActivityMissionAdminVO activityMissionAdminVO = new ActivityMissionAdminVO();
        BeanUtil.copyBeanIgnoreNullProperties(activityMissionDO, activityMissionAdminVO);
        activityMissionAdminVO.setGiftKey(activityMissionDO.getGiftKey());
        activityMissionAdminVO.setGiftCount(activityMissionDO.getGiftCount());
        return activityMissionAdminVO;
    }

    private PrizeItemAdminVO do2vo(PrizeItemDO prizeItemDO) {
        if (prizeItemDO == null) {
            return null;
        }

        return PrizeItemAdminVO.builder()
        		.id(prizeItemDO.getId())
        		.prizeKey(prizeItemDO.getPrizeKey())
        		.prizeType(prizeItemDO.getPrizeType())
        		.prizeSubType(prizeItemDO.getPrizeSubType())
        		.effectiveDays(prizeItemDO.getEffectiveDays())
        		.prizeNum(prizeItemDO.getPrizeNum())
        		.acquireProb(String.valueOf(prizeItemDO.getAcquireProb()))
        		.build();
    }

}
