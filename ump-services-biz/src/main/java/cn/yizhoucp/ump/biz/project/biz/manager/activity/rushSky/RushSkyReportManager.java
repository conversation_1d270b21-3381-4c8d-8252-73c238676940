package cn.yizhoucp.ump.biz.project.biz.manager.activity.rushSky;

import cn.yizhoucp.family.api.dto.family.FamilyInfoDTO;
import cn.yizhoucp.ms.core.base.config.ThreadPoolConfig;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.enums.ServicesNameEnum;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.starter.cassandra.base.kafka.YzKafkaProducerManager;
import com.google.common.collect.Maps;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

@Async(ThreadPoolConfig.ASYNC_EXECUTOR_NAME)
@Component
public class RushSkyReportManager {

    @Resource
    private YzKafkaProducerManager yzKafkaProducerManager;

    public void familyRankReport(Long familyId, Long rank) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("family_id", familyId);
        params.put("honour_type", rank);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), -1L, "daily_activity_rush_to_the_sky_family_honour_rank_top3", params, ServicesNameEnum.ump_services.getCode());
    }

    public void welcomeNewUserReport(Long uid, FamilyInfoDTO familyInfoDTO, Integer index) {
        if (Objects.isNull(index)) {
            return;
        }
        String welcome_period = "00_08";
        switch (index) {
            case 1:
                welcome_period = "08_12";
                break;
            case 2:
                welcome_period = "12_16";
                break;
            case 3:
                welcome_period = "16_18";
                break;
            case 4:
                welcome_period = "18_20";
                break;
            case 5:
                welcome_period = "20_24";
                break;
        }
        Map<String, Object> params = Maps.newHashMap();
        params.put("medalpk_num", 3);
        params.put("family_id", Objects.nonNull(familyInfoDTO) ? familyInfoDTO.getId() : -1L);
        params.put("welcome_period", welcome_period);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "daily_activity_rush_to_the_sky_finish_welcome_task", params, ServicesNameEnum.ump_services.getCode());
    }

    public void giveGiftReport(CoinGiftGivedModel item, Long medalNum) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("from", item.getFrom());
        params.put("medalpk_num", medalNum);
        params.put("gift_id", item.getGiftKey());
        params.put("coin_value", item.getCoin());
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), item.getFromUid(), "daily_activity_rush_to_the_sky_send_activity_gift", params, ServicesNameEnum.ump_services.getCode());
    }

    public void inviteJoinFamilyReport(Long uid, FamilyInfoDTO familyInfoDTO, Long targetUserId) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("medalpk_num", 5);
        params.put("family_id", Objects.isNull(familyInfoDTO) ? -1L : familyInfoDTO.getId());
        params.put("target_user_id", targetUserId);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "daily_activity_rush_to_the_sky_finish_invite_task", params, ServicesNameEnum.ump_services.getCode());
    }

    public void completeAuctionReport(Long uid, FamilyInfoDTO familyInfoDTO) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("medalpk_num", 3);
        params.put("family_id", Objects.isNull(familyInfoDTO) ? -1L : familyInfoDTO.getId());
        params.put("user_id", uid);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "daily_activity_rush_to_the_sky_finish_auction_task", params, ServicesNameEnum.ump_services.getCode());
    }

    public void stayRoom20minsReport(Long uid, FamilyInfoDTO familyInfoDTO) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("medalpk_num", 3);
        params.put("family_id", Objects.isNull(familyInfoDTO) ? -1L : familyInfoDTO.getId());
        params.put("user_id", uid);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "daily_activity_rush_to_the_sky_finish_chat_room_stay_task", params, ServicesNameEnum.ump_services.getCode());
    }

    public void pkWinReport(Long coinAwardNum, Long medalPkNum, Long familyId, Integer level) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("coin_award_num", coinAwardNum);
        params.put("medalpk_num", medalPkNum);
        params.put("family_id", familyId);
        params.put("pk_rank", level);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), -1L, "daily_activity_rush_to_the_sky_family_get_award_in_pk", params, ServicesNameEnum.ump_services.getCode());
    }

    public void drawReport(Long uid, Integer times, String prizeValue) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("lottery_type", times);
        params.put("award_type", prizeValue);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "daily_activity_rush_to_the_sky_open_blind_box", params, ServicesNameEnum.ump_services.getCode());
    }

}
