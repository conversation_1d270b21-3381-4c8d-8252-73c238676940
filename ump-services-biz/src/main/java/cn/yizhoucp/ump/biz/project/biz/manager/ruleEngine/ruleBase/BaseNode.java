package cn.yizhoucp.ump.biz.project.biz.manager.ruleEngine.ruleBase;

import cn.yizhoucp.ump.biz.project.biz.enums.NodeRunStateEnum;
import cn.yizhoucp.ump.biz.project.biz.enums.TimeTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.util.TimeUtils;
import cn.yizhoucp.ump.biz.project.dto.RuleContext;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.InvocationTargetException;

/**
 * 基础Node
 * <AUTHOR>
 */
@Data
@Slf4j
public abstract class BaseNode {

  /** 节点ID */
  private long nodeId;

  /** 时间类型 */
  private TimeTypeEnum iceTimeTypeEnum;

  /** 开始时间 */
  private long startTime;

  /** 结束时间 */
  private long endTime;

  /** 测试开始时间 */
  private long testStartTime;

  /** 测试结束时间 */
  private long testEndTime;

  /**
   * 前置节点
   * 如果前置节点返回FALSE,节点的执行将被拒绝
   * forward节点可以理解为是用AND连接的forward和this
   */
  private BaseNode forward;

  /**
   * process
   *
   * @param cxt 入参
   * @return true(f通过 r获得) false(f不通过 r丢失)
   */
  public NodeRunStateEnum process(RuleContext cxt) throws InvocationTargetException, IllegalAccessException {
    cxt.setCurrentNodeId(this.nodeId);
    if (TimeUtils.timeEnable(iceTimeTypeEnum, cxt.getRequestTime(), startTime, endTime)) {
      log.info("");
      return NodeRunStateEnum.NONE;
    }
    if (forward != null) {
      NodeRunStateEnum forwardRes = forward.process(cxt);
      if (forwardRes != NodeRunStateEnum.FALSE) {
        NodeRunStateEnum res = processNode(cxt);
        res = forwardRes == NodeRunStateEnum.NONE ? res : (res == NodeRunStateEnum.NONE ? NodeRunStateEnum.TRUE : res);
        log.info("");
        return res;
      }
      log.info("");
      return NodeRunStateEnum.FALSE;
    }
    NodeRunStateEnum res = processNode(cxt);
    log.info("");
    return res;
  }

  /**
   * processNode
   *
   * @param cxt 入参
   * @return 节点执行结果
   */
  protected abstract NodeRunStateEnum processNode(RuleContext cxt) throws InvocationTargetException, IllegalAccessException;

  public Long getNodeId() {
    return nodeId;
  }

  public long findNodeId() {
    return nodeId;
  }
}
