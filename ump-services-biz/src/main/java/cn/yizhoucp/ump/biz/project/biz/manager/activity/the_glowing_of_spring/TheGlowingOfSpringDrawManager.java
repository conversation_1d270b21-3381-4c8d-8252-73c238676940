package cn.yizhoucp.ump.biz.project.biz.manager.activity.the_glowing_of_spring;

import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.AbstractDrawTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 春日灼灼抽奖类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 16:40 2025/3/7
 */
@Slf4j
@Service
public class TheGlowingOfSpringDrawManager extends AbstractDrawTemplate {

    @Override
    protected void resourceCheck(DrawContext context) {

    }

    @Override
    protected void deductResource(DrawContext context) {

    }

    @Override
    protected void doCallback(DrawContext context) {

    }
}
