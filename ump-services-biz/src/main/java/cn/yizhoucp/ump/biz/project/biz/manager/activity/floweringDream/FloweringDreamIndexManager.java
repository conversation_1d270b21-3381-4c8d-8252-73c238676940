package cn.yizhoucp.ump.biz.project.biz.manager.activity.floweringDream;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.roomservices.RoomVO;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.activity.floweringDream.*;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.FloweringDreamConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.IndexManager;

import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.remoteService.FeignRoomService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.FeignUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * 主界面
 */
@Service
@Slf4j
public class FloweringDreamIndexManager implements IndexManager {

    @Resource
    private RedisManager redisManager;

    @Resource
    private FloweringDreamRankManager floweringDreamRankManager;

    @Resource
    private FeignRoomService feignRoomService;

    @Resource
    private FeignUserService feignUserService;

    @Override
    @ActivityCheck(activityCode = FloweringDreamConstant.ACTIVITY_CODE)
    public FloweringDreamIndexVO getIndex(BaseParam param, Long toUid, String extData) {
        if (param.getUid() == null || param.getAppId() == null || param.getUnionId() == null) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        Long uid = param.getUid();

        FloweringDreamIndexVO indexVO = new FloweringDreamIndexVO();

        // 通行证数量
        String key = String.format(FloweringDreamConstant.ACTIVITY_PASS_CERTIFICATE, uid);
        indexVO.setTrafficPermit(redisManager.hasKey(key) ? redisManager.getLong(key) : 0L);

        // 累计抽奖次数奖励列表
        indexVO.setDrawCountRewards(getDrawCountRewardList(uid));

        // 任务列表
        indexVO.setDreamTask(getTaskList(uid));

        // 繁华使者榜 + 自己
        FloweringDreamIndexVO indexToEnvoy = this.getEnvoyLeaderboard(indexVO, param);

        // 梦境剧场榜 + 自己
        return this.getTheaterLeaderboard(indexToEnvoy, uid);
    }

    /**
     * 累计抽奖次数奖励列表
     *
     * @return
     */
    public List<DrawCountRewardVO> getDrawCountRewardList(Long uid) {
        List<DrawCountRewardVO> drawCountRewards = new ArrayList<>();
        for (FloweringDreamConstant.DrawCountEnum drawCountItem : FloweringDreamConstant.DrawCountEnum.values()) {
            DrawCountRewardVO drawCountRewardVO = new DrawCountRewardVO();

            drawCountRewardVO.setRewardsKey(drawCountItem.getRewardsKey());
            String key = FloweringDreamConstant.createUserDreamCountAwardKey(uid, drawCountItem.getAwardKey());
            drawCountRewardVO.setAwardReceive(redisManager.hasKey(key) ? redisManager.getString(key) : FloweringDreamConstant.AwardReceiveEnum.NOT_COMPLETED.getStatus());

            log.debug("drawCountRewardVO to uid:{}", uid);
            drawCountRewards.add(drawCountRewardVO);
        }
        return drawCountRewards;
    }

    /**
     * 任务列表
     *
     * @return
     */
    public List<FDTaskVO> getTaskList(Long uid) {
        List<FDTaskVO> dreamTask = new ArrayList<>();
        for (FloweringDreamConstant.TaskEnum taskItem : FloweringDreamConstant.TaskEnum.values()) {
            FDTaskVO fdTaskVO = new FDTaskVO();
            fdTaskVO.setTaskKey(taskItem.getTaskCode());
            String key = FloweringDreamConstant.createUserTaskProgressKey(uid, taskItem);
            // 存在 则为已经发放奖励 - 通行证
            fdTaskVO.setTaskAccomplished(redisManager.hasKey(key) ? Boolean.TRUE : Boolean.FALSE);

            dreamTask.add(fdTaskVO);
        }
        return dreamTask;
    }

    /**
     * 繁华使者榜
     *
     * @return
     */
    public FloweringDreamIndexVO getEnvoyLeaderboard(FloweringDreamIndexVO indexVO, BaseParam param) {

        // 保存榜单信息
        RankVO topTenRanks = null;
        try {
            topTenRanks = floweringDreamRankManager.getRank(RankContext.builder()
                    .activityCode(FloweringDreamConstant.ACTIVITY_CODE)
                    .rankKey(FloweringDreamConstant.ENVOY_DREAM_RANK_KEY)
                    .type(RankContext.RankType.user)
                    .rankLen(10L)
                    .param(param)
                    .build());
        } catch (Exception e) {
            log.warn("获取繁华使者榜单信息失败", e);
        }
        List<FDLeaderboardVO> envoyLeaderboard = new ArrayList<>();
        if (topTenRanks != null && topTenRanks.getRankList() != null) {
            envoyLeaderboard = topTenRanks.getRankList().stream()
                    .map(rankItem -> FDLeaderboardVO.builder()
                            .userName(rankItem.getName())
                            .avatar(rankItem.getIcon())
                            .rank(rankItem.getRank())
                            .score(rankItem.getValue())
                            .build())
                    .collect(Collectors.toList());
        }
        indexVO.setEnvoyLeaderboard(envoyLeaderboard);

        // 保存自己信息
        FDLeaderboardVO userEnvoyLeaderboard = new FDLeaderboardVO();
        RankItem myselfRank = null;
        if (topTenRanks != null && topTenRanks.getMyselfRank() != null) {
            myselfRank = topTenRanks.getMyselfRank();
            userEnvoyLeaderboard = FDLeaderboardVO.builder()
                    .userName(myselfRank.getName())
                    .avatar(myselfRank.getIcon())
                    .rank(myselfRank.getRank())
                    .score(myselfRank.getValue())
                    .build();
        } else {
            Long uid = MDCUtil.getCurUserIdByMdc();
            log.info("userEnvoyLeaderboard is null uid:{}", uid);

            Result<UserVO> result = feignUserService.getBasic(uid, ServicesAppIdEnum.lanling.getAppId());
            if (null == result || !StringUtils.equalsIgnoreCase(ErrorCode.SUCCESS.getCode(), result.getCode())) {
                log.warn("feignUserService getBasic error:id:{}, appID:{}", uid, ServicesAppIdEnum.lanling.getAppId());
                throw new ServiceException(ErrorCode.LANLING_GET_COIN_BALANCE_ERROR);

            } else {
                UserVO data = result.getData();
                userEnvoyLeaderboard.setAvatar(data.getAvatar());
                userEnvoyLeaderboard.setUserName(data.getName());
                userEnvoyLeaderboard.setScore(0L);
            }
        }

        indexVO.setUserEnvoyLeaderboard(userEnvoyLeaderboard);
        return indexVO;
    }

    /**
     * 梦境剧场榜
     *
     * @return
     */
    public FloweringDreamIndexVO getTheaterLeaderboard(FloweringDreamIndexVO indexVO, Long uid) {

        // 获取榜单
        String theaterLeaderboardKey = FloweringDreamConstant.createDreamTheaterRankKey(dayToStr());

        RankVO topTenRanks = null;
        try {
            topTenRanks = floweringDreamRankManager.getRank(RankContext.builder()
                    .activityCode(FloweringDreamConstant.ACTIVITY_CODE)
                    .rankKey(theaterLeaderboardKey)
                    .type(RankContext.RankType.room)
                    .rankLen(10L)
                    .build());
        } catch (Exception e) {
            log.warn("获取梦境剧场榜单信息失败", e);
        }

        List<FDPlusLeaderboardVO> theaterLeaderboard = new ArrayList<>();
        if (topTenRanks != null && topTenRanks.getRankList() != null) {
            for (RankItem rankItem : topTenRanks.getRankList()) {
                Long value = rankItem.getValue();
                long result = Math.floorDiv(value, FloweringDreamConstant.THRESHOLD_VALUE);
                FDPlusLeaderboardVO fdPlusLeaderboardVO = FDPlusLeaderboardVO.builder()
                        .avatar(rankItem.getIcon())
                        .rank(rankItem.getRank())
                        .score(value)
                        .userName(rankItem.getName())
                        .theaterAward(result > FloweringDreamConstant.MAX_COUNT ? FloweringDreamConstant.MAX_COUNT : result)
                        .build();
                theaterLeaderboard.add(fdPlusLeaderboardVO);
            }
        }
        log.debug("theaterLeaderboardByDate:{} to date:{}", theaterLeaderboard, dayToStr());
        indexVO.setTheaterLeaderboard(theaterLeaderboard);

        // 判断用户是否存在自己的房间 uid -- 房主 id
        Result<RoomVO> roomVOResult = feignRoomService.roomMessage(null, "ROOM_SOURCE_PERSONAL");
        if (!StringUtils.equalsIgnoreCase(ErrorCode.SUCCESS.getCode(), roomVOResult.getCode()) || Objects.isNull(roomVOResult.getData())) {
            log.info("[feignRoomService - roomMessage] to null uid {}", uid);
            return indexVO;
        }

        RoomVO roomVO = roomVOResult.getData();
        if (Objects.isNull(roomVO.getRoomId())) {
            return indexVO;
        }

        // 保存自己榜单信息
        Long rank = Optional.ofNullable(redisManager.reverseRank(theaterLeaderboardKey, roomVO.getRoomId().toString())).orElse(-1L);
        Double score = Optional.ofNullable(redisManager.score(theaterLeaderboardKey, roomVO.getRoomId().toString())).orElse(0.0);
        if (rank < 0 || score < 1) {
            log.info("user room rank to null to null uid {} --> roomId:{}", uid, roomVO.getRoomId());
            FDPlusLeaderboardVO userTheaterLeaderboard = FDPlusLeaderboardVO.builder()
                    .userName(roomVO.getRoomName())
                    .avatar(roomVO.getRoomIcon())
                    .rank(0L)
                    .score(0L)
                    .theaterAward(0L)
                    .build();
            indexVO.setUserTheaterLeaderboard(userTheaterLeaderboard);
            return indexVO;
        }

        // 林中微光 奖励个数
        long theaterAward = Math.floorDiv(score.longValue(), FloweringDreamConstant.THRESHOLD_VALUE);
        FDPlusLeaderboardVO userTheaterLeaderboard = FDPlusLeaderboardVO.builder()
                .userName(roomVO.getRoomName())
                .avatar(roomVO.getRoomIcon())

                // 这里获得的排名从 0 开始，需要对rank++
                .rank(++rank)

                .score(score.longValue())
                .theaterAward(theaterAward > FloweringDreamConstant.MAX_COUNT ? FloweringDreamConstant.MAX_COUNT : theaterAward)
                .build();

        indexVO.setUserTheaterLeaderboard(userTheaterLeaderboard);
        return indexVO;
    }

    /**
     * 获取当前日期 yyyyMMdd 格式
     *
     * @return
     */
    public String dayToStr() {
        LocalDate yesterday = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        return yesterday.format(formatter);
    }


    @Override
    public String getTemplateType() {
        return null;
    }

    @Override
    public String getActivityCode() {
        return FloweringDreamConstant.ACTIVITY_CODE;
    }
}
