package cn.yizhoucp.ump.biz.project.dal.jpa.dao;

import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;


/**
 * 奖池
 *
 * @author: lianghu
 */
@Repository
public interface DrawPoolJpaDAO extends JpaRepository<DrawPoolDO, Long>, JpaSpecificationExecutor<DrawPoolDO>, CrudRepository<DrawPoolDO, Long> {

    /**
     * 查询奖池信息（管理后台使用）
     *
     * @param poolCodeList
     * @return api.project.dal.jpa.dataobject.ActivityDO
     */
    @Query(value = "select * from activity_draw_pool where pool_code in ?1", nativeQuery = true)
    List<DrawPoolDO> getByCodeList(Set<String> poolCodeList);

    /**
     * 根据活动与时间查询奖池
     *
     * @param activityCode
     * @param now
     * @return api.project.dal.jpa.dataobject.ActivityDO
     */
    @Query(value = "select * from activity_draw_pool where activity_code = ?1 and biz_key = ?2 and start_time <= ?3 and end_time >= ?3 and status = 1", nativeQuery = true)
    DrawPoolDO getByActivityCodeAndTime(String activityCode, String bizKey, LocalDateTime now);

    /**
     * 根据活动与业务KEY查询奖池
     *
     * @param activityCode
     * @param bizKey
     * @return api.project.dal.jpa.dataobject.ActivityDO
     */
    @Query(value = "select * from activity_draw_pool where activity_code = ?1 and biz_key = ?2 and status = 1", nativeQuery = true)
    List<DrawPoolDO> getByActivityCodeAndBizKey(String activityCode, String bizKey);

    @Query(value = "select * from activity_draw_pool where pool_code = ?1", nativeQuery = true)
    List<DrawPoolDO> getByCode(String poolCode);

    /**
     * 根据奖池业务KEY查询
     *
     * @param activityCode
     * @return api.project.dal.jpa.dataobject.ActivityDO
     */
    @Query(value = "select * from activity_draw_pool where activity_code = ?1 and pool_code = ?2 and status = 1", nativeQuery = true)
    DrawPoolDO getByActivityCodeAndPoolCode(String activityCode, String poolCode);

    DrawPoolDO findByActivityCodeAndPoolCode(String activityCode, String poolCode);

    List<DrawPoolDO> findByUnionIdAndActivityCode(String unionId, String activityCode);

    List<DrawPoolDO> findByUnionIdAndActivityCodeAndStatus(String unionId, String activityCode, Integer status);

    List<DrawPoolDO> findByUnionIdAndStatus(String unionId, Integer status);

    List<DrawPoolDO> findByUnionId(String unionId);

    @Query(value = "select pool_code from activity_draw_pool", nativeQuery = true)
    List<String> findAllPoolCode();

    DrawPoolDO findByPoolCode(String poolCode);

    List<DrawPoolDO> findByActivityCode(String activityCode);
}
