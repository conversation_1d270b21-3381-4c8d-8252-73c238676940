package cn.yizhoucp.ump.biz.project.biz.manager.activity.seasonalFestival;

import cn.yizhoucp.ms.core.base.config.ThreadPoolConfig;
import cn.yizhoucp.ms.core.base.env.DynamicEnvManager;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.seasonalFestival.common.SeasonalFestivalConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.seasonalFestival.common.SeasonalFestivalRedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.clientPush.ServerPushManager;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityUrlUtil;
import cn.yizhoucp.ump.biz.project.common.ActivityComponent;
import cn.yizhoucp.ump.biz.project.common.event.GiftGiveEvent;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 节气活动业务类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 12:11 2025/2/10
 */
@Slf4j
@Service
public class SeasonalFestivalBizManager implements ActivityComponent {

    @Resource
    private SeasonalFestivalRedisManager seasonalFestivalRedisManager;
    @Resource
    private SeasonalFestivalRankManager seasonalFestivalRankManager;
    @Resource
    private ServerPushManager serverPushManager;
    @Value("${spring.profiles.active}")
    private String env;
    @Resource
    private Environment environment;


    @Override
    public String getActivityCode() {
        return "";
    }

    @Async(ThreadPoolConfig.ASYNC_EXECUTOR_NAME)
    @EventListener(GiftGiveEvent.class)
    @ActivityCheck(activityCode = SeasonalFestivalConstant.ACTIVITY_CODE, isThrowException = false)
    public void eventSendGiftHandle(GiftGiveEvent event) {
        log.info("sendGiftHandle event:{}", event);
        sendGiftHandle(event.getBaseParam(), event.getCoinGiftGivedModels());
    }


    public Boolean sendGiftHandle(BaseParam param, List<CoinGiftGivedModel> coinGiftGivedModelList) {
        log.info("sendGiftHandle param:{}, coinGiftGivedModelList:{}", param, coinGiftGivedModelList);
        for (CoinGiftGivedModel coinGiftGivedModel : coinGiftGivedModelList) {
            String giftKey = coinGiftGivedModel.getGiftKey();
            ScenePrizeDO taskGift = seasonalFestivalRedisManager.getTaskGift(giftKey);
            if (taskGift == null) {
                continue;
            }
            //任务统计
            updateTaskProgress(param, coinGiftGivedModel);
            //排行榜统计
            updateRankProgress(param, coinGiftGivedModel);
        }
        return Boolean.TRUE;
    }

    private void updateRankProgress(BaseParam param, CoinGiftGivedModel coinGiftGivedModel) {
        Long uid = param.getUid();
        Long toUid = coinGiftGivedModel.getToUid();
        seasonalFestivalRankManager.incrRankValue(uid, coinGiftGivedModel.getCoin() * SeasonalFestivalConstant.BASE_RANK_NUM, seasonalFestivalRedisManager.getRankKey(SeasonalFestivalConstant.GIVING_GIFT));
        seasonalFestivalRankManager.incrRankValue(toUid, coinGiftGivedModel.getCoin() * SeasonalFestivalConstant.BASE_RANK_NUM, seasonalFestivalRedisManager.getRankKey(SeasonalFestivalConstant.RECEIVING_GIFT));
    }

    private void updateTaskProgress(BaseParam param, CoinGiftGivedModel coinGiftGivedModel) {
        Long uid = param.getUid();
        String giftKey = coinGiftGivedModel.getGiftKey();
        Boolean isTaskClaimed = seasonalFestivalRedisManager.getTaskClaimed(uid, giftKey);
        if (Boolean.FALSE.equals(isTaskClaimed)) {
            return;
        }
        ScenePrizeDO taskGift = seasonalFestivalRedisManager.getTaskGift(giftKey);
        Long productCount=taskGift.getNeedCount()*coinGiftGivedModel.getProductCount();
        seasonalFestivalRedisManager.incrementSeasonCard(uid, productCount);

        //发送弹窗
        String url = ActivityUrlUtil.getH5BaseUrl(MDCUtil.getCurUnionIdByMdc()
                , env
                , Boolean.TRUE
                , environment.getProperty(DynamicEnvManager.GLOBAL_ENV_NAME)) + "jieqi-push" + "?num=" + productCount;

        serverPushManager.sendH5ServerPush(uid, url);
    }
}
