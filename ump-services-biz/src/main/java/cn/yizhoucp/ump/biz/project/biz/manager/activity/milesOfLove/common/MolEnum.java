package cn.yizhoucp.ump.biz.project.biz.manager.activity.milesOfLove.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

import static cn.yizhoucp.ump.biz.project.biz.manager.activity.milesOfLove.common.MolConstant.*;

public class MolEnum {

    @AllArgsConstructor
    @Getter
    public enum StatusEnum {
        /**
         * 未开启
         */
        NOT_FINISHED("NOT_FINISHED", 0),
        /**
         * 可领取
         */
        REWARDED_AVAILABLE("REWARDED_AVAILABLE", 1),
        /**
         * 已领取
         */
        COMPLETED("COMPLETED", 2);

        private final String status;
        private final Integer statusValue;


        public static StatusEnum getByCode(String item) {
            for (StatusEnum statusEnum : StatusEnum.values()) {
                if (statusEnum.getStatus().equals(item)) {
                    return statusEnum;
                }
            }
            return null;
        }
    }


    /**
     * 任务枚举
     */
    @AllArgsConstructor
    @Getter
    public enum TaskEnum {

        task_1("task_1", "SDXS_GIFT", 6L, LUCKINESS_POOL, 1L),
        task_2("task_2", "XYP_GIFT", 4L, FITNESS_POOL, 1L),
        task_3("task_3", "YHJL_GIFT", 3L, RICH_POOL, 3L),
        task_4("task_4", "XGSN_GIFT", 2L, LOVE_POOL, 4L),
        task_5("task_5", "YMYYY_GIFT", 1L, LOVE_POOL, 5L),
        task_6("task_6", "TMFX_GIFT", 1L, RICH_POOL, 6L);

        private final String taskKey;
        // 需要礼物价值
        private final String needGiftKey;
        // 需要礼物数量
        private final Long needGiftCount;
        // 奖励签key
        private final String signKey;
        // 奖励个数
        private final Long sendValue;

        public static TaskEnum getByGiftValue(String item) {
            for (TaskEnum taskEnum : TaskEnum.values()) {
                if (taskEnum.getNeedGiftKey().equals(item)) {
                    return taskEnum;
                }
            }
            return null;
        }

        public static TaskEnum getByTaskKey(String item) {
            for (TaskEnum taskEnum : TaskEnum.values()) {
                if (taskEnum.getTaskKey().equals(item)) {
                    return taskEnum;
                }
            }
            return null;
        }
    }


    /**
     * 纪念币解锁枚举
     */
    @AllArgsConstructor
    @Getter
    public enum CoinEnum {

        task_1("1", RICH_POOL, 1L),
        task_2("2", LUCKINESS_POOL, 2L),
        task_3("3", LOVE_POOL, 4L),
        task_4("4", FITNESS_POOL, 8L),
        task_5("5", LUCKINESS_POOL, 9L),
        task_6("6", FITNESS_POOL, 11L);

        private final String key;
        // 奖励签key
        private final String signKey;
        // 奖励个数
        private final Long sendValue;

        public static CoinEnum getByKey(String item) {
            for (CoinEnum coinEnum : CoinEnum.values()) {
                if (coinEnum.getKey().equals(item)) {
                    return coinEnum;
                }
            }
            return null;
        }
    }
}
