package cn.yizhoucp.ump.biz.project.biz.manager.activity.component.sendPrize;

import cn.yizhoucp.ms.core.base.UserPackageScene;
import cn.yizhoucp.ms.core.base.enums.*;
import cn.yizhoucp.ms.core.base.international.InternationalManager;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.lovePostcard.LovePostcardManager;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityTimeUtil;
import cn.yizhoucp.ump.api.enums.ActivityCheckListEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.AbstractComponent;
import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.TopicTagEnum;
import cn.yizhoucp.ms.core.base.enums.dressup.DressUpType;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw.inner.CoinTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw.inner.ExpiredTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw.inner.PrizeTypeEnum;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

import static cn.yizhoucp.ms.core.base.enums.CouponEnum.SQFD_EXPERIENCE_COUPON;

/**
 * 奖励下发组件
 *
 * @author: lianghu
 */
@Deprecated
@Slf4j
@Component
public class SendPrizeComponent extends AbstractComponent {

    @Resource
    private InternationalManager internationalManager;

    /** 头像框 */
    private static final String HEAD_FRAME = "woaizhongguo_head_frame";

    /**
     * 根据场景下发奖励
     *
     * @param activityCode
     * @param sceneCode
     * @param uid
     * @return java.lang.Boolean
     */
    public Boolean sendPrize(String activityCode, String sceneCode, Long uid) {
        return sendPrize(null, activityCode, sceneCode, uid, null);
    }

    /**
     * 下发奖励
     * <p>
     * todo:
     * 1. 支持双人头像下发
     * 2. 支持家族装扮下发
     * 3. 支持国王下发
     * 4. 支持按截止时间下发礼物
     * 5. 剥离对 ScenePrizeDO 数据结构依赖，转为 DTO
     *
     * @param activityCode
     * @param sceneCode
     * @param uid
     * @param scenePrizeDOS
     * @return java.lang.Boolean
     */
    @Deprecated
    public Boolean sendPrize(Long appId, String activityCode, String sceneCode, Long uid, List<ScenePrizeDO> scenePrizeDOS) {

        // 获取场景奖励列表
        if (Objects.isNull(scenePrizeDOS)) {
            scenePrizeDOS = scenePrizeService.getListBySceneCode(activityCode, sceneCode);
        }

        String fullActivityCode = String.format(FULL_ACTIVITY_CODE, activityCode);
        for (ScenePrizeDO item : scenePrizeDOS) {
            log.info("下发奖励 uid:{}, item:{}", uid, JSONObject.toJSONString(item));
            PrizeTypeEnum instance = PrizeTypeEnum.getInstance(item.getPrizeType());
            if (Objects.isNull(instance)) {
                return false;
            }
            switch (instance) {
                case PRIZE_COIN:
                    sendSceneCoin(appId, uid, activityCode, fullActivityCode, item);
                    break;
                case PRIZE_REAL:
                case PRIZE_GIFT:
                    sendSceneGift(appId, uid, fullActivityCode, item);
                    break;
                case PRIZE_DRESS:
                    // TODO 临时解决明信片头像框 1008 可删除
                    if (ActivityCheckListEnum.LOVE_POSTCARD.getCode().equals(activityCode) && item.getPrizeValue().equals(HEAD_FRAME)) {
                        double prizeDay = redisManager.hincr(LovePostcardManager.USER_HEAD_FRAME_CACHE, String.valueOf(uid), item.getPrizeEffectiveDay(), DateUtil.ONE_MONTH_SECOND);
                        if (prizeDay > 70) {
                            break;
                        }
                    }
                    sendDressUp(appId, uid, DressUpType.getByCode(item.getPrizeSubType()), item.getPrizeValue(), item.getPrizeEffectiveDay(), item.getPrizeNum());
                    break;
                case PRIZE_FAMILY_DRESS:
                    // todo:
                    break;
                case PRIZE_VIP:
                    sendVip(uid, item.getPrizeEffectiveDay());
                    break;
                case PRIZE_POINT:
                    sendScenePoint(appId, uid, fullActivityCode, item);
                    break;
                case COUPON:
                    sendCoupon(appId, MDCUtil.getCurUnionIdByMdc(), uid, fullActivityCode, item);
                    break;
            }
        }
        return true;
    }

    /**
     * 按场景下发积分
     *
     * @param uid
     * @param fullActivityCode
     * @param param
     * @return boolean
     */
    public boolean sendScenePoint(Long appId, Long uid, String fullActivityCode, ScenePrizeDO param) {
        String pointTips = internationalManager.changeCommonText(null, "simple.point.send.user.tips");
        String activityTips = internationalManager.changeCommonText(null, ActivityCheckListEnum.getInstanceByCode(param.getActivityCode()).getDescWithInternational());
        if (StringUtils.isNotBlank(param.getPrizeSubType())) {
            switch (CoinTypeEnum.getInstanceByCode(param.getPrizeSubType())) {
                case FEE:
                    sendFeePoint(appId == null ? ServicesAppIdEnum.lanling.getAppId() : appId, uid, fullActivityCode, Long.parseLong(param.getPrizeValue()) * 100, param.getSceneCode(), fullActivityCode, activityTips + pointTips, param.getPrizeNum());
                    break;
                case FREE:
                    sendFreePoint(appId == null ? ServicesAppIdEnum.lanling.getAppId() : appId, uid, fullActivityCode, Long.parseLong(param.getPrizeValue()) * 100, param.getSceneCode(), fullActivityCode, activityTips + pointTips, param.getPrizeNum());
                    break;
                default:
                    log.error("场景金币配置错误 uid:{}, activityCode:{}, coin:{}", uid, param.getActivityCode(), param.getPrizeValue());
            }
            return true;
        }
        log.error("场景金币配置错误 uid:{}, activityCode:{}, coin:{}", uid, param.getActivityCode(), param.getPrizeValue());
        return false;
    }

    /**
     * 下发付费积分
     */
    public boolean sendFeePoint(Long appId, Long uid, String fullActivityCode, Long feePoint, String businessKey, String businessType, String desc, int num) {
        for (int i = 0; i < num; i++) {
            userCoinAccountManager.sendPoint(null, appId, uid, AppScene.activity, fullActivityCode,
                    feePoint, 0L, businessKey, businessType, String.format(desc, feePoint / 100));
        }
        return true;
    }

    /**
     * 下发免费积分
     */
    public boolean sendFreePoint(Long appId, Long uid, String fullActivityCode, Long freePoint, String businessKey, String businessType, String desc, int num) {
        appId = appId == null ? ServicesAppIdEnum.lanling.getAppId() : appId;
        for (int i = 0; i < num; i++) {
            userCoinAccountManager.sendPoint(null, appId, uid, AppScene.activity, fullActivityCode,
                    0L, freePoint, businessKey, businessType, String.format(desc, freePoint / 100));
        }
        return true;
    }

    /**
     * 按场景下发金币
     *
     * @param uid
     * @param fullActivityCode
     * @param param
     * @return boolean
     */
    public boolean sendSceneCoin(Long appId, Long uid, String activityCode, String fullActivityCode, ScenePrizeDO param) {
        String coinTips = internationalManager.changeCommonText(null, "simple.coin.send.user.tips");
        String activityTips = internationalManager.changeCommonText(null, ActivityCheckListEnum.getInstanceByCode(param.getActivityCode()).getDescWithInternational());
        if (StringUtils.isNotBlank(param.getPrizeSubType())) {
            switch (CoinTypeEnum.getInstanceByCode(param.getPrizeSubType())) {
                case FEE:
                    sendFeeCoin(appId, uid, activityCode, Long.valueOf(param.getPrizeValue()), param.getSceneCode(), fullActivityCode, activityTips + coinTips, param.getPrizeNum());
                    break;
                case FREE:
                    sendFreeCoin(appId, uid, activityCode, Long.valueOf(param.getPrizeValue()), param.getSceneCode(), fullActivityCode, activityTips + coinTips, param.getPrizeNum());
                    break;
                default:
                    log.error("场景金币配置错误 uid:{}, activityCode:{}, coin:{}", uid, param.getActivityCode(), param.getPrizeValue());
            }
            return true;
        }
        log.error("场景金币配置错误 uid:{}, activityCode:{}, coin:{}", uid, param.getActivityCode(), param.getPrizeValue());
        return false;
    }

    /**
     * 下发付费金币
     */
    public boolean sendFeeCoin(Long appId, Long uid, String appFunction, Long feeCoin, String businessKey, String businessType, String desc, int num) {
        for (int i = 0; i < num; i++) {
            userCoinAccountManager.sendCoin(null, appId, uid, null, AppScene.activity, appFunction,
                    feeCoin, 0L, businessKey, businessType, desc,null,null);
        }
        return true;
    }

    /**
     * 下发免费金币
     */
    public boolean sendFreeCoin(Long appId, Long uid, String appFunction, Long freeCoin, String businessKey, String businessType, String desc, int num) {
        for (int i = 0; i < num; i++) {
            userCoinAccountManager.sendCoin(null, appId, uid, null, AppScene.activity, appFunction,
                    0L, freeCoin, businessKey, businessType, desc,null,null);
        }
        return true;
    }

    /**
     * 下发免费积分
     *
     * @param appId        应用id
     * @param uid          用户id
     * @param activityCode 活动 code
     * @param freePoint    免费积分
     * @param businessKey  关键关联信息
     * @param businessType 业务类型
     * @param desc         coin log
     * @return 是否成功
     */
    public boolean sendFreePoint(Long appId, Long uid, String activityCode, Long freePoint, String businessKey, String businessType, String desc) {
        return userCoinAccountManager.sendPoint(null, appId, uid, AppScene.activity, activityCode,
                0L, freePoint, businessKey, businessType, desc);
    }

    /**
     * 按场景下发礼物
     *
     * @param uid
     * @param fullActivityCode
     * @param param
     * @return boolean
     */
    public boolean sendSceneGift(Long appId, Long uid, String fullActivityCode, ScenePrizeDO param) {
        Long num = StringUtils.isNotBlank(param.getPrizeSubType()) ? param.getPrizeNum() * Long.valueOf(param.getPrizeSubType()) : param.getPrizeNum().longValue();
        if (StringUtils.isNotBlank(param.getExpiredType())) {
            switch (ExpiredTypeEnum.getInstanceByCode(param.getExpiredType())) {
                case LIMIT_TIME:
                    sendGift(appId, param.getPrizeValue(), uid, num, fullActivityCode, param.getLimitTime());
                    break;
                case EFFECTIVE_DAYS:
                    sendGift(appId, param.getPrizeValue(), uid, num, param.getPrizeEffectiveDay(), fullActivityCode);
                    break;
                case ACTIVITY_DAILY:
                    sendGift(appId, param.getPrizeValue(), uid, num, fullActivityCode, ActivityTimeUtil.getLastTimeOfTodayLong(param.getActivityCode()));
                    break;
                case ACTIVITY_WEEKLY:
                    sendGift(appId, param.getPrizeValue(), uid, num, fullActivityCode, ActivityTimeUtil.getLastTimeOfWeek(param.getActivityCode()));
                    break;
                default:
                    log.error("场景礼品配置错误 uid:{}, activityCode:{}, giftKey:{}", uid, param.getActivityCode(), param.getPrizeValue());
            }
            return true;
        }
        // 默认以有效天数下发
        sendGift(appId, param.getPrizeValue(), uid, num, param.getPrizeEffectiveDay(), fullActivityCode);
        return false;
    }

    /**
     * 下发礼品，过期时间按天计算 TODO
     */
    public boolean sendGift(Long appId, String productKey, Long uid, Long num, Integer effectiveDays, String scene) {
        packageProductRemoteService.sendGiftToPackage(appId, uid, num, productKey, UserPackageBizType.GIFT.getCode(), scene,
                null, null, effectiveDays, Boolean.FALSE, Boolean.TRUE);
        return true;
    }

    /**
     * 下发礼品，过期时间截止到 timeLimit TODO
     */
    public boolean sendGift(Long appId, String productKey, Long uid, Long num, String scene, Long timeLimit) {
        try {
            packageProductRemoteService.sendGiftToPackage(appId, uid, num, productKey, UserPackageBizType.GIFT.getCode(), scene,
                    null, timeLimit, null, Boolean.FALSE, Boolean.TRUE);
            log.info("调用 packageProductRemoteService.sendGiftToPackage 成功 productKey:{}, uid:{}, num:{}, scene:{}, timeLimit:{}", productKey, uid, num, scene, timeLimit);
        } catch (Exception e) {
            log.error("调用 packageProductRemoteService.sendGiftToPackage 异常 productKey:{}, uid:{}, num:{}, scene:{}, timeLimit:{}", productKey, uid, num, scene, timeLimit, e);
            return false;
        }
        return true;
    }

    /**
     * 下发VIP特权
     */
    public boolean sendVip(Long uid, Integer days) {
        Result<Boolean> result;
        try {
            result = feignLanlingService.membershipBatchSend(uid.toString(), null, ServicesAppIdEnum.lanling.getAppId(), days);
            if (!result.success()) {
                log.error("调用 feignLanlingService.membershipBatchSend 失败 uid:{}, days:{}", uid, days);
                return false;
            }
            log.info("调用 feignLanlingService.membershipBatchSend 成功 uid:{}, days:{}", uid, days);
        } catch (Exception e) {
            log.error("调用 feignLanlingService.membershipBatchSend 异常 uid:{}, days:{}", uid, days, e);
            return false;
        }
        return true;
    }

    /**
     * 下发装扮
     *
     * @param uid         用户id
     * @param dressUpType 装扮类型
     * @param uniqueKey   唯一key
     * @param rewardDays  有效期
     * @param num         下发数量
     * @return String
     */
    public boolean sendDressUp(Long appId, Long uid, DressUpType dressUpType, String uniqueKey, Integer rewardDays, Integer num) {
        appId = appId == null ? ServicesAppIdEnum.lanling.getAppId() : appId;
        for (int i = 0; i < num; i++) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("appId", appId);
            jsonObject.put("userId", uid);
            jsonObject.put("dressUpType", dressUpType.getCode());
            jsonObject.put("uniqueKey", uniqueKey);
            jsonObject.put("rewardDays", rewardDays);
            mqTransitManager.chooseAndSendMq(appId, TopicTagEnum.TOPIC_LANLING_SEND_DRESS_UP.getTagKey(), jsonObject.toJSONString(), TopicTagEnum.TOPIC_LANLING_SEND_DRESS_UP.getTagKey() + System.currentTimeMillis());
        }
        return true;
    }

    /**
     * 下发券
     * <p>
     * todo: 战术编程，后续接入 coupon 流程
     *
     * @param appId
     * @param unionId
     * @param uid
     * @param fullActivityCode
     * @param item
     * @return
     */
    public boolean sendCoupon(Long appId, String unionId, Long uid, String fullActivityCode, ScenePrizeDO item) {
        if (!Objects.equals(SQFD_EXPERIENCE_COUPON.getCode(), item.getPrizeValue())) {
            return false;
        }
        packageProductRemoteService.sendGiftToPackage(appId, unionId, uid, null, item.getPrizeNum().longValue(), SQFD_EXPERIENCE_COUPON.getCode(), UserPackageBizType.COIN_COUPON.getCode(),
                UserPackageScene.SQFD.getCode(), null, -1L, null, Boolean.FALSE, Boolean.TRUE, fullActivityCode);
        return true;
    }

    @Deprecated
    public boolean sendDressUp(Long appId, Long uid, DressUpType dressUpType, String uniqueKey, Integer rewardDays) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("appId", appId);
        jsonObject.put("userId", uid);
        jsonObject.put("dressUpType", dressUpType.getCode());
        jsonObject.put("uniqueKey", uniqueKey);
        jsonObject.put("rewardDays", rewardDays);
        mqTransitManager.chooseAndSendMq(appId, TopicTagEnum.TOPIC_LANLING_SEND_DRESS_UP.getTagKey(), jsonObject.toJSONString(), TopicTagEnum.TOPIC_LANLING_SEND_DRESS_UP.getTagKey() + System.currentTimeMillis());
        return true;
    }

    @Deprecated
    public boolean sendCoupon(Long appId, Long uid, Long num, UserPackageBizType bizType, String bizId, String memo,
                              Long timeLimit, UserPackageScene scene, CommonStatus status) {
        packageProductRemoteService.sendGiftToPackage(appId, null, uid, null, num, bizId, bizType.getCode(),
                scene.getCode(), null, timeLimit, null, Boolean.FALSE, Boolean.TRUE, memo);
        return true;
    }

}
