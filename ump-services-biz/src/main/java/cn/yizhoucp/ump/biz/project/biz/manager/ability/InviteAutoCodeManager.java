package cn.yizhoucp.ump.biz.project.biz.manager.ability;

import cn.yizhoucp.ms.core.base.enums.ServicesNameEnum;
import cn.yizhoucp.ms.core.base.util.FeiShuUtil;
import cn.yizhoucp.ms.core.base.util.LocationUtil;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.base.util.UAUtil;
import cn.yizhoucp.ms.core.vo.IP2RegionVO;
import cn.yizhoucp.starter.cassandra.base.kafka.YzKafkaProducerManager;
import cn.yizhoucp.ump.api.param.activity.ReportDownloadParam;
import cn.yizhoucp.ump.biz.project.dal.mp.dao.InviteDownloadRecordDAO;
import cn.yizhoucp.ump.biz.project.dal.mp.dataobject.InviteDownloadRecordDO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Service
@Slf4j
public class InviteAutoCodeManager {

    @Resource
    private InviteDownloadRecordDAO inviteDownloadRecordDAO;

    @Resource
    private YzKafkaProducerManager yzKafkaProducerManager;

    public Boolean reportDownload(ReportDownloadParam param, String uaStr, String ip) {
        if (StringUtils.isEmpty(ip) || StringUtils.isEmpty(param.getInviteCode()) || StringUtils.isEmpty(param.getAppName())) {
            return Boolean.FALSE;
        }
        val builder = InviteDownloadRecordDO.builder()
                .appName(param.getAppName())
                .inviteCode(param.getInviteCode())
                .ip(ip)
                .innerIp(param.getInnerIp())
                .downloadTime(System.currentTimeMillis()/1000);
        if (!StringUtils.isEmpty(ip)) {
            builder.region(getRegionByIp(ip));
        }
        if (!StringUtils.isEmpty(uaStr)) {
            builder.mobileModel(Optional.ofNullable(UAUtil.getMobileModel(uaStr)).orElse("unknown"));
        }
        InviteDownloadRecordDO recordDO = builder.build();
        inviteDownloadRecordDAO.save(recordDO);
        try {
            Long inviteCode = Long.valueOf(param.getInviteCode());
            yzKafkaProducerManager.dataRangerTrack(MDCUtil.getCurAppIdByMdc(), inviteCode,
                    "invite_h5_apk_download", new HashMap<>(), ServicesNameEnum.ump_services.getCode());
        } catch (NumberFormatException e) {
            log.warn("邀请码无法转换为Long类型，跳过埋点上报 {}",param);
        }

        return Boolean.TRUE;
    }

    private String getRegionByIp(String ip) {
        IP2RegionVO ip2RegionVO = LocationUtil.IP2region(ip);
        if (ip2RegionVO == null) {
            return null;
        }
        return ip2RegionVO.getCity();
    }

    public String matchInviteCode(ReportDownloadParam param, String uaStr, String ip) {
        if (StringUtils.isEmpty(ip)) {
            return null;
        }
        String mobileModel = UAUtil.getMobileModel(uaStr);
        // 根据ip匹配
        List<InviteDownloadRecordDO> matchList = inviteDownloadRecordDAO.listSameIp(ip, param.getAppName());
        if (!CollectionUtils.isEmpty(matchList)) {
            if (matchList.size() == 1) {
                return matchList.get(0).getInviteCode();
            }
            if (!StringUtils.isEmpty(param.getInnerIp())) {
                val innnerMatchList = filterInnerIp(matchList, param.getInnerIp());
                if (!CollectionUtils.isEmpty(innnerMatchList)) {
                    return innnerMatchList.get(0).getInviteCode();
                }
            }
            if (!StringUtils.isEmpty(mobileModel)) {
                val modelMatchList = filterMobileModel(matchList, mobileModel);
                if (!CollectionUtils.isEmpty(modelMatchList)) {
                    return modelMatchList.get(0).getInviteCode();
                }
            }
        }

        String region = getRegionByIp(ip);
        if (region == null && mobileModel == null) {
            return null;
        }
        // 根据地区及手机型号匹配
        List<InviteDownloadRecordDO> matchRegionList =
                inviteDownloadRecordDAO.listSameRegionAndModel(region, mobileModel, param.getAppName())
                        .stream()
                        .filter(distantCode(InviteDownloadRecordDO::getInviteCode)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(matchRegionList)) {
            return null;
        }
        sendNotice(String.format("根据手地区及手机型号匹配 -> %s \n-> %s", mobileModel, JSON.toJSONString(matchRegionList)));
        if (matchRegionList.size() == 1 || (region != null && mobileModel != null)) {
            return matchRegionList.get(0).getInviteCode();
        }
        return null;
    }

    private List<InviteDownloadRecordDO> filterMobileModel(List<InviteDownloadRecordDO> matchList, String mobileModel) {
        return matchList.stream().filter(x -> mobileModel.equals(x.getMobileModel()))
                .filter(distantCode(InviteDownloadRecordDO::getInviteCode))
                .collect(Collectors.toList());
    }

    private static <T> Predicate<T> distantCode(Function<? super T, ?> keyExtractor) {
        Set<Object> seen = ConcurrentHashMap.newKeySet();
        return t -> seen.add(keyExtractor.apply(t));
    }

    private List<InviteDownloadRecordDO> filterInnerIp(List<InviteDownloadRecordDO> matchList, String innerIp) {
        return matchList.stream().filter(x -> innerIp.equals(x.getInnerIp()))
                .filter(distantCode(InviteDownloadRecordDO::getInviteCode))
                .collect(Collectors.toList());
    }

    private void sendNotice(String content) {
        FeiShuUtil.sendHookText(content + "\n" +
                "traceId -> "+ MDC.get("traceId"),
                "52d8ce5a-1a63-4585-be36-11b89848d9f3");
    }
}
