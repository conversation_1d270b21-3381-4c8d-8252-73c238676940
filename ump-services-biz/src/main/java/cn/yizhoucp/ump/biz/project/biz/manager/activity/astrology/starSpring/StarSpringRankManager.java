package cn.yizhoucp.ump.biz.project.biz.manager.activity.astrology.starSpring;

import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.AbstractRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSpringConstant.*;

@Service
@Slf4j
public class StarSpringRankManager extends AbstractRankManager {

    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private StarSpringTrackManager starSpringTrackManager;

    @Override
    protected void doPreProcess(RankContext rankContext) {
        rankContext.setRankLen(10L);
    }

    @Override
    protected void postProcess(RankContext rankContext) {
    }

    public Boolean sendStarSpringSumPrize() {
        List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(1L, ACTIVITY_CODE, "sum_rank");
        log.info("sendStarSpringSumPrize {}", JSON.toJSONString(scenePrizeDOList));
        if (CollectionUtils.isEmpty(scenePrizeDOList)) {
            return Boolean.FALSE;
        }

        RankVO rankVO = this.getRank(RankContext.builder()
                .activityCode(ACTIVITY_CODE)
                .rankKey(OVERALL_RANK)
                .type(RankContext.RankType.user)
                .build());
        log.info("rankVO {}", rankVO);
        if (rankVO == null) {
            return Boolean.FALSE;
        }
        log.info("sendStarSpringSumPrize rankVO {}", rankVO);
        List<RankItem> rankList = rankVO.getRankList();
        if (CollectionUtils.isEmpty(rankList)) {
            return Boolean.FALSE;
        }

        for (RankItem rankItem : rankList) {
            Long rank = rankItem.getRank();
            List<ScenePrizeDO> scenePrizeDOs = scenePrizeDOList.stream().filter(scenePrizeDO -> Objects.equals(rank, scenePrizeDO.getPrizeBelongToRank())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(scenePrizeDOs)) {
                log.info("uid {} rank {} 没有奖励", rankItem.getId(), rank);
                continue;
            }
            log.info("sendStarSpringSumPrize#sendPrize rankItem {} scenePrize {}", rankItem, scenePrizeDOs);
            sendPrizeManager.sendPrize(
                    BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(rankItem.getId()).build(),
                    scenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, rankItem.getId())).collect(Collectors.toList())
            );
            starSpringTrackManager.allActivityReceiveAward("on_list", null, scenePrizeDOList.get(0).getPrizeValue(), scenePrizeDOList.get(0).getPrizeValueGold(), scenePrizeDOList.get(0).getPrizeNum(), rankItem.getId());
        }

        return Boolean.TRUE;
    }

    public Boolean sendStarSpringDayPrize() {
        if (!Boolean.TRUE.equals(redisManager.setnx(String.format("ump:star_spring:send_prize_idempotent:%s", DateUtil.getNowYyyyMMdd()), System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND))) {
            return Boolean.TRUE;
        }

        List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(1L, ACTIVITY_CODE, "day_rank");
        log.info("scenePrizeDOList {}", JSON.toJSONString(scenePrizeDOList));
        if (CollectionUtils.isEmpty(scenePrizeDOList)) {
            return Boolean.FALSE;
        }

        RankVO rankVO = this.getRank(RankContext.builder()
                .activityCode(ACTIVITY_CODE)
                .rankKey(String.format(DAY_RANK, DateUtil.getYesterdayYyyyMMdd()))
                .type(RankContext.RankType.user)
                .build());
        log.info("rankVO {}", rankVO);
        if (rankVO == null) {
            return Boolean.FALSE;
        }
        log.info("sendStarSpringDayPrize rankVO {}", rankVO);

        List<RankItem> rankList = rankVO.getRankList();
        if (CollectionUtils.isEmpty(rankList)) {
            return Boolean.FALSE;
        }

        for (RankItem rankItem : rankList) {
            Long rank = rankItem.getRank();
            List<ScenePrizeDO> scenePrizeDOs = scenePrizeDOList.stream().filter(scenePrizeDO -> Objects.equals(rank, scenePrizeDO.getPrizeBelongToRank())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(scenePrizeDOs) || rankItem.getValue() < 300) {
                log.info("uid {} rank {} 没有奖励", rankItem.getId(), rank);
                continue;
            }
            log.info("sendStarSpringDayPrize#sendPrize rankItem {} scenePrize {}", rankItem, scenePrizeDOs);
            sendPrizeManager.sendPrize(
                    BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(rankItem.getId()).build(),
                    scenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, rankItem.getId())).collect(Collectors.toList())
            );
            starSpringTrackManager.allActivityReceiveAward("on_list", null, scenePrizeDOList.get(0).getPrizeValue(), scenePrizeDOList.get(0).getPrizeValueGold(), scenePrizeDOList.get(0).getPrizeNum(), rankItem.getId());
        }

        return Boolean.TRUE;
    }

}
