package cn.yizhoucp.ump.biz.project.biz.manager.activity.legendary_star_park;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.activity.eternalLove.DrawLogItemVO;
import cn.yizhoucp.ump.api.vo.activity.eternalLove.DrawLogTimeItemVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.DrawLogNewVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.inner.DrawLogItem;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.log.LogComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.AbstractDrawTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawContext;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawLogParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.legendary_star_park.internal.LegendaryStarParkConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.legendary_star_park.internal.LegendaryStarParkEnums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.legendary_star_park.internal.LegendaryStarParkRedisManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawLogDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import cn.yizhoucp.user.manager.UserFeignManager;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class LegendaryStarParkDrawManager extends AbstractDrawTemplate {

    @Resource
    private LegendaryStarParkRedisManager legendaryStarParkRedisManager;
    @Resource
    private LogComponent logComponent;
    @Resource
    private RedisManager redisManager;
    @Resource
    private UserFeignManager userFeignManager;
    @Resource
    private LegendaryStarParkBizManager legendaryStarParkBizManager;
    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    private LegendaryStarParkIndexManager legendaryStarParkIndexManager;
    @Resource
    private LegendaryStarParkTrackManager legendaryStarParkTrackManager;
    @Resource
    private NotifyComponent notifyComponent;


    @Override
    protected void resourceCheck(DrawContext context) {
        Long uid = context.getDrawParam().getUid();
        String poolCode = context.getDrawParam().getPoolCode();
        Integer drawTimes = context.getDrawParam().getTimes();
        int drawItems = legendaryStarParkRedisManager.getDrawItems(uid).intValue();
        LegendaryStarParkEnums.LegendaryStarParkPoolType poolType = LegendaryStarParkEnums.LegendaryStarParkPoolType.getPoolTypeByPoolCode(poolCode);
        if (poolType == null) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "抽奖池不存在~");
        }
        Integer itemsConsumed = poolType.getItemsConsumed();
        if (drawItems < drawTimes * itemsConsumed) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "星乐卷不足~");
        }
        String otherIdStr = context.getDrawParam().getExtValue();
        if (CharSequenceUtil.isBlank(otherIdStr)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "请更新版本后再试");
        }
        Long otherId = Long.parseLong(otherIdStr);
        Boolean isGloryMoment = legendaryStarParkRedisManager.isGloryMoment(otherId, poolCode);
        if (Boolean.TRUE.equals(isGloryMoment)) {
            context.getDrawParam().setPoolCode(poolType.getGloryMomentPool());
        }
    }


    @Override
    protected void deductResource(DrawContext context) {
        Long uid = context.getDrawParam().getUid();
        String poolCode = context.getDrawParam().getPoolCode();
        Integer drawTimes = context.getDrawParam().getTimes();
        LegendaryStarParkEnums.LegendaryStarParkPoolType poolType = LegendaryStarParkEnums.LegendaryStarParkPoolType.getPoolTypeByPoolCode(poolCode);
        LegendaryStarParkEnums.LegendaryStarParkPoolType gloryMomentPoolType = LegendaryStarParkEnums.LegendaryStarParkPoolType.getPoolTypeByGloryMomentPoolOrPoolCode(poolCode);
        Integer itemConsumed = 0;
        if (poolType == null) {
            if (gloryMomentPoolType != null) {
                itemConsumed = gloryMomentPoolType.getItemsConsumed();
            }
        } else {
            itemConsumed = poolType.getItemsConsumed();
        }
        legendaryStarParkRedisManager.decrementDrawItems(uid, drawTimes * itemConsumed);
    }

    @Override
    protected void recordDrawLog(DrawContext context, DrawParam drawParam){
    }

    @Override
    protected void doCallback(DrawContext context) {
        LegendaryStarParkEnums.LegendaryStarParkPoolType poolType = LegendaryStarParkEnums.LegendaryStarParkPoolType.getPoolTypeByGloryMomentPoolOrPoolCode(context.getDrawParam().getPoolCode());
        if (poolType == null) {
            return;
        }
        context.getDrawParam().setPoolCode(poolType.getPoolCode());
        DrawParam drawParam = context.getDrawParam();
        String poolCode = drawParam.getPoolCode();
        List<DrawPoolItemDTO> prizeItemList = replacePoolCode(poolCode, context.getPrizeItemList());
        //抽奖记录
        logComponent.putDrawLog(drawParam.getBaseParam(), drawParam.getActivityCode(), prizeItemList);
        //中奖广播
        recordBroadcast(context);
        //推送舰长
        promoteToCaptain(context);

        for (DrawPoolItemDTO item : prizeItemList) {
            //抽奖收集
            legendaryStarParkBizManager.collectedTask(context.getDrawParam().getBaseParam(), drawParam.getPoolCode(), item.getDrawPoolItemDO().getItemKey(), item.getTargetTimes());
            //排行榜
            legendaryStarParkBizManager.calculateRankValues(drawParam.getBaseParam(), drawParam.getPoolCode(), item.getTargetTimes().longValue());
            //统计用户抽奖数
            Long drawTimes = legendaryStarParkRedisManager.incrementDrawTimes(drawParam.getUid(), drawParam.getPoolCode(), item.getTargetTimes().longValue());
            trackUserDrawCount(drawParam.getUid(), drawTimes, poolCode);
            Boolean isGloryMoment = legendaryStarParkRedisManager.isGloryMoment(Long.parseLong(drawParam.getExtValue()), drawParam.getPoolCode());
            if (Boolean.FALSE.equals(isGloryMoment)) {
                //统计房间抽奖数
                legendaryStarParkBizManager.isGloryMomentEnabled(drawParam.getUid(), drawParam.getPoolCode(), Long.valueOf(drawParam.getExtValue()), item.getTargetTimes().longValue());
            }
            //排行榜奖次数
            legendaryStarParkRedisManager.incrementDrawPoolGiftItems(drawParam.getUid(), drawParam.getPoolCode(), item.getDrawPoolItemDO().getItemKey(), item.getTargetTimes().longValue());
            //发送小助手消息
            sendDrawMsg(drawParam.getUid(), item, item.getDrawPoolItemDO().getPoolCode());
            //埋点
            LegendaryStarParkEnums.LegendaryStarParkLottryTrackEnum parkLottryTrackEnum = LegendaryStarParkEnums.LegendaryStarParkLottryTrackEnum.getTrackByPoolCode(poolCode);
            if (parkLottryTrackEnum != null) {
                legendaryStarParkTrackManager.allActivityLottery(drawParam.getUid(), parkLottryTrackEnum.getTrackCode(), item.getTargetTimes().toString(), item.getDrawPoolItemDO().getItemKey(), item.getDrawPoolItemDO().getItemValueGold() * item.getTargetTimes());
            }
        }
    }

    private void trackUserDrawCount(Long uid, Long drawTimes, String poolCode) {
        if (drawTimes >= LegendaryStarParkConstant.MAX_DRAW_TIMES) {
            Boolean isReported = legendaryStarParkRedisManager.isReported(uid, poolCode);
            if (Boolean.TRUE.equals(isReported)) {
                LegendaryStarParkEnums.LegendaryStarParkDrawTaskTrackEnum poolType = LegendaryStarParkEnums.LegendaryStarParkDrawTaskTrackEnum.getTrackByPoolCode(poolCode);
                if (poolType == null) {
                    return;
                }
                legendaryStarParkTrackManager.allActivityTaskFinish(uid, poolType.getTrackCode());
            }
        }
    }

    private void sendDrawMsg(Long uid, DrawPoolItemDTO item, String poolCode) {
        LegendaryStarParkEnums.LegendaryStarParkPoolType poolType = LegendaryStarParkEnums.LegendaryStarParkPoolType.getPoolTypeByPoolCode(poolCode);
        notifyComponent.npcNotify(
                ServicesAppIdEnum.lanling.getUnionId(),
                uid,
                String.format("恭喜星乐园管理员在「传说中的星乐园」活动中，抽取%s%s礼物%s个，奖励已经下发至背包，快去查看吧～", poolType.getPoolName(), item.getDrawPoolItemDO().getItemName(), item.getTargetTimes()));
    }

    private List<DrawPoolItemDTO> replacePoolCode(String poolCode, List<DrawPoolItemDTO> prizeItemList) {
        LegendaryStarParkEnums.LegendaryStarParkPoolType poolType = LegendaryStarParkEnums.LegendaryStarParkPoolType.getPoolTypeByGloryMomentPoolOrPoolCode(poolCode);
        if (poolType == null) {
            return Collections.emptyList();
        }
        if (CollUtil.isEmpty(prizeItemList)) {
            return Collections.emptyList();
        }
        for (DrawPoolItemDTO item : prizeItemList) {
            item.getDrawPoolItemDO().setPoolCode(poolType.getPoolCode());
        }
        return prizeItemList;
    }

    private void promoteToCaptain(DrawContext context) {
        List<DrawPoolItemDTO> prizeItemList = context.getPrizeItemList();
        for (DrawPoolItemDTO item : prizeItemList) {
            LegendaryStarParkEnums.LegendaryStarParkPoolType poolType = LegendaryStarParkEnums.LegendaryStarParkPoolType.getPoolTypeByGloryMomentPoolOrPoolCode(item.getDrawPoolItemDO().getPoolCode());
            if (poolType == null) {
                continue;
            }
            if (poolType.getTargetItemKey().equals(item.getDrawPoolItemDO().getItemKey())) {
                setCaptain(context);
            }
        }
    }

    private void setCaptain(DrawContext context) {
        Long uid = context.getDrawParam().getUid();
        LegendaryStarParkEnums.LegendaryStarParkPoolType poolType = LegendaryStarParkEnums.LegendaryStarParkPoolType.getPoolTypeByGloryMomentPoolOrPoolCode(context.getDrawParam().getPoolCode());
        if (poolType == null) {
            return;
        }
        JSONObject captainInfo = legendaryStarParkRedisManager.getCaptainInfo(Long.valueOf(context.getDrawParam().getExtValue()), poolType.getPoolCode());
        Long captainUid = captainInfo.getLong("captainId");
        String tenure = captainInfo.getString("tenure");
        if (ObjectUtil.equals(uid, captainUid)) {
            return;
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("captainId", uid);
        jsonObject.put("tenure", System.currentTimeMillis());
        Long roomId = Long.valueOf(context.getDrawParam().getExtValue());
        //记录历史舰长
        Object historyInfo = legendaryStarParkRedisManager.popCaptainInfo(poolType.getPoolCode(), roomId);
        if (historyInfo != null) {
            JSONObject histroyJsonObject = JSON.parseObject(historyInfo.toString());
            histroyJsonObject.put("rewardCoins", legendaryStarParkIndexManager.calGoldReward(tenure, poolType.getPoolCode()));
            histroyJsonObject.put("endTime", System.currentTimeMillis());
            legendaryStarParkRedisManager.setHistoryCaptainInfo(poolType.getPoolCode(), roomId, histroyJsonObject);
        }
        legendaryStarParkRedisManager.setCaptainInfo(poolType.getPoolCode(), roomId, jsonObject);
        sendCoin(captainUid, legendaryStarParkIndexManager.calGoldReward(tenure, poolType.getPoolCode()));
        //埋点
        legendaryStarParkTrackManager.allActivityTaskFinish(uid, LegendaryStarParkTrackManager.CAPTAIN_TRACK_CODE);
    }

    public void sendCoin(Long uid, Long rewardNum) {
        List<ScenePrizeDO> scenePrizeDOS = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), LegendaryStarParkConstant.ACTIVITY_CODE, LegendaryStarParkConstant.COIN_SCENE_KEY);
        for (ScenePrizeDO scenePrizeDO : scenePrizeDOS) {
            scenePrizeDO.setPrizeValue(rewardNum.toString());
            //埋点
            legendaryStarParkTrackManager.allActivityReceiveAward(LegendaryStarParkConstant.STAR_PARADISE_CAPTAIN, scenePrizeDO.getPrizeValue(), scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeNum(), uid);
        }
        sendPrizeManager.sendPrize(
                BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(uid).build(),
                scenePrizeDOS.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, uid)).collect(Collectors.toList())
        );
    }

    private void recordBroadcast(DrawContext context) {
        String poolCode = context.getDrawParam().getPoolCode();
        String brodCastRedisKey = String.format(LegendaryStarParkConstant.BROD_CAST_KEY, LegendaryStarParkConstant.ACTIVITY_CODE, poolCode);
        brodCastRedisKey = legendaryStarParkRedisManager.getWeeklyKey(brodCastRedisKey);
        List<DrawPoolItemDTO> drawPoolItemDTO = context.getPrizeItemList();
        Long uid = context.getDrawParam().getUid();
        UserVO userVO = userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), uid);
        DrawPoolItemDTO mostValuableItem = drawPoolItemDTO.stream().max(Comparator.comparing(item -> item.getDrawPoolItemDO().getItemValueGold())).orElse(null);
        if (mostValuableItem == null) {
            return;
        }
        if (ObjectUtil.isNotNull(userVO)) {
            HashMap<String, String> broadCast = new HashMap<>();
            broadCast.put("userName", userVO.getName());
            broadCast.put("ItemName", mostValuableItem.getDrawPoolItemDO().getItemName());
            broadCast.put("ItemValue", String.valueOf(mostValuableItem.getDrawPoolItemDO().getItemValueGold()));
            redisManager.listLpush(brodCastRedisKey, JSON.toJSONString(broadCast), DateUtil.ONE_MONTH_SECOND);
        }
        redisManager.listTrim(brodCastRedisKey, 0, 20);
    }

    @Override
    public DrawLogNewVO drawLogLatest(DrawLogParam param) {
        LocalDateTime now = LocalDateTime.now();
/*
        LocalDateTime now = LocalDateTime.of(2024, 12, 23, 0, 0, 0);
*/
        LocalDateTime startTime = now.with(DayOfWeek.MONDAY).withHour(0).withMinute(0).withSecond(0);
        LocalDateTime endTime = now.with(DayOfWeek.SUNDAY).withHour(23).withMinute(59).withSecond(59);
        List<DrawLogDO> drawLogDOList = drawLogJpaDAO.findByActivityCodeAndPoolCodeAndUidAndTime(param.getActivityCode(), param.getPoolCode(), param.getUid(), startTime, endTime);
        List<DrawLogItem> drawLogWrapperList = drawLogWrapper(DrawLogParam.builder().appId(param.getAppId()).unionId(param.getUnionId()).uid(param.getUid()).build(), drawLogDOList);

        DrawLogNewVO drawLogVO = new DrawLogNewVO();
        Map<String, List<DrawLogItemVO>> timeDrawLogItemMap = new HashMap<>();
        for (DrawLogItem drawLogItem : drawLogWrapperList) {
            if (timeDrawLogItemMap.containsKey(drawLogItem.getTime())) {
                timeDrawLogItemMap.get(drawLogItem.getTime()).add(DrawLogItemVO.builder().itemNum(drawLogItem.getItemNum()).text(drawLogItem.getText()).build());
            } else {
                List<DrawLogItemVO> drawLogItemList = new ArrayList<>();
                drawLogItemList.add(DrawLogItemVO.builder().itemNum(drawLogItem.getItemNum()).text(drawLogItem.getText()).build());
                timeDrawLogItemMap.put(drawLogItem.getTime(), drawLogItemList);
            }
        }
        List<DrawLogTimeItemVO> drawLogTimeItemList = new ArrayList<>();
        timeDrawLogItemMap.forEach((k, v) -> drawLogTimeItemList.add(DrawLogTimeItemVO.builder().time(k).drawLogItemList(v).build()));
        drawLogTimeItemList.sort((o1, o2) -> o2.getTime().compareTo(o1.getTime()));
        drawLogVO.setDrawLogTimeItemList(drawLogTimeItemList);

        return drawLogVO;

    }
}
