package cn.yizhoucp.ump.biz.project.web.rest.controller.activity;


import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.auth.AuthConstant;
import cn.yizhoucp.ms.core.base.auth.Authorize;
import cn.yizhoucp.ms.core.base.enums.coin.TreasurePoolEnum;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.enums.ActivityCheckListEnum;
import cn.yizhoucp.ump.api.enums.ActivityTemplateEnum;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityTimeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Optional;

import static cn.yizhoucp.ms.core.base.enums.coin.TreasurePoolEnum.CJXYLH_GIFT;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.TwinsConstant.BOX_LIMIT_TODAY;


/**
 * 活动送礼验证
 * <p>
 * todo: 战术编程，接入 >= 3场活动后拓展
 *
 * @author: lianghu
 */
@Slf4j
@RestController
public class ActivitySendGiftCheckController {

    @Resource
    private RedisManager redisManager;

    /**
     * 送礼验证
     *
     * @param activityCode
     * @param param
     * @return
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/inner/activity/send-gift-check")
    public Result<Boolean> activityIsEnable(String activityCode, BaseParam param, String ext) {
        Boolean result = Boolean.TRUE;
        // 守护星座活动送礼验证
        if (ActivityCheckListEnum.GUARD_CONSTELLATION.getCode().equals(activityCode) && StringUtils.isNotBlank(ext)) {
            JSONObject bizParam = JSON.parseObject(ext);
            JSONObject getGiftInfoVO = Optional.ofNullable(bizParam.getJSONObject("giftInfoVO")).orElse(new JSONObject());
            log.debug("getGiftInfoVO {}", JSON.toJSONString(getGiftInfoVO));
            TreasurePoolEnum giftEnum = TreasurePoolEnum.of(getGiftInfoVO.getString("giftKey"));
            Integer count = Optional.ofNullable(bizParam.getInteger("productCount")).orElse(0);
            if (CJXYLH_GIFT.equals(giftEnum) || TreasurePoolEnum.GJXYLH_GIFT.equals(giftEnum) || TreasurePoolEnum.HHXYLH_GIFT.equals(giftEnum)) {
                // 当日次数限制
                Long after = Optional.ofNullable(redisManager.getLong(String.format(BOX_LIMIT_TODAY, param.getUid(), giftEnum.name(), ActivityTimeUtil.getToday(null)))).orElse(0L);
                if (!check(giftEnum, after + count)) {
                    throw new ServiceException(ErrorCode.MISS_PARAM, "当日送出礼盒次数达到上线");
                }
            }
        }
        return Result.successResult(result);
    }

    private Boolean check(TreasurePoolEnum giftEnum, Long after) {
        //取消守护星座送礼盒限制
/*
        switch (giftEnum) {
            case CJXYLH_GIFT:
                return after >= 5000 ? Boolean.FALSE : Boolean.TRUE;
            case GJXYLH_GIFT:
                return after >= 1000 ? Boolean.FALSE : Boolean.TRUE;
            case HHXYLH_GIFT:
                return after >= 150 ? Boolean.FALSE : Boolean.TRUE;
            default:
                return Boolean.FALSE;
        }
*/
        return Boolean.TRUE;
    }
}
