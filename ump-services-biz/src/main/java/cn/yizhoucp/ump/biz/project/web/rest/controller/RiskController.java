package cn.yizhoucp.ump.biz.project.web.rest.controller;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.RiskManager;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@RestController
public class RiskController{
    @Resource
    private RiskManager riskManager;



    @RequestMapping("/callback/ump/risk")
    public Result<Boolean> riskCallback(HttpServletRequest request) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            return riskManager.riskCallback(request);
        });
    }

}
