package cn.yizhoucp.ump.biz.project.common.checker.component;

import cn.yizhoucp.ms.core.base.enums.ABTestTypeEnum;
import cn.yizhoucp.track.client.ABTestService;
import cn.yizhoucp.ump.biz.project.common.checker.BaseChecker;
import cn.yizhoucp.ump.biz.project.common.checker.CheckerContext;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * dataranger 分组验证
 * <p>
 * 相关参数 uid
 *
 * <AUTHOR>
 * @Date 2022/8/9 15:09
 * @Version 1.0
 */
@Component("abTestChecker")
@Slf4j
public class AbTestChe<PERSON> extends BaseChecker {

    @Resource
    ABTestService abTestService;

    /** 默认要求实验组 */
    private static final String DEFAULT_USER_GROUP_EXPERIMENT_LIMIT = ABTestTypeEnum.experiment_group_a.getType();

    @Override
    public Boolean doCheck(CheckerContext context, JSONObject param) {
        if (Objects.isNull(context.getAbTestKey())) {
            return Boolean.TRUE;
        }
        if (Objects.isNull(param.getLong("uid"))) {
            return Boolean.FALSE;
        }
        String userGroupLimit = context.getAbTestKey();
        String experimentLimit = Optional.ofNullable(context.getAbTestExperiment()).orElse(DEFAULT_USER_GROUP_EXPERIMENT_LIMIT);
        Long uid = param.getLong("uid");
        Long appId = param.getLong("appId");

        // 获取用户实验组名称
        String experiment = abTestService.generalGetAbtestResult(appId, uid, userGroupLimit, Boolean.TRUE).successData();
        log.info("uid {} testKey {} group {} appId {}", uid, userGroupLimit, experiment, appId);
        return StringUtils.equals(experimentLimit, experiment);
    }

}
