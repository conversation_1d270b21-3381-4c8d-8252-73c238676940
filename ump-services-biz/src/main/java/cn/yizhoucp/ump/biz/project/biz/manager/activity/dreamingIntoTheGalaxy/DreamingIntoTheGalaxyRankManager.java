package cn.yizhoucp.ump.biz.project.biz.manager.activity.dreamingIntoTheGalaxy;

import cn.hutool.core.collection.CollectionUtil;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.vo.activity.dreamingIntoTheGalaxy.DreamingIntoTheGalaxyHomePageVO;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.qixiActivity.QiXiFestivalConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.AbstractRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DreamingIntoTheGalaxyRankManager extends AbstractRankManager {

    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;

    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private NotifyComponent notifyComponent;


    @Override
    protected void postProcess(RankContext rankContext) {

    }

    @Override
    protected void doPreProcess(RankContext rankContext) {
        Long rankLen = rankContext.getRankLen();
        if (rankLen == null || rankLen <= 0) {
            rankContext.setRankLen(10L);
        }
    }

    public DreamingIntoTheGalaxyHomePageVO.Rank getRankByDate(Long uid, String date) {
        RankContext rankContext = RankContext.builder()
                .param(BaseParam.builder().uid(uid).build())
                .activityCode(DreamingIntoTheGalaxyConstant.ACTIVITY_CODE)
                .rankKey(DreamingIntoTheGalaxyConstant.createRankingKey(date))
                .type(RankContext.RankType.user)
                .rankLen(10L)
                .build();
        RankVO rankVO = getRank(rankContext);
        DreamingIntoTheGalaxyHomePageVO.Rank rank = new DreamingIntoTheGalaxyHomePageVO.Rank();
        rank.setMyRank(rankVO.getMyselfRank());
        rank.setRankList(rankVO.getRankList());
        return rank;
    }

    public Boolean sendRankPrize(String date) {
        if (!Boolean.TRUE.equals(redisManager.setnx(String.format("ump:dreaming_into_the_galaxy:send_prize_idempotent:%s", DateUtil.getNowYyyyMMdd()), System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND))) {
            return Boolean.TRUE;
        }
        //时间校验
        if (Objects.isNull(date)) {
            LocalDateTime yesterday = LocalDateTime.now().minusDays(1);
            date = yesterday.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        }
        List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(1L, DreamingIntoTheGalaxyConstant.ACTIVITY_CODE, "rank");
        log.info("scenePrizeDOList {}", JSON.toJSONString(scenePrizeDOList));
        if (CollectionUtil.isEmpty(scenePrizeDOList)) {
            return Boolean.FALSE;
        }
        String rankKey = DreamingIntoTheGalaxyConstant.createRankingKey(date);
        RankContext rankContext = RankContext.builder()
                .activityCode(DreamingIntoTheGalaxyConstant.ACTIVITY_CODE)
                .rankKey(rankKey)
                .type(RankContext.RankType.user)
                .rankLen(10L)
                .build();
        RankVO rankVO = getRank(rankContext);
        for (RankItem rankItem : rankVO.getRankList()) {
            List<ScenePrizeDO> scenePrizeDOs = scenePrizeDOList.stream().filter(scenePrizeDO -> Objects.equals(rankItem.getRank(), scenePrizeDO.getPrizeBelongToRank())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(scenePrizeDOs)) {
                continue;
            }
            if (rankItem.getValue() < 200) {
                log.info("DreamingINtoTheGalaxyRankManager#sendRankPrize 单日绮梦值不满足200 rankItem {}", JSON.toJSONString(rankItem));
                continue;
            }
            sendPrizeManager.sendPrize(
                    BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(rankItem.getId()).build(),
                    scenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, rankItem.getId())).collect(Collectors.toList())
            );
            notifyComponent.npcNotify(
                    ServicesAppIdEnum.lanling.getUnionId(),
                    rankItem.getId(),
                    String.format("恭喜您在”星河入梦“活动中，成功上榜“绮梦榜”第%s名，榜单奖励：价值%s金币的%s，已放至您的背包，有效期15天，请尽快使用", rankItem.getRank(),scenePrizeDOs.get(0).getPrizeValueGold(),scenePrizeDOs.get(0).getPrizeDesc())
            );
        }
        return Boolean.TRUE;
    }

}
