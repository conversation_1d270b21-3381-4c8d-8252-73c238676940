package cn.yizhoucp.ump.biz.project.biz.manager.jimu.context;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2023/3/10 12:06
 * @Version 1.0
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class RankContext {

    private BaseParam param;
    private String activityCode;
    private String rankKey;
    private RankVO rankVO;
    /** 需要的榜单长度 */
    private Long rankLen;
    /** 榜单偏移量 */
    @Builder.Default
    private Long offset = 0L;
    private RankType type;
    private Boolean supportDiff = Boolean.FALSE;
    /** 内部直接获取榜单才会填充 */
    private String rankStrategy;
    private Long otherUid;
    /** 榜单累计值是否支持小数 */
    private Boolean rankIsSupportDecimal;
    /** 标记是否为内部调用榜单信息 比如job */
    @Builder.Default
    private Boolean innerInvoker = Boolean.FALSE;
    private Object ext;
    /** 临时修复bug 方案 */
    private Boolean ext2;

    @Getter
    @AllArgsConstructor
    public enum RankType {

        user("user", "用户榜单"),
        family("family", "家族榜单"),
        room("room", "房间榜单"),
        cp("cp", "cp 榜单类型"),
        ;

        private String type;
        private String desc;

        public static RankType convert(String str) {
            if (StringUtils.isBlank(str)) {
                throw new ServiceException(ErrorCode.INVALID_PARAM);
            }
            for (RankType value : values()) {
                if (StringUtils.equalsIgnoreCase(str, value.type)) {
                    return value;
                }
            }
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }

    }


}
