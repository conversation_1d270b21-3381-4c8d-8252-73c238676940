package cn.yizhoucp.ump.biz.project.biz.manager.activity.curettage;

import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import static cn.yizhoucp.ump.biz.project.biz.manager.activity.curettage.CurettageConstant.*;

@Service
public class CurettageDateHelpManager {
    public LocalDateTime getNow() {
        return LocalDateTime.now();
    }

    public Integer getTodayIndex() {
        LocalDateTime now = LocalDateTime.now();
        int hour = now.getHour();
        if (hour < 2) {
            now = now.plusDays(-1);
        }
        String dayFormat = now.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        return CHANGE_RULE_DATE_STEP.get(dayFormat);
    }

    public int getHour() {
        int hour = DateUtil.getHourOfDay();
        if (hour < 2) {
            hour += 24;
        }
        return hour;
    }

    public boolean isLastDay() {
        return isLastDay(DateUtil.getNowYyyyMMdd());
    }

    public boolean isLastDay(String yesterdayStr) {
        return lastDay.equals(yesterdayStr);
    }
}
