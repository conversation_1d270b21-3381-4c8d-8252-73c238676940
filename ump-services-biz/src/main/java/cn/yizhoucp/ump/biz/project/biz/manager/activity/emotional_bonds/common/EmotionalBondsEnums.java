package cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.common;

import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.strategy.*;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.seasonalFestival.strategy.SeasonalFestivalDemoStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.StrategyEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;


/**
 * 天生羁绊枚举类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 18:18 2025/4/22
 */
public class EmotionalBondsEnums {


    @AllArgsConstructor
    @Getter
    public enum RoleIconEnum{
        dahuaao("dahuaao", "花棉袄"),
        jundayi("jundayi", "军大衣"),
        dalao("dalao","大佬"),
        xia<PERSON>ban("xiaogenban","小跟班"),
        ma<PERSON>("maomi","猫咪"),
        si<PERSON><PERSON>("siyangyuan","饲养员"),
        guniannian("guniangniang","姑奶奶"),
        xiaokelian("xiaokelian","小可怜"),
        bazong("bazong","霸总"),
        baiyueguang("baiyueguang","白月光")
        ;
        private final String icon;
        private final String role;

        public static String getIconByRole(String userRole) {
            for(RoleIconEnum roleIconEnum : RoleIconEnum.values()){
                if(roleIconEnum.getRole().equals(userRole)){
                    return roleIconEnum.getIcon();
                }
            }
            return null;
        }
    }
    @AllArgsConstructor
    @Getter
    public enum ButtonEnum implements StrategyEnum {
        DEMO_BUTTON("demo_button", "seasonalFestivalDemoStrategy", SeasonalFestivalDemoStrategy.class),
        BUY("buy", "emotionalBondsBuyStrategy", EmotionalBondsBuyStrategy.class),
        BOND("bond", "emotionalBondBondStrategy", EmotionalBondBondStrategy.class),
        GET_FRIENDS("get_friends", "emotionalBondGetFriends", EmotionalBondGetFriends.class),
        MY_BOND("my_bond_list", "emotionalBondMyBondList", EmotionalBondMyBondList.class),
        BOND_LIST("bond_list", "emotionalBondsBondList", EmotionalBondsBondList.class),
        CUSTOM("costume", "emotionalBondCostume", EmotionalBondCostume.class),
        AGREED("agreed", "emotionalBondsAgreed", EmotionalBondsAgreed.class)
        ;
        private final String strategyName;
        private final String beanName;
        private final Class<? extends ExecutableStrategy> strategyClass;
    }

    /**
     * 角色配对枚举
     */
    @AllArgsConstructor
    @Getter
    public enum RolePairEnum {

        // 定义缔结关系的角色对
        huamianaohejundayi_entry_special_effect("花棉袄", "军大衣",30L,"花棉袄和军大衣",1000L,"successfully_buy_1"),
        dalaohexiaogenban_entry_special_effect("大佬", "小跟班",30L,"大佬和小跟班",2000L,"successfully_buy_2"),
        maomihesiyangyuan_entry_special_effect("猫咪", "饲养员",30L,"猫咪和饲养员",3000L,"successfully_buy_3"),
        gunainaihexiaokelian_entry_special_effect("姑奶奶", "小可怜",30L,"姑奶奶和小可怜",4000L,"successfully_buy_4"),
        bazonghebaiyueguang_entry_special_effect("霸总", "白月光",30L,"霸总和白月光",5000L,"successfully_buy_5");

        private final String roleA;
        private final String roleB;
        //有效时间
        private final Long expireTime;
        private final String effectName;
        private final Long rankValue;
        private final String trackKey;

        // 用于快速查找对应角色
        private static final Map<String, String> ROLE_PAIR_MAP = new HashMap<>();

        static {
            for (RolePairEnum pair : RolePairEnum.values()) {
                ROLE_PAIR_MAP.put(pair.roleA, pair.roleB);
                ROLE_PAIR_MAP.put(pair.roleB, pair.roleA);
            }
        }

        // 根据给定角色返回对应的另一个角色
        public static String getPairedRole(String role) {
            return ROLE_PAIR_MAP.get(role);
        }

        public static RolePairEnum getRolePairEnum(String role) {
            for(RolePairEnum rolePairEnum : RolePairEnum.values()){
                if(rolePairEnum.roleA.equals(role) || rolePairEnum.roleB.equals(role)){
                    return rolePairEnum;
                }
            }
            return null;
        }

        public static RolePairEnum getRolePairEnumByName(String name) {
            for(RolePairEnum rolePairEnum : RolePairEnum.values()){
                if(rolePairEnum.name().equals(name)){
                    return rolePairEnum;
                }
            }
            return RolePairEnum.huamianaohejundayi_entry_special_effect;
        }
    }

    /**
     * 状态枚举
     */
    @AllArgsConstructor
    @Getter
    public enum StatusEnum {
        /**
         * 未激活
         */
        INACTIVE(0, "未激活"),
        /**
         * 已激活
         */
        ACTIVE(1, "已激活");

        private final Integer code;
        private final String desc;
    }

}
