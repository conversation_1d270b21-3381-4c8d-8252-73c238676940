package cn.yizhoucp.ump.biz.project.dal.mp.dataobject;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@TableName("random_game_hidden")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RandomGameHiddenDO {
    @TableId(value = "uid")
    private Long uid;

    /** 上次活跃日期, 当天 - 功能上线日期的天数 */
    private Integer lastActiveDay;

    /** 进入游戏天数 */
    private Integer enterGameDays;

    /** 是否隐藏概率性玩法 {yes or no} */
    private String hidden;

    private Date createTime;

    private Date updateTime;
}
