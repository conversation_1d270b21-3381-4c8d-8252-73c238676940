package cn.yizhoucp.ump.biz.project.dto.redPacket;


import cn.yizhoucp.ump.api.vo.redPacket.OpenRedPacketResult;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.RedPacketInfoDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OpenCheckDTO {

    /** 展示 VO */
    private OpenRedPacketResult result;
    /** 持久化红包信息 */
    private RedPacketInfoDO redPacketInfoDO;
    /** 剩余红包数量（扣除本次后） */
    private Long leftTimes;

}
