package cn.yizhoucp.ump.biz.project.dal.mp.dao.navigation;

import cn.yizhoucp.ump.biz.project.dal.mp.dataobject.navigation.ExchangeGiftRecordDO;
import cn.yizhoucp.ump.biz.project.dal.mp.mapper.navigation.ExchangeGiftRecordMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ExchangeGiftRecordDAO extends ServiceImpl<ExchangeGiftRecordMapper, ExchangeGiftRecordDO> {

    public List<ExchangeGiftRecordDO> listByUidLessThanLastIdByIdDescLimit(Long uid, Long lastId, int pageSize) {
        return this.lambdaQuery()
                .eq(ExchangeGiftRecordDO::getUid, uid)
                .lt(ExchangeGiftRecordDO::getId, lastId)
                .orderByDesc(ExchangeGiftRecordDO::getId)
                .last("limit " + pageSize)
                .list();
    }
}
