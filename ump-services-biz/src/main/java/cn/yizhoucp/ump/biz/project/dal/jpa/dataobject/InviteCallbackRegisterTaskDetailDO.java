package cn.yizhoucp.ump.biz.project.dal.jpa.dataobject;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 邀请大户邀请注册明细
 *
 * @author: dongming
 */
@Table(name = "invite_callback_register_task_detail")
@Entity
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InviteCallbackRegisterTaskDetailDO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    /** 任务记录id */
    private Long taskId;
    /** 用户id */
    private Long uid;
    /** 被邀请用户id */
    private Long invitedUid;
    /** 创建时间 */
    @CreatedDate
    private LocalDateTime createTime;
    /** 更新时间 */
    @LastModifiedDate
    private LocalDateTime updateTime;

}
