package cn.yizhoucp.ump.biz.project.common.checker.component.bizChecker;

import cn.yizhoucp.ump.biz.project.common.checker.CheckerChainEnum;
import cn.yizhoucp.ump.biz.project.common.checker.BaseChecker;
import cn.yizhoucp.ump.biz.project.common.checker.CheckerContext;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 业务验证
 * <p>
 * 相关参数 uid、activityCode
 *
 * @author: lianghu
 */
@Slf4j
@Component("bizChe<PERSON>")
public class BizChe<PERSON> extends BaseChecker {

    @Resource
    private List<CallBackChecker> callBackCheckerList;

    private static final Map<String, JoinActivityChecker> joinActivityCheckerMap = new ConcurrentHashMap<>();
    private static final Map<String, GetAdSpaceChecker> getAdSpaceCheckerMap = new ConcurrentHashMap<>();


    @PostConstruct
    public void init() {
        for (CallBackChecker checker : callBackCheckerList) {
            if (checker instanceof JoinActivityChecker) {
                joinActivityCheckerMap.put(checker.getActivityCode(), (JoinActivityChecker) checker);
            }
            if (checker instanceof GetAdSpaceChecker) {
                getAdSpaceCheckerMap.put(checker.getActivityCode(), (GetAdSpaceChecker) checker);
            }
        }
    }

    @Override
    protected Boolean doCheck(CheckerContext context, JSONObject param) {
        String activityCode = param.getString("activityCode");
        if (StringUtils.isBlank(activityCode)) {
            log.debug("参数错误 activityCode:{}", activityCode);
            return Boolean.FALSE;
        }

        try {
            log.debug("BizChecker docheck:{} type {} context {}", activityCode,context.getChainEnum(), JSON.toJSONString(context));
            if (CheckerChainEnum.JOIN_USER_ACTIVITY.equals(context.getChainEnum()) && Boolean.TRUE.equals(context.getJoinActivityBizCheck())) {
                return doJoinActivityCheck(context, param, activityCode);
            }
            if (CheckerChainEnum.GET_AD_SPACE_LIST.equals(context.getChainEnum()) && Boolean.TRUE.equals(context.getQueryAdSpaceBizCheck())) {
                return doGetAdSpaceCheck(context, param, activityCode);
            }
        } catch (Exception e) {
            log.error("bizChecker 处理失败 activityCode:{}", activityCode, e);
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private Boolean doJoinActivityCheck(CheckerContext context, JSONObject param, String activityCode) {
        return joinActivityCheckerMap.get(activityCode).getJoinActivityChecker().apply(context, param);
    }

    private Boolean doGetAdSpaceCheck(CheckerContext context, JSONObject param, String activityCode) {
        log.debug("BizChecker doGetAdSpaceCheck:{} param {}", activityCode,param);
        return getAdSpaceCheckerMap.get(activityCode).getQueryAdSpaceChecker().apply(context, param);
    }
}
