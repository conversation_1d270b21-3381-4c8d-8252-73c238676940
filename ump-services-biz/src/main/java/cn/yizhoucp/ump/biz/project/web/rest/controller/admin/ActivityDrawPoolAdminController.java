package cn.yizhoucp.ump.biz.project.web.rest.controller.admin;


import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.vo.landingservices.AdminPageVO;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw.DrawPooExtractType;
import cn.yizhoucp.ump.biz.project.biz.util.PageUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.DrawPoolItemJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.DrawPoolJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.web.vo.DrawPoolAdminVO;
import cn.yizhoucp.ump.biz.project.web.vo.DrawPoolItemAdminVO;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 活动奖池管理
 *
 * @author: lianghu
 */
@RequestMapping("/api/admin/ump/draw-pool")
@RestController
public class ActivityDrawPoolAdminController {

    @Resource
    private DrawPoolJpaDAO drawPoolJpaDAO;

    @GetMapping("/query")
    public Result<AdminPageVO<DrawPoolItemAdminVO>> query(DrawPoolAdminVO param, Integer page, Integer perPage) {
        // 查询奖品明细
        Page<DrawPoolDO> pageResult = drawPoolJpaDAO.findAll(getPageDetailInfoSql(param), PageUtil.defaultPage(page - 1, perPage, "updateTime"));
        if (CollectionUtils.isEmpty(pageResult.getContent())) {
            return Result.successResult(AdminPageVO.getDefault(page, perPage, pageResult));
        }
        List<DrawPoolAdminVO> result = pageResult.getContent().stream().map(DrawPoolDO::convert2AdminVO).collect(Collectors.toList());
        // 补充活动信息
        return Result.successResult(AdminPageVO.getDefault(page, perPage, pageResult, result));
    }

    @PostMapping("/update")
    public Result<Boolean> update(@RequestBody DrawPoolAdminVO param) {
        DrawPoolDO drawPoolDO;
        if (Objects.nonNull(param.getId())) {
            // 更新
            drawPoolDO = drawPoolJpaDAO.findById(param.getId()).get();
            drawPoolDO.setPoolName(param.getPoolName());
            drawPoolDO.setPoolCode(param.getPoolCode());
            drawPoolDO.setActivityCode(param.getActivityCode());
            drawPoolDO.setPoolType(DrawPooExtractType.valueOf(param.getPoolType()));
        } else {
            // 新增
            drawPoolDO = DrawPoolDO.builder()
                    .unionId(ServicesAppIdEnum.lanling.getUnionId())
                    .activityCode(param.getActivityCode())
                    .poolCode(param.getPoolCode())
                    .bizKey(param.getPoolCode())
                    .poolType(DrawPooExtractType.valueOf(param.getPoolType()))
                    .poolName(param.getPoolName())
                    .extData("{\"useDoubleProb\":true}")
                    .status(1)
                    .build();
        }
        drawPoolJpaDAO.save(drawPoolDO);
        return Result.successResult(Boolean.TRUE);
    }

    /**
     * 获取 select 选项
     *
     * @param
     * @return java.util.List<com.alibaba.fastjson.JSONObject>
     */
    @GetMapping("/get-select-item")
    public Result<List<JSONObject>> getSelectItemList(String activityCode) {
        List<JSONObject> result = Lists.newArrayList();
        if (StringUtils.isBlank(activityCode)) {
            return Result.successResult(result);
        }
        Iterable<DrawPoolDO> activityDOS = drawPoolJpaDAO.findAll((r, q, c) -> {
            List<javax.persistence.criteria.Predicate> predicates = new ArrayList<>();
            if (StringUtils.isNotBlank(activityCode)) {
                predicates.add(c.equal(r.get("activityCode"), activityCode));
            }
            q.orderBy(c.desc(r.get("updateTime"))).getRestriction();
            return c.and(predicates.toArray(new Predicate[0]));
        });
        activityDOS.forEach(item -> {
            JSONObject obj = new JSONObject();
            obj.put("text", item.getPoolName());
            obj.put("value", item.getPoolCode());
            result.add(obj);
        });
        return Result.successResult(result);
    }

    private Specification<DrawPoolDO> getPageDetailInfoSql(DrawPoolAdminVO param) {
        return (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (StringUtils.isNotBlank(param.getActivityCode())) {
                predicates.add(criteriaBuilder.equal(root.get("activityCode"), param.getActivityCode()));
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

}
