package cn.yizhoucp.ump.biz.project.web.rest.controller;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.enums.DeployEnvEnum;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ump.api.vo.OpenRoomCoinBoxVO;
import cn.yizhoucp.ump.api.vo.RoomCoinBoxInfoVO;
import cn.yizhoucp.ump.biz.project.biz.manager.RoomCoinBoxManager;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RestController
public class RoomCoinBoxController {
    @Resource
    private RoomCoinBoxManager roomCoinBoxManager;

    @GetMapping("/api/inner/ump/room-coin-box/box-info")
    public Result<RoomCoinBoxInfoVO> roomCoinBoxInfo(@RequestParam("uid") Long uid, @RequestParam("roomId") Long roomId) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> roomCoinBoxManager.boxInfo(uid, roomId));
    }

    @GetMapping("/api/inner/ump/room-coin-box/open-box")
    public Result<OpenRoomCoinBoxVO> openRoomCoinBox(@RequestParam("uid") Long uid, @RequestParam("boxId") Long boxId) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> roomCoinBoxManager.openCoinBox(uid, boxId));
    }

    @GetMapping("/api/inner/ump/give-gift-draw-coin-box")
    public Result<Boolean> giveGiftDrawCoinBox(@RequestParam("uid") Long uid, @RequestParam("roomId") Long roomId, @RequestParam("feeCoin") Integer feeCoin) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> roomCoinBoxManager.giveGiftDrawRoomBox(uid, roomId, feeCoin));
    }

    @GetMapping("/api/ump/room-coin-box/test-create-box")
    public Result<Boolean> testCreateBox(Long uid, Long roomId, String type) {
        if (!DeployEnvEnum.dev() && !DeployEnvEnum.test()) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "no permission");
        }
        return RestBusinessTemplate.executeWithoutTransaction(() -> roomCoinBoxManager.testNewRoomCoinBox(uid, roomId, type));
    }

    /**
     * 根据语音房 id 获取状态是 OPEN 和 INIT 的宝箱
     * @param roomIds 房间 id list
     * @return List
     */
    @GetMapping("/api/inner/ump/room-coin-box/get-box-info-by-ids")
    public Result<Map<Long,RoomCoinBoxInfoVO>> getBoxInfoByIds(@RequestParam("roomIds") List<Long> roomIds) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> roomCoinBoxManager.getBoxInfoByIds(roomIds));
    }
}
