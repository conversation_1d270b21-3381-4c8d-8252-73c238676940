package cn.yizhoucp.ump.biz.project.biz.mq;

import cn.yizhoucp.ms.core.base.TopicConstant;
import cn.yizhoucp.starter.cassandra.base.rocketmq.RocketmqAbstractConsumer;
import cn.yizhoucp.ump.biz.project.biz.manager.goddessTrain.GoddessTrainActivityManager;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Set;

import static cn.yizhoucp.ms.core.base.TopicTagEnum.TOPIC_GODDESS_TRAIN_GRADUATION;

/**
 * 语音房事件消费
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = "goddess_train_topic", consumerGroup = "UMP_GODDESS_TRAIN_GROUP")
public class GoddessTrainConsumer extends RocketmqAbstractConsumer {

    @Resource
    GoddessTrainActivityManager goddessTrainActivityManager;

    private static final Set<String> LISTEN_TAG_SET = Sets.newHashSet(TOPIC_GODDESS_TRAIN_GRADUATION.getTagKey());

    @Override
    protected Boolean consume(String unionId, Long userId, String tag, String param) {
        if (TOPIC_GODDESS_TRAIN_GRADUATION.getTagKey().equals(tag)) {
            return userGraduation(JSONObject.parseObject(param));
        }
        return Boolean.FALSE;
    }

    private Boolean userGraduation(JSONObject bizParam) {
        Long uid = bizParam.getLong("uid");
        Long point = bizParam.getLong("trainPoint");
        goddessTrainActivityManager.userGraduation(uid, point);
        return Boolean.TRUE;
    }

    @Override
    protected String getTopic() {
        return TopicConstant.TOPIC_GODDESS_TRAIN.getTopicKey();
    }

    @Override
    protected Set<String> getTags() {
        return LISTEN_TAG_SET;
    }
}
