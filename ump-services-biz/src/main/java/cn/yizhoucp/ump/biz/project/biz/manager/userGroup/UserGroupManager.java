package cn.yizhoucp.ump.biz.project.biz.manager.userGroup;


import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.biz.project.biz.constant.UserGroupConstant;
import cn.yizhoucp.ump.biz.project.biz.remoteService.FeignAppService;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityTimeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.constant.UserGroupConstant.*;


/**
 * 用户分群
 *
 * @author: lianghu
 */
@Slf4j
@Service
public class UserGroupManager {

    @Resource
    private RedisManager redisManager;
    @Resource
    private FeignAppService feignAppService;

    /**
     * 增加每日更新用户分群
     *
     * @param cohortId
     * @return
     */
    public Boolean addToRefreshUserGroup(Long cohortId) {
        // 0是为了新后台全部用户的兼容处理
        if (Objects.isNull(cohortId) || cohortId.equals(0L)) {
            return Boolean.FALSE;
        }
        Set<Long> userGroupSet;
        String cache = (String) redisManager.get(UserGroupConstant.TO_REFRESH_USER_GROUP_SET_CONFIG);
        if (StringUtils.isNotBlank(cache)) {
            userGroupSet = JSON.parseObject(cache, new TypeReference<Set<Long>>() {
            });
        } else {
            userGroupSet = new HashSet<>();
        }
        if (!userGroupSet.contains(cohortId)) {
            userGroupSet.add(cohortId);
            redisManager.set(UserGroupConstant.TO_REFRESH_USER_GROUP_SET_CONFIG, JSON.toJSONString(userGroupSet), RedisManager.ONE_DAY_SECONDS * 60);
            log.info("用户分群集合更新 userGroupSet:{}", JSON.toJSONString(userGroupSet));
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }


    /**
     * 更新应用分组用户清单
     *
     * @param appId
     * @return
     */
    public Boolean refreshUserGroupCache(Long appId) {
        Set<Long> toRefreshSet = getToRefreshSet();
        for (Long cohortId : toRefreshSet) {
            List<Long> userList = feignAppService.getUserGroupUserIdList(appId, cohortId, DEFAULT_USER_GROUP_SIZE).successData();
            log.info("用户分群列表 cohortId:{}, list:{}", cohortId, JSON.toJSONString(userList));
            if (!CollectionUtils.isEmpty(userList)) {
                Set<Long> userSet = userList.stream().collect(Collectors.toSet());
                redisManager.set(String.format(USER_GROUP_DAILY_CACHE, cohortId, ActivityTimeUtil.getToday(null)), JSON.toJSONString(userSet), RedisManager.ONE_DAY_SECONDS * 3);
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 判断用户是否在对应分群中
     *
     * @param appId
     * @param cohortId
     * @param uid
     * @return
     */
    public Boolean isMember(Long appId, Long cohortId, Long uid) {
        String cache = (String) redisManager.get(String.format(USER_GROUP_DAILY_CACHE, cohortId, ActivityTimeUtil.getToday(null)));
        log.debug("userGroup cache key:{}, value:{}", String.format(USER_GROUP_DAILY_CACHE, cohortId, ActivityTimeUtil.getToday(null)), cache);
        if (StringUtils.isBlank(cache)) {
            return Boolean.FALSE;
        }
        try {
            Set<Long> userSet = JSON.parseObject(cache, new TypeReference<Set<Long>>() {
            });
            if (userSet.contains(uid)) {
                return Boolean.TRUE;
            }
        } catch (Exception e) {
            return Boolean.FALSE;
        }
        return Boolean.FALSE;
    }

    private Set<Long> getToRefreshSet() {
        Set<Long> toRefreshSet;
        String config = (String) redisManager.get(TO_REFRESH_USER_GROUP_SET_CONFIG);
        if (StringUtils.isNotBlank(config)) {
            toRefreshSet = JSON.parseObject(config, new TypeReference<Set<Long>>() {
            });
        } else {
            toRefreshSet = TO_REFRESH_USER_GROUP_SET;
        }
        return toRefreshSet;
    }

}
