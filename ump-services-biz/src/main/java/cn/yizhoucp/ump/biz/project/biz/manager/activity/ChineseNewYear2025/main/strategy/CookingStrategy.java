package cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.strategy;

import cn.hutool.core.collection.CollUtil;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.product.client.UserPackageFeignService;
import cn.yizhoucp.product.dto.BatchUsePackageDetailDTO;
import cn.yizhoucp.product.dto.BatchUsePackageParamDTO;
import cn.yizhoucp.product.dto.BatchUsePackageResultDTO;
import cn.yizhoucp.product.enums.PackageUseScene;
import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.common.MainChineseNewYear2025Constant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.common.MainChineseNewYear2025Enums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.common.MainChineseNewYear2025RedisManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class CookingStrategy implements MainChineseNewYear2025Strategy {

    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    private UserPackageFeignService userPackageFeignService;
    @Resource
    private MainChineseNewYear2025RedisManager mainChineseNewYear2025RedisManager;

    @Override
    public Boolean execute(ButtonEventParam buttonEventParam) {
        String dishKey = buttonEventParam.getBizKey();
        MainChineseNewYear2025Enums.DishsEnum dishsEnum = MainChineseNewYear2025Enums.DishsEnum.getByDishCode(dishKey);
        if (dishsEnum == null) {
            return Boolean.FALSE;
        }
        //扣减食材
        Boolean result = deductPackageItem(buttonEventParam.getBaseParam(), dishKey);
        if (Boolean.FALSE.equals(result)) {
            return Boolean.FALSE;
        }
        //保存制作状态
        startCooking(buttonEventParam.getBaseParam().getUid(), dishsEnum.getDishCode(), dishsEnum.getCookingTime());
        return result;
    }

    private void startCooking(Long uid, String dishKey, Integer cookingTime) {
        Long countDown = System.currentTimeMillis() + cookingTime * 1000L;
        mainChineseNewYear2025RedisManager.setCurrentDish(uid, dishKey);
        mainChineseNewYear2025RedisManager.setCountDown(uid, countDown);
    }

    private Boolean deductPackageItem(BaseParam baseParam, String dishCode) {
        List<ScenePrizeDO> scenePrizeDOS = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), MainChineseNewYear2025Constant.ACTIVITY_CODE, dishCode);
        if (CollUtil.isEmpty(scenePrizeDOS)) {
            log.error("未配置菜品所需食材 dishCode {}", dishCode);
            return Boolean.FALSE;
        }
        BatchUsePackageResultDTO batchUsePackageResultDTO = userPackageFeignService.batchUsePackage(BatchUsePackageParamDTO.builder()
                .appId(baseParam.getAppId())
                .uid(baseParam.getUid())
                .unionId(baseParam.getUnionId())
                .detailList(scenePrizeDOS.stream().map(scenePrizeDO -> BatchUsePackageDetailDTO.builder()
                        .bizId(scenePrizeDO.getPrizeValue())
                        .bizType("gift")
                        .useNum(scenePrizeDO.getPrizeNum())
                        .build()).collect(Collectors.toList())
                )
                .useScene(PackageUseScene.activity)
                .build()).successData();
        if (batchUsePackageResultDTO == null) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "缺少食材");
        }
        return Boolean.TRUE;
    }
}
