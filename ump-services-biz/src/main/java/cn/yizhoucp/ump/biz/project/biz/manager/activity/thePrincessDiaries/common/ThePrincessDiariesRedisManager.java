package cn.yizhoucp.ump.biz.project.biz.manager.activity.thePrincessDiaries.common;

import cn.hutool.core.text.CharSequenceUtil;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.util.AppUtil;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityRedisKeyGenerator;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.user.manager.UserFeignManager;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 公主日记redis管理类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 11:34 2025/3/3
 */
@Component
public class ThePrincessDiariesRedisManager {
    @Resource
    private RedisManager redisManger;
    @Resource
    private ActivityRedisKeyGenerator activityRedisKeyGenerator;
    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    private UserFeignManager userFeignManager;

    private String getActivityCode() {
        return ThePrincessDiariesConstant.ACTIVITY_CODE;
    }

    public List<ScenePrizeDO> getListBySceneCode(String taskSceneKey) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(ThePrincessDiariesConstant.LIST_BY_SCENE_CODE, getActivityCode(), taskSceneKey);
        String cache = redisManger.getString(key);
        if (CharSequenceUtil.isBlank(cache)) {
            return null;
        }
        return JSON.parseArray(cache, ScenePrizeDO.class);
    }

    public void setListBySceneCode(String taskSceneKey, List<ScenePrizeDO> scenePrizeDOList) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(ThePrincessDiariesConstant.LIST_BY_SCENE_CODE, getActivityCode(), taskSceneKey);
        redisManger.set(key, JSON.toJSONString(scenePrizeDOList), DateUtil.ONE_HOUR_SECOND);
    }

    public String getMsgCache(String taskId) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(ThePrincessDiariesConstant.MSG_CACHE, getActivityCode(), taskId);
        return redisManger.getString(key);
    }

    public void setMsgCache(String taskId, String messageTemplate) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(ThePrincessDiariesConstant.MSG_CACHE, getActivityCode(), taskId);
        redisManger.set(key, messageTemplate, DateUtil.ONE_HOUR_SECOND);
    }

    /**
     * 任务是否领取
     *
     * @param uid    用户 id
     * @param taskId 任务 id
     * @return 是否领取
     */
    public Boolean getTaskClaimed(Long uid, String taskId) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(ThePrincessDiariesConstant.TASK_IS_CLAIMED, getActivityCode(), taskId, uid.toString());
        return redisManger.hasKey(key);
    }

    /**
     * 设置任务领取状态
     *
     * @param uid
     * @param taskId
     * @return
     */
    public void setTaskClaimed(Long uid, String taskId) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(ThePrincessDiariesConstant.TASK_IS_CLAIMED, getActivityCode(), taskId, uid.toString());
        redisManger.set(key, System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND);
    }

    public Long getPairValue(Long uid, Long toUid) {
        String bindKey = AppUtil.splicUserId(uid, toUid);
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(ThePrincessDiariesConstant.PAIR_VALUE, getActivityCode(), bindKey);
        return Optional.ofNullable(redisManger.getLong(key)).orElse(0L);
    }

    public void incrementPairValue(Long uid, Long toUid, Long num) {
        String bindKey = AppUtil.splicUserId(uid, toUid);
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(ThePrincessDiariesConstant.PAIR_VALUE, getActivityCode(), bindKey);
        redisManger.incrLong(key, num, DateUtil.ONE_MONTH_SECOND);
    }

    public void decrementPairValue(Long uid, Long toUid, Long num) {
        String bindKey = AppUtil.splicUserId(uid, toUid);
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(ThePrincessDiariesConstant.PAIR_VALUE, getActivityCode(), bindKey);
        redisManger.decrLong(key, num, DateUtil.ONE_MONTH_SECOND);
    }


    public Boolean getLightTaskIsComplete(Long uid, Long toUid, String lightTaskId) {
        String bindKey = AppUtil.splicUserId(uid, toUid);
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(ThePrincessDiariesConstant.LIGHT_TASK_IS_COMPLETE, getActivityCode(), lightTaskId, bindKey);
        return redisManger.hasKey(key);
    }

    public Boolean setLightTaskIsComplete(Long uid, Long toUid, String lightTaskId) {
        String bindKey = AppUtil.splicUserId(uid, toUid);
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(ThePrincessDiariesConstant.LIGHT_TASK_IS_COMPLETE, getActivityCode(), lightTaskId, bindKey);
        return redisManger.set(key, System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND);
    }


    public Boolean getLightRewardClaimed(Long uid, Long toUid) {
        String bindKey = AppUtil.splicUserId(uid, toUid);
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(ThePrincessDiariesConstant.LIGHT_REWARD_CLAIMED, getActivityCode(), bindKey);
        return redisManger.hasKey(key);
    }

    public Boolean setLightRewardClaimed(Long uid, Long toUid) {
        String bindKey = AppUtil.splicUserId(uid, toUid);
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(ThePrincessDiariesConstant.LIGHT_REWARD_CLAIMED, getActivityCode(), bindKey);
        return redisManger.set(key, System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND);
    }

    public Long getDrawItemCount(Long uid) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(ThePrincessDiariesConstant.DRAW_ITEM_NUM, getActivityCode(), uid.toString());
        return Optional.ofNullable(redisManger.getLong(key)).orElse(0L);
    }

    public void incrementDrawItemCount(Long uid, Long num) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(ThePrincessDiariesConstant.DRAW_ITEM_NUM, getActivityCode(), uid.toString());
        redisManger.incrLong(key, num, DateUtil.ONE_MONTH_SECOND);
    }

    public void decrementDrawItemCount(Long uid, Long num) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(ThePrincessDiariesConstant.DRAW_ITEM_NUM, getActivityCode(), uid.toString());
        redisManger.decrLong(key, num, DateUtil.ONE_MONTH_SECOND);
    }


    public String getRankKey(String rankCode) {
        return activityRedisKeyGenerator.generateKeyWithActivityStartTime(ThePrincessDiariesConstant.RANK_KEY, getActivityCode(), rankCode);
    }

    public Integer getCurrentShopItemIndex() {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(ThePrincessDiariesConstant.SHOP_ITEM_INDEX, getActivityCode());
        String indexStr = redisManger.getString(key);
        if (CharSequenceUtil.isBlank(indexStr)) {
            return 0;
        }
        return Integer.parseInt(indexStr);
    }

    public Long getRemainingCount(String prizeValue) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(ThePrincessDiariesConstant.REMAIN_COUNT, getActivityCode(), prizeValue);
        key += ":" + DateUtil.getNowYyyyMMdd();
        return Optional.ofNullable( redisManger.getLong(key)).orElse(0L);
    }

    public Long incrementRemainingCount(String prizeValue, Long num) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(ThePrincessDiariesConstant.REMAIN_COUNT, getActivityCode(), prizeValue);
        key += ":" + DateUtil.getNowYyyyMMdd();
        return redisManger.incrLong(key, num, DateUtil.ONE_MONTH_SECOND);
    }

    public void decrementRemainingCount(String prizeValue, Long num) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(ThePrincessDiariesConstant.REMAIN_COUNT, getActivityCode(), prizeValue);
        key += ":" + DateUtil.getNowYyyyMMdd();
        redisManger.decrLong(key, num, DateUtil.ONE_MONTH_SECOND);
    }


    public ScenePrizeDO getTaskGift(String coinGiftTaskKey) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(ThePrincessDiariesConstant.TASK_CACHE_MAP, getActivityCode(), coinGiftTaskKey);
        String cache = redisManger.getString(key);
        if (CharSequenceUtil.isBlank(cache)) {
            List<String> taskList = Arrays.stream(ThePrincessDiariesEnums.TaskEnum.values()).map(ThePrincessDiariesEnums.TaskEnum::getTaskId).collect(Collectors.toList());
            List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCodeList(1L, getActivityCode(), taskList);
            Map<String, ScenePrizeDO> map = scenePrizeDOList.stream().collect(Collectors.toMap(ScenePrizeDO::getPrizeValue, scenePrizeDO -> scenePrizeDO));
            redisManger.set(key, JSON.toJSONString(map), DateUtil.ONE_HOUR_SECOND);
            return map.get(coinGiftTaskKey);
        }
        Map<String, ScenePrizeDO> map = JSON.parseObject(cache, new TypeReference<Map<String, ScenePrizeDO>>() {
        });
        return map.get(coinGiftTaskKey);
    }

    public Long getTopPairedFriend(Long uid) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(ThePrincessDiariesConstant.BIND_FRIEND_RANK_KEY, getActivityCode(), uid.toString());
        Set<ZSetOperations.TypedTuple<Object>> typedTuples = redisManger.reverseRangeByScoreWithScores(key, 0, Double.MAX_VALUE, 0, 10);
        if (typedTuples == null || typedTuples.isEmpty()) {
            return null;
        }

        for (ZSetOperations.TypedTuple<Object> tuple : typedTuples) {
            Long friendId = Long.valueOf(Objects.requireNonNull(tuple.getValue()).toString());
            if (!isUserBanned(friendId)) {
                return friendId;
            }
        }
        return null;
    }

    public void incrementBindRank(Long uid, Long toUid, Long num) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(ThePrincessDiariesConstant.BIND_FRIEND_RANK_KEY, getActivityCode(), uid.toString());
        redisManger.zIncrby(key, toUid, num.doubleValue(), DateUtil.ONE_MONTH_SECOND);
    }


    private boolean isUserBanned(Long userId) {
        UserVO userVO = userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), userId);
        return userVO == null;
    }

    public List<ScenePrizeDO> getListByListSceneCode(List<String> taskSceneKey) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(ThePrincessDiariesConstant.TASK_SCENE_GIFT, getActivityCode());
        String cache = redisManger.getString(key);
        if (CharSequenceUtil.isBlank(cache)) {
            return null;
        }
        return JSON.parseArray(cache, ScenePrizeDO.class);
    }

    public void setListByListSceneCode(List<ScenePrizeDO> scenePrizeDOList) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(ThePrincessDiariesConstant.TASK_SCENE_GIFT, getActivityCode());
        redisManger.set(key, JSON.toJSONString(scenePrizeDOList), DateUtil.ONE_HOUR_SECOND);
    }


    public Long getActivityEndTime() {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(ThePrincessDiariesConstant.ACTIVITY_END_TIME, getActivityCode());
        return redisManger.getLong(key);
    }

    public void setActivityEndTime(Long endTime) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(ThePrincessDiariesConstant.ACTIVITY_END_TIME, getActivityCode());
        redisManger.set(key, endTime, DateUtil.ONE_HOUR_SECOND);
    }

    public Boolean getIsDepth(Long uid, Long toUid) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(ThePrincessDiariesConstant.IS_DEPTH, getActivityCode(), AppUtil.splicUserId(uid, toUid));
        return redisManger.hasKey(key);
    }

    public void setIsDepth(Long uid, Long toUid) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(ThePrincessDiariesConstant.IS_DEPTH, getActivityCode(), AppUtil.splicUserId(uid, toUid));
        redisManger.setnx(key, System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND);
    }

    public Boolean getBuyLock(Long uid, String prizeValue, Long remainingCount) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(ThePrincessDiariesConstant.BUY_LOCK, getActivityCode(), uid.toString(), prizeValue, remainingCount.toString());
        key=key+":"+DateUtil.getNowYyyyMMdd();
        return redisManger.hasKey(key);
    }
    public Boolean setBuyLock(String prizeValue, Long remainingCount) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(ThePrincessDiariesConstant.BUY_LOCK, getActivityCode(), prizeValue, remainingCount.toString());
        key=key+":"+DateUtil.getNowYyyyMMdd();
        return redisManger.setnx(key, System.currentTimeMillis(), DateUtil.ONE_DAY_SECOND);
    }

    public ScenePrizeDO getShopItem(String shopKey) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(ThePrincessDiariesConstant.SHOP_ITEM_CACHE_MAP, getActivityCode());
        String cache = redisManger.getString(key);
        if (CharSequenceUtil.isBlank(cache)) {
            List<String> taskList = Arrays.stream(ThePrincessDiariesEnums.ShopItemEnum.values()).map(ThePrincessDiariesEnums.ShopItemEnum::getShopKey).collect(Collectors.toList());
            List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCodeList(1L, getActivityCode(), taskList);
            Map<String, ScenePrizeDO> map = scenePrizeDOList.stream().collect(Collectors.toMap(ScenePrizeDO::getSceneCode, scenePrizeDO -> scenePrizeDO));
            redisManger.set(key, JSON.toJSONString(map), DateUtil.ONE_HOUR_SECOND);
            return map.get(shopKey);
        }
        Map<String, ScenePrizeDO> map = JSON.parseObject(cache, new TypeReference<Map<String, ScenePrizeDO>>() {
        });
        return map.get(shopKey);
    }
}
