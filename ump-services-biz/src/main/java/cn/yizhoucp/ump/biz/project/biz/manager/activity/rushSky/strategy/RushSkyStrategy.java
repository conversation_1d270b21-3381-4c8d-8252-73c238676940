package cn.yizhoucp.ump.biz.project.biz.manager.activity.rushSky.strategy;

import cn.yizhoucp.family.api.dto.family.FamilyInfoDTO;
import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.vo.activity.rushSky.inner.TaskItem;
import cn.yizhoucp.ump.biz.project.biz.constant.RushSkyConstant;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.rushSky.FamilyPkLevelInfo;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public interface RushSkyStrategy {

    String getFamilyRankDressUpKey(Integer rank);

    String getFamilyRankGiftKey(Integer rank);

    List<String> getPersonRankDressUpList();

    void completeTaskNotify(String unionId, Long uid, RushSkyConstant.TASK_KEY_ENUM taskKey, FamilyInfoDTO familyInfoDTO, Long relationId, Integer index);

    Long getMinimumMedalNum(FamilyPkLevelInfo item);

    String getMinimumMedalNumDesc(FamilyPkLevelInfo item);

    String getBonusDesc(FamilyPkLevelInfo item);

    Integer getPkHandleRankSize();

    double getProportion();

    String getPoolCode(String giftKey);

    List<TaskItem> getTaskItem(Long uid);

    Boolean activityIsEnable();

    Boolean callAirdrop(BaseParam param, FamilyInfoDTO family, Long medalNum, Double after);

    Result<Boolean> batchSendFamilyDressUp(String familyId, String uniqueKey, Integer rewardDays);

}
