package cn.yizhoucp.ump.biz.project.dal.jpa.dao;


import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ActivityMissionRecordDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface ActivityMissionRecordJpaDAO extends JpaRepository<ActivityMissionRecordDO, Long> {

    @Query(value = "select * from activity_mission_record where user_id = ?1 and mission_id = ?2 and deadline = ?3 limit 1", nativeQuery = true)
    ActivityMissionRecordDO getByUserIdAndMissionIdAndDeadlineLimit1(Long userId, Long missionId, String deadline);

    @Query(value = "select * from activity_mission_record where user_id = ?1 and mission_code = ?2 limit 1", nativeQuery = true)
    ActivityMissionRecordDO getByUserIdAndMissionCodeLimit1(Long userId, String missionCode);

    @Query(value = "select * from activity_mission_record where user_id = ?1 and mission_code = ?2 and deadline = ?3 limit 1", nativeQuery = true)
    ActivityMissionRecordDO getByUserIdAndMissionCodeAndDeadlineLimit1(Long userId, String missionCode, String deadline);

}
