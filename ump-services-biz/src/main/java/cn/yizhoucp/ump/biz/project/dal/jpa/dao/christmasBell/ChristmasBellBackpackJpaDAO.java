package cn.yizhoucp.ump.biz.project.dal.jpa.dao.christmasBell;

import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.christmasBell.ChristmasBellBackpackDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.List;

/**
 * <AUTHOR>
 * @description 圣诞铃铛背包
 * @create 2024-12-13
 **/
@Repository
public interface ChristmasBellBackpackJpaDAO extends JpaRepository<ChristmasBellBackpackDO, Long>, JpaSpecificationExecutor<ChristmasBellBackpackDO>, CrudRepository<ChristmasBellBackpackDO, Long> {

    List<ChristmasBellBackpackDO> findByUid(Long uid);

    @Transactional
    @Modifying
    @Query(value = "update christmas_bell_backpack set bell_count = bell_count + ?2 where id = ?1 limit 1", nativeQuery = true)
    Integer updateBellCountById(Long id, Integer bellCount);

    ChristmasBellBackpackDO findTopByUidAndBellKey(Long uid, String bellKey);

}
