package cn.yizhoucp.ump.biz.project.dal.mp.dao;

import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.AdSpaceResourceDO;
import cn.yizhoucp.ump.biz.project.dal.mp.mapper.AdSpaceResourceMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;


@Repository
@Slf4j
public class AdSpaceResourceDAO extends ServiceImpl<AdSpaceResourceMapper, AdSpaceResourceDO> {

    public Boolean saveOrUpdateBatchTypeAndBelongActivityCodeAndUnionId(List<AdSpaceResourceDO> adSpaceResourceDOList) {
        for (AdSpaceResourceDO adSpaceResourceDO : adSpaceResourceDOList) {
            LambdaQueryWrapper<AdSpaceResourceDO> updateWrapper = new LambdaQueryWrapper<>();
            updateWrapper.eq(AdSpaceResourceDO::getUnionId, adSpaceResourceDO.getUnionId())
                    .eq(AdSpaceResourceDO::getBelongActivityCode, adSpaceResourceDO.getBelongActivityCode())
                    .eq(AdSpaceResourceDO::getType, adSpaceResourceDO.getType());
            log.info("AdSpaceResourceDAO#saveOrUpdateBatchTypeAndBelongActivityCodeAndUnionId adSpaceResourceDO {}", adSpaceResourceDO);
            if (!this.update(adSpaceResourceDO, updateWrapper)) {
                this.save(adSpaceResourceDO);
                log.info("AdSpaceResourceDAO#saveOrUpdateBatchTypeAndBelongActivityCodeAndUnionId save adSpaceResourceDO {}", adSpaceResourceDO);
            }
        }
        return true;
    }

}
