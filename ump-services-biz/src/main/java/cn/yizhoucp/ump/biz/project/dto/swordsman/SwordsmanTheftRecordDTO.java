package cn.yizhoucp.ump.biz.project.dto.swordsman;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @createDate 2024/11/18
 */
@Data
public class SwordsmanTheftRecordDTO {

    private Long id;

    /**
     * 窃取用户 ID
     */
    private Long fromUid;

    /**
     * 被窃取用户 ID
     */
    private Long toUid;

    /**
     * 窃取元宝数量
     */
    private Long theftCount;

    /**
     * 损失元宝数量
     */
    private Long lossCount;

    /**
     * 窃取时间
     */
    private Date createTime;

    /**
     * 窃取时间，格式为
     */
    private String theftTime;

}
