package cn.yizhoucp.ump.biz.project.web.rest.controller.admin;


import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.vo.landingservices.AdminPageVO;
import cn.yizhoucp.ms.core.vo.productServices.CoinGiftProductVO;
import cn.yizhoucp.ms.core.vo.productServices.EntrySpecialEffectProductVO;
import cn.yizhoucp.ms.core.vo.productServices.HeadFrameProductVO;
import cn.yizhoucp.ms.core.vo.productServices.MountProductVO;
import cn.yizhoucp.product.client.EntrySpecialEffectProductFeignService;
import cn.yizhoucp.product.client.HeadFrameProductFeignService;
import cn.yizhoucp.product.client.MountProductFeignService;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw.inner.PrizeSubTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw.inner.PrizeTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.remoteService.product.FeignProductService;
import cn.yizhoucp.ump.biz.project.biz.util.PageUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.DrawPoolItemJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.DrawPoolJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.web.vo.DrawPoolItemAdminVO;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 活动奖池管理
 *
 * @author: lianghu
 */
@Slf4j
@RequestMapping("/api/admin/ump/draw-pool-item")
@RestController
public class ActivityDrawPoolItemAdminController {

    @Resource
    private DrawPoolJpaDAO drawPoolJpaDAO;
    @Resource
    private DrawPoolItemJpaDAO drawPoolItemJpaDAO;
    @Resource
    private FeignProductService feignProductService;
    @Resource
    EntrySpecialEffectProductFeignService eseProductFeignService;
    @Resource
    HeadFrameProductFeignService hdFrameProductFeignService;
    @Resource
    MountProductFeignService mountProductFeignService;

    @GetMapping("/query")
    public Result<AdminPageVO<DrawPoolItemAdminVO>> query(DrawPoolItemAdminVO param, Integer page, Integer perPage) {
        // 查询奖品明细
        Page<DrawPoolItemDO> pageResult = drawPoolItemJpaDAO.findAll(getPageDetailInfoSql(param), PageUtil.defaultPage(page - 1, perPage, "updateTime"));
        if (CollectionUtils.isEmpty(pageResult.getContent())) {
            return Result.successResult(AdminPageVO.getDefault(page, perPage, pageResult));
        }
        // 补充活动信息
        return Result.successResult(AdminPageVO.getDefault(page, perPage, pageResult, fillActivityCode(pageResult.getContent())));
    }

    @PostMapping("/update")
    public Result<Boolean> update(@RequestBody DrawPoolItemAdminVO param) {
        DrawPoolItemDO drawPoolItemDO;
        if (Objects.nonNull(param.getId())) {
            // 更新
            drawPoolItemDO = drawPoolItemJpaDAO.findById(param.getId()).get();
            drawPoolItemDO.setPoolCode(param.getPoolCode());
            drawPoolItemDO.setItemType(param.getItemType());
            drawPoolItemDO.setItemSubType(param.getItemSubType());
            drawPoolItemDO.setItemKey(param.getItemKey());
            drawPoolItemDO.setItemName(param.getItemName());
            drawPoolItemDO.setItemIcon(param.getItemIcon());
            drawPoolItemDO.setItemNum(param.getItemNum());
            drawPoolItemDO.setItemEffectiveDay(param.getItemEffectiveDay());
            drawPoolItemDO.setItemValueGold(param.getItemValueGold());
            drawPoolItemDO.setProb(param.getProb());
        } else {
            // 新增
            drawPoolItemDO = DrawPoolItemDO.builder()
                    .unionId(ServicesAppIdEnum.lanling.getUnionId())
                    .poolCode(param.getPoolCode())
                    .itemName(param.getItemName())
                    .itemIcon(param.getItemIcon())
                    .itemType(param.getItemType())
                    .itemSubType(param.getItemSubType())
                    .itemKey(param.getItemKey())
                    .itemNum(param.getItemNum())
                    .itemEffectiveDay(param.getItemEffectiveDay())
                    .itemValueGold(param.getItemValueGold())
                    .status(1)
                    .build();
            drawPoolItemDO.setProb(param.getProb());
        }

        // 礼物、装扮验证
        if (PrizeTypeEnum.PRIZE_GIFT.getCode().equals(param.getItemType())) {
            CoinGiftProductVO gift = feignProductService.getGiftByGiftKey(ServicesAppIdEnum.lanling.getAppId(), param.getItemKey()).successData();
            if (Objects.isNull(gift)) {
                throw new ServiceException(ErrorCode.MISS_PARAM, "礼物 KEY 不存在");
            }
            drawPoolItemDO.setItemIcon(gift.getIcon());
        } else if (PrizeTypeEnum.PRIZE_DRESS.getCode().equals(param.getItemType())) {
            if (PrizeSubTypeEnum.HEAD_FRAME.getCode().equals(param.getItemSubType())) {
                List<HeadFrameProductVO> headFrameProductVOS = hdFrameProductFeignService.batchGetHfProductByUniqueKeys(Lists.newArrayList(param.getItemKey())).successData();
                if (CollectionUtils.isEmpty(headFrameProductVOS)) {
                    throw new ServiceException(ErrorCode.MISS_PARAM, "装扮 KEY 不存在");
                }
                drawPoolItemDO.setItemIcon(headFrameProductVOS.get(0).getIcon());
            } else if (PrizeSubTypeEnum.ENTRY_SPECIAL_EFFECT.getCode().equals(param.getItemSubType())) {
                List<EntrySpecialEffectProductVO> entrySpecialEffectProductVOS = eseProductFeignService.batchGetEseProductByUniqueKeys(Lists.newArrayList(param.getItemKey())).successData();
                if (CollectionUtils.isEmpty(entrySpecialEffectProductVOS)) {
                    throw new ServiceException(ErrorCode.MISS_PARAM, "装扮 KEY 不存在");
                }
                drawPoolItemDO.setItemIcon(entrySpecialEffectProductVOS.get(0).getIcon());
            } else if (PrizeSubTypeEnum.MOUNT.getCode().equals(param.getItemSubType())) {
                List<MountProductVO> mountProductVOS = mountProductFeignService.batchGetMountProductByUniqueKeys(Lists.newArrayList(param.getItemKey()));
                if (CollectionUtils.isEmpty(mountProductVOS)) {
                    throw new ServiceException(ErrorCode.MISS_PARAM, "装扮 KEY 不存在");
                }
                drawPoolItemDO.setItemIcon(mountProductVOS.get(0).getIcon());
            }
        }
        drawPoolItemJpaDAO.save(drawPoolItemDO);
        return Result.successResult(Boolean.TRUE);
    }

    /**
     * 获取 select 选项
     *
     * @param
     * @return java.util.List<com.alibaba.fastjson.JSONObject>
     */
    @GetMapping("/get-select-item")
    public Result<List<JSONObject>> getSelectItemList(String activityCode) {
        List<JSONObject> result = Lists.newArrayList();
        if (StringUtils.isBlank(activityCode)) {
            return Result.successResult(result);
        }
        Iterable<DrawPoolDO> activityDOS = drawPoolJpaDAO.findAll((r, q, c) -> {
            List<javax.persistence.criteria.Predicate> predicates = new ArrayList<>();
            if (StringUtils.isNotBlank(activityCode)) {
                predicates.add(c.equal(r.get("activityCode"), activityCode));
            }
            q.orderBy(c.desc(r.get("updateTime"))).getRestriction();
            return c.and(predicates.toArray(new Predicate[0]));
        });
        activityDOS.forEach(item -> {
            JSONObject obj = new JSONObject();
            obj.put("text", item.getPoolName());
            obj.put("value", item.getPoolCode());
            result.add(obj);
        });
        return Result.successResult(result);
    }

    private Specification<DrawPoolItemDO> getPageDetailInfoSql(DrawPoolItemAdminVO param) {
        return (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (StringUtils.isNotBlank(param.getPoolCode())) {
                predicates.add(criteriaBuilder.equal(root.get("poolCode"), param.getPoolCode()));
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    private List<DrawPoolItemAdminVO> fillActivityCode(List<DrawPoolItemDO> prizes) {
        // 获取奖池集合
        Set<String> poolCodeSet = Sets.newHashSet();
        prizes.stream().forEach(p -> poolCodeSet.add(p.getPoolCode()));
        // 组装奖池-活动映射
        Map<String, String> poolActivityMap = Maps.newHashMap();
        List<DrawPoolDO> pools = drawPoolJpaDAO.getByCodeList(poolCodeSet);
        if (!CollectionUtils.isEmpty(pools)) {
            pools.stream().forEach(p -> poolActivityMap.put(p.getPoolCode(), p.getActivityCode()));
        }
        // 补充活动信息
        return prizes.stream().map(p -> p.convert2AdminVO(poolActivityMap.get(p.getPoolCode()))).collect(Collectors.toList());
    }

}
