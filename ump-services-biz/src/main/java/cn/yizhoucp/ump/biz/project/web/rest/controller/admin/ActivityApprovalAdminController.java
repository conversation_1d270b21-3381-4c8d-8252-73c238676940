package cn.yizhoucp.ump.biz.project.web.rest.controller.admin;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.auth.AuthConstant;
import cn.yizhoucp.ms.core.base.auth.Authorize;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.userActivity.admin.ActivityApprovalManager;
import cn.yizhoucp.ump.biz.project.web.vo.ActivityApprovalVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
public class ActivityApprovalAdminController {

    @Resource
    private ActivityApprovalManager activityApprovalManager;

    /**
     * 审批活动
     * @return
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @PostMapping("/api/ump/open-api/activity-approval/process")
    public Result<Boolean> process(@RequestBody ActivityApprovalVO activityApprovalVO) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> activityApprovalManager.process(activityApprovalVO));
    }

}
