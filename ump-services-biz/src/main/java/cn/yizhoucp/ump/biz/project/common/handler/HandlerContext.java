package cn.yizhoucp.ump.biz.project.common.handler;

import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ump.api.vo.appAdSpace.AppAdSpaceVO;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.LoginPopDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * @author: lianghu
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HandlerContext {

    /** 链枚举 */
    private HandlerChainEnum chainEnum;
    /** appId */
    private Long appId;
    /** unionId */
    private String unionId;
    /** uid */
    private Long uid;
    /** toUid */
    private Long toUid;
    /** 模板编号 */
    private String templateType;
    /** 活动编号 */
    private String activityCode;
    /** 周期活动编号 */
    private String cycleActivityCode;
    /** 活动类型 */
    private String activityType;
    /** 登录弹窗 code */
    private String loginPopCode;
    /** 加入活动业务处理 */
    private Boolean joinActivityBizHandle;
    /** 退出活动业务处理 */
    private Boolean exitActivityBizHandle;
    /** 查询资源位业务处理 */
    private Boolean queryAdResourceBizHandle;
    /** 推送弹窗业务处理 */
    private Boolean popBizHandle;
    /** 下发礼物信息 */
    private List<SendPrizeDTO> sendPrizeConfig;
    /** 上报埋点 */
    private String reportKey;
    /**
     * 当前资源位
     * {@link cn.yizhoucp.ump.biz.project.common.handler.component.bizHandler.BizHandler}
     * {@link cn.yizhoucp.ump.biz.project.common.handler.component.bizHandler.BizHandler}
     */
    private String resourceCode;
    /** 资源位列表 */
    private List<AppAdSpaceVO> adResourceList;
    /**
     * 弹窗信息
     * {@link cn.yizhoucp.ump.biz.project.common.handler.component.bizHandler.BizHandler}
     */
    private LoginPopDO popDO;
    /**
     * 榜单数据
     */
    private RankVO rankVO;
    /**
     * 榜单key
     */
    private String rankKey;
    /** 榜单累计值是否支持小数 */
    private Boolean rankIsSupportDecimal;

    public static HandlerContext getQueryAdResourceChainContext(List<AppAdSpaceVO> adResourceList) {
        HandlerContext context = HandlerContext.builder()
                .chainEnum(HandlerChainEnum.QUERY_AD_RESOURCE)
                .queryAdResourceBizHandle(Boolean.TRUE)
                .adResourceList(adResourceList).build();
        fillBaseInfo(context);
        return context;
    }

    public static HandlerContext getJoinActivityChainContext(String config) {
        if (StringUtils.isBlank(config)) {
            return null;
        }
        JSONObject param = JSONObject.parseObject(config).getJSONObject("handlerContext");
        if (Objects.isNull(param)) {
            return null;
        }
        HandlerContext context = param.getObject(HandlerChainEnum.JOIN_ACTIVITY.getConfigKey(), HandlerContext.class);
        context.setChainEnum(HandlerChainEnum.JOIN_ACTIVITY);
        fillBaseInfo(context);
        return context;
    }

    public static HandlerContext getExitActivityChainContext(String config) {
        if (StringUtils.isBlank(config)) {
            return null;
        }
        JSONObject param = JSONObject.parseObject(config).getJSONObject("handlerContext");
        if (Objects.isNull(param)) {
            return null;
        }
        HandlerContext context = param.getObject(HandlerChainEnum.EXIT_ACTIVITY.getConfigKey(), HandlerContext.class);
        context.setChainEnum(HandlerChainEnum.EXIT_ACTIVITY);
        fillBaseInfo(context);
        return context;
    }

    public static HandlerContext getPushPopChainContext(String config) {
        if (StringUtils.isBlank(config)) {
            return null;
        }
        JSONObject param = JSONObject.parseObject(config).getJSONObject("handlerContext");
        if (Objects.isNull(param)) {
            return null;
        }
        HandlerContext context = param.getObject(HandlerChainEnum.PUSH_POP.getConfigKey(), HandlerContext.class);
        context.setChainEnum(HandlerChainEnum.PUSH_POP);
        fillBaseInfo(context);
        return context;
    }


    private static void fillBaseInfo(HandlerContext context) {
        if (Objects.isNull(context.getAppId())) {
            context.setAppId(MDCUtil.getCurAppIdByMdc());
        }
        if (Objects.isNull(context.getUnionId())) {
            context.setUnionId(MDCUtil.getCurUnionIdByMdc());
        }
        if (Objects.isNull(context.getUid())) {
            context.setUid(MDCUtil.getCurUserIdByMdc());
        }
    }

}
