package cn.yizhoucp.ump.biz.project.biz.manager.activity.worldAnnounceLoveYou;

import cn.yizhoucp.ms.core.base.env.DynamicEnvManager;
import cn.yizhoucp.ms.core.base.util.FunctionUtil;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityUrlUtil;
import cn.yizhoucp.ump.biz.project.common.handler.HandlerContext;
import cn.yizhoucp.ump.biz.project.common.handler.component.bizHandler.BuoyInfoHandler;
import cn.yizhoucp.ump.biz.project.dto.adSpace.BuoyInfoDTO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.function.Function;

@Service
public class WorldAnnounceLoveYouADManager implements BuoyInfoHandler {
    @Value("${spring.profiles.active}")
    private String env;

    @Resource
    private Environment environment;

    @Override
    public Function<HandlerContext, BuoyInfoDTO> getBuoyInfoHandler() {
        return handlerContext -> {

            String routerUrl = ActivityUrlUtil.getH5BaseUrl(handlerContext.getUnionId(), env, Boolean.TRUE, environment.getProperty(DynamicEnvManager.GLOBAL_ENV_NAME)) + "guanxuan-world-0418" + "?from=chat_buoy&toUid=" + FunctionUtil.orDefault(handlerContext.getToUid(), "");
            return BuoyInfoDTO.builder()
                    .routerUrl(routerUrl)
                    .build();
        };
    }


    @Override
    public String getActivityCode() {
        return WorldAnnounceLoveYouConstant.ACTIVITY_CODE;
    }
}
