package cn.yizhoucp.ump.biz.project.web.rest.controller.activity;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.floweringDream.FloweringDreamRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.palpitatingHeartYou.PalpitatingHeartYouRankManager;
import com.alibaba.fastjson.JSONObject;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 活动
 */
@RestController
public class PalpitatingHeartYouController {

    @Resource
    private PalpitatingHeartYouRankManager palpitatingHeartYouRankManager;

    /**
     * 合成
     */
    @GetMapping("/api/inner/activity/palpitating-heart-you/craft")
    Result<Long> craft(String activityCode) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> palpitatingHeartYouRankManager.craft(activityCode));
    }

    /**
     * 兑换
     */
    @GetMapping("/api/inner/activity/palpitating-heart-you/exchange")
    Result<Boolean> exchange(String activityCode, String rewardsKey) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> palpitatingHeartYouRankManager.exchange(activityCode, rewardsKey));
    }
}
