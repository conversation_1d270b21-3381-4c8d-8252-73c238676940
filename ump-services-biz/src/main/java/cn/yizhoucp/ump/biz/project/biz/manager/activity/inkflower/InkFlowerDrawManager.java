package cn.yizhoucp.ump.biz.project.biz.manager.activity.inkflower;

import cn.hutool.core.util.ObjectUtil;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.env.DynamicEnvManager;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.log.LogComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.AbstractDrawTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawContext;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.doDrawStrategy.common.ProbStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.biz.manager.clientPush.ServerPushManager;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityUrlUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 墨花月白长相思
 *
 * <AUTHOR>
 * @version V1.0
 * @since 10:44 2025/1/16
 */
@Service
@Slf4j
public class InkFlowerDrawManager extends AbstractDrawTemplate {

    @Resource
    private ProbStrategy probStrategy;

    @Resource
    private LogComponent logComponent;

    @Resource
    private InkFlowerConstant inkFlowerConstant;
    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    private InkFlowerTrackManager inkFlowerTrackManager;
    @Resource
    private ServerPushManager serverPushManager;
    @Value("${spring.profiles.active}")
    private String env;
    @Resource
    private Environment environment;


    @Override
    protected void resourceCheck(DrawContext context) {
        String poolCode = context.getDrawParam().getPoolCode();
        InkFlowerConstant.GiftBoxRewardType rewardNumByGiftKey = InkFlowerConstant.GiftBoxRewardType.getRewardNumByGiftKey(poolCode);
        Boolean isGiftBox = InkFlowerConstant.LONG_LING_FOR_HOME_GIFT_BOX_LIST.contains(poolCode);
        if (rewardNumByGiftKey != null || isGiftBox) {
            context.getDrawParam().setNoSendPrize(Boolean.TRUE);
            return;
        }
        //检查雪花烙数量
        Boolean checkInkFlowerNum = checkInkFlowerNum(context);
        if (Boolean.FALSE.equals(checkInkFlowerNum)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "当前长安花不够哦～");
        }
    }

    private Boolean checkInkFlowerNum(DrawContext context) {
        Long uid = context.getDrawParam().getUid();
        Long inkFlowerNum = inkFlowerConstant.getInkFlowerNum(uid);
        if (inkFlowerNum < context.getDrawParam().getTimes() * 10) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    @Override
    protected void draw(DrawContext context) {
        List<DrawPoolItemDTO> drawPoolItemDTOList = probStrategy.getDrawPoolItems(context.getDrawPoolItemDOS(),
                context.getDrawParam().getTimes(),
                Boolean.TRUE);
        context.setPrizeItemList(drawPoolItemDTOList);

    }

    @Override
    protected void deductResource(DrawContext context) {
        //扣减雪花烙
        decrementInkFlower(context);
    }

    private void decrementInkFlower(DrawContext context) {
        String poolCode = context.getDrawParam().getPoolCode();
        if (!InkFlowerConstant.POOL_CODE.equals(poolCode)) {
            return;
        }
        inkFlowerConstant.decrementInkFlowerNum(context.getDrawParam().getUid(), context.getDrawParam().getTimes() * 10L);
    }


    @Override
    protected void doCallback(DrawContext context) {
        //记录日志
        recordDrawLog(context, context.getDrawParam());
        //下发落雪烙
        sendInkFlower(context);
        //增加梅花值
        incrementPlumValue(context);
        //埋点
        String poolCode = context.getDrawPoolDO().getPoolCode();
        if (InkFlowerConstant.POOL_CODE.equals(poolCode)) {
            List<DrawPoolItemDTO> drawPoolItemDTOList = context.getPrizeItemList();
            for (DrawPoolItemDTO drawPoolItemDTO : drawPoolItemDTOList) {
                inkFlowerTrackManager.allActivityLottery(context.getDrawParam().getUid(), poolCode, drawPoolItemDTO.getTargetTimes().toString(), drawPoolItemDTO.getDrawPoolItemDO().getItemKey(), drawPoolItemDTO.getDrawPoolItemDO().getItemValueGold());
            }
        }
    }

    private void incrementPlumValue(DrawContext context) {
        String poolCode = context.getDrawPoolDO().getPoolCode();
        if (!InkFlowerConstant.POOL_CODE.equals(poolCode)) {
            return;
        }
        Long uid = context.getDrawParam().getUid();
        Integer count = context.getDrawParam().getTimes();
        inkFlowerConstant.incrementPlumValue(uid, count * InkFlowerConstant.DEFAULT_PLUM_VALUE_REWARD);
    }

    private void sendInkFlower(DrawContext context) {
        String poolCode = context.getDrawPoolDO().getPoolCode();
        BaseParam baseParam = context.getDrawParam().getBaseParam();
        InkFlowerConstant.GiftBoxRewardType giftBoxRewardType = InkFlowerConstant.GiftBoxRewardType.getRewardNumByGiftKey(poolCode);
        if (giftBoxRewardType == null) {
            return;
        }
        Long incrementNum = giftBoxRewardType.getRewardNum() * context.getDrawParam().getTimes();
        log.info("sendInkFlower uid {} giftBoxRewardType {}", baseParam.getUid(), giftBoxRewardType);
        inkFlowerConstant.incrementInkFlowerNum(baseParam.getUid(), incrementNum);
        //获得雪花烙h5弹窗
        String url = ActivityUrlUtil.getH5BaseUrl(MDCUtil.getCurUnionIdByMdc()
                , env
                , Boolean.TRUE
                , environment.getProperty(DynamicEnvManager.GLOBAL_ENV_NAME)) + "changan-0113-popup" + String.format("?giftKey=snow&giftNum=%s", incrementNum);

        serverPushManager.sendH5ServerPush(baseParam.getUid(), url);
    }

    @Override
    protected void recordDrawLog(DrawContext context, DrawParam drawParam) {
        logComponent.putDrawLog(drawParam.getBaseParam(), drawParam.getActivityCode(), context.getPrizeItemList());
    }

    public Boolean bindFriend(BaseParam baseParam, Long toUid) {
        if (ObjectUtil.isNull(toUid)) {
            return Boolean.FALSE;
        }
        inkFlowerConstant.bindFriend(baseParam.getUid(), toUid);
        return Boolean.TRUE;
    }

}
