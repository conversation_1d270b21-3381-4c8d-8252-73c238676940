package cn.yizhoucp.ump.biz.project.common.handler;

import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Component
public class HandlerChainFactory {

    @Resource(name = "popHandler")
    private Handler popHandler;
    @Resource(name = "bizHandler")
    private Handler bizHandler;
    @Resource(name = "rankBaseHandler")
    private Handler rankBaseHandler;
    @Resource(name = "rankRewordHandler")
    private Handler rankRewordHandler;
    @Resource(name = "refreshAdSpaceHandler")
    private Handler refreshAdSpaceHandler;
    @Resource(name = "sendPrizeHandler")
    private Handler sendPrizeHandler;
    @Resource(name = "reportHandler")
    private Handler reportHandler;

    private Map<HandlerChainEnum, Handler> chainMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        // 查询资源位链
        chainMap.put(HandlerChainEnum.QUERY_AD_RESOURCE, buildChain(HandlerChainEnum.QUERY_AD_RESOURCE, bizHandler));
        // 加入活动链
        chainMap.put(HandlerChainEnum.JOIN_ACTIVITY, buildChain(HandlerChainEnum.JOIN_ACTIVITY, reportHandler, popHandler, sendPrizeHandler, bizHandler, refreshAdSpaceHandler));
        // 退出活动链
        chainMap.put(HandlerChainEnum.EXIT_ACTIVITY, buildChain(HandlerChainEnum.EXIT_ACTIVITY, popHandler, refreshAdSpaceHandler, bizHandler));
        // 推送弹窗链
        chainMap.put(HandlerChainEnum.PUSH_POP, buildChain(HandlerChainEnum.PUSH_POP, bizHandler));
        chainMap.put(HandlerChainEnum.QUERY_RANK, buildChain(HandlerChainEnum.QUERY_RANK, rankBaseHandler, rankRewordHandler));
    }

    public Handler getChainByType(HandlerChainEnum type) {
        return chainMap.get(type);
    }


    /**
     * 构建JoinAfterHandler链
     *
     * @param handlers
     * @return
     */
    private Handler buildChain(HandlerChainEnum chainEnum, Handler... handlers) {
        if (Objects.isNull(handlers)) {
            return null;
        }

        List<Handler> list = Arrays.stream(handlers).collect(Collectors.toList());
        for (int i = 0; i <= list.size() - 2; i++) {
            list.get(i).add(chainEnum, list.get(i + 1));
        }
        return list.get(0);
    }

}
