package cn.yizhoucp.ump.biz.project.biz.manager.activity.milesOfLove;

import cn.yizhoucp.depth.api.client.DepthFeignService;
import cn.yizhoucp.ms.core.base.config.ThreadPoolConfig;
import cn.yizhoucp.ms.core.base.convert.SexType;
import cn.yizhoucp.ms.core.base.enums.GiftFrom;
import cn.yizhoucp.ms.core.base.enums.GiftWay;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.enums.sns.UserRelationType;
import cn.yizhoucp.ms.core.base.util.AppUtil;
import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.milesOfLove.common.MolEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.milesOfLove.common.MolRedisControls;
import cn.yizhoucp.ump.biz.project.biz.remoteService.FeignSnsService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.FeignUserService;
import cn.yizhoucp.ump.biz.project.common.ActivityComponent;
import cn.yizhoucp.ump.biz.project.common.event.GiftGiveEvent;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

import static cn.yizhoucp.ump.biz.project.biz.manager.activity.milesOfLove.common.MolConstant.*;

@Service
@Slf4j
public class MilesOfLoveBizManager implements ActivityComponent {

    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    private MolRedisControls molRedisControls;
    @Resource
    private MilesOfLoveRankManager milesOfLoveRankManager;
    @Resource
    protected FeignUserService feignUserService;
    @Resource
    private FeignSnsService feignSnsService;
    @Resource
    private DepthFeignService depthFeignService;
    @Resource
    private RedisManager redisManager;
    @Autowired
    private MilesOfLoveTrackManager trackManager;

    @Async(ThreadPoolConfig.ASYNC_EXECUTOR_NAME)
    @EventListener(GiftGiveEvent.class)
    @ActivityCheck(activityCode = ACTIVITY_CODE, isThrowException = false)
    public void eventSendGiftHandle(GiftGiveEvent event) {
        log.info("MilesOfLoveBizManager sendGiftHandle event:{}", event);
        sendGiftHandle(event.getBaseParam(), event.getCoinGiftGivedModels());
    }

    public Boolean sendGiftHandle(BaseParam param, List<CoinGiftGivedModel> coinGiftGivedModelList) {
        log.info("MilesOfLoveBizManager sendGiftHandle param:{}, coinGiftGivedModelList:{}", param, coinGiftGivedModelList);
        // 获取送的礼物
        for (CoinGiftGivedModel coinGiftGivedModel : coinGiftGivedModelList) {
            log.info("MilesOfLoveBizManager sendGiftHandle coinGiftGivedModel:{} for fromId:{} --> toUid:{}", coinGiftGivedModel, coinGiftGivedModel.getFromUid(), coinGiftGivedModel.getToUid());
            if (filterBackpackGift(coinGiftGivedModel)) {
                continue;
            }
            log.info("over MilesOfLoveBizManager sendGiftHandle coinGiftGivedModel:{} for fromId:{} --> toUid:{}", coinGiftGivedModel, coinGiftGivedModel.getFromUid(), coinGiftGivedModel.getToUid());

            Long uid = coinGiftGivedModel.getFromUid();
            Long coin = coinGiftGivedModel.getCoin();
            Long toUid = coinGiftGivedModel.getToUid();
            Long productCount = coinGiftGivedModel.getProductCount();
            if (Objects.isNull(productCount) || productCount <= 0) {
                log.warn("sendGiftHandle: productCount is null or <= 0");
                continue;
            }
            if (Objects.isNull(uid) || Objects.isNull(coin) || Objects.isNull(toUid)) {
                log.warn("sendGiftHandle: uid or coin or toUid is null");
                continue;
            }

            // 处理任务模块
            handleTask(coinGiftGivedModel);

            // 处理恋爱纪念册
            handleLoveBook(coinGiftGivedModel);

            // 处理跨年倒计时
            handleTheNewYearS(coinGiftGivedModel);

            // 处理榜单
            handleRank(coinGiftGivedModel);


        }

        return true;
    }

    /**
     * 处理榜单
     */
    private void handleRank(CoinGiftGivedModel coinGiftGivedModel) {
        Long uid = coinGiftGivedModel.getFromUid();
        Long toUid = coinGiftGivedModel.getToUid();
        Long coin = coinGiftGivedModel.getCoin();
        String giftKey = coinGiftGivedModel.getGiftKey();
        // 1金币=10豪气值 || 1金币=10魅力值
        Long value = coin * 10;

        log.info("handleRank uid:{},toUid:{} coin:{}", uid, toUid, coin);

        // 「跨年零时之吻」和「2025」礼物
        if (!NEW_YEAR_S_EVE_KISS.equals(giftKey) && !NEW_YEAR_2025.equals(giftKey)) {
            log.info("handleRank giftKey:{} is not NEW_YEAR_S_EVE_KISS or NEW_YEAR_2025", giftKey);
            return;
        }
        // 获取送礼用户信息
        UserVO userVO = feignUserService.getBasic(uid, ServicesAppIdEnum.lanling.getAppId()).getData();
        UserVO toUserVO = feignUserService.getBasic(toUid, ServicesAppIdEnum.lanling.getAppId()).getData();
        if (Objects.isNull(userVO) || Objects.isNull(toUserVO)) {
            log.warn("userVO or toUserVO is null uid:{}, toUid:{}", uid, toUid);
            return;
        }
        SexType sex = userVO.getSex();
        SexType toSex = toUserVO.getSex();

        log.info("handleRank uid:{} - sex:{}, toUid:{} - toSex:{}", uid, sex.getCode(), toUid, toSex.getCode());

        if (SexType.MAN.equals(sex) && SexType.WOMAN.equals(toSex)) {
            // 豪气榜
            milesOfLoveRankManager.incrRankValue(uid, value, HIGH_GAS_RANK);
            // 魅力榜
            milesOfLoveRankManager.incrRankValue(toUid, value, CHARM_RANK);
        }
    }

    /**
     * 处理跨年倒计时
     */
    private void handleTheNewYearS(CoinGiftGivedModel coinGiftGivedModel) {
        String giftKey = coinGiftGivedModel.getGiftKey();
        Long productCount = coinGiftGivedModel.getProductCount();
        if (NEW_YEAR_GIFT_KEY.equals(giftKey)) {
            redisManager.incrLong(NEW_YEAR_COUNTDOWN_PROGRESS, productCount * 2, DateUtil.ONE_MONTH_SECOND);
        }
    }

    /**
     * 处理任务
     */
    private void handleTask(CoinGiftGivedModel coinGiftGivedModel) {
        Long uid = coinGiftGivedModel.getFromUid();
        Long toUid = coinGiftGivedModel.getToUid();
        Long count = coinGiftGivedModel.getProductCount();
        String giftKey = coinGiftGivedModel.getGiftKey();

        log.info("handleTask uid:{},toUid:{}, giftKey:{}", uid, toUid, giftKey);

        MolEnum.TaskEnum taskEnum = MolEnum.TaskEnum.getByGiftValue(giftKey);
        if (Objects.isNull(taskEnum)) {
            return;
        }

        // 处理赠送
        Integer taskStatus = molRedisControls.getTaskStatus(uid, taskEnum.getTaskKey());
        // 只统计任务状态为未完成，已解锁的
        if (1 != taskStatus) {
            log.info("over handleTask uid:{}, taskStatus:{}", uid, taskStatus);
        } else {
            Long taskValue = molRedisControls.addTaskValue(uid, taskEnum.getTaskKey(), count);
            if (taskValue >= taskEnum.getNeedGiftCount()) {
                // 大于需要的数量，可领取+置为最大值
                molRedisControls.setTaskStatus(uid, taskEnum.getTaskKey(), 2);
                molRedisControls.resetTaskValue(uid, taskEnum.getTaskKey(), taskEnum.getNeedGiftCount());

                trackManager.allActivityTaskFinish(uid, taskEnum.getTaskKey());
            }
        }


        // 处理收取
        Integer taskStatusTo = molRedisControls.getTaskStatus(toUid, taskEnum.getTaskKey());
        // 只统计任务状态为未完成，已解锁的
        if (1 != taskStatusTo) {
            log.info("over handleTask toUid:{}, taskStatusTo:{}", toUid, taskStatusTo);
        } else {
            Long taskValueTo = molRedisControls.addTaskValue(toUid, taskEnum.getTaskKey(), count);
            if (taskValueTo >= taskEnum.getNeedGiftCount()) {
                // 大于需要的数量，可领取+置为最大值
                molRedisControls.setTaskStatus(toUid, taskEnum.getTaskKey(), 2);
                molRedisControls.resetTaskValue(toUid, taskEnum.getTaskKey(), taskEnum.getNeedGiftCount());

                trackManager.allActivityTaskFinish(toUid, taskEnum.getTaskKey());
            }
        }
    }

    /**
     * 处理恋爱纪念册
     */
    private void handleLoveBook(CoinGiftGivedModel coinGiftGivedModel) {
        Long uid = coinGiftGivedModel.getFromUid();
        Long coin = coinGiftGivedModel.getCoin();
        Long toUid = coinGiftGivedModel.getToUid();
        String giftKey = coinGiftGivedModel.getGiftKey();

        // 排除活动礼物
        if (NEW_YEAR_GIFT_KEY.equals(giftKey) || NEW_YEAR_S_EVE_KISS.equals(giftKey) || NEW_YEAR_2025.equals(giftKey)) {
            log.info("handleLoveBook uid:{}, giftKey:{} is not NEW_YEAR_GIFT_KEY or NEW_YEAR_S_EVE_KISS or NEW_YEAR_2025", uid, giftKey);
            return;
        }

        // 排除活动任务礼物
        MolEnum.TaskEnum taskEnum = MolEnum.TaskEnum.getByGiftValue(giftKey);
        if (Objects.nonNull(taskEnum)) {
            log.info("handleLoveBook taskGift uid:{}, giftKey:{} is task gift", uid, giftKey);
            return;
        }

        // 排除不是1v1
        if (!oneVone(coinGiftGivedModel)) {
            log.info("handleLoveBook not oneVone uid:{}, toUid:{}", uid, toUid);
            return;
        }

        // 排除异性
        if (sameSex(uid, toUid)) {
            log.info("handleLoveBook sameSex uid:{}, toUid:{}", uid, toUid);
            return;
        }

        // 排除无任何关系
        if (!isFriend(uid, toUid)) {
            log.info("handleLoveBook noFriend uid:{}, toUid:{}", uid, toUid);
            return;
        }

        String splicId = AppUtil.splicUserId(uid, toUid);
        log.debug("handleLoveBook addCoinValue splicId:{}, value:{}", splicId, coin);
        molRedisControls.addCoinValue(splicId, coin);

        // 留存
        milesOfLoveRankManager.incrRankValue(toUid, coin, String.format(COIN_COUNT_RETAIN, uid));
        milesOfLoveRankManager.incrRankValue(uid, coin, String.format(COIN_COUNT_RETAIN, toUid));
    }


    /**
     * 处理1v1
     */
    private Boolean oneVone(CoinGiftGivedModel coinGiftGivedModel) {
        String from = coinGiftGivedModel.getFrom();

        // 只要1 V 1
        if (StringUtils.isEmpty(from) ||
                StringUtils.equalsIgnoreCase(from, GiftFrom.room.getCode()) ||
                StringUtils.equalsIgnoreCase(from, GiftFrom.gift_in_return_voice_room.getCode()) ||
                StringUtils.equalsIgnoreCase(from, GiftFrom.family_gift.getCode()) ||
                StringUtils.equalsIgnoreCase(from, GiftFrom.gift_in_return_family.getCode()) ||
                StringUtils.equalsIgnoreCase(from, GiftFrom.chatroom.getCode()) ||
                StringUtils.equalsIgnoreCase(from, GiftFrom.gift_in_return_chatroom.getCode()) ||
                StringUtils.equalsIgnoreCase(from, GiftFrom.chatroom_red_packet.getCode())) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    /**
     * 过滤背包礼物
     *
     * @return
     */
    private Boolean filterBackpackGift(CoinGiftGivedModel coinGiftGivedModel) {
        // 数据校验
        if (Objects.isNull(coinGiftGivedModel.getFromUid()) ||
                StringUtils.isBlank(coinGiftGivedModel.getGiftKey()) ||
                Objects.isNull(coinGiftGivedModel.getCoin()) ||
                Objects.isNull(coinGiftGivedModel.getProductCount()) ||
                StringUtils.isBlank(coinGiftGivedModel.getGiftWay())) {
            log.info("sendGiftHandle: argument is null");
            return true;
        }
        String giftWay = coinGiftGivedModel.getGiftWay();
        // 面板礼物
        if (StringUtils.isEmpty(giftWay) || !StringUtils.equalsIgnoreCase(giftWay, GiftWay.NORMAL.getCode())) {
            return true;
        }
        return false;
    }

    /**
     * 同性不能参加活动
     * 是同性
     */
    public Boolean sameSex(Long uid, Long toUid) {
        if (Objects.isNull(uid) || Objects.isNull(toUid)) {
            return true;
        }
        UserVO userVO = feignUserService.getBasic(uid, ServicesAppIdEnum.lanling.getAppId()).getData();
        UserVO toUserVO = feignUserService.getBasic(toUid, ServicesAppIdEnum.lanling.getAppId()).getData();
        if (Objects.isNull(userVO) || Objects.isNull(toUserVO)) {
            log.warn("userVO or toUserVO is null uid:{}, toUid:{}", uid, toUid);
            return true;
        }
        return userVO.getSex().equals(toUserVO.getSex());
    }


    public Boolean isFriend(Long uid, Long toUid) {
        if (Objects.isNull(toUid)) {
            return Boolean.FALSE;
        }
        Boolean result = depthFeignService.hasUserCardiacRelationship(uid, toUid).successData();
        String relation = feignSnsService.getUserRelation(uid, toUid, ServicesAppIdEnum.lanling.getAppId()).getData();
        if (UserRelationType.friend.getCode().equals(relation)
                || UserRelationType.subscribe.getCode().equals(relation)
                || UserRelationType.fans.getCode().equals(relation)
                || result) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }


    @Override
    public String getActivityCode() {
        return ACTIVITY_CODE;
    }

}
