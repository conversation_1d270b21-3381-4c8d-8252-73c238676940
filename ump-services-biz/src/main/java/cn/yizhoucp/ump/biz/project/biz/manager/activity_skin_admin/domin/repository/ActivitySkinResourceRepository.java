package cn.yizhoucp.ump.biz.project.biz.manager.activity_skin_admin.domin.repository;

import cn.yizhoucp.ump.biz.project.biz.manager.activity_skin_admin.domin.model.ActivitySkinResource;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

public interface ActivitySkinResourceRepository{

    Integer saveResource(ActivitySkinResource resource);

    Integer deleteByResourceKey(String resourceKey);

    Integer deleteByParentKey(String parentKey);

    Page<ActivitySkinResource> selectPage(String resourceKey, String parentKey, Integer page, Integer size);

    ActivitySkinResource findByResourceKey(String resourceKey);

    ActivitySkinResource findResourceConfigByResourceKey(String resourceKey);

    Integer saveConfigByResourceKey(String resourceKey, JSON resourceConfig);
}
