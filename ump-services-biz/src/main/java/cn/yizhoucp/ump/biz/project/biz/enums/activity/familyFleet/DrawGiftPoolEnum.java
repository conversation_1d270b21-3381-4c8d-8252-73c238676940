package cn.yizhoucp.ump.biz.project.biz.enums.activity.familyFleet;

import cn.yizhoucp.ms.core.base.enums.dressup.DressUpType;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 抽奖奖池
 *
 * @author: lianghu
 */
@Getter
@AllArgsConstructor
public enum DrawGiftPoolEnum {

    DRAW_GIFT_1314("SHSL", "守护神鹿", "https://res-cdn.nuan.chat/nuanliao-fronted/activity/family-racing/reward/SHSL.png", "gift", null, "SHSL_GIFT", 15, 1, 2),
    DRAW_GIFT_FHYF("FHYF", "凤凰于飞", "https://res-cdn.nuan.chat/nuanliao-fronted/activity/family-racing/reward/FHYF.png", "dress", DressUpType.mount.getCode(), "fenghuangyufei_mount", 1, 1, 800),
    DRAW_GIFT_YHZL("YHZL", "永恒之恋头像框", "https://res-cdn.nuan.chat/nuanliao-fronted/activity/family-racing/reward/YHZL.png", "dress", DressUpType.head_frame.getCode(), "yonghengzhilian_head_frame", 1, 1, 1000),
    DRAW_GIFT_50COIN("20COIN", "20 金币", "https://res-cdn.nuan.chat/nuanliao-fronted/activity/family-racing/reward/20COIN.png", "coin", null, "20", null, 1, 200),
    DRAW_GIFT_XDGD("XDGD", "限定宫灯", "https://res-cdn.nuan.chat/nuanliao-fronted/activity/family-racing/reward/XDGD.png", "gift", null, "XDGD_GIFT", 15, 1, 14),
    DRAW_GIFT_CYS("CYS", "超音速进场特效", "https://res-cdn.nuan.chat/nuanliao-fronted/activity/family-racing/reward/CYS.png", "dress", DressUpType.entry_special_effect.getCode(), "chaoyinsu_entry_special_effect", 1, 1, 2000),
    DRAW_GIFT_BBK("BBK", "表白卡礼物", "https://res-cdn.nuan.chat/nuanliao-fronted/activity/family-racing/reward/BBK.png", "gift", null, "BBK_ITEM_GIFT", 15, 1, 3000),
    DRAW_GIFT_10COIN("5COIN", "5 金币", "https://res-cdn.nuan.chat/nuanliao-fronted/activity/family-racing/reward/5COIN.png", "coin", null, "5", null, 1, 1000),
    DRAW_GIFT_LMXH("LMXH", "浪漫星河进场特效", "https://res-cdn.nuan.chat/nuanliao-fronted/activity/family-racing/reward/LMXH.png", "dress", DressUpType.entry_special_effect.getCode(), "langmanxinghe_entry_special_effect", 1, 1, 1983),
    ;

    /** 奖品 code */
    private String code;
    /** 奖品描述 */
    private String desc;
    /** 奖品 img */
    private String icon;
    /** 奖品类型 */
    private String type;
    /** 奖品子类型 */
    private String subType;
    /** 值（金币使用） */
    private String value;
    /** 有效天数 */
    private Integer effectiveDays;
    /** 默认中奖数量 */
    private Integer defaultNum;
    /** 默认抽中概率 */
    private Integer defaultProbability;

    private static final Map<String, DrawGiftPoolEnum> instanceMap = new HashMap<>();

    static {
        for (DrawGiftPoolEnum item : values()) {
            instanceMap.put(item.getCode(), item);
        }
    }

    public static List<DrawGiftPoolEnum> getGiftList() {
        return Lists.newArrayList(values());
    }

    public static DrawGiftPoolEnum getInstance(String code) {
        return instanceMap.get(code);
    }
}