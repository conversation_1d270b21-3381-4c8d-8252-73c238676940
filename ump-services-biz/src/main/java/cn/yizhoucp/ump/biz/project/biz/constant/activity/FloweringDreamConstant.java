package cn.yizhoucp.ump.biz.project.biz.constant.activity;


import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

/**
 * 繁花梦境 活动常量
 */
@Component
public class FloweringDreamConstant {

    /**
     * 活动代码
     */
    public static final String ACTIVITY_CODE = "flowering_dream";

    /**
     * 抽奖奖池
     */
    public static final String DREAM_POLL = "blossoms_pool";

    /**
     * 活动通行证
     */
    public static final String ACTIVITY_PASS_CERTIFICATE = "ump:fairy_tale_dream:pass_certificate:user:%s";

    /**
     * 用户抽奖次数统计
     */
    public static final String USER_DREAM_COUNT = "ump:fairy_tale_dream:dream_count:user:%s";

    /**
     * 繁花使者榜单 key
     */
    public static final String ENVOY_DREAM_RANK_KEY = "ump:fairy_tale_dream:envoy:dream_rank";

    /**
     * 梦境剧场榜单 key
     */
    public static final String DREAM_THEATER_RANK_KEY = "ump:fairy_tale_dream:dream_theater:rank:%s";

    /**
     * 用户抽奖次数奖励key
     */
    public static final String USER_DREAM_COUNT_AWARD = "ump:fairy_tale_dream:dream_count:user:%s:%s:awardKey";

    /**
     * 用户奖励记录留存
     */
    public static final String USER_DREAM_RECORD = "ump:fairy_tale_dream:dream_record:user:%s";

    /**
     * 用户任务进度-每日
     */
    public static final String USER_TASK_PROGRESS = "ump:fairy_tale_dream:task:%s:user:%s:%s:taskCode";

    /** 梦境剧场获得奖励阈值 */
    public static final Long THRESHOLD_VALUE = 500000L;

    /** 梦境剧场获得奖励最大数量 */
    public static final Long MAX_COUNT = 3L;

    /**
     * 抽奖次数统计奖励枚举
     */
    @AllArgsConstructor
    @Getter
    public enum DrawCountEnum {
        AWARD_10("AWARD_10", 10L, "XHYW_GIFT"),
        AWARD_25("AWARD_25", 25L, "LZDG_GIFT"),
        AWARD_50("AWARD_50", 50L, "YJZQ_GIFT");

        /**
         * 次数code
         */
        private final String rewardsKey;
        /**
         * 需要达到抽奖次数
         */
        private final Long count;
        /**
         * 奖励Key
         */
        private final String awardKey;

        public static DrawCountEnum getByRewardsKey(String item) {
            for (DrawCountEnum drawCountEnum : DrawCountEnum.values()) {
                if (drawCountEnum.getRewardsKey().equals(item)) {
                    return drawCountEnum;
                }
            }
            return null;
        }
    }

    /**
     * 抽奖奖励领取状态枚举
     */
    @AllArgsConstructor
    @Getter
    public enum AwardReceiveEnum {
        /**
         * 未完成
         */
        NOT_COMPLETED("NOT_COMPLETED"),

        /**
         * 已完成
         */
        COMPLETED("COMPLETED"),

        /**
         * 已领取
         */
        RECEIVED("RECEIVED");

        /**
         * 状态
         */
        private final String status;
    }

    /**
     * 任务枚举
     */
    @AllArgsConstructor
    @Getter
    public enum TaskEnum {
        TASK_1("TASK_1", 1L, "YXDH_GIFT"),
        TASK_2("TASK_2", 2L, "LMYW_GIFT"),
        TASK_4("TASK_4", 4L, "YYQY_GIFT"),
        TASK_5("TASK_5", 5L, "AZHJ_GIFT"),
        TASK_8("TASK_8", 8L, "DWJQ_GIFT");

        /**
         * 任务code
         */
        private final String taskCode;
        /**
         * 任务奖励通行证个数
         */
        private final Long rewardCount;
        /**
         * 任务礼物Key
         */
        private final String giftKey;

        public static TaskEnum getByGiftKey(String giftKey) {
            for (TaskEnum taskEnum : TaskEnum.values()) {
                if (taskEnum.getGiftKey().equals(giftKey)) {
                    return taskEnum;
                }
            }
            return null;
        }
    }

    /**
     * 用户抽奖次数 统计奖励 key
     */
    public static String createUserDreamCountAwardKey(Long userId, String awardKey) {
        if (Objects.isNull(userId) || StringUtils.isEmpty(awardKey)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        return String.format(USER_DREAM_COUNT_AWARD, userId, awardKey);
    }

    /**
     * 用户任务进度
     */
    public static String createUserTaskProgressKey(Long userId, TaskEnum itemTask) {
        if (Objects.isNull(userId) || Objects.isNull(itemTask)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        return String.format(USER_TASK_PROGRESS, createDateKey(null), userId, itemTask.getTaskCode());
    }

    /**
     * 创建梦境剧场排行榜 key
     */
    public static String createDreamTheaterRankKey(String date) {
        return String.format(DREAM_THEATER_RANK_KEY, createDateKey(date));
    }

    /**
     * 通用日期 key
     */
    public static String createDateKey(String date) {
        if (Objects.isNull(date)) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            String newDate = LocalDateTime.now().format(formatter);
            return String.format("date:%s", newDate);
        } else {
            return String.format("date:%s", date);
        }
    }
}
