package cn.yizhoucp.ump.biz.project.biz.manager.activity.timeCarnival.rankStrategy.stragegy;

import cn.hutool.core.util.ObjectUtil;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.activity.timeCarnival.TimeCarnivalIndexVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.timeCarnival.TimeCarnivalConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.timeCarnival.rankStrategy.RankStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.timeCarnival.rankStrategy.RankStrategyEnum;
import cn.yizhoucp.user.manager.UserFeignManager;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

@Component
@Slf4j
public class UserBandRankStrategy implements RankStrategy {

    @Resource
    private RedisManager redisManager;
    @Resource
    private UserFeignManager userFeignManager;

    @Override
    public TimeCarnivalIndexVO.BandRankVO getRank(Long uid, Long toUid, String rewardId) {
        TimeCarnivalIndexVO.BandRankVO bandRankVO = new TimeCarnivalIndexVO.BandRankVO();
        String key = TimeCarnivalConstant.createRankKey(RankStrategyEnum.USER_BAND_RANK.getCode());
        log.debug("getRankList key {}", key);
        Set<ZSetOperations.TypedTuple<Object>> typedTuples = redisManager.reverseRangeByScoreWithScores(key,
                0, Double.MAX_VALUE, 0, 10L);
        if (CollectionUtils.isEmpty(typedTuples)) {
            return bandRankVO;
        }
        List<TimeCarnivalIndexVO.BandRankItem> bandRankItems = Lists.newArrayList();
        Long rank = 1L;
        for (ZSetOperations.TypedTuple<Object> typedTuple : typedTuples) {
            bandRankItems.add(TimeCarnivalIndexVO.BandRankItem.builder()
                    .rank(rank)
                    .bandId(String.valueOf(typedTuple.getValue()))
                    .score(Optional.ofNullable(typedTuple.getScore()).orElse(0d).longValue())
                    .build());
            rank++;
        }
        for (TimeCarnivalIndexVO.BandRankItem item : bandRankItems) {
/*
            String bandInfoKey = TimeCarnivalConstant.createBandInfoKey(item.getBandId());
*/
            String bandInfoKey = String.format(TimeCarnivalConstant.BAND_INFO_KEY, item.getBandId());
            log.info("bandInfoKey {}", bandInfoKey);
            Map<Object, Object> bandInfo = redisManager.hmget(bandInfoKey);
            Long leaderId = Long.valueOf(bandInfo.get("leaderId").toString());
            String[] members = ((String) bandInfo.get("members")).split(",");
            item.setBandMembers(buildBandMembers(members, leaderId));
            item.setBandName(bandInfo.get("bandName").toString());
        }
        bandRankVO.setBandRankList(bandRankItems);
        bandRankVO.setMyBandRankItem(buildMyBandRankItem(key, uid));
        return bandRankVO;
    }

    private List<TimeCarnivalIndexVO.BandMember> buildBandMembers(String[] members, Long leaderId) {
        List<TimeCarnivalIndexVO.BandMember> bandMembers = Lists.newArrayList();
        for (String member : members) {
            UserVO userVO = userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), Long.valueOf(member));
            TimeCarnivalIndexVO.BandMember bandMember = new TimeCarnivalIndexVO.BandMember();
            if (userVO == null) {
                bandMember.setUserName("神秘嘉宾");
                bandMember.setUserId(Long.valueOf(member));
                bandMember.setUserAvatar("https://res-cdn.nuan.chat/nuanliao-fronted/activity/guanxuan-jun/default-avatar.png");
            } else {
                bandMember.setUserId(userVO.getId());
                bandMember.setUserName(userVO.getName());
                bandMember.setUserAvatar(userVO.getAvatar());
            }
            bandMember.setIsLeader(ObjectUtil.equals(leaderId, Long.valueOf(member)));
            bandMembers.add(bandMember);
        }
        return bandMembers;
    }

    private TimeCarnivalIndexVO.BandRankItem buildMyBandRankItem(String rankKey, Long uid) {
        String itemKey = redisManager.getString(TimeCarnivalConstant.createUserBandKey(uid));
        log.debug("getSelfRank relationId {}", itemKey);
        if (StringUtils.isBlank(itemKey)) {
            return null;
        }
        Long rank = redisManager.reverseRank(rankKey, itemKey);
        Long rankLen = 10L;
        // 榜单中不存在
        if (Objects.isNull(rank)) {
            log.debug("不在榜上 item {}", itemKey);
            return null;
        }
        Double score = redisManager.score(rankKey, itemKey);
        Long diff = null;
        // 计算差值
        // 获取前一名的榜单值
        Set<ZSetOperations.TypedTuple<Object>> typedTuples = redisManager.reverseRangeByScoreWithScores(rankKey, 0d, Double.MAX_VALUE, rank - 1, 1);
        log.debug("前一名榜单数据 {}", JSON.toJSONString(typedTuples));
        if (CollectionUtils.isEmpty(typedTuples)) {
            diff = 0L;
        } else {
            for (ZSetOperations.TypedTuple<Object> typedTuple : typedTuples) {
                diff = typedTuple.getScore().longValue() - score.longValue();
            }
        }
        if (rankLen - 1 < rank) {
            // 未上榜的情况
            rank = -1L;
        } else {
            // 上榜
            rank++;
        }
        TimeCarnivalIndexVO.BandRankItem bandRankItem = TimeCarnivalIndexVO.BandRankItem.builder().build();
        String bandInfoKey = TimeCarnivalConstant.createBandInfoKey(itemKey);
        Map<Object, Object> bandInfo = redisManager.hmget(bandInfoKey);
        String[] members = ((String) bandInfo.get("members")).split(",");
        bandRankItem.setDiffScore(diff);
        bandRankItem.setBandId(itemKey);
        bandRankItem.setBandName(bandInfo.get("bandName").toString());
        bandRankItem.setBandMembers(buildBandMembers(members, Long.valueOf(bandInfo.get("leaderId").toString())));
        return bandRankItem;
    }
}
