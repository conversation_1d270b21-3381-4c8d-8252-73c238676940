package cn.yizhoucp.ump.biz.project.dal.jpa.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.LocalDateTime;

@Table(name = "activity_user_check_list")
@Entity
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserActivityDO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    /** 应用 ID */
    private String unionId;
    /** 应用 ID */
    private Long appId;
    /** 活动名称 */
    private String activityName;
    /** 活动编号 */
    private String activityCode;
    /** 所属活动编号 */
    private String belongActivityCode;
    /** 所属活动模板编号 */
    private String belongTemplateCode;
    /** 所属活动模板类型 */
    private String belongTemplateType;
    /** 活动类型 */
    private String activityType;
    /** 活动链接 */
    private String activityUrl;
    /** 用户ID */
    private Long userId;
    /** 活动开始时间 */
    private LocalDateTime startTime;
    /** 活动结束时间 */
    private LocalDateTime endTime;
    /** 实际退出时间 */
    private LocalDateTime exitTime;
    /** 创建时间 */
    private LocalDateTime createTime;
    /** 更新时间 */
    private LocalDateTime updateTime;
    /** 状态 enable - disable */
    private String status;

}
