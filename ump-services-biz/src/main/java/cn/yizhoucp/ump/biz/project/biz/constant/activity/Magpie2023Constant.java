package cn.yizhoucp.ump.biz.project.biz.constant.activity;

import cn.yizhoucp.ump.api.vo.jimu.rank.RewardVO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class Magpie2023Constant {

    public static final String ACTIVITY_CODE = "magpie2023";

    /**
     * 发起每日限制一次（时间、type、userId）
     */
    public static final String INITIATE_EVERY_DAY_LIMIT_1 = "ump:magpie2023:initiateEveryLimit1_%s_%s_%s";

    /**
     * 七日签到（splicUserId）
     */
    public static final String SEVEN_DAY_SIGN_IN = "ump:magpie2023:sevenDaySignIn_%s";

    /**
     * 爱意值（时间、userId）
     */
    public static final String LOVE_VALUE = "ump:magpie2023:loveValue_%s_%s";

    /**
     * 爱意奖池抽奖次数（userId、toUserId）
     */
    public static final String LOVE_POOL_DRAW_TIME = "ump:magpie2023:lovePoolDrawTime_%s_%s";

    /**
     * 爱意奖池刷新次数（userId）
     */
    public static final String LOVE_POOL_REFRESH_TIME = "ump:magpie2023:lovePoolRefreshTime_%s";

    /**
     * 爱意奖池（userId、toUserId）
     */
    public static final String LOVE_DRAW_POOL_CODE = "ump:magpie2023:loveDrawPoolCode_%s_%s";
    /**
     * 爱意奖池（splicUserId）
     */
    public static final String LOVE_DRAW_POOL_ITEM = "ump:magpie2023:loveDrawPoolItem_%s";

    public static final List<String> LOVE_DRAW_POOL_CODE_MAN = Arrays.asList("LOVE_DRAW_POOL_1_MAN","LOVE_DRAW_POOL_2_MAN","LOVE_DRAW_POOL_3_MAN","LOVE_DRAW_POOL_4_MAN","LOVE_DRAW_POOL_5_MAN");
    public static final List<String> LOVE_DRAW_POOL_CODE_WOMAN = Arrays.asList("LOVE_DRAW_POOL_1_WOMAN","LOVE_DRAW_POOL_2_WOMAN","LOVE_DRAW_POOL_3_WOMAN","LOVE_DRAW_POOL_4_WOMAN","LOVE_DRAW_POOL_5_WOMAN");

    public static final List<String> LOVE_DRAW_POOL_399_GIFT = Arrays.asList("AXFS_GIFT", "LSSQ_GIFT", "THF_GIFT");
    public static final List<String> LOVE_DRAW_POOL_520_GIFT = Arrays.asList("XDPF_GIFT", "XRNYZS_GIFT", "ZNDGZ_GIFT", "BQSF_GIFT", "LLZ_GIFT", "AQG_GIFT", "SXXB_GIFT", "NSHG_GIFT", "LAXJ_GIFT", "QQQ_GIFT");
    public static final List<String> LOVE_DRAW_POOL_888_GIFT = Arrays.asList("XYHT_GIFT", "TMBJ_GIFT", "BYSN_GIFT", "HCXY_GIFT", "520MGH_GIFT");
    public static final List<String> LOVE_DRAW_POOL_1314_GIFT = Arrays.asList("YSCA_GIFT", "XYLY_GIFT", "LMTC_GIFT", "TMYH_GIFT", "QHZZJ_GIFT");
    public static final List<String> LOVE_DRAW_POOL_1999_GIFT = Arrays.asList("MGLC_GIFT", "CLHS_GIFT");
    public static final List<String> LOVE_DRAW_POOL_5200_GIFT = Arrays.asList("Romantic_Galaxy_GIFT", "DNHJ_GIFT", "YYJR_GIFT", "PC_GIFT", "CBHL_GIFT");
    public static final List<String> LOVE_DRAW_POOL_HEAD_FRAME = Arrays.asList("xunai_head_frame", "enaiqinglv_head_frame", "yonghengzhilian_head_frame", "tianmiqinglv_head_frame", "tianmixiaotu_head_frame", "aiqingshu_head_frame");
    public static final List<String> LOVE_DRAW_POOL_MOUNT = Arrays.asList("mengjinghuahai_mount", "nanguamache_mount", "gaobaichaopao_mount", "yunxiaoxianlu_mount", "zhuanshudingshehuachuan_mount");
    public static final List<String> LOVE_DRAW_POOL_ENTER_SPECIAL_EFFECT = Arrays.asList("meiguihuachuan_entry_special_effect", "langmanxinghe_entry_special_effect", "wozhixihuanni_entry_special_effect");

    /**
     * 爱意榜（时间）
     */
    public static final String LOVE_BOARD = "ump:magpie2023:loveBoard_%s";

    /**
     * 喜鹊数量（userId）
     */
    public static final String MAGPIE_COUNT = "ump:magpie2023:magpieCount_%s";

    /**
     * 喜上眉梢奖池抽奖次数（userId）
     */
    public static final String XSMS_POOL_DRAW_TIME = "ump:magpie2023:xsmsPoolDrawTime_%s";

    /**
     * 发送表白文案（时间、userId）
     */
    public static final String SEND_CONFESS_COPY = "ump:magpie2023:sendConfessCopy_%s_%s";

    /**
     * 发送表白文案间隔（时间、userId）
     */
    public static final String SEND_CONFESS_COPY_INTERVAL = "ump:magpie2023:sendConfessCopyInterval_%s_%s";

    /**
     * 甜蜜连线次数（时间、userId）
     */
    public static final String SWEET_CONNECT_TIME = "ump:magpie2023:sweetConnectTime_%s_%s";

    /**
     * 甜蜜连线间隔（时间、userId）
     */
    public static final String SWEET_CONNECT_INTERVAL = "ump:magpie2023:sweetConnectInterval_%s_%s";

    /**
     * 为爱祝福设备 ID
     */
    public static final String BLESSING_FOR_LOVE_DEVICE_ID = "ump:magpie2023:blessingForLoveDeviceId";

    /**
     * 为爱祝福用户 ID
     */
    public static final String BLESSING_FOR_LOVE_USER_ID = "ump:magpie2023:blessingForLoveUserId";

    /**
     * 全服发起表白次数
     */
    public static final String CONFESS_TIME_TOTAL = "ump:magpie2023:confessTimeTotal";

    /**
     * 眷侣榜
     */
    public static final String COUPLE_BOARD = "ump:magpie2023:coupleBoard";

    /**
     * 神秘面纱（splicUserId）
     */
    public static final String COUPLE_BOARD_HIDE = "ump:magpie2023:coupleBoardHide_%s";

    /**
     * 活动日期
     */
//    public static final List<String> ACTIVITY_DATE = Arrays.asList("8.15", "8.16", "8.17", "8.18", "8.19", "8.20", "8.21");
    public static final List<String> ACTIVITY_DATE = Arrays.asList("8.20", "8.21", "8.22", "8.23", "8.24", "8.25", "8.26");

    @Getter
    @AllArgsConstructor
    public enum Magpie {
        XSQ("QXXSQ_GIFT", "相思鹊"),
        JXQ("QXJXQ_GIFT", "吉祥鹊"),
        RYQ("QXRYQ_GIFT", "如意鹊"),
        CQQ("QXCQQ_GIFT", "长情鹊"),
        HXQ("QXHXQ_GIFT", "欢喜鹊"),
        YYQ("QXYYQ_GIFT", "姻缘鹊"),
        FGQ("QXFGQ_GIFT", "富贵鹊");

        private String giftKey;
        private String name;

        public static String getNameByGiftKey(String giftKey) {
            if (StringUtils.isBlank(giftKey)) {
                return null;
            }

            for (Magpie magpie : values()) {
                if (magpie.giftKey.equals(giftKey)) {
                    return magpie.getName();
                }
            }

            return null;
        }
    }

    /**
     * 爱意榜奖励列表
     */
    public static final Map<Long, List<RewardVO>> LOVE_RANK_REWARD = Maps.newHashMap();

    static {
        LOVE_RANK_REWARD.put(1L, Lists.newArrayList(
                RewardVO.builder()
                        .rewardName("鹊桥之恋")
                        .giftKey("QQZL_GIFT")
                        .type("gift")
                        .value(2888L)
                        .icon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-08/1691563682274079.png")
                        .times("2")
                        .build()));
        LOVE_RANK_REWARD.put(2L, Lists.newArrayList(
                RewardVO.builder()
                        .rewardName("纯恋婚纱")
                        .giftKey("CLHS_GIFT")
                        .type("gift")
                        .value(1999L)
                        .icon("https://design-platform-cdn.yizhoucp.cn/gift-image/2021-06/1624619364763738.png")
                        .times("2")
                        .build()));
        LOVE_RANK_REWARD.put(3L, Lists.newArrayList(
                RewardVO.builder()
                        .rewardName("爱情海")
                        .giftKey("AQH_GIFT")
                        .type("gift")
                        .value(1500L)
                        .icon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-08/1691563192428730.png")
                        .times("2")
                        .build()));
        LOVE_RANK_REWARD.put(4L, Lists.newArrayList(
                RewardVO.builder()
                        .rewardName("星空瓶")
                        .giftKey("XKP_GIFT")
                        .type("gift")
                        .value(1200L)
                        .icon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-08/1691564046087055.png")
                        .times("2")
                        .build()));
        LOVE_RANK_REWARD.put(5L, Lists.newArrayList(
                RewardVO.builder()
                        .rewardName("白羊少女")
                        .giftKey("BYSN_GIFT")
                        .type("gift")
                        .value(888L)
                        .icon("https://design-platform-cdn.yizhoucp.cn/gift-image/2022-02/1645710123307790.png")
                        .times("2")
                        .build()));
        LOVE_RANK_REWARD.put(6L, Lists.newArrayList(
                RewardVO.builder()
                        .rewardName("连理枝")
                        .giftKey("LLZ_GIFT")
                        .type("gift")
                        .value(520L)
                        .icon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-08/1691563412734019.png")
                        .times("2")
                        .build()));
        LOVE_RANK_REWARD.put(7L, Lists.newArrayList(
                RewardVO.builder()
                        .rewardName("爱心发射")
                        .giftKey("AXFS_GIFT")
                        .type("gift")
                        .value(399L)
                        .icon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-08/1691563292601018.png")
                        .times("2")
                        .build()));
    }

    /**
     * 眷侣榜奖励列表
     */
    public static final Map<Long, List<RewardVO>> COUPLE_RANK_REWARD = Maps.newHashMap();
    static {
        COUPLE_RANK_REWARD.put(1L, Lists.newArrayList(
                RewardVO.builder()
                        .rewardName("神都夜宴")
                        .giftKey("SDYY_GIFT")
                        .type("gift")
                        .value(10000L)
                        .icon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-03/1678422151487847.png")
                        .build(),
                RewardVO.builder()
                        .rewardName("金币")
                        .giftKey("coin")
                        .type("mount")
                        .value(5200L)
                        .icon("https://res-cdn.nuan.chat/res/activity/magpie2023-coin-img.png")
                        .build()));
        COUPLE_RANK_REWARD.put(2L, Lists.newArrayList(
                RewardVO.builder()
                        .rewardName("心动小酒馆")
                        .giftKey("XDXJG_GIFT")
                        .type("gift")
                        .value(8888L)
                        .icon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-05/1684649708473155.png")
                        .build(),
                RewardVO.builder()
                        .rewardName("金币")
                        .giftKey("coin")
                        .type("coin")
                        .value(3344L)
                        .icon("https://res-cdn.nuan.chat/res/activity/magpie2023-coin-img.png")
                        .build()));
        COUPLE_RANK_REWARD.put(3L, Lists.newArrayList(
                RewardVO.builder()
                        .rewardName("悠悠假日")
                        .giftKey("YYJR_GIFT")
                        .type("gift")
                        .value(5200L)
                        .icon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-03/1679890525794682.png")
                        .build(),
                RewardVO.builder()
                        .rewardName("金币")
                        .giftKey("coin")
                        .type("coin")
                        .value(2000L)
                        .icon("https://res-cdn.nuan.chat/res/activity/magpie2023-coin-img.png")
                        .build()));
        COUPLE_RANK_REWARD.put(4L, Lists.newArrayList(
                RewardVO.builder()
                        .rewardName("云端乐园")
                        .giftKey("YDLY_GIFT")
                        .type("gift")
                        .value(3344L)
                        .icon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-04/1681097257992745.png")
                        .build(),
                RewardVO.builder()
                        .rewardName("金币")
                        .giftKey("coin")
                        .type("coin")
                        .value(1200L)
                        .icon("https://res-cdn.nuan.chat/res/activity/magpie2023-coin-img.png")
                        .build()));
        COUPLE_RANK_REWARD.put(5L, Lists.newArrayList(
                RewardVO.builder()
                        .rewardName("一世长安")
                        .giftKey("YSCA_GIFT")
                        .type("gift")
                        .value(1314L)
                        .icon("https://design-platform-cdn.yizhoucp.cn/gift-image/2022-11/1668581226604341.png")
                        .build(),
                RewardVO.builder()
                        .rewardName("金币")
                        .giftKey("coin")
                        .type("coin")
                        .value(999L)
                        .icon("https://res-cdn.nuan.chat/res/activity/magpie2023-coin-img.png")
                        .build()));
        COUPLE_RANK_REWARD.put(6L, Lists.newArrayList(
                RewardVO.builder()
                        .rewardName("花车巡游")
                        .giftKey("HCXY_GIFT")
                        .type("gift")
                        .value(888L)
                        .icon("https://design-platform-cdn.yizhoucp.cn/gift-image/2022-11/1667904805831492.png")
                        .build(),
                RewardVO.builder()
                        .rewardName("金币")
                        .giftKey("coin")
                        .type("coin")
                        .value(777L)
                        .icon("https://res-cdn.nuan.chat/res/activity/magpie2023-coin-img.png")
                        .build()));
        COUPLE_RANK_REWARD.put(7L, Lists.newArrayList(
                RewardVO.builder()
                        .rewardName("爱情果")
                        .giftKey("AQG_GIFT")
                        .type("gift")
                        .value(520L)
                        .icon("https://design-platform-cdn.yizhoucp.cn/gift-image/2022-11/1669367511989231.png")
                        .build(),
                RewardVO.builder()
                        .rewardName("金币")
                        .giftKey("coin")
                        .type("coin")
                        .value(520L)
                        .icon("https://res-cdn.nuan.chat/res/activity/magpie2023-coin-img.png")
                        .build()));
    }

}
