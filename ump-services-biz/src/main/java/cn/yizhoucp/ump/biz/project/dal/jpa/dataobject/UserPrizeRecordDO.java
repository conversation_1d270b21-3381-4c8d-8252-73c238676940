package cn.yizhoucp.ump.biz.project.dal.jpa.dataobject;

import cn.yizhoucp.ms.core.base.enums.CommonStatus;
import cn.yizhoucp.ms.core.base.enums.UserPackageBizType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.Date;

/**
 * 用户获奖记录
 *
 * @author: lianghu
 */
@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "lucky_bag_user_record")
public class UserPrizeRecordDO {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "lucky_bag_user_record_generator")
    @SequenceGenerator(name = "lucky_bag_user_record_generator",sequenceName = "lucky_bag_user_record_seq", allocationSize = 10)
    private Long id;
    /** 应用ID */
    private Long appId;
    /** 活动ID */
    private Long activityId;
    /** 用户ID */
    private Long userId;
    /** 奖池ID */
    private Long prizePoolId;
    /** 奖品单价值 */
    private Long prizeCoin;
    /** 奖品总数量 */
    private Long prizeNum;
    /** 奖品类型 */
    @Enumerated(EnumType.STRING)
    private UserPackageBizType prizeBizType;
    /** 奖品业务ID */
    private String prizeBizId;
    /** 记录描述 */
    private String memo;
    /** 可用状态 */
    @Enumerated(EnumType.STRING)
    private CommonStatus status;
    /** 创建时间 */
    private Date createTime;
    /** 更新时间 */
    private Date updateTime;


}
