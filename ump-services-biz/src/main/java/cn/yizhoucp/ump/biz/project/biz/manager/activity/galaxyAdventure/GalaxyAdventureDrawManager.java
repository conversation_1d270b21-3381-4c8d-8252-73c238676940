package cn.yizhoucp.ump.biz.project.biz.manager.activity.galaxyAdventure;

import cn.hutool.core.collection.CollUtil;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.activity.galaxyAdventure.OpenTreasureVO;
import cn.yizhoucp.ump.api.vo.activity.galaxyAdventure.TreasureVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.inner.DrawLogItem;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.GalaxyAdventureConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.log.LogComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.AbstractDrawTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawContext;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawLogParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.doDrawStrategy.common.ProbStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ActivityDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawLogDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.dal.service.ActivityService;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 银河探险-抽奖处理类
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class GalaxyAdventureDrawManager extends AbstractDrawTemplate {


    private final RedisManager redisManager;

    private final ActivityService activityService;

    private final NotifyComponent notifyComponent;

    private final ProbStrategy probStrategy;

    private final LogComponent logComponent;

    private final GalaxyAdventureTrackManager trackManager;

    /**
     * 删除燃料值
     * @param uid 用户id
     * @param points 积分
     */
    public void removePoints(Long uid, Long points) {
        String pointsKey = GalaxyAdventureConstant.createPointsKey(uid);
        redisManager.decrLong(pointsKey, points, DateUtil.ONE_MONTH_SECOND);
    }

    /**
     * 获取用户当前燃料值
     * @param uid 用户id
     */
    public Long getUserPoints(Long uid) {
        String pointsKey = GalaxyAdventureConstant.createPointsKey(uid);
        return Optional.ofNullable(redisManager.getLong(pointsKey)).orElse(0L);
    }


    /**
     * 获取活动结束时间
     *
     * @return LocalDateTime
     */
    private LocalDateTime getActivityEndTime() {
        ActivityDO activityDO = activityService.getByCode(MDCUtil.getCurAppIdByMdc(), MDCUtil.getCurUnionIdByMdc(), GalaxyAdventureConstant.ACTIVITY_CODE);
        if (Objects.isNull(activityDO)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "活动不存在");
        }
        return activityDO.getEndTime();
    }

    @Override
    protected void resourceCheck(DrawContext context) {
        DrawParam drawParam = context.getDrawParam();
        drawParam.setTimes(1);
        DrawParam param = context.getDrawParam();
        Long uid = param.getUid();
        Long points = getUserPoints(uid);
        TreasureVO treasureInfo = getTreasureInfo(context);
        if (points < treasureInfo.getNeedPoints()) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "燃料值不足");
        }
    }

    @Override
    protected List<DrawLogItem> drawLogWrapper(DrawLogParam param, List<DrawLogDO> drawLogDOList) {
        List<DrawLogItem> drawLogItemList = drawLogDOList.stream().map(drawLogDO -> {
            DrawPoolItemDTO drawPoolItemDTO = JSON.parseObject(drawLogDO.getLogJson(), DrawPoolItemDTO.class);
            DrawPoolItemDO drawPoolItemDO = drawPoolItemDTO.getDrawPoolItemDO();
            return DrawLogItem.builder()
                    .icon(drawPoolItemDO.getItemIcon())
                    .valueGold(drawPoolItemDO.getItemValueGold())
                    .text(drawPoolItemDO.getItemName())
                    .itemNum(drawPoolItemDO.getItemNum() * drawPoolItemDTO.getTargetTimes())
                    .time(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm").format(drawLogDO.getCreateTime())).build();
        }).collect(Collectors.toList());
        log.debug("drawLogItemList {}", JSON.toJSONString(drawLogItemList));
        Collections.reverse(drawLogItemList);
        return drawLogItemList;
    }

    @Override
    protected void draw(DrawContext context) {
        long exchangeEndTime = getActivityEndTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        if (System.currentTimeMillis() > exchangeEndTime) {
            throw new ServiceException(ErrorCode.SERVER_EXCEPTION, "活动已下线～");
        }
        List<DrawPoolItemDTO> drawPoolItems = probStrategy.getDrawPoolItems(context);
        context.setPrizeItemList(drawPoolItems);
    }


    @Override
    protected void deductResource(DrawContext context) {
        DrawParam drawParam = context.getDrawParam();
        TreasureVO treasureInfo = getTreasureInfo(context);
        removePoints(drawParam.getUid(), treasureInfo.getNeedPoints());
    }

    @Override
    protected void doCallback(DrawContext context) {
        TreasureVO treasureInfo = getTreasureInfo(context);
        List<DrawPoolItemDTO> prizeItemList = context.getPrizeItemList();
        if (CollUtil.isEmpty(prizeItemList)) {
            log.warn("用户抽奖结果为空,{}", JSON.toJSONString(prizeItemList));
            throw new ServiceException(ErrorCode.INVALID_PARAM, "用户抽奖结果为空");
        }
        DrawPoolItemDO drawPoolItemDO = prizeItemList.get(0).getDrawPoolItemDO();
        // 发送消息提醒
        notifyComponent.npcNotify(MDCUtil.getCurUserIdByMdc(), String.format(GalaxyAdventureConstant.TREASURE_MSG, treasureInfo.getName(), drawPoolItemDO.getItemName()));
        // 记录抽奖日志
        logComponent.putDrawLog(BaseParam.ofMDC(), GalaxyAdventureConstant.TREASURE_OPEN, prizeItemList);
        // 上报埋点
        trackManager.allActivityLottery(MDCUtil.getCurUserIdByMdc(), treasureInfo.getPoolCode(), treasureInfo.getTrackName(), drawPoolItemDO.getItemKey(), 1, drawPoolItemDO.getItemValueGold());
    }

    /**
     * 获取宝箱信息
     * @return /
     */
    public TreasureVO getTreasureInfo(DrawContext context) {
        OpenTreasureVO openTreasureVO = JSON.parseObject(context.getDrawParam().getExtValue(), OpenTreasureVO.class);
        Integer treasureIndex = openTreasureVO.getTreasureIndex();
        TreasureVO treasureVO = GalaxyAdventureConstant.treasureMap.get(treasureIndex);
        if (Objects.isNull(treasureVO)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "宝箱序列不存在，index" + treasureIndex);
        }
        return treasureVO;
    }

}
