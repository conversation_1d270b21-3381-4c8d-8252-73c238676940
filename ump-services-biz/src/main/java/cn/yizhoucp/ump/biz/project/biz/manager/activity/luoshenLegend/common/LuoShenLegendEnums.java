package cn.yizhoucp.ump.biz.project.biz.manager.activity.luoshenLegend.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.LocalTime;

/**
 * 洛神赋枚举类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 19:24 2025/1/14
 */
public class LuoShenLegendEnums {



    @AllArgsConstructor
    @Getter
    public enum GoddessEnum {
        PLUM_GOD("梅花神", "plum_god", LuoShenLegendConstant.TARGET_REWARD_1, LocalTime.of(0, 0), LocalTime.of(2, 59, 59)),
        PEACH_GOD("桃花神", "peach_god", LuoShenLegendConstant.TARGET_REWARD_1, LocalTime.of(3, 0), LocalTime.of(5, 59, 59)),
        APRICOT_GOD("杏花神", "apricot_god", LuoShenLegendConstant.TARGET_REWARD_1, LocalTime.of(6, 0), LocalTime.of(8, 59, 59)),
        OSMANTHUS_GOD("桂花神", "osmanthus_god", LuoShenLegendConstant.TARGET_REWARD_2, LocalTime.of(9, 0), LocalTime.of(11, 59, 59)),
        POMEGRANATE_GOD("石榴花神", "pomegranate_god", LuoShenLegendConstant.TARGET_REWARD_2, LocalTime.of(12, 0), LocalTime.of(14, 59, 59)),
        HIBISCUS_GOD("芙蓉花神", "hibiscus_god", LuoShenLegendConstant.TARGET_REWARD_2, LocalTime.of(15, 0), LocalTime.of(17, 59, 59)),
        PEONY_GOD("牡丹花神", "peony_god", LuoShenLegendConstant.TARGET_REWARD_3, LocalTime.of(18, 0), LocalTime.of(20, 59, 59)),
        LOTUS_GOD("荷花神", "lotus_god", LuoShenLegendConstant.TARGET_REWARD_3, LocalTime.of(21, 0), LocalTime.of(23, 59, 59)),
        CAMELLIA_GOD("山茶花神", "camellia_god", LuoShenLegendConstant.TARGET_REWARD_4, LocalTime.MIN, LocalTime.MAX);

        private final String godName;
        private final String godCode;
        private final String targetReward;
        private final LocalTime startTime;
        private final LocalTime endTime;

    }

}
