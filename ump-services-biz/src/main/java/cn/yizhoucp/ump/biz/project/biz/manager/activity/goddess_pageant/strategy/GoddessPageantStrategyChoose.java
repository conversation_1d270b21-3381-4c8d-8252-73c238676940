package cn.yizhoucp.ump.biz.project.biz.manager.activity.goddess_pageant.strategy;

import cn.yizhoucp.ump.biz.project.biz.manager.activity.goddess_pageant.common.GoddessPageantEnums;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.StrategyManager;
import org.springframework.stereotype.Component;


/**
 * 女神评选策略选择类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 15:19 2025/3/18
 */
@Component
public class GoddessPageantStrategyChoose extends StrategyManager<GoddessPageantEnums.ButtonEnum, ExecutableStrategy> {
}
