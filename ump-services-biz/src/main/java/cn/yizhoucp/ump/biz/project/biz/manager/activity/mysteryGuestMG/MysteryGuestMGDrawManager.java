package cn.yizhoucp.ump.biz.project.biz.manager.activity.mysteryGuestMG;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.jimu.draw.inner.DrawLogItem;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.log.LogComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.AbstractDrawTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawContext;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawLogParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.doDrawStrategy.common.ProbStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawLogDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.manager.activity.mysteryGuestMG.MysteryGuestMGYouConstant.*;


@Service
@Slf4j
public class MysteryGuestMGDrawManager extends AbstractDrawTemplate {

    @Resource
    private RedisManager redisManager;

    @Resource
    private ProbStrategy probStrategy;

    @Resource
    private LogComponent logComponent;

    @Resource
    private MysteryGuestMGTrackManager trackManager;

    @Resource
    private MysteryGuestMGYouConstant mysteryGuestMGYouConstant;

    @Override
    protected void resourceCheck(DrawContext context) {
        DrawParam drawParam = context.getDrawParam();
        String poolCode = drawParam.getPoolCode();
        Integer poolTimes = drawParam.getTimes();
        // 校验奖池
        if (Objects.isNull(poolCode) || Objects.isNull(poolTimes) || !poolCode.equals(POOL_CODE)) {
            log.warn("Exception prize pool poolCode");
            throw new ServiceException(ErrorCode.INVALID_PARAM, "奖池错误!");
        }

        // 校验
        String key = String.format(USER_FRAGMENT_KEY, drawParam.getUid());
        long itemNum = Optional.ofNullable(redisManager.getLong(key)).orElse(0L);
        if (itemNum <= 0 || itemNum < poolTimes * 10) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "喧嚣碎片不足哦～");
        }
    }


    @Override
    protected void deductResource(DrawContext context) {
        DrawParam drawParam = context.getDrawParam();
        String key = String.format(USER_FRAGMENT_KEY, drawParam.getUid());
        redisManager.decrLong(key, drawParam.getTimes() * 10, DateUtil.ONE_MONTH_SECOND);
    }


    @Override
    protected void draw(DrawContext context) {
        List<DrawPoolItemDTO> drawPoolItemDTOList = probStrategy.getDrawPoolItems(context.getDrawPoolItemDOS(),
                context.getDrawParam().getTimes(),
                Boolean.TRUE);
        context.setPrizeItemList(drawPoolItemDTOList);
    }


    @Override
    protected void doCallback(DrawContext context) {
        DrawParam drawParam = context.getDrawParam();
        // 抽奖结果留存
        logComponent.putDrawLog(drawParam.getBaseParam(), drawParam.getActivityCode(), context.getPrizeItemList());

        // 用户在活动中抽奖开奖时 埋点
        String poolCode = drawParam.getPoolCode();
        for (DrawPoolItemDTO drawPoolItemDTO : context.getPrizeItemList()) {
            DrawPoolItemDO drawPoolItemDO = drawPoolItemDTO.getDrawPoolItemDO();
            Integer targetTimes = drawPoolItemDTO.getTargetTimes();
            Long awardAmount = drawPoolItemDO.getItemValueGold() * targetTimes;

            // 一次抽奖多个参数分别传递
            String itemKey = drawPoolItemDO.getItemKey();
            Long uid = drawParam.getUid();
            trackManager.allActivityLottery(uid, poolCode, itemKey, awardAmount, targetTimes);
            log.info("trackManager allActivityLottery argument uid:{} --> targetTimes:{}, awardAmount:{}", uid, targetTimes, awardAmount);

            // 处理喧嚣剧场
            PersonEnum personEnum = PersonEnum.getBySendKey(itemKey);
            if (Objects.nonNull(personEnum)) {
                mysteryGuestMGYouConstant.incrPerson(uid, personEnum, Long.valueOf(targetTimes));
            }
        }
    }

    @Override
    protected List<DrawLogItem> drawLogWrapper(DrawLogParam param, List<DrawLogDO> drawLogDOList) {
        List<DrawLogItem> drawLogItemList = drawLogDOList.stream().map(drawLogDO -> {
            DrawPoolItemDTO drawPoolItemDTO = JSON.parseObject(drawLogDO.getLogJson(), DrawPoolItemDTO.class);
            DrawPoolItemDO drawPoolItemDO = drawPoolItemDTO.getDrawPoolItemDO();
            return DrawLogItem.builder()
                    .icon(drawPoolItemDO.getItemIcon())
                    .valueGold(drawPoolItemDO.getItemValueGold())
                    .text(drawPoolItemDO.getItemName())
                    .itemNum(drawPoolItemDO.getItemNum() * drawPoolItemDTO.getTargetTimes())
                    .time(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm").format(drawLogDO.getCreateTime())).build();
        }).collect(Collectors.toList());
        log.debug("drawLogItemList {}", JSON.toJSONString(drawLogItemList));
        Collections.reverse(drawLogItemList);

        return drawLogItemList;
    }
}
