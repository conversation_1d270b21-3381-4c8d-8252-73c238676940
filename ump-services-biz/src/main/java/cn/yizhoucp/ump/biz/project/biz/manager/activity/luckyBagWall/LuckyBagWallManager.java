package cn.yizhoucp.ump.biz.project.biz.manager.activity.luckyBagWall;


import cn.yizhoucp.family.api.client.FamilyFeignService;
import cn.yizhoucp.family.api.dto.family.FamilyInfoDTO;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.enums.EnvType;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.enums.ActivityCheckListEnum;
import cn.yizhoucp.ump.api.vo.activity.luckyBagWall.IndexVO;
import cn.yizhoucp.ump.api.vo.activity.luckyBagWall.TradeVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.PrizeItem;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ActivityReportManager;
import cn.yizhoucp.ump.biz.project.biz.manager.commonActivity.ActivityStatusManager;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityTimeUtil;
import cn.yizhoucp.ump.biz.project.common.ActivityComponent;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.UserMissionDO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bytedance.tester.utils.MapUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LuckyBagWallConstant.*;


/**
 * 福袋墙活动
 *
 * @author: lianghu
 */
@Slf4j
@Service
public class LuckyBagWallManager implements ActivityComponent {

    @Resource
    private RedisManager redisManager;
    @Resource
    private RedissonClient redissonClient;
    @Value("${spring.profiles.active}")
    private String env;
    @Resource
    private FamilyFeignService familyFeignService;
    @Resource
    private ActivityReportManager activityReportManager;
    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private ActivityStatusManager activityStatusManager;

    /**
     * 获取首页信息
     *
     * @param param
     * @return
     */
    public IndexVO getIndex(BaseParam param) {
        // 库存初始化
        initStock(param);
        // 获取是否加入家族
        FamilyInfoDTO family = familyFeignService.findFamilyInfoByUid(param.getUid(), param.getAppId()).successData();
        // 获取关联 ID
        String relationId = Objects.nonNull(family) ? family.getId().toString() : getChatRoomId();
        // 获取用户福袋墙
        Map<Object, Object> prizeKeys = redisManager.hmget(String.format(USER_WALL, param.getUid()));
        // 补充用户数据
        List<PrizeItem> prizeWall = getUserWallTemplate();
        prizeWall.stream().filter(p -> Objects.nonNull(prizeKeys.get(p.getPrizeKey()))).forEach(p -> p.setPrizeNum(1));
        // 按价值金币排序
        prizeWall = prizeWall.stream().sorted(Comparator.comparing(PrizeItem::getValueGold).reversed()).collect(Collectors.toList());
        // 补充奖品信息
        List<PrizeItem> tradePrize = getTradePrizeTemplate();
        tradePrize.stream().forEach(p -> p.setPrizeNum(Optional.ofNullable((Integer) redisManager.hget(String.format(PRIZE_STOCK, param.getUid()), p.getPrizeKey())).orElse(0)));
        // 获取兑换状态
        List<Integer> statusList = getStatusList(param);
        return IndexVO.builder()
                .hasJoinFamily(Objects.nonNull(family))
                .relationId(relationId)
                .prizes(prizeWall)
                .stocks(tradePrize)
                .statusList(statusList).build();
    }

    /**
     * 更新礼物墙
     *
     * @param param
     * @param bizParam
     * @return
     */
    public Boolean drawHandle(BaseParam param, JSONObject bizParam) {
        if (!activityStatusManager.activityIsEnable(param, getActivityCode())) {
            return Boolean.FALSE;
        }
        log.debug("luckyBagWall 礼物墙处理 uid:{}, param:{}", param.getUid(), JSON.toJSONString(bizParam));
        // 解析抽取福袋结果
        List<DrawPoolItemDO> prizes = bizParam.getJSONArray("prizeList").toJavaList(DrawPoolItemDO.class);
        // 数据结构转化
        Map<Object, Object> userWall = prizes.stream().filter(p -> ACTIVITY_GIFT_SET.contains(p.getItemKey())).collect(Collectors.toMap(DrawPoolItemDO::getItemKey, v1 -> System.currentTimeMillis()));
        log.info("luckyBagWall 更新礼物墙 uid:{}, param:{}", param.getUid(), JSON.toJSONString(bizParam));
        // 上墙
        redisManager.hmset(String.format(USER_WALL, param.getUid()), userWall, RedisManager.ONE_DAY_SECONDS * 28);
        return Boolean.TRUE;
    }

    /**
     * 兑换
     *
     * @param param
     * @param code
     * @return
     */
    public TradeVO trade(BaseParam param, String code) {
        if (!activityStatusManager.activityIsEnable(param, getActivityCode())) {
            throw new ServiceException(ErrorCode.MISS_PARAM, "活动已结束哦～");
        }

        if (StringUtils.isBlank(code) || (!"MHHH_GIFT".equals(code) && !"CLCH_GIFT".equals(code))) {
            throw new ServiceException(ErrorCode.MISS_PARAM);
        }

        PrizeItem prizeItem;
        RLock lock = redissonClient.getLock(String.format(TRADE_LOCK, param.getUid()));
        lock.lock();
        try {
            // 验证礼物墙
            Map<Object, Object> userWall = getUserWall(param);
            if (!tradeCheck(code, userWall)) {
                throw new ServiceException(ErrorCode.MISS_PARAM, "礼物墙尚未集齐哦～");
            }

            // 获取兑换礼物信息
            prizeItem = getTradePrize(code);

            // 库存验证
            if (Optional.ofNullable((Integer) redisManager.hget(String.format(PRIZE_STOCK, param.getUid()), code)).orElse(0) < 1) {
                throw new ServiceException(ErrorCode.MISS_PARAM, "库存不足～");
            }

            // 扣件库存
            redisManager.hdecr(String.format(PRIZE_STOCK, param.getUid()), code, 1);

            // 更新礼物墙
            redisManager.hdel(String.format(USER_WALL, param.getUid()), getCheckSet(code).toArray());

            // 埋点上报
//            activityReportManager.receiveReport(param, getReportActivityCode(), "trade", prizeItem.getPrizeKey(), prizeItem.getValueGold().longValue());

            // 下发奖励
            sendPrizeManager.sendGift(param.getAppId(), prizeItem.getPrizeKey(), param.getUid(), 1L, 15, "ACTIVITY_DEFAULT");
        } finally {
            lock.unlock();
        }

        return TradeVO.builder()
                .indexVO(getIndex(param))
                .prizeItem(prizeItem)
                .build();
    }

    private Map<Object, Object> getUserWall(BaseParam param) {
        return Optional.ofNullable(redisManager.hmget(String.format(USER_WALL, param.getUid()))).orElse(new HashMap<>());
    }

    private Map<Object, Object> deductCheckKey(String code, Map<Object, Object> param) {
        Set<String> checkSet = getCheckSet(code);
        for (String key : checkSet) {
            param.remove(key);
        }
        return param;
    }

    private Boolean tradeCheck(String code, Map<Object, Object> userWall) {
        if (MapUtils.isEmpty(userWall)) {
            return Boolean.FALSE;
        }
        // 验证礼物墙
        Set<String> checkSet = getCheckSet(code);
        for (String key : checkSet) {
            if (Objects.isNull(userWall.get(key))) {
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }

    private PrizeItem getTradePrize(String code) {
        PrizeItem result;
        switch (code) {
            case "MHHH_GIFT":
                result = PrizeItem.builder().prizeName("梦幻花海").prizeIcon("https://design-platform-cdn.yizhoucp.cn/gift-image/2021-10/1633747585600425.png").prizeKey("MHHH_GIFT").valueGold(3344).prizeNum(0).build();
                break;
            case "CLCH_GIFT":
                result = PrizeItem.builder().prizeName("苍龙出海").prizeIcon("https://design-platform-cdn.yizhoucp.cn/gift-image/2022-11/1668581168151975.png").prizeKey("CLCH_GIFT").valueGold(29999).prizeNum(0).build();
                break;
            default:
                throw new ServiceException(ErrorCode.MISS_PARAM);
        }
        return result;
    }

    private Boolean initStock(BaseParam param) {
        if (redisManager.setnx(String.format(PRIZE_STOCK_INIT, ActivityTimeUtil.getToday(getActivityCode()), param.getUid()), System.currentTimeMillis(), RedisManager.ONE_DAY_SECONDS * 3)) {
            redisManager.hset(String.format(PRIZE_STOCK, param.getUid()), "MHHH_GIFT", 1, RedisManager.ONE_DAY_SECONDS * 3);
            redisManager.hset(String.format(PRIZE_STOCK, param.getUid()), "CLCH_GIFT", 999999, RedisManager.ONE_DAY_SECONDS * 3);
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public String getActivityCode() {
        return ActivityCheckListEnum.LUCKY_BAG_WALL.getCode();
    }

    private String getReportActivityCode() {
        return "collect_lucky_bags_to_exchange_for_dragon";
    }

    private Set<String> getCheckSet(String code) {
        if ("MHHH_GIFT".equals(code)) {
            return MMHHCheckSet;
        } else if ("CLCH_GIFT".equals(code)) {
            return CLCHCheckSet;
        } else {
            return null;
        }
    }

    private String getChatRoomId() {
        EnvType envType = EnvType.convertFormEnv(env);
        if (Objects.isNull(envType)) {
            envType = EnvType.DEV;
        }
        switch (envType) {
            case LOCAL:
            case DEV:
                return "61846";
            case TEST:
                return "61093";
            case PRE:
            case PROD:
                return "11457107";
            default:
                log.error("未接入环境 env:{}", env);
                return "";
        }
    }

    private List<Integer> getStatusList(BaseParam param) {
        List<Integer> result = Lists.newArrayList(0, 0);
        Map<Object, Object> userWall = getUserWall(param);
        if (tradeCheck("MHHH_GIFT", userWall) && Optional.ofNullable((Integer) redisManager.hget(String.format(PRIZE_STOCK, param.getUid()), "MHHH_GIFT")).orElse(0) >= 1) {
            result.set(0, 1);
        }
        if (tradeCheck("CLCH_GIFT", userWall)) {
            result.set(1, 1);
        }
        return result;
    }

    private List<PrizeItem> getUserWallTemplate() {
        List<PrizeItem> result = Lists.newArrayList();
        for (PrizeItem i : prizeWallTemplate) {
            PrizeItem r = new PrizeItem();
            BeanUtils.copyProperties(i, r);
            result.add(r);
        }
        return result;
    }

    private List<PrizeItem> getTradePrizeTemplate() {
        List<PrizeItem> result = Lists.newArrayList();
        for (PrizeItem i : tradePrizeTemplate) {
            PrizeItem r = new PrizeItem();
            BeanUtils.copyProperties(i, r);
            result.add(r);
        }
        return result;
    }
}
