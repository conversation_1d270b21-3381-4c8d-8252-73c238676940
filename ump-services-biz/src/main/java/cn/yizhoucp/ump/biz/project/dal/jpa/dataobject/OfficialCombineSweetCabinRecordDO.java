package cn.yizhoucp.ump.biz.project.dal.jpa.dataobject;

import cn.yizhoucp.ump.biz.project.biz.enums.officialCombine.CabinRecordTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 官宣甜蜜小屋日志信息（甜蜜足迹）
 *
 * <AUTHOR>
 */
@EntityListeners(value = AuditingEntityListener.class)
@Table(name = "official_combine_sweet_cabin_record")
@Entity
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OfficialCombineSweetCabinRecordDO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    /** 日期 */
    private String curDate;
    /** 小屋id */
    private Long cabinId;
    /** 用户id */
    private Long uid;
    /** 类型 {@link CabinRecordTypeEnum} */
    private String type;
    /** 关联值（礼物 code、打扫/装修次数、收取恋屋币数量、甜蜜榜单排名） */
    private String relationValue;
    /** 扩展字段（甜蜜值、靓屋值、礼物数量） */
    private String extend;
    @CreatedDate
    private LocalDateTime createTime;
    @LastModifiedDate
    private LocalDateTime updateTime;

}
