package cn.yizhoucp.ump.biz.project.biz.manager.activity.music_for_heart;

import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.AbstractDrawTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 随悦而动抽奖类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 14:07 2025/4/7
 */
@Slf4j
@Service
public class MusicForHeartDrawManager extends AbstractDrawTemplate {

    @Override
    protected void resourceCheck(DrawContext context) {

    }

    @Override
    protected void deductResource(DrawContext context) {

    }

    @Override
    protected void doCallback(DrawContext context) {

    }
}
