package cn.yizhoucp.ump.biz.project.biz.manager.activity.dragon_Boat_Festival_2025.common;

import cn.yizhoucp.ump.biz.project.biz.manager.activity.dragon_Boat_Festival_2025.strategy.DragonBoatFestival2025Claimed;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.seasonalFestival.strategy.SeasonalFestivalDemoStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.StrategyEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * 2025端午节枚举类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 17:21 2025/5/29
 */
public class DragonBoatFestival2025Enums {

    @AllArgsConstructor
    @Getter
    public enum ButtonEnum implements StrategyEnum {
        DEMO_BUTTON("demo_button", "seasonalFestivalDemoStrategy", SeasonalFestivalDemoStrategy.class),
        CLAIMED("claimed", "dragonBoatFestival2025Claimed", DragonBoatFestival2025Claimed.class),
        ;
        private final String strategyName;
        private final String beanName;
        private final Class<? extends ExecutableStrategy> strategyClass;
    }

}
