package cn.yizhoucp.ump.biz.project.dal.jpa.dao;

import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.UserLuckDrawInfoDO;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import java.util.List;

public interface UserLuckDrawInfoJpaDAO extends CrudRepository<UserLuckDrawInfoDO, Long> {

    /**
     * 查询指定用户的数据
     *
     * @param userId
     * @param appId
     * @return
     */
    UserLuckDrawInfoDO findByUserIdAndAppId(Long userId, Long appId);

    List<UserLuckDrawInfoDO> findByUserIdIn(List<Long> userIds);

    /** 获取坟爷数据 */
    @Query(value = "select * from lucky_bag_user_draw_info where total_input <= ?1  order by update_time desc limit ?2,?3 ", nativeQuery = true)
    List<UserLuckDrawInfoDO> findByTotalCoinAndPage(Long totalCoin, Integer pageIndex, Integer pageSize);

    @Query(value = "select count(1) from lucky_bag_user_draw_info where total_input <= ?1", nativeQuery = true)
    Integer countByTotalCoinAndPage(Long totalCoin);

}
