package cn.yizhoucp.ump.biz.project.web.rest.controller.activity;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.landingservices.AdminPageVO;
import cn.yizhoucp.ump.api.enums.ActivityTemplateEnum;
import cn.yizhoucp.ump.api.vo.activity.twins.ExchangeResultVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.DrawReturn;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.twins.TwinsDrawManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.twins.TwinsDrawV2Manager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.twins.TwinsPageManager;
import cn.yizhoucp.ump.biz.project.biz.manager.userActivity.ActivityManager;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityInfoUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ActivityDO;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import java.util.Objects;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.TwinsConstant.ACTIVITY_CODE;

/**
 * <AUTHOR>
 * @Date 2023/5/31 15:42
 * @Version 1.0
 */
@RestController
@Slf4j
public class TwinsController {

    @Resource
    private TwinsPageManager twinsPageManager;
    @Resource
    private TwinsDrawV2Manager twinsDrawV2Manager;
    @Resource
    private ActivityManager activityManager;

    @GetMapping("/api/inner/activity/twins/take-prize")
    public Result<Boolean> takePrize(BaseParam param, String key) {
        return Result.successResult(twinsPageManager.takePrize(param, key));
    }

    /**
     * 点亮星座
     *
     * @param param
     * @param activityCode
     * @param constellationKey
     * @return
     */
    @GetMapping("/api/inner/activity/twins/light-up-constellation")
    public Result<Boolean> lightUpConstellation(BaseParam param, String activityCode, String constellationKey) {
        return Result.successResult(twinsPageManager.lightUpConstellation(param, activityCode, constellationKey));
    }

    @GetMapping("/api/inner/activity/twins/exchange-gift")
    public Result<Long> exchangeGift(BaseParam param, String activityCode, String giftKey) {
        return Result.successResult(twinsPageManager.exchangeGift(param, activityCode, giftKey));
    }

    @GetMapping("/api/inner/activity/twins/exchange-astrological-value")
    public Result<ExchangeResultVO> exchangeAstrologicalValue(BaseParam param, String activityCode, Integer nums) {
        return Result.successResult(twinsPageManager.exchangeAstrologicalValue(param, activityCode, nums));
    }

    @GetMapping("/api/inner/activity/twins/astrological-reword")
    public Result<AdminPageVO> astrologicalReword(BaseParam param, String activityCode, Integer page, Integer pageSize) {
        return Result.successResult(twinsPageManager.astrologicalReword(param, activityCode, page, pageSize));
    }

    @GetMapping("/api/inner/activity/twins/lucky-time-person")
    public Result<Boolean> luckyTimePerson(BaseParam param) {
        return Result.successResult(twinsPageManager.luckyTimePerson(param));
    }

    @GetMapping("/api/inner/activity/twins/lucky-time")
    public Result<Boolean> luckyTime(BaseParam param) {
        return Result.successResult(twinsPageManager.luckyTime(param));
    }

    @RequestMapping("/api/inner/activity/twins/draw")
    public Result<DrawReturn> draw(String type, String poolCode, Integer times, String extValue) {
        setActivityInfo(ACTIVITY_CODE);
        return RestBusinessTemplate.executeWithoutTransaction(() -> twinsDrawV2Manager.draw(DrawParam.builder()
                .unionId(MDCUtil.getCurUnionIdByMdc())
                .appId(MDCUtil.getCurAppIdByMdc())
                .uid(MDCUtil.getCurUserIdByMdc())
                .activityCode(ACTIVITY_CODE)
                .type(type)
                .poolCode(poolCode)
                .times(times)
                .extValue(extValue).build()));
    }

    private void setActivityInfo(Object param) {
        String activityCode;
        try {
            activityCode = (String) param;
        } catch (Exception e) {
            log.error("活动 key 异常 param:{}", JSONObject.toJSONString(param));
            return;
        }
        ActivityDO info = activityManager.getActivityInfo(BaseParam.ofMDC(), activityCode);
        if (Objects.isNull(info)) {
            log.error("活动 key 配置错误 code:{}, unionId:{}, appId:{}", activityCode, MDCUtil.getCurUnionIdByMdc(), MDCUtil.getCurAppIdByMdc());
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        ActivityInfoUtil.set(info);
    }

}
