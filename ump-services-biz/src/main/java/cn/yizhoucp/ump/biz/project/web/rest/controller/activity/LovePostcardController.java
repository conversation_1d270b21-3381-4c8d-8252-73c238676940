package cn.yizhoucp.ump.biz.project.web.rest.controller.activity;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.auth.SecurityUser;
import cn.yizhoucp.ms.core.base.auth.SecurityUtils;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ms.core.vo.CommonListVO;
import cn.yizhoucp.ms.core.vo.CommonResultVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.lovePostcard.LovePostcardManager;
import cn.yizhoucp.ump.api.vo.activity.lovePostcard.IndexVO;
import cn.yizhoucp.ump.api.vo.activity.lovePostcard.PostcardIndexVO;
import cn.yizhoucp.ump.api.vo.activity.lovePostcard.SendPostcardIndexVO;
import cn.yizhoucp.ump.api.vo.activity.lovePostcard.SendPostcardVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;


/**
 * 国庆-明信片活动
 *
 * <AUTHOR>
 */
@RequestMapping("/api/inner/activity/love-postcard")
@RestController
public class LovePostcardController {

    @Resource
    LovePostcardManager lovePostcardManager;

    /**
     * 主页信息
     *
     * @return MidAutumn2024PreIndexVO
     */
    @GetMapping("/index")
    public Result<IndexVO> index() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            SecurityUser user = SecurityUtils.getCurrentUser();
            if (Objects.isNull(user)) {
                return null;
            }
            return lovePostcardManager.index(user.getUserId());
        });
    }

    /**
     * 获取收发明信片记录
     *
     * @param pageIndex 页码
     * @param pageSize  条数
     * @param send      true-寄出明信片；false-收到明信片
     * @return CommonListVO
     */
    @GetMapping("/postcard-record")
    public Result<CommonListVO> postcardRecord(int pageIndex, int pageSize, Boolean send) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            SecurityUser user = SecurityUtils.getCurrentUser();
            if (Objects.isNull(user)) {
                return null;
            }
            return lovePostcardManager.postcardRecord(user.getUserId(), pageIndex, pageSize, send);
        });
    }

    /**
     * 查看明信片详情
     *
     * @param uid      对方id
     * @param id       明信片id
     * @return PostcardIndexVO
     */
    @GetMapping("/postcard-info")
    public Result<PostcardIndexVO> postcardInfo(Long uid, Long id) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            SecurityUser user = SecurityUtils.getCurrentUser();
            if (Objects.isNull(user)) {
                return null;
            }
            return lovePostcardManager.postcardInfo(user.getUserId(), uid, id);
        });
    }

    /**
     * 获取寄明信片页面信息
     *
     * @return CommonListVO
     */
    @GetMapping("/send-postcard-index")
    public Result<SendPostcardIndexVO> sendPostcardIndex() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            SecurityUser user = SecurityUtils.getCurrentUser();
            if (Objects.isNull(user)) {
                return null;
            }
            return lovePostcardManager.sendPostcardIndex(user.getUserId());
        });
    }

    /**
     * 寄出明信
     *
     * @param sendPostcard 寄出明信片内容
     * @return CommonResultVO
     */
    @PostMapping("/send-postcard")
    public Result<CommonResultVO> sendPostcard(@RequestBody SendPostcardVO sendPostcard) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> lovePostcardManager.sendPostcard(sendPostcard));
    }

    /**
     * "十一浪漫旅行"领取奖励任务
     *
     * @param uid 用户id
     * @return Boolean
     */
    @GetMapping("/complete-task")
    public Result<Boolean> completeTask(Long uid) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> lovePostcardManager.receiveRewardTask(uid));
    }

}
