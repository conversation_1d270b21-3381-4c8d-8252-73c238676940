package cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds;

import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.user.manager.UserFeignManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.common.EmotionalBondsEnums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.common.EmotionalBondsRedisManager;
import cn.yizhoucp.ump.biz.project.dal.mybatis.domain.EmotionalBondSummaryDO;
import cn.yizhoucp.ump.biz.project.dal.mybatis.service.EmotionalBondSummaryService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 情感纽带关系过期提醒管理类
 */
@Service
@Slf4j
public class EmotionalBondsReminderManager {

    @Resource
    private EmotionalBondSummaryService emotionalBondSummaryService;

    @Resource
    private EmotionalBondsRedisManager emotionalBondsRedisManager;

    @Resource
    private NotifyComponent notifyComponent;

    @Resource
    private UserFeignManager userFeignManager;

    /**
     * 处理即将过期的关系提醒
     * 查找3天内即将过期的关系，并发送提醒消息
     * @return 处理的关系数量
     */
    public int processExpiringRelationships() {
        log.info("开始处理即将过期的情感纽带关系提醒");
        if(!emotionalBondsRedisManager.reminderLock()){
            log.warn("关系过期提醒锁被占用，请稍后再试");
            return 0;
        }

        // 计算3天后的日期
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 3);
        Date threeDaysLater = calendar.getTime();
        
        // 计算2天后的日期（用于确定范围）
        calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 2);
        Date twoDaysLater = calendar.getTime();
        
        // 查询3天内即将过期的关系
        LambdaQueryWrapper<EmotionalBondSummaryDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EmotionalBondSummaryDO::getStatus, 1) // 已激活状态
                .gt(EmotionalBondSummaryDO::getExpireTime, twoDaysLater) // 大于2天后
                .le(EmotionalBondSummaryDO::getExpireTime, threeDaysLater); // 小于等于3天后
        
        List<EmotionalBondSummaryDO> expiringRelationships = emotionalBondSummaryService.list(queryWrapper);
        
        if (expiringRelationships == null || expiringRelationships.isEmpty()) {
            log.info("没有找到即将过期的情感纽带关系");
            return 0;
        }
        
        log.info("找到 {} 个即将过期的情感纽带关系", expiringRelationships.size());
        
        // 收集所有需要获取的用户ID
        List<Long> userIds = expiringRelationships.stream()
                .flatMap(relationship -> Stream.of(relationship.getUserId(), relationship.getOppositeId()))
                .distinct()
                .collect(Collectors.toList());
        
        // 获取用户信息
        Map<Long, UserVO> userMap = userIds.stream()
                .map(id -> userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), id))
                .filter(Objects::nonNull) // 过滤掉空值
                .collect(Collectors.toMap(
                        UserVO::getId,
                        user -> user,
                        (existing, replacement) -> existing // 保留第一个值，忽略重复键
                ));
        
        int notifiedCount = 0;
        
        // 处理每个即将过期的关系
        for (EmotionalBondSummaryDO relationship : expiringRelationships) {
            try {
                // 获取关系信息
                Long userId = relationship.getUserId();
                Long oppositeId = relationship.getOppositeId();
                String effectKey = relationship.getEffectKey();
                
                // 获取角色对信息
                EmotionalBondsEnums.RolePairEnum rolePairEnum = EmotionalBondsEnums.RolePairEnum.getRolePairEnumByName(effectKey);
                if (rolePairEnum == null) {
                    log.warn("未找到对应的角色对信息 effectKey: {}", effectKey);
                    continue;
                }
                
                // 获取用户信息
                UserVO userA = userMap.get(userId);
                UserVO userB = userMap.get(oppositeId);
                
                if (userA == null || userB == null) {
                    log.warn("未找到用户信息 userId: {}, oppositeId: {}", userId, oppositeId);
                    continue;
                }
                
                // 为双方用户发送提醒
                notifiedCount += sendReminderToUser(relationship.getId(), userId, userB, rolePairEnum,relationship.getExpireTime());
                notifiedCount += sendReminderToUser(relationship.getId(), oppositeId, userA, rolePairEnum,relationship.getExpireTime());
            } catch (Exception e) {
                log.error("处理关系提醒时发生错误 relationshipId: {}", relationship.getId(), e);
            }
        }
        
        log.info("成功发送 {} 条关系过期提醒", notifiedCount);
        return notifiedCount;
    }
    
    /**
     * 向用户发送关系即将过期的提醒
     *
     * @param relationshipId 关系ID
     * @param userId         用户ID
     * @param partnerUser    伙伴用户信息
     * @param rolePairEnum   角色对信息
     * @param expireTime     过期时间
     * @return 是否发送成功 (1: 成功, 0: 失败或已提醒)
     */
    private int sendReminderToUser(Long relationshipId, Long userId, UserVO partnerUser, EmotionalBondsEnums.RolePairEnum rolePairEnum, Date expireTime) {
        // 检查用户是否已经收到提醒
        if (emotionalBondsRedisManager.isUserReminded(relationshipId, userId)) {
            log.info("用户已收到关系过期提醒 userId: {}, relationshipId: {}", userId, relationshipId);
            return 0;
        }
        
        try {
            log.info("开始发送关系过期提醒 userId: {}, relationshipId: {}", userId, relationshipId);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(expireTime);
            int dayOfMonth = calendar.get(Calendar.DAY_OF_MONTH);
            int hourOfDay = calendar.get(Calendar.HOUR_OF_DAY);
            // 发送提醒消息
            String message = String.format("亲爱的用户，您与「%s」的「%s」羁绊关系，在%s日的%s点就要过期了～快去重新选购吧～～",
                    partnerUser.getName(), rolePairEnum.getEffectName(),  dayOfMonth, hourOfDay);
            
            boolean notifyResult = notifyComponent.npcNotify(
                    ServicesAppIdEnum.lanling.getUnionId(),
                    userId,
                    message
            );
            
            if (notifyResult) {
                // 标记用户已收到提醒
                emotionalBondsRedisManager.markUserReminded(relationshipId, userId);
                log.info("成功发送关系过期提醒 userId: {}, relationshipId: {}", userId, relationshipId);
                return 1;
            } else {
                log.warn("发送关系过期提醒失败 userId: {}, relationshipId: {}", userId, relationshipId);
                return 0;
            }
        } catch (Exception e) {
            log.error("发送关系过期提醒时发生错误 userId: {}, relationshipId: {}", userId, relationshipId, e);
            return 0;
        }
    }
}
