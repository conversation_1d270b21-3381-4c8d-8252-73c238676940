package cn.yizhoucp.ump.biz.project.biz.manager.activity.floweringDream;

import cn.yizhoucp.ump.biz.project.biz.constant.activity.FloweringDreamConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.AbstractMissionTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.MissionContext;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class FloweringDreamMissionManager extends AbstractMissionTemplate {
    @Override
    protected Boolean doCheck(MissionContext context) {
        return null;
    }

    @Override
    protected Boolean init(MissionContext context) {
        return null;
    }

    @Override
    protected Boolean completedProcess(MissionContext context) {
        return null;
    }

    @Override
    public String getActivityCode() {
        return FloweringDreamConstant.ACTIVITY_CODE;
    }
}
