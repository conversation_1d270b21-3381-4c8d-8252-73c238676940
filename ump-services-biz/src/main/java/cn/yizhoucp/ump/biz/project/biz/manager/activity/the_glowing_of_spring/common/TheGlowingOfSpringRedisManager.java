package cn.yizhoucp.ump.biz.project.biz.manager.activity.the_glowing_of_spring.common;

import cn.hutool.core.text.CharSequenceUtil;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.util.AppUtil;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityRedisKeyGenerator;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.user.manager.UserFeignManager;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 春日灼灼redis管理类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 16:40 2025/3/7
 */
@Component
public class TheGlowingOfSpringRedisManager {
    @Resource
    private RedisManager redisManager;
    @Resource
    private ActivityRedisKeyGenerator activityRedisKeyGenerator;
    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    private UserFeignManager userFeignManager;

    public String getActivityCode() {
        return TheGlowingOfSpringConstant.ACTIVITY_CODE;
    }

    public Long getCollectItemCount(Long uid, Integer itemId) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(TheGlowingOfSpringConstant.COLLECT_ITEM_COUNT_KEY, getActivityCode(), uid.toString(), itemId.toString());
        return Optional.ofNullable(redisManager.getLong(key)).orElse(0L);
    }

    public void decrementCollectItemCount(Long uid, Integer itemId, Long count) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(TheGlowingOfSpringConstant.COLLECT_ITEM_COUNT_KEY, getActivityCode(), uid.toString(), itemId.toString());
        redisManager.decrLong(key, count, DateUtil.ONE_MONTH_SECOND);
    }

    public void incrementCollectItemCount(Long uid, Integer itemId, Long count) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(TheGlowingOfSpringConstant.COLLECT_ITEM_COUNT_KEY, getActivityCode(), uid.toString(), itemId.toString());
        redisManager.incrLong(key, count, DateUtil.ONE_MONTH_SECOND);
    }


    public Boolean getTaskReceive(Long uid, String taskCode) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(TheGlowingOfSpringConstant.TASK_RECEIVE_KEY, getActivityCode(), uid.toString(), taskCode);
        return redisManager.hasKey(key);
    }

    public void deleteTaskReceive(Long uid, String taskCode) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(TheGlowingOfSpringConstant.TASK_RECEIVE_KEY, getActivityCode(), uid.toString(), taskCode);
        redisManager.delete(key);
    }

    public Boolean setTaskReceive(Long uid, String taskCode) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(TheGlowingOfSpringConstant.TASK_RECEIVE_KEY, getActivityCode(), uid.toString(), taskCode);
        return redisManager.set(key, System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND);
    }

    public Long getItemCount(Long uid) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(TheGlowingOfSpringConstant.ITEM_COUNT_KEY, getActivityCode(), uid.toString());
        return Optional.ofNullable(redisManager.getLong(key)).orElse(0L);
    }

    public void decrementItemCount(Long uid, Long count) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(TheGlowingOfSpringConstant.ITEM_COUNT_KEY, getActivityCode(), uid.toString());
        redisManager.decrLong(key, count, DateUtil.ONE_MONTH_SECOND);
    }

    public void incrementItemCount(Long uid, Long count) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(TheGlowingOfSpringConstant.ITEM_COUNT_KEY, getActivityCode(), uid.toString());
        redisManager.incrLong(key, count, DateUtil.ONE_MONTH_SECOND);
    }


    public int getCurrentMeter(Long uid) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(TheGlowingOfSpringConstant.METER_KEY, getActivityCode(), uid.toString());
        return Optional.ofNullable(redisManager.getInteger(key)).orElse(0);
    }

    public void incrementCurrentMeter(Long uid, Long count) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(TheGlowingOfSpringConstant.METER_KEY, getActivityCode(), uid.toString());
        redisManager.incrLong(key, count, DateUtil.ONE_MONTH_SECOND);
    }


    public int getFlyItemCount(Long uid) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(TheGlowingOfSpringConstant.FLY_ITEM_COUNT_KEY, getActivityCode(), uid.toString());
        return Optional.ofNullable(redisManager.getInteger(key)).orElse(0);
    }

    public void incrementFlyItemCount(Long uid, Integer rewardCount) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(TheGlowingOfSpringConstant.FLY_ITEM_COUNT_KEY, getActivityCode(), uid.toString());
        redisManager.incrLong(key, rewardCount, DateUtil.ONE_MONTH_SECOND);
    }


    public void decrementFlyItemCount(Long uid, Integer flyCount) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(TheGlowingOfSpringConstant.FLY_ITEM_COUNT_KEY, getActivityCode(), uid.toString());
        redisManager.decrLong(key, flyCount, DateUtil.ONE_MONTH_SECOND);
    }


    public Boolean getNodeReceive(Long uid, String sceneCode) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(TheGlowingOfSpringConstant.NODE_RECEIVE_KEY, getActivityCode(), uid.toString(), sceneCode);
        return redisManager.hasKey(key);
    }

    public void refreshNodeReceive(Long uid, String sceneCode) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(TheGlowingOfSpringConstant.NODE_RECEIVE_KEY, getActivityCode(), uid.toString(), sceneCode);
        redisManager.delete(key);
    }


    public Boolean setNodeReceive(Long uid, String sceneCode) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(TheGlowingOfSpringConstant.NODE_RECEIVE_KEY, getActivityCode(), uid.toString(), sceneCode);
        return redisManager.set(key, System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND);
    }


    public ScenePrizeDO getScenePrize(String giftKey) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(TheGlowingOfSpringConstant.SHOP_CACHE_MAP, getActivityCode());
        String cache = redisManager.getString(key);
        if (CharSequenceUtil.isBlank(cache)) {
            List<String> taskList = Arrays.stream(TheGlowingOfSpringEnums.ExchangeStoreEnum.values()).map(TheGlowingOfSpringEnums.ExchangeStoreEnum::getStoreCode).collect(Collectors.toList());
            List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCodeList(1L, getActivityCode(), taskList);
            Map<String, ScenePrizeDO> map = scenePrizeDOList.stream().collect(Collectors.toMap(ScenePrizeDO::getPrizeValue, scenePrizeDO -> scenePrizeDO));
            redisManager.set(key, JSON.toJSONString(map), DateUtil.ONE_HOUR_SECOND);
            return map.get(giftKey);
        }
        Map<String, ScenePrizeDO> map = JSON.parseObject(cache, new TypeReference<Map<String, ScenePrizeDO>>() {
        });
        return map.get(giftKey);
    }

    public String getRankKey() {
        return activityRedisKeyGenerator.generateKeyWithActivityStartTime(TheGlowingOfSpringConstant.RANK_KEY, getActivityCode());
    }

    public Long getTopMyRank(Long uid) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(TheGlowingOfSpringConstant.MY_RANK_KEY, getActivityCode(), uid.toString());
        Set<ZSetOperations.TypedTuple<Object>> typedTuples = redisManager.reverseRangeByScoreWithScores(key, 0, Double.MAX_VALUE, 0, 10);
        if (typedTuples == null || typedTuples.isEmpty()) {
            return null;
        }

        for (ZSetOperations.TypedTuple<Object> tuple : typedTuples) {
            Long friendId = Long.valueOf(Objects.requireNonNull(tuple.getValue()).toString());
            if (!isUserBanned(friendId)) {
                return friendId;
            }
        }
        return null;
    }

    public void incrementMyRank(Long uid, Long toUid, Long value) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(TheGlowingOfSpringConstant.MY_RANK_KEY, getActivityCode(), uid.toString());
        redisManager.zIncrby(key, toUid, value.doubleValue(), DateUtil.ONE_MONTH_SECOND);
    }


    private boolean isUserBanned(Long userId) {
        UserVO userVO = userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), userId);
        return userVO == null;
    }

    private Long getFreshFlyNode(Long uid) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(TheGlowingOfSpringConstant.REFRESH_FLY_NODE_KEY, getActivityCode(), uid.toString());
        return Optional.ofNullable(redisManager.getLong(key)).orElse(0L);
    }

    public void refreshFlyNode(Long uid) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(TheGlowingOfSpringConstant.REFRESH_FLY_NODE_KEY, getActivityCode(), uid.toString());
        redisManager.incrLong(key, System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND);
    }

    public ScenePrizeDO getTaskScenePrize(String giftKey) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(TheGlowingOfSpringConstant.TASK_CACHE_MAP, getActivityCode());
        String cache = redisManager.getString(key);
        if (CharSequenceUtil.isBlank(cache)) {
            List<String> taskList = Arrays.stream(TheGlowingOfSpringEnums.TaskEnum.values()).map(TheGlowingOfSpringEnums.TaskEnum::getTaskCode).collect(Collectors.toList());
            List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCodeList(1L, getActivityCode(), taskList);
            Map<String, ScenePrizeDO> map = scenePrizeDOList.stream().collect(Collectors.toMap(ScenePrizeDO::getPrizeValue, scenePrizeDO -> scenePrizeDO));
            redisManager.set(key, JSON.toJSONString(map), DateUtil.ONE_HOUR_SECOND);
            return map.get(giftKey);
        }
        Map<String, ScenePrizeDO> map = JSON.parseObject(cache, new TypeReference<Map<String, ScenePrizeDO>>() {
        });
        return map.get(giftKey);
    }

    public void incrementTaskProgress(Long uid, String taskCode, Long count) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(TheGlowingOfSpringConstant.TASK_PROGRESS_KEY, getActivityCode(), uid.toString(), taskCode);
        redisManager.incrLong(key, count, DateUtil.ONE_MONTH_SECOND);
    }

    public Long getTaskProgress(Long uid, String taskCode) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(TheGlowingOfSpringConstant.TASK_PROGRESS_KEY, getActivityCode(), uid.toString(), taskCode);
        return Optional.ofNullable(redisManager.getLong(key)).orElse(0L);
    }
    public void deleteTaskProgress(Long uid, String taskCode) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(TheGlowingOfSpringConstant.TASK_PROGRESS_KEY, getActivityCode(), uid.toString(), taskCode);
        redisManager.delete(key);
    }



    public ScenePrizeDO getPeachBlossomFairyScenePrize(String giftKey) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(TheGlowingOfSpringConstant.PEACH_BLOSSOM_FAIRY_CACHE_MAP, getActivityCode());
        String cache = redisManager.getString(key);
        if (CharSequenceUtil.isBlank(cache)) {
            List<String> taskList = Arrays.stream(TheGlowingOfSpringEnums.CollectTaskItemEnum.values()).map(TheGlowingOfSpringEnums.CollectTaskItemEnum::getItemCode).collect(Collectors.toList());
            List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCodeList(1L, getActivityCode(), taskList);
            Map<String, ScenePrizeDO> map = scenePrizeDOList.stream().collect(Collectors.toMap(ScenePrizeDO::getPrizeValue, scenePrizeDO -> scenePrizeDO));
            redisManager.set(key, JSON.toJSONString(map), DateUtil.ONE_HOUR_SECOND);
            return map.get(giftKey);
        }
        Map<String, ScenePrizeDO> map = JSON.parseObject(cache, new TypeReference<Map<String, ScenePrizeDO>>() {
        });
        return map.get(giftKey);
    }

    public ScenePrizeDO getUnLockRankScenePrize(String giftKey) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(TheGlowingOfSpringConstant.UNLOCK_CACHE_MAP, getActivityCode());
        String cache = redisManager.getString(key);
        if (CharSequenceUtil.isBlank(cache)) {
            List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(1L, getActivityCode(), TheGlowingOfSpringConstant.UNLOCK_RANK_SCENE_CODE);
            Map<String, ScenePrizeDO> map = scenePrizeDOList.stream().collect(Collectors.toMap(ScenePrizeDO::getPrizeValue, scenePrizeDO -> scenePrizeDO));
            redisManager.set(key, JSON.toJSONString(map), DateUtil.ONE_HOUR_SECOND);
            return map.get(giftKey);
        }
        Map<String, ScenePrizeDO> map = JSON.parseObject(cache, new TypeReference<Map<String, ScenePrizeDO>>() {
        });
        return map.get(giftKey);
    }

    public void setUnLockRank(Long uid, Long toUid) {
        String bindId = AppUtil.splicUserId(uid, toUid);
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(TheGlowingOfSpringConstant.UNLOCK_RANK_KEY, getActivityCode(), bindId);
        redisManager.set(key, System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND);
    }

    public Boolean getUnLockRank(Long uid, Long toUid) {
        String bindId = AppUtil.splicUserId(uid, toUid);
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(TheGlowingOfSpringConstant.UNLOCK_RANK_KEY, getActivityCode(), bindId);
        return redisManager.hasKey(key);
    }

    public boolean getRewardClaimed(Long uid, String taskCode) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(TheGlowingOfSpringConstant.TASK_REWARD_CLAIMED, getActivityCode(), uid.toString(), taskCode);
        return redisManager.hasKey(key);
    }

    public void deleteRewardClaimed(Long uid, String taskCode) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(TheGlowingOfSpringConstant.TASK_REWARD_CLAIMED, getActivityCode(), uid.toString(), taskCode);
        redisManager.delete(key);
    }



    public boolean setRewardClaimed(Long uid, String taskCode) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(TheGlowingOfSpringConstant.TASK_REWARD_CLAIMED, getActivityCode(), uid.toString(), taskCode);
        return redisManager.set(key, System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND);
    }

    public void refreshCurrentMeters(Long uid) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(TheGlowingOfSpringConstant.METER_KEY, getActivityCode(), uid.toString());
        redisManager.delete(key);
    }
}
