package cn.yizhoucp.ump.biz.project.biz.manager.activity.timeCarnival;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.activity.eternalLove.DrawLogItemVO;
import cn.yizhoucp.ump.api.vo.activity.eternalLove.DrawLogTimeItemVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.DrawLogNewVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.inner.DrawLogItem;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.log.LogComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.AbstractDrawTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawContext;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawLogParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.doDrawStrategy.common.ProbStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawLogDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import cn.yizhoucp.user.manager.UserFeignManager;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Example;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.MidAutumn2023Constant.ACTIVITY_CODE;

@Service
@Slf4j
public class TimeCarnivalDrawManager extends AbstractDrawTemplate {
    @Resource
    private TimeCarnivalConstant timeCarnivalConstant;
    @Resource
    private ProbStrategy probStrategy;
    @Resource
    private LogComponent logComponent;
    @Resource
    private UserFeignManager userFeignManager;
    @Resource
    private NotifyComponent notifyComponent;
    @Resource
    private TimeCarnivalIndexManager timeCarnivalIndexManager;
    @Resource
    private RedisManager redisManager;
    @Resource
    private TimeCarnivalTrackManager timeCarnivalTrackManager;


    @Override
    protected void resourceCheck(DrawContext context) {
        String poolCode = context.getDrawParam().getPoolCode();
        Long uid = context.getDrawParam().getUid();
        if (TimeCarnivalConstant.GIFT_BOX.contains(poolCode)) {
            context.getDrawParam().setNoSendPrize(Boolean.TRUE);
        }
        if (TimeCarnivalConstant.DrawPoolCodeEnum.TIME_MACHINE_GAME.getCode().equals(poolCode)) {
            Long coins = timeCarnivalConstant.getCoins(uid);
            if (coins < context.getDrawParam().getTimes() * 10) {
                throw new ServiceException(ErrorCode.ACTIVITY_PARAM_ERROR, "游戏币不足");
            }
        }
        if (TimeCarnivalConstant.DrawPoolCodeEnum.LUMINOUS_CONCERT_HALL.getCode().equals(poolCode)) {
            String bandId = timeCarnivalConstant.getBandId(uid);
            Map<Object, Object> bandInfo = redisManager.hmget(TimeCarnivalConstant.createBandInfoKey(bandId));
            Long leaderId = Long.valueOf(bandInfo.get("leaderId").toString());
            if (!ObjectUtil.equals(leaderId, uid)) {
                throw new ServiceException(ErrorCode.ACTIVITY_PARAM_ERROR, "您不是队长，无法抽奖");
            }
            Long musicSheet = timeCarnivalConstant.getMusicSheetNum(bandId);
            if (musicSheet < context.getDrawParam().getTimes() * 10) {
                throw new ServiceException(ErrorCode.ACTIVITY_PARAM_ERROR, "曲谱不足");
            }
        }

    }

    @Override
    protected void draw(DrawContext context) {
        List<DrawPoolItemDTO> drawPoolItemDTOList = probStrategy.getDrawPoolItems(context.getDrawPoolItemDOS(),
                context.getDrawParam().getTimes(),
                Boolean.TRUE);
        context.setPrizeItemList(drawPoolItemDTOList);

    }

    @Override
    protected void deductResource(DrawContext context) {
        String poolCode = context.getDrawParam().getPoolCode();
        Long uid = context.getDrawParam().getUid();
        if (TimeCarnivalConstant.DrawPoolCodeEnum.TIME_MACHINE_GAME.getCode().equals(poolCode)) {
            timeCarnivalConstant.decrementCoins(uid, context.getDrawParam().getTimes().longValue() * 10);
        }
        if (TimeCarnivalConstant.DrawPoolCodeEnum.LUMINOUS_CONCERT_HALL.getCode().equals(poolCode)) {
            String bandId = timeCarnivalConstant.getBandId(uid);
            timeCarnivalConstant.decrementMusicSheetNum(bandId, context.getDrawParam().getTimes().longValue() * 10);
        }

    }

    @Override
    protected void doCallback(DrawContext context) {
        TimeCarnivalConstant.DrawPoolCodeEnum drawPoolCodeEnum = TimeCarnivalConstant.DrawPoolCodeEnum.getByCode(context.getDrawParam().getPoolCode());
        if (ObjectUtil.isNotNull(drawPoolCodeEnum)) {
            DrawParam drawParam = context.getDrawParam();
            List<DrawPoolItemDTO> prizeItemList = context.getPrizeItemList();
            BaseParam baseParam = drawParam.getBaseParam();
            logComponent.putDrawLog(baseParam, drawParam.getActivityCode(), prizeItemList);
            for (DrawPoolItemDTO drawPoolItemDTO : prizeItemList) {
                DrawPoolItemDO drawPoolItemDO = drawPoolItemDTO.getDrawPoolItemDO();
                Integer time = drawPoolItemDTO.getTargetTimes();
                timeCarnivalTrackManager.allActivityLottery(baseParam.getUid(), drawPoolItemDO.getPoolCode(), drawPoolItemDO.getItemKey(), drawPoolItemDO.getItemNum().longValue(), drawPoolItemDO.getItemValueGold() * time);
            }
        }
    }

    @Override
    protected List<DrawLogItem> drawLogWrapper(DrawLogParam param, List<DrawLogDO> drawLogDOList) {
        List<DrawLogItem> drawLogItemList = drawLogDOList.stream().map(drawLogDO -> {
            DrawPoolItemDTO drawPoolItemDTO = JSON.parseObject(drawLogDO.getLogJson(), DrawPoolItemDTO.class);
            DrawPoolItemDO drawPoolItemDO = drawPoolItemDTO.getDrawPoolItemDO();
            return DrawLogItem.builder()
                    .icon(drawPoolItemDO.getItemIcon())
                    .valueGold(drawPoolItemDO.getItemValueGold())
                    .text(drawPoolItemDO.getItemName())
                    .itemNum(drawPoolItemDO.getItemNum() * drawPoolItemDTO.getTargetTimes())
                    .time(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm").format(drawLogDO.getCreateTime())).build();
        }).collect(Collectors.toList());
        log.debug("drawLogItemList {}", JSON.toJSONString(drawLogItemList));
        Collections.reverse(drawLogItemList);

        return drawLogItemList;
    }


    @Override
    protected void sendPrize(DrawParam drawParam, List<DrawPoolItemDTO> prizeList) {
        if (Boolean.TRUE.equals(drawParam.getNoSendPrize())) {
            return;
        }
        if (TimeCarnivalConstant.DrawPoolCodeEnum.LUMINOUS_CONCERT_HALL.getCode().equals(drawParam.getPoolCode())) {
            Long totalGoldCoins = prizeList.stream().mapToLong(prize -> prize.getDrawPoolItemDO().getItemValueGold() * prize.getTargetTimes()).sum();
            distributedGoldCoins(drawParam, totalGoldCoins, prizeList);
            return;
        }
        sendPrizeManager.sendPrize(drawParam.getBaseParam(), prizeList.stream().map(SendPrizeDTO::of).collect(Collectors.toList()));
    }

    private void distributedGoldCoins(DrawParam drawParam, Long totalGoldCoins, List<DrawPoolItemDTO> prizeList) {
        List<Long> bandMembers = timeCarnivalConstant.getBandMembers(drawParam.getUid());
        String bandId = timeCarnivalConstant.getBandId(drawParam.getUid());
        String bandName = timeCarnivalConstant.getBandName(bandId);
        DrawPoolItemDTO drawPoolItemDTO = prizeList.get(0);
        DrawPoolItemDO drawPoolItemDO = drawPoolItemDTO.getDrawPoolItemDO();
        Long totalContribution = bandMembers.stream().mapToLong(timeCarnivalConstant::getMusicSheet).sum();
        if (CollectionUtil.isEmpty(bandMembers)) {
            return;
        }
        for (Long memberId : bandMembers) {
            Long memberContribution = timeCarnivalConstant.getMusicSheet(memberId);
            if (memberContribution <= 52) {
                continue;
            }
            Long goldCoins = Math.floorDiv(memberContribution * totalGoldCoins, totalContribution);
            UserVO fromUser = userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), memberId);
            if (fromUser != null) {
                sendPrizeManager.sendFreeCoin(fromUser.getUnionId(), ServicesAppIdEnum.lanling.getAppId(), memberId, TimeCarnivalConstant.ACTIVITY_CODE, goldCoins, TimeCarnivalConstant.ACTIVITY_CODE, TimeCarnivalConstant.ACTIVITY_CODE, "时光嘉年华活动", 1);
            }
/*
            List<DrawPoolItemDTO> userPrizeList = Collections.singletonList(DrawPoolItemDTO.builder()
                    .targetTimes(1)
                    .drawPoolItemDO(DrawPoolItemDO.builder()
                            .itemType(drawPoolItemDO.getItemType())
                            .itemValueGold(goldCoins)
                            .itemKey(goldCoins.toString())
                            .itemName(drawPoolItemDO.getItemName())
                            .itemSubType(drawPoolItemDO.getItemSubType())
                            .itemNum(drawPoolItemDO.getItemNum())
                            .itemEffectiveDay(drawPoolItemDO.getItemEffectiveDay())
                            .poolCode(drawPoolItemDO.getPoolCode())
                            .unionId(drawPoolItemDO.getUnionId())
                            .updateTime(LocalDateTime.now())
                            .createTime(LocalDateTime.now())
                            .status(drawPoolItemDO.getStatus())
                            .build())
                    .build());
            sendPrizeManager.sendPrize(BaseParam.builder()
                    .uid(memberId)
                    .unionId(ServicesAppIdEnum.lanling.getUnionId())
                    .build(), userPrizeList.stream().map(SendPrizeDTO::of).collect(Collectors.toList()));
*/
            log.info("send band member coin uid {} coins {} totalContribution {} memberContribution {}", memberId, goldCoins, totalContribution, memberContribution);
            sendDistributedGoldCoinsMessage(memberId, bandName);
        }
    }

    private void sendDistributedGoldCoinsMessage(Long memberId, String bandName) {
        String msg = "亲爱的%s乐团成员，您乐团的队长已经在流光演奏厅抽取金币奖励，根据贡献值，您的金币已下发，快去查看吧~";
        notifyComponent.npcNotify(
                memberId,
                String.format(msg, bandName)
        );
    }

    @Override
    public DrawLogNewVO drawLogNew(DrawLogParam param) {
        List<DrawLogDO> drawLogDOList = drawLogJpaDAO.findAll(Example.of(DrawLogDO.builder()
                .appId(param.getAppId())
                .unionId(param.getUnionId())
                .toUid(param.getUid())
                .activityCode(param.getActivityCode())
                .poolCode(param.getPoolCode())
                .build()));

        List<DrawLogItem> drawLogWrapperList = drawLogWrapper(DrawLogParam.builder().appId(param.getAppId()).unionId(param.getUnionId()).uid(param.getUid()).build(), drawLogDOList);

        DrawLogNewVO drawLogVO = new DrawLogNewVO();
        Map<String, List<DrawLogItemVO>> timeDrawLogItemMap = new HashMap<>();
        for (DrawLogItem drawLogItem : drawLogWrapperList) {
            if (timeDrawLogItemMap.containsKey(drawLogItem.getTime())) {
                timeDrawLogItemMap.get(drawLogItem.getTime()).add(DrawLogItemVO.builder().itemNum(drawLogItem.getItemNum()).text(drawLogItem.getText()).build());
            } else {
                List<DrawLogItemVO> drawLogItemList = new ArrayList<>();
                drawLogItemList.add(DrawLogItemVO.builder().itemNum(drawLogItem.getItemNum()).text(drawLogItem.getText()).build());
                timeDrawLogItemMap.put(drawLogItem.getTime(), drawLogItemList);
            }
        }
        List<DrawLogTimeItemVO> drawLogTimeItemList = new ArrayList<>();
        timeDrawLogItemMap.forEach((k, v) -> drawLogTimeItemList.add(DrawLogTimeItemVO.builder().time(k).drawLogItemList(v).build()));
        drawLogTimeItemList.sort((o1, o2) -> o2.getTime().compareTo(o1.getTime()));
        drawLogVO.setDrawLogTimeItemList(drawLogTimeItemList);

        return drawLogVO;
    }


}
