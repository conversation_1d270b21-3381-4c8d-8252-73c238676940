package cn.yizhoucp.ump.biz.project.biz.manager.activity.luckyBagHunt;


import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.enums.UserPackageBizType;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.product.client.UserPackageFeignService;
import cn.yizhoucp.product.dto.BatchUsePackageDetailDTO;
import cn.yizhoucp.product.dto.BatchUsePackageResultDTO;
import cn.yizhoucp.product.enums.PackageUseScene;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.activity.luckyBagHunt.AreaVO;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.LuckyBagHuntConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.log.LogComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.AbstractDrawTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawContext;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.doDrawStrategy.common.ProbStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.biz.util.PartitionUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.yizhoucp.ms.core.base.ErrorCode.ACTIVITY_ERROR_180034;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LuckyBagHuntConstant.*;

@Slf4j
@Service
public class LuckyBagHuntDrawManager extends AbstractDrawTemplate {

    @Resource
    private UserPackageFeignService userPackageFeignService;
    @Resource
    private LuckyBagHuntManager luckyBagHuntManager;
    @Resource
    private LogComponent logComponent;
    @Resource
    private ProbStrategy probStrategy;
    @Resource
    private RedisManager redisManager;

    @Override
    protected void resourceCheck(DrawContext context) {
        DrawParam drawParam = context.getDrawParam();

        // 检查是否已全部完成
        if (redisManager.hHasKey(String.format(GAME_DONE_FLAG, PartitionUtil.getIndex(drawParam.getUid(), PARTITION_NUM), drawParam.getUid()), drawParam.getUid().toString())) {
            throw new ServiceException(ACTIVITY_ERROR_180034, "已解锁全部区域哦～");
        }

        // 扣除资源
        List<BatchUsePackageDetailDTO> detailList = Lists.newArrayList(
                BatchUsePackageDetailDTO.builder().bizId("BZSP1_GIFT").useNum(1).bizType(UserPackageBizType.GIFT.getCode()).build(),
                BatchUsePackageDetailDTO.builder().bizId("BZSP2_GIFT").useNum(1).bizType(UserPackageBizType.GIFT.getCode()).build(),
                BatchUsePackageDetailDTO.builder().bizId("BZSP3_GIFT").useNum(1).bizType(UserPackageBizType.GIFT.getCode()).build(),
                BatchUsePackageDetailDTO.builder().bizId("BZSP4_GIFT").useNum(1).bizType(UserPackageBizType.GIFT.getCode()).build(),
                BatchUsePackageDetailDTO.builder().bizId("BZSP5_GIFT").useNum(1).bizType(UserPackageBizType.GIFT.getCode()).build(),
                BatchUsePackageDetailDTO.builder().bizId("BZSP6_GIFT").useNum(1).bizType(UserPackageBizType.GIFT.getCode()).build());
        BatchUsePackageResultDTO result = userPackageFeignService.batchUsePackage(drawParam.getAppId(), drawParam.getUnionId(), drawParam.getUid(),
                detailList, PackageUseScene.activity.getCode(), Boolean.FALSE);
        if (Objects.isNull(result)) {
            throw new ServiceException(ErrorCode.ACTIVITY_PARAM_ERROR, "可用解锁次数不足哦～");
        }

        // 根据当前区域
        List<AreaVO> areaList = luckyBagHuntManager.getAreaList(BaseParam.builder().appId(drawParam.getAppId()).unionId(drawParam.getUnionId()).uid(drawParam.getUid()).build());
        log.debug("areaList:{}", JSON.toJSONString(areaList));
        AreaVO areaVO = areaList.stream().filter(a -> a.getCurrent() < a.getLimit()).findFirst().get();

        // 累计解锁次数
        LuckyBagHuntConstant.AreaEnum areaEnum = LuckyBagHuntConstant.AreaEnum.valueOf(areaVO.getArea());
        Integer after = luckyBagHuntManager.unlockArea(drawParam.getUid(), areaEnum);
        if (after < areaEnum.getLimit()) {
            // 小于阈值直接返回
            throw new ServiceException(ACTIVITY_ERROR_180034, "解锁成功");
        } else if (after > areaEnum.getLimit()) {
            // 大于阈值提示已解锁全部区域
            throw new ServiceException(ACTIVITY_ERROR_180034, "已解锁全部区域哦～");
        } else {
            // 等于阈值根据解锁区域匹配奖池
            drawParam.setPoolCode(areaVO.getArea());
            drawParam.setType(areaVO.getArea());
            // 领取区域8奖励时标记已全部完成
            if (AreaEnum.Area8.equals(areaEnum)) {
                redisManager.hset(String.format(GAME_DONE_FLAG, PartitionUtil.getIndex(drawParam.getUid(), PARTITION_NUM), drawParam.getUid()), drawParam.getUid().toString(), System.currentTimeMillis(), RedisManager.ONE_DAY_SECONDS * 30);
            }
        }
    }

    @Override
    protected void draw(DrawContext context) {
        DrawParam drawParam = context.getDrawParam();
        List<DrawPoolItemDTO> prizeItems = probStrategy.getDrawPoolItems(context);
        context.setPrizeItemList(prizeItems.stream().map(p -> {
            DrawPoolItemDO drawPoolItemDO = p.getDrawPoolItemDO();
            drawPoolItemDO.setScene(drawParam.getActivityCode());
            p.setDrawPoolItemDO(drawPoolItemDO);
            return p;
        }).collect(Collectors.toList()));
    }

    @Override
    protected void deductResource(DrawContext context) {

    }

    @Override
    protected void doCallback(DrawContext context) {
        DrawParam drawParam = context.getDrawParam();
        // 记录解锁日志
        logComponent.putDrawLog(drawParam.getBaseParam(), drawParam.getActivityCode(), context.getPrizeItemList());
    }

}
