package cn.yizhoucp.ump.biz.project.web.rest.controller.femaleSys;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.biz.project.biz.manager.femaleSys.FemaleSystemManager;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2023/4/27 16:20
 * @Version 1.0
 */
@RestController
public class FemaleSystemController {

    @Resource
    private FemaleSystemManager femaleSystemManager;

    @GetMapping("/api/inner/female-system/login-handle")
    public Result<Boolean> loginHandle(BaseParam param) {
        femaleSystemManager.loginHandle(param);
        return Result.successResult();
    }

}
