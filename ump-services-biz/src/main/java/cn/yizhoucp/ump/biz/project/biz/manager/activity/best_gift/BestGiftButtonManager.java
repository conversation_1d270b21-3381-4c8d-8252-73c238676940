package cn.yizhoucp.ump.biz.project.biz.manager.activity.best_gift;

import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.best_gift.strategy.BestGiftStrategyChoose;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.AbstractButtonManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


@Component
@Slf4j
public class BestGiftButtonManager extends AbstractButtonManager {

    @Resource
    private BestGiftStrategyChoose strategyChoose;

    @Override
    public Object event(ButtonEventParam buttonEventParam) {
        return strategyChoose.executeStrategy(buttonEventParam);
    }

}
