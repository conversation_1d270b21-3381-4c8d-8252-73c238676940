package cn.yizhoucp.ump.biz.project.biz.manager.activity_skin_admin.domin.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
@TableName("activity_skin_resource")
public class ActivitySkinResource {


    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String parentKey;

    private String resourceKey;

    private String activityCode;

    private String label;

    private String resourceConfig;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

}
