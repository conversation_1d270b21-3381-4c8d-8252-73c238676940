package cn.yizhoucp.ump.biz.project.web.rest.controller.navigation;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ms.core.vo.landingservices.AdminPageVO;
import cn.yizhoucp.ump.api.vo.luckBag.admin.DrawPoolAdminVO;
import cn.yizhoucp.ump.biz.project.biz.manager.navigation.NavigationPoolManager;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
public class NavigationAdminController {
    @Resource
    private NavigationPoolManager navigationPoolManager;

    @GetMapping("/api/admin/ump/navigation/list-pool")
    public Result<AdminPageVO<DrawPoolAdminVO>> listPool() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> navigationPoolManager.listDrawPool());
    }

    @PostMapping("/api/admin/ump/navigation/refresh-pool")
    public Result<Boolean> refreshPool(@RequestBody DrawPoolAdminVO drawPoolAdminVO) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> navigationPoolManager.saveOrUpdatePrizePool(drawPoolAdminVO));
    }
}
