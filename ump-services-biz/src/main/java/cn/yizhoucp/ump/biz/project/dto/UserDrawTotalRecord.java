package cn.yizhoucp.ump.biz.project.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 用户福袋抽奖记录
 *
 * @author: dongming
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserDrawTotalRecord {

    private Long id;
    /** 应用唯一标识 */
    private String unionId;
    /** 活动ID */
    private Long activityId;
    /** 抽奖类型 */
    private String drawType;
    /** 用户ID */
    private Long uid;
    /** 总抽奖次数 */
    private Long drawCount;
    /** 大奖（保底）限制 */
    private Long limitCount;
    /** 中大奖（保底）剩余次数 */
    private Long leftCount;
    /** 中大奖（保底）次数 */
    private Integer count;
    /** 创建时间 */
    private Date createTime;
    /** 更新时间 */
    private Date updateTime;


}
