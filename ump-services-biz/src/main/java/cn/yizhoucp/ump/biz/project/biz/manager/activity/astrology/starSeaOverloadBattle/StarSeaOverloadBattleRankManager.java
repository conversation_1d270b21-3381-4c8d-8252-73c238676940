package cn.yizhoucp.ump.biz.project.biz.manager.activity.astrology.starSeaOverloadBattle;

import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.SpringStarCeremonyConstant;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaOverloadBattleConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.AbstractRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaOverloadBattleConstant.ACTIVITY_CODE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaOverloadBattleConstant.OVERLOAD_RANK;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaOverloadBattleConstant.STAR_SEA_RANK;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaOverloadBattleConstant.STAR_SEA_VALUE;

@Service
@Slf4j
public class StarSeaOverloadBattleRankManager extends AbstractRankManager {

    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private StarSeaOverloadBattleTrackManager starSeaOverloadBattleTrackManager;

    @Override
    protected void doPreProcess(RankContext rankContext) {
        rankContext.setRankLen(10L);
    }

    @Override
    protected void postProcess(RankContext rankContext) {
        RankVO rankVO = rankContext.getRankVO();
        if (rankVO == null) {
            return;
        }
        if (!OVERLOAD_RANK.equals(rankContext.getRankKey())) {
            return;
        }

        for (RankItem rankItem : rankVO.getRankList()) {
            Long value = rankItem.getValue() / 1000;
            long hour = value / 3600;
            long minute = (value - hour * 3600) / 60;
            long second = value % 60;
            rankItem.setExt(String.format("%02d:%02d:%02d", hour, minute, second));
            rankItem.setValue(Optional.ofNullable(redisManager.getLong(String.format(STAR_SEA_VALUE, rankItem.getId()))).orElse(0L));
        }

        RankItem rankItem = rankVO.getMyselfRank();
        if (rankItem != null && rankItem.getValue() != null) {
            Long value = rankItem.getValue() / 1000;
            long hour = value / 3600;
            long minute = (value - hour * 3600) / 60;
            long second = value % 60;
            rankItem.setExt(String.format("%02d:%02d:%02d", hour, minute, second));
            rankItem.setValue(Optional.ofNullable(redisManager.getLong(String.format(STAR_SEA_VALUE, rankItem.getId()))).orElse(0L));
        }
    }

    @Override
    public Boolean sendPrize(RankContext rankContext) {
        if (OVERLOAD_RANK.equals(rankContext.getRankKey())) {
            if (!Boolean.TRUE.equals(redisManager.setnx("ump:star_sea_overload_battle2:send_prize_idempotent", System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND))) {
                return Boolean.TRUE;
            }

            List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(1L, ACTIVITY_CODE, "overload_rank");
            log.info("scenePrizeDOList {}", JSON.toJSONString(scenePrizeDOList));
            if (CollectionUtils.isEmpty(scenePrizeDOList)) {
                return Boolean.FALSE;
            }

            RankVO rankVO = this.getRank(RankContext.builder()
                    .activityCode(ACTIVITY_CODE)
                    .rankKey(OVERLOAD_RANK)
                    .type(RankContext.RankType.user)
                    .build());
            log.info("rankVO {}", rankVO);
            if (rankVO == null) {
                return Boolean.FALSE;
            }

            List<RankItem> rankList = rankVO.getRankList();
            if (CollectionUtils.isEmpty(rankList)) {
                return Boolean.FALSE;
            }

            for (RankItem rankItem : rankList) {
                Long rank = rankItem.getRank();
                List<ScenePrizeDO> scenePrizeDOs = scenePrizeDOList.stream().filter(scenePrizeDO -> Objects.equals(rank, scenePrizeDO.getPrizeBelongToRank())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(scenePrizeDOs)) {
                    log.info("uid {} rank {} 没有奖励", rankItem.getId(), rank);
                    continue;
                }
                sendPrizeManager.sendPrize(
                        BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(rankItem.getId()).build(),
                        scenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, rankItem.getId())).collect(Collectors.toList())
                );
                starSeaOverloadBattleTrackManager.allActivityReceiveAward("list", "on_sea_star_king_list", scenePrizeDOList.get(0).getPrizeValue(), scenePrizeDOList.get(0).getPrizeValueGold(), scenePrizeDOList.get(0).getPrizeNum(), rankItem.getValue() / 1000, Optional.ofNullable(redisManager.getLong(String.format(STAR_SEA_VALUE, rankItem.getId()))).orElse(0L), rank, rankItem.getId());
            }
        } else {
            if (!Boolean.TRUE.equals(redisManager.setnx(String.format("ump:star_sea_overload_battle2:send_prize_idempotent:%s", DateUtil.getNowYyyyMMdd()), System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND))) {
                return Boolean.TRUE;
            }

            List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(1L, ACTIVITY_CODE, "star_sea_rank");
            log.info("scenePrizeDOList {}", JSON.toJSONString(scenePrizeDOList));
            if (CollectionUtils.isEmpty(scenePrizeDOList)) {
                return Boolean.FALSE;
            }

            RankVO rankVO = this.getRank(RankContext.builder()
                    .activityCode(ACTIVITY_CODE)
                    .rankKey(String.format(STAR_SEA_RANK, DateUtil.getYesterdayYyyyMMdd()))
                    .type(RankContext.RankType.user)
                    .build());
            log.info("rankVO {}", rankVO);
            if (rankVO == null) {
                return Boolean.FALSE;
            }

            List<RankItem> rankList = rankVO.getRankList();
            if (CollectionUtils.isEmpty(rankList)) {
                return Boolean.FALSE;
            }

            for (RankItem rankItem : rankList) {
                Long rank = rankItem.getRank();
                List<ScenePrizeDO> scenePrizeDOs = scenePrizeDOList.stream().filter(scenePrizeDO -> Objects.equals(rank, scenePrizeDO.getPrizeBelongToRank())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(scenePrizeDOs)) {
                    log.info("uid {} rank {} 没有奖励", rankItem.getId(), rank);
                    continue;
                }
                sendPrizeManager.sendPrize(
                        BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(rankItem.getId()).build(),
                        scenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, rankItem.getId())).collect(Collectors.toList())
                );
                starSeaOverloadBattleTrackManager.allActivityReceiveAward("list", "on_sea_star_list", scenePrizeDOList.get(0).getPrizeValue(), scenePrizeDOList.get(0).getPrizeValueGold(), scenePrizeDOList.get(0).getPrizeNum(), null, null, rank, rankItem.getId());
            }
        }

        return Boolean.TRUE;
    }

}
