package cn.yizhoucp.ump.biz.project.biz.manager.activity.labourDay2024;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.jimu.draw.inner.DrawLogItem;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.log.LogComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.AbstractDrawTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawContext;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawLogParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.doDrawStrategy.common.ProbStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.FeignUserService;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawLogDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.BROADCAST;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.LOTTERY_GIFT_KEY;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.TICKET;

@Service
@Slf4j
public class LabourDay2024DrawManager extends AbstractDrawTemplate {

    @Resource
    private ProbStrategy probStrategy;
    @Resource
    private RedisManager redisManager;
    @Resource
    private LogComponent logComponent;
    @Resource
    private FeignUserService feignUserService;
    @Resource
    private LabourDay2024TrackManager labourDay2024TrackManager;

    @Override
    protected void resourceCheck(DrawContext context) {
        DrawParam drawParam = context.getDrawParam();
        String poolCode = drawParam.getPoolCode();

        if (LOTTERY_GIFT_KEY.contains(poolCode)) {
            drawParam.setNoSendPrize(Boolean.TRUE);
            return;
        }

        if ("labour_day_2024_LAJC".equals(poolCode)) {
            int ticket = Optional.ofNullable(redisManager.getInteger(String.format(TICKET, drawParam.getUid()))).orElse(0);
            if (ticket < drawParam.getTimes()) {
                throw new ServiceException(ErrorCode.INVALID_PARAM, "抽奖次数不足噢。快去参与活动获得吧~");
            }
            redisManager.decrLong(String.format(TICKET, drawParam.getUid()), drawParam.getTimes(), DateUtil.ONE_MONTH_SECOND);
            return;
        }

        throw new ServiceException(ErrorCode.INVALID_PARAM);
    }

    @Override
    protected void deductResource(DrawContext context) {
    }

    @Override
    protected void draw(DrawContext context) {
        List<DrawPoolItemDTO> drawPoolItemDTOList = probStrategy.getDrawPoolItems(context.getDrawPoolItemDOS(),
                context.getDrawParam().getTimes(),
                Boolean.TRUE);
        context.setPrizeItemList(drawPoolItemDTOList);
    }

    @Override
    protected void doCallback(DrawContext context) {
        DrawParam drawParam = context.getDrawParam();
        String poolCode = drawParam.getPoolCode();

        if ("labour_day_2024_LAJC".equals(poolCode)) {
            logComponent.putDrawLog(drawParam.getBaseParam(), drawParam.getActivityCode(), context.getPrizeItemList());

            UserVO userVO = feignUserService.getBasic(context.getDrawParam().getUid(), context.getDrawParam().getAppId()).successData();
            if (userVO != null) {
                for (DrawPoolItemDTO drawPoolItemDTO : context.getPrizeItemList()) {
                    if (drawPoolItemDTO.getDrawPoolItemDO().getItemValueGold() > 52) {
                        redisManager.listLpush(BROADCAST, String.format("恭喜 %s 抽出【%s】", userVO.getName(), drawPoolItemDTO.getDrawPoolItemDO().getItemName()), DateUtil.ONE_MONTH_SECOND);
                    }
                }
                redisManager.listTrim(BROADCAST, 0, 20);
            }

            context.setExtData(Optional.ofNullable(redisManager.getInteger(String.format(TICKET, drawParam.getUid()))).orElse(0));
            for (DrawPoolItemDTO drawPoolItemDTO : context.getPrizeItemList()) {
                labourDay2024TrackManager.allActivityReceiveAward("lottery", drawPoolItemDTO.getDrawPoolItemDO().getItemKey(), drawPoolItemDTO.getDrawPoolItemDO().getItemValueGold(), drawPoolItemDTO.getDrawPoolItemDO().getItemNum() * drawPoolItemDTO.getTargetTimes(), null, drawParam.getUid());
            }
        }
    }

    @Override
    protected List<DrawLogItem> drawLogWrapper(DrawLogParam param, List<DrawLogDO> drawLogDOList) {
//        drawLogDOList.sort(Comparator.comparingLong(d -> d.getCreateTime().toInstant(ZoneOffset.UTC).toEpochMilli()));
        List<DrawLogItem> drawLogItemList = drawLogDOList.stream().map(drawLogDO -> {
            DrawPoolItemDTO drawPoolItemDTO = JSON.parseObject(drawLogDO.getLogJson(), DrawPoolItemDTO.class);
            DrawPoolItemDO drawPoolItemDO = drawPoolItemDTO.getDrawPoolItemDO();
            return DrawLogItem.builder()
                    .icon(drawPoolItemDO.getItemIcon())
                    .valueGold(drawPoolItemDO.getItemValueGold())
                    .text(drawPoolItemDO.getItemName())
                    .itemNum(drawPoolItemDO.getItemNum() * drawPoolItemDTO.getTargetTimes())
                    .time(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm").format(drawLogDO.getCreateTime())).build();
        }).collect(Collectors.toList());
        log.debug("drawLogItemList {}", JSON.toJSONString(drawLogItemList));
        Collections.reverse(drawLogItemList);

        return drawLogItemList;
    }

}
