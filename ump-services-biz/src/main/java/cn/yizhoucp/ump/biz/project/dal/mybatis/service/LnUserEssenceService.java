package cn.yizhoucp.ump.biz.project.dal.mybatis.service;

import cn.yizhoucp.ump.biz.project.dal.mybatis.domain.LnUserEssence;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【ln_user_essence(传说中的梦幻岛用户精华表)】的数据库操作Service
* @createDate 2024-12-08 18:44:23
*/
public interface LnUserEssenceService extends IService<LnUserEssence> {

    /**
     * 扣减精华
     */
    boolean deductEssence(Long uid, Long deductEssence);

    /**
     * 获取精华数量
     */
    Long getEssence(Long uid);
}
