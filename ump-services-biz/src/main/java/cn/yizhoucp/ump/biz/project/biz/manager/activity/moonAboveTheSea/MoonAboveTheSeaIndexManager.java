package cn.yizhoucp.ump.biz.project.biz.manager.activity.moonAboveTheSea;

import cn.hutool.core.collection.CollUtil;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.moonAboveTheSea.common.MoonAboveTheSeaConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.moonAboveTheSea.common.MoonAboveTheSeaEnums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.moonAboveTheSea.common.MoonAboveTheSeaRedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.moonAboveTheSea.strategy.event.CardDrawMissionEvent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.moonAboveTheSea.vo.MoonAboveTheSeaIndexVO;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.IndexManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 月照星海首页
 *
 * <AUTHOR>
 * @version V1.0
 * @since 16:13 2025/1/13
 */
@Service
@Slf4j
public class MoonAboveTheSeaIndexManager implements IndexManager {

    @Resource
    private MoonAboveTheSeaRedisManager moonAboveTheSeaRedisManager;
    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    private CardDrawMissionEvent cardDrawMissionEvent;


    @Override
    public MoonAboveTheSeaIndexVO getIndex(BaseParam param, Long toUid, String extData) {
        MoonAboveTheSeaIndexVO vo = new MoonAboveTheSeaIndexVO();
        vo.setCards(buildCards());
        vo.setDrawItem(moonAboveTheSeaRedisManager.getDrawItem(param.getUid()));
        vo.setHasCompletedTask(buildHasCompletedTask(param.getUid()));
        return vo;
    }

    private Boolean buildHasCompletedTask(Long uid) {
        for (MoonAboveTheSeaEnums.TaskEnum taskEnum : MoonAboveTheSeaEnums.TaskEnum.values()) {
            int status = cardDrawMissionEvent.getClaimButtonStatus(uid, taskEnum);
            if (MoonAboveTheSeaEnums.TaskStatusEnum.FINISHED.getCode().equals(status)) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    private List<MoonAboveTheSeaIndexVO.Card> buildCards() {
        List<MoonAboveTheSeaIndexVO.Card> cards = new ArrayList<>();
        List<ScenePrizeDO> scenePrizeDOS = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), MoonAboveTheSeaConstant.ACTIVITY_CODE, MoonAboveTheSeaConstant.CARD_SCENE_CODE);
        if (CollUtil.isEmpty(scenePrizeDOS)) {
            return cards;
        }
        scenePrizeDOS.forEach(scenePrizeDO -> {
            MoonAboveTheSeaIndexVO.Card card = new MoonAboveTheSeaIndexVO.Card();
            String cardKey = scenePrizeDO.getPrizeValue();
            card.setCardKey(cardKey);
            card.setRewards(buildRewardByCardKey(cardKey));
            cards.add(card);
        });
        return cards;
    }

    private List<MoonAboveTheSeaIndexVO.Reward> buildRewardByCardKey(String cardKey) {
        List<MoonAboveTheSeaIndexVO.Reward> rewards = new ArrayList<>();
        List<ScenePrizeDO> scenePrizeDOS = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), MoonAboveTheSeaConstant.ACTIVITY_CODE, cardKey);
        if (CollUtil.isEmpty(scenePrizeDOS)) {
            return rewards;
        }
        scenePrizeDOS.forEach(scenePrizeDO -> {
            MoonAboveTheSeaIndexVO.Reward reward = new MoonAboveTheSeaIndexVO.Reward();
            reward.setGiftKey(scenePrizeDO.getPrizeValue());
            reward.setGiftValue(scenePrizeDO.getPrizeValue());
            reward.setGiftName(scenePrizeDO.getPrizeDesc());
            reward.setGiftIcon(scenePrizeDO.getPrizeIcon());
            rewards.add(reward);
        });
        return rewards;
    }

    @Override
    public String getTemplateType() {
        return "";
    }

    @Override
    public String getActivityCode() {
        return MoonAboveTheSeaConstant.ACTIVITY_CODE;
    }
}
