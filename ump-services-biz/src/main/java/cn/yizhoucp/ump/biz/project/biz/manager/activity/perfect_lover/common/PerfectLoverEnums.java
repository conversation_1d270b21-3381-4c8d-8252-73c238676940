package cn.yizhoucp.ump.biz.project.biz.manager.activity.perfect_lover.common;

import cn.yizhoucp.ump.biz.project.biz.manager.activity.perfect_lover.strategy.*;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.StrategyEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * 满分恋人枚举类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 20:18 2025/5/13
 */
public class PerfectLoverEnums {

    @AllArgsConstructor
    @Getter
    public enum ButtonEnum implements StrategyEnum {
        FORWARD("moveForward", "perfectLoverForwardStrategy", PerfectLoverForwardStrategy.class),
        REFRESH("refresh", "perfectLoverRefreshStrategy", PerfectLoverRefreshStrategy.class),
        CHECK_IN("check_in", "perfectLoverCheckInStrategy", PerfectLoverCheckInStrategy.class),
        REMIND("reminder", "perfectLoverRemindStrategy", PerfectLoverRemindStrategy.class),
        COLLECT_FLOWER("claimReward", "perfectLoverCollectFlowerStrategy", PerfectLoverCollectFlowerStrategy.class),
        FRIEND_LIST("get_friend_list", "perfectLoverGetFriendList", PerfectLoverGetFriendList.class)
        ;


        private final String strategyName;
        private final String beanName;
        private final Class<? extends ExecutableStrategy> strategyClass;
    }

    @AllArgsConstructor
    @Getter
    public enum NodeStatusEnum {
        NOT_REACHED(0, "未到达"),
        CURRENT(1, "当前位置"),
        PASSED(2, "已通过");

        private final Integer code;
        private final String desc;
    }

    @AllArgsConstructor
    @Getter
    public enum TimeSlotEnum {
        EARLY_MORNING(0, 10, PerfectLoverConstant.EARLY_MORNING_REWARD, "早上"),
        NOON(11, 13, PerfectLoverConstant.NOON_REWARD, "中午"),
        AFTERNOON(14, 18, PerfectLoverConstant.AFTERNOON_REWARD, "下午"),
        EVENING(19, 24, PerfectLoverConstant.EVENING_REWARD, "晚上");

        private final Integer startHour;
        private final Integer endHour;
        private final Integer reward;
        private final String desc;

        /**
         * 根据小时获取时间段
         * @param hour 小时
         * @return 时间段
         */
        public static TimeSlotEnum getByHour(int hour) {
            if (hour >= EARLY_MORNING.startHour && hour <= EARLY_MORNING.endHour) {
                return EARLY_MORNING;
            } else if (hour >= NOON.startHour && hour <= NOON.endHour) {
                return NOON;
            } else if (hour >= AFTERNOON.startHour && hour <= AFTERNOON.endHour) {
                return AFTERNOON;
            } else {
                return EVENING;
            }
        }
    }
}
