package cn.yizhoucp.ump.biz.project.web.rest.controller.activity;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.vo.activity.princessRoseKnight.BuyMagicPotionResult;
import cn.yizhoucp.ump.api.vo.jimu.draw.DrawReturn;
import cn.yizhoucp.ump.api.vo.jimu.task.TakePrizeReturn;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.princessRoseAndKnight.PrincessRoseAndKnightDrawManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.princessRoseAndKnight.PrincessRoseAndKnightIndexManager;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.ACTIVITY_CODE;

@RestController
public class PrincessRoseAndKnightController {

    @Resource
    private PrincessRoseAndKnightDrawManager princessRoseAndKnightDrawManager;
    @Resource
    private PrincessRoseAndKnightIndexManager princessRoseAndKnightIndexManager;

    @GetMapping("/api/inner/activity/princess_rose_and_knight/box_draw")
    public Result<DrawReturn> draw(String type, String poolCode, Integer times, String extValue) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> princessRoseAndKnightDrawManager.draw(DrawParam.builder()
                .unionId(MDCUtil.getCurUnionIdByMdc())
                .appId(MDCUtil.getCurAppIdByMdc())
                .uid(MDCUtil.getCurUserIdByMdc())
                .activityCode(ACTIVITY_CODE)
                .type(type)
                .poolCode(poolCode)
                .times(times)
                .extValue(extValue).build()));
    }

    @GetMapping("/api/inner/activity/princess_rose_and_knight/buy_magic_potion")
    public Result<BuyMagicPotionResult> buyMagicPotion(BaseParam param, Long toUid) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> princessRoseAndKnightIndexManager.buyMagicPotion(param, toUid));
    }

    @GetMapping("/api/inner/activity/princess_rose_and_knight/take_prize")
    public Result<TakePrizeReturn> takePrize(BaseParam param, Long toUid, String taskCode) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> princessRoseAndKnightIndexManager.takePrize(param, toUid, taskCode));
    }

    @GetMapping("/api/inner/activity/princess_rose_and_knight/draw")
    public Result<DrawReturn> draw(BaseParam param) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> princessRoseAndKnightIndexManager.draw(param));
    }

}
