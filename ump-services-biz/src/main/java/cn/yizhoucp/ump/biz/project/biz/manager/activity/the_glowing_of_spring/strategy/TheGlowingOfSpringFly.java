package cn.yizhoucp.ump.biz.project.biz.manager.activity.the_glowing_of_spring.strategy;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.annotation.NoRepeatSubmit;
import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.the_glowing_of_spring.common.TheGlowingOfSpringConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.the_glowing_of_spring.common.TheGlowingOfSpringRedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class TheGlowingOfSpringFly implements ExecutableStrategy {
    @Resource
    private TheGlowingOfSpringRedisManager theGlowingOfSpringRedisManager;

    @Override
    @NoRepeatSubmit(time = 3)
    public Boolean execute(ButtonEventParam buttonEventParam) {
        Long uid = buttonEventParam.getBaseParam().getUid();
        Integer flyCount = Integer.parseInt(buttonEventParam.getBizKey());
        Integer currentCount = theGlowingOfSpringRedisManager.getFlyItemCount(uid);
        if (currentCount < flyCount) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "当前纸鸢轮不够哦～");
        }
        Integer currentMeters = theGlowingOfSpringRedisManager.getCurrentMeter(uid);
        if (currentMeters >= 33440) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "先刷新高度哦～");
        }
        theGlowingOfSpringRedisManager.decrementFlyItemCount(uid, flyCount);
        theGlowingOfSpringRedisManager.incrementCurrentMeter(uid, TheGlowingOfSpringConstant.BASE_METER * flyCount);
        return Boolean.TRUE;
    }
}
