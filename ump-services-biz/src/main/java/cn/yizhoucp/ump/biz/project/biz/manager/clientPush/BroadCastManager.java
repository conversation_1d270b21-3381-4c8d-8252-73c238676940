package cn.yizhoucp.ump.biz.project.biz.manager.clientPush;

import cn.yizhoucp.ump.biz.project.biz.remoteService.FeignImService;
import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.SystemNPC;
import cn.yizhoucp.ms.core.base.enums.ClientPushKey;
import cn.yizhoucp.ms.core.base.util.Base64Util;
import cn.yizhoucp.ms.core.vo.imservices.ServerPushData;
import cn.yizhoucp.ms.core.vo.imservices.ServerPushModel;
import cn.yizhoucp.ms.core.vo.imservices.SystemMessageAttrs;
import cn.yizhoucp.ms.core.vo.imservices.SystemMessageAttrsBase;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 客户端广播消息
 *
 * @author: lianghu
 */
@Slf4j
@Service
public class BroadCastManager {

    @Resource
    private FeignImService feignImService;

    public Boolean reRequestAdSpace(Long appId) {
        // 设置消息体
        SystemMessageAttrsBase base = new SystemMessageAttrsBase();
        base.setKey(ClientPushKey.re_request_adSpace.getCode());
        List<SystemMessageAttrs> eventList = new ArrayList<>();
        eventList.add(base);
        ServerPushData serverPushData = new ServerPushData();
        serverPushData.setEventList(eventList);
        ServerPushModel serverPushModel = new ServerPushModel();
        serverPushModel.setData(serverPushData);
        // 发送消息
        SystemNPC npc = SystemNPC.findByAppId(appId);
        Result<Boolean> result = feignImService.broadcastMsg(Base64Util.encodeToString(JSON.toJSONString(serverPushModel)), npc.getUserId(), "false", 0, npc.getAppId(), Boolean.TRUE);
        log.info("广播完成 result:{}, serverPushModel:{}", result, JSON.toJSONString(serverPushData));
        return Boolean.TRUE;
    }

}
