package cn.yizhoucp.ump.biz.project.biz.manager.activity.loveInBloomFestival.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class LoveInBloomFestivalIndexVO {
    private RewardInfoVO rewardInfo;
    private Integer buttonStatus;
    private List<TaskInfoVO> taskInfo;
    private PuzzleInfoVO puzzleInfo;
    private PairingInfoVO pairingInfo;
    private List<GiftVO> prizePoolInfo;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RewardInfoVO {
        private List<GiftVO> rewards;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TaskInfoVO {
        private String taskIcon;
        private String taskName;
        private String taskCode;
        private Integer taskStatus;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PuzzleInfoVO {
        private Integer currentPieceIndex;
        private List<PieceVO> pieces;
        private Long props;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PieceVO {
        private Integer index;
        private Boolean isIlluminated;
        private Integer requiredProps;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PairingInfoVO {
        private UserVO currentUser;
        private UserVO pairedUser;
        private String pairingValue;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserVO {
        private String userAvatar;
        private Long userId;
        private String userName;
        private String userSex;
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GiftVO {
        private String giftIcon;
        private String giftName;
        private Long giftValue;
    }

}
