package cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class CollectCardsForLuckVO {

    private List<CardVO> cards;
    private CardVO specialCard;

    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Data
    public static class CardVO{
        private String cardKey;
        private String cardName;
        private Integer quantity;
    }
}
