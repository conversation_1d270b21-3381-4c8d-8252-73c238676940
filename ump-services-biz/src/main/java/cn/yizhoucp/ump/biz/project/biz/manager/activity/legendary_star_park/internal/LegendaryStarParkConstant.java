package cn.yizhoucp.ump.biz.project.biz.manager.activity.legendary_star_park.internal;

public class LegendaryStarParkConstant {


    public static final String DAILY_SCENE_CODE = "daily_task_scene";
    public static final String DRAW_TIMES_SCENE_CODE = "draw_times_scene";
    public static final String COIN_SCENE_KEY = "legendary_star_park_coin";
    public static final String GLORY_STAR_PARADISE = "glory_star_paradise";
    public static final String STAR_PARADISE = "star_paradise";
    public static final String STAR_PARADISE_CAPTAIN = "star_paradise_captain";
    public static final String REPORTED_KEY = "ump:activity:%s:reported_key:%s:%s";

    private LegendaryStarParkConstant(){}

    public static final String ACTIVITY_CODE = "legendary_star_park";
    public static final String DRAW_ITEMS_KEY = "ump:activity:%s:draw_items:%s";
    public static final String BROD_CAST_KEY = "ump:activity:%s:broadcast:%s";
    public static final String DAILY_TASK_KEY = "ump:activity:%s:daily_task:%s:%s:%s:%s";
    public static final String DAILY_TASK_TITLE = "赠送%s个「%s」，获取%s张星乐券";
    public static final String DAILY_TASK_CLAIMED_KEY = "ump:activity:%s:daily_task_claimed:%s:%s:%s";
    public static final String ROOM_GLORY_VALUE_KEY = "ump:activity:%s:room_glory_value:%s:%s";
    public static final Integer GLORY_MAX_VALUE_KEY = 500;
    public static final String COLLECTED_TASK_KEY = "ump:activity:%s:collected_task:%s:%s";
    public static final String COLLECTED_TASK_CLAIMED_KEY = "ump:activity:%s:collected_task_claimed:%s:%s";
    public static final String CAPTAIN_ID_KEY = "ump:activity:%s:captain_id:%s";
    public static final String HISTORY_CAPTAIN_KEY = "ump:activity:%s:history_captain:%s";
    public static final String GLORY_MOMENT_KEY = "ump:activity:%s:glory_moment:%s:%s";
    public static final Long GLORY_MOMENT_TIME = 180L;
    public static final String COLLECTED_TASK_MAP_KEY = "ump:activity:%s:collected_task_map:%s";
    public static final String TASK_ID_KEY = "taskId";
    public static final String POOL_RANK_KEY = "ump:activity:%s:pool:rank:%s";
    public static final Long RANK_LEN = 20L;
    public static final Long MAX_DRAW_TIMES = 55L;
    public static final String DRAW_TIMES_TASK_CLAIMED_KEY = "ump:activity:%s:draw:times:task:claimed:%s:%s:%s";
    public static final String DRAW_TIMES_KEY = "ump:activity:%s:draw:times:%s:%s";
    public static final String DRAW_POOL_GIFT_ITEMS_KEY = "ump:activity:%s:draw:pool:gift:item:%s:%s:%s";
    public static final String ROOM_DRAW_TIMES_KEY = "ump:activity:%s:room:draw:times:%s:%s";

}
