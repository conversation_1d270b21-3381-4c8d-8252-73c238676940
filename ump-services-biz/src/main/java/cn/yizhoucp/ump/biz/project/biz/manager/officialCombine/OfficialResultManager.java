package cn.yizhoucp.ump.biz.project.biz.manager.officialCombine;

import cn.yizhoucp.ms.core.base.config.ThreadPoolConfig;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.hallowmasDayHD.HallowmasDayHDBizManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.worldAnnounceLoveYou.WorldAnnounceLoveYouBizManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 官宣结果处理
 */
@Service
@Slf4j
public class OfficialResultManager {

    @Resource
    private HallowmasDayHDBizManager hallowmasDayHDBizManager;

    @Resource
    private WorldAnnounceLoveYouBizManager worldAnnounceLoveYouBizManager;

    /**
     * 发送两人官宣成功消息
     */
    @Async(ThreadPoolConfig.ASYNC_EXECUTOR_NAME)
    public void sendOfficialSuccessMsg(Long maleUid, Long femaleUid) {
        log.info("sendOfficialSuccessMsg maleUid:{}, femaleUid:{}", maleUid, femaleUid);
        hallowmasDayHDBizManager.initiateFormOfficialCombine(maleUid, femaleUid);
        worldAnnounceLoveYouBizManager.checkLoveTrackAllAndSend(maleUid, femaleUid);

    }

    /**
     * 发送两人官宣取消消息
     */
    @Async(ThreadPoolConfig.ASYNC_EXECUTOR_NAME)
    public void sendOfficialCancelMsg(Long maleUid, Long femaleUid, String bagType) {
        log.info("sendOfficialCancelMsg maleUid:{}, femaleUid:{}", maleUid, femaleUid);
        worldAnnounceLoveYouBizManager.cancelOfficial(maleUid, femaleUid, bagType);
    }

}
