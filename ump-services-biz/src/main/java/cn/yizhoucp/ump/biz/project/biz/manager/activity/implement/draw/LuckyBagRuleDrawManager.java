package cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw;

import cn.yizhoucp.family.api.client.FamilyRoomRelationFeignService;
import cn.yizhoucp.family.api.dto.family.FamilyRoomRelationDTO;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.TopicConstant;
import cn.yizhoucp.ms.core.base.TopicTagEnum;
import cn.yizhoucp.ms.core.base.auth.SecurityUser;
import cn.yizhoucp.ms.core.base.auth.SecurityUtils;
import cn.yizhoucp.ms.core.base.enums.CommonStatus;
import cn.yizhoucp.ms.core.base.enums.GiftFrom;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.enums.ServicesNameEnum;
import cn.yizhoucp.ms.core.base.enums.luckyBag.LuckDrawType;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.base.util.RandomUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.ms.core.vo.landingservices.UserCharmGenerosityVO;
import cn.yizhoucp.product.dto.UsePackageDTO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.enums.ActivityCheckListEnum;
import cn.yizhoucp.ump.biz.project.biz.constant.AdSpaceConstant;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw.inner.PrizeTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.event.astrologydraw.AstrologyDrawEvent;
import cn.yizhoucp.ump.biz.project.biz.event.astrologydraw.AstrologyDrawMessage;
import cn.yizhoucp.ump.biz.project.biz.manager.AstrologyGuideManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.astrologyAdventure.AstrologyAdventureManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.cardCollect.AstrologyCardCollectManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.dreamingIntoTheGalaxy.DreamingIntoTheGalaxyIndexManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawContext;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.luckyBag.HighProbabilityLuckyBagManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.luckyBag.RoomHighProbabilityLuckyBagManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.luckyBagHunt.LuckyBagHuntManager;
import cn.yizhoucp.ump.biz.project.biz.manager.commonActivity.ActivityStatusManager;
import cn.yizhoucp.ump.biz.project.biz.manager.luckyBag.LuckyBagConfigManager;
import cn.yizhoucp.ump.biz.project.biz.manager.luckyBag.LuckyBagManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.UserRemoteService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.userAccount.UserAccountRemoteService;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.common.checker.CheckerContext;
import cn.yizhoucp.ump.biz.project.common.checker.component.bizChecker.GetAdSpaceChecker;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.LuckyBagUserBuyRecordJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.UserLuckDrawInfoJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.UserPrizeRecordDOJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.UserLuckDrawInfoDO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.util.Pair;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.manager.luckyBag.LuckyBagManager.FAMILY_LUCKDRAW_TICKET_PRICE;

/**
 * <AUTHOR>
 * @Description 规则触发的抽奖 福袋调整V2
 * @date 2022-07-08 16:06
 */
@Slf4j
@Service
public class LuckyBagRuleDrawManager extends AbstractDrawManager implements GetAdSpaceChecker {

    @Resource
    private FamilyRoomRelationFeignService familyRoomRelationFeignService;

    @Resource
    private UserAccountRemoteService userAccountRemoteService;

    @Resource
    private DrawAsyncManager drawAsyncManager;

    @Resource
    ActivityStatusManager activityStatusManager;

    @Resource
    UserPrizeRecordDOJpaDAO userPrizeRecordDOJpaDAO;

    @Resource
    UserRemoteService userRemoteService;

    @Resource
    LuckyBagConfigManager luckyBagConfigManager;

    @Resource
    LuckyBagUserBuyRecordJpaDAO luckyBagUserBuyRecordJpaDAO;

    @Resource
    private UserLuckDrawInfoJpaDAO userLuckDrawInfoJpaDAO;

    @Resource
    private LuckyBagManager luckyBagManager;


    @Resource
    private AstrologyGuideManager astrologyGuideManager;

    @Resource
    private LuckyBagHuntManager luckyBagHuntManager;

    @Resource
    HighProbabilityLuckyBagManager highProbabilityLuckyBagManager;

    @Resource
    RoomHighProbabilityLuckyBagManager roomHighProbabilityLuckyBagManager;

    @Resource
    AstrologyCardCollectManager astrologyCardCollectManager;
    @Resource
    private AstrologyAdventureManager astrologyAdventureManager;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Resource
    private DreamingIntoTheGalaxyIndexManager dreamingIntoTheGalaxyIndexManager;

    public static final String ACTIVITY_CODE = "lucky-bag-with-rule";

    /** 抽奖额外赠送礼物的配置 */
    public static final String ADD_GIFT_CONFIG_KEY = "DRAW_ADD_GIFT_CONFIG";

    /** 每日抽奖次数上限 */
    public static final Integer dailyLimitTimes = 50000;
    /** 每日中奖金币上限 */
    public static final Integer dailyLimitPriceCoin = 1300000;
    /** 每日抽奖次数缓存key */
    public static final String DAILY_LIMIT_TIMES_KEY = "DAILY_LIMIT_TIMES_%s_%s";
    /** 每日中奖上限key */
    public static final String DAILY_LIMIT_PRICE_COIN_KEY = "DAILY_LIMIT_PRICE_COIN_%s_%s";

    public void giveGiftHandle(BaseParam param, List<CoinGiftGivedModel> modelList) {
        log.debug("giveGiftHandle data {}", JSON.toJSONString(modelList));
        if (1679155200000L <= System.currentTimeMillis()) {
            return;
        }
        for (CoinGiftGivedModel item : modelList) {
            // 空投礼物不计
            if (GiftFrom.airdrop_family.getCode().equals(item.getFrom())) {
                continue;
            }
            String andCacheSysConfig = luckyBagConfigManager.getAndCacheSysConfig(ADD_GIFT_CONFIG_KEY, DateUtil.ONE_HOUR_SECOND);
            if (andCacheSysConfig.contains(item.getGiftKey())) {
                log.info("福袋活动送礼增加 fromUid:{}, toUid:{}", item.getFromUid(), item.getToUid());
                sendPrizeComponent.sendGift(null, item.getGiftKey(), item.getToUid(), item.getProductCount(), 15, String.format("%s_activity", ACTIVITY_CODE));
            }
        }
    }

    @Override
    protected void resourceCheck(DrawContext context) {
        // 活动开关检测
        if (!activityStatusManager.activityIsEnable(BaseParam.ofMDC(), ActivityCheckListEnum.LUCKY_BAG_WITH_RULE.getCode())) {
            throw new ServiceException(ErrorCode.ACTIVITY_END_ERROR);
        }

        DrawParam drawParam = context.getDrawParam();
        Integer times = drawParam.getTimes();
        SecurityUser user = SecurityUtils.getCurrentUser();
        if (null == user) {
            return;
        }
        Long uid = user.getUserId();
        Long appId = user.getAppId();
        Boolean passCheck = checkPassUserDrawLimit(uid);
        if (!passCheck) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "今日占星已达上限");
        }
        // 高爆期间，消耗 5 倍
        Boolean appOpen = roomHighProbabilityLuckyBagManager.allOpenCondition(appId, drawParam.getUnionId(), uid);
        drawParam.setAllOpen(appOpen);
        if (appOpen) {
            times = times * 5;
            log.info("resourceCheck allOpen uid {} from {} rid {} times {}", uid, drawParam.getFrom(), drawParam.getRelationId(), times);
        }

        // 背包余额是否足够
        boolean enough = luckyBagManager.availableNumCheck(uid, times);
        if (!enough) {
            throw new ServiceException(ErrorCode.COIN_PARAM_ERROR, "simple.user.bag.is.not.enough");
        }
    }

    @Override
    protected void deductResource(DrawContext context) {
        DrawParam drawParam = context.getDrawParam();
        Long uid = drawParam.getUid();
        Long appId = drawParam.getAppId();
        Integer times = drawParam.getTimes();

        //判断是不是存在高爆的礼物
        Boolean appOpen = drawParam.getAllOpen();
        if (appOpen) {
            times = times * 5;
            log.info("deductResource allOpen uid {} from {} rid {} times {}", uid, drawParam.getFrom(), drawParam.getRelationId(), times);
        }
        //进行背包扣减
        Boolean result = Boolean.FALSE;
        Long freeCount = 0L;
        try {
            log.info("调用 UserAccountRemoteService.usePackageLuckyBagTicket 入参 appId:{}, uid:{}, count:{}", appId, uid, times);
            List<UsePackageDTO> usePackageList = userPackageFeignService.usePackageWithLuckyBag(appId, MDCUtil.getCurUnionIdByMdc(), uid, Long.valueOf(times));
            freeCount = usePackageList.stream().filter(one -> Boolean.TRUE.equals(one.getFree())).mapToLong(UsePackageDTO::getNum).sum();
            if (!CollectionUtils.isEmpty(usePackageList)) {
                log.info("调用 UserAccountRemoteService.usePackageLuckyBagTicket 成功 appId {} uid {} count {}", appId, uid, times);
                result = Boolean.TRUE;
            } else {
                result = Boolean.FALSE;
            }
        } catch (Exception e) {
            log.error("调用 UserAccountRemoteService.usePackageLuckyBagTicket 异常", e);
        }

        if (!Boolean.TRUE.equals(result)) {
            throw new ServiceException(ErrorCode.COIN_PARAM_ERROR, "simple.error.request.fast");
        }

        //记录当前的消耗
        incrUserLimitTimes(uid, times);
        drawAsyncManager.userDrawRankIncr(drawParam, times, freeCount);
    }


    @Override
    protected void callback(DrawContext context) {
        SecurityUser user = SecurityUtils.getCurrentUser();
        if (Objects.nonNull(user)) {
            DrawParam drawParam = context.getDrawParam();
            highProbabilityLuckyBagManager.openLuckDraw(user.getUnionId(), user.getAppId(), user.getUserId(), user.getUserPlatform(),
                    user.getVersionName(), LuckDrawType.ruleDraw.getType(), context.getDrawParam().getTimes().longValue());

            List<DrawPoolItemDO> drawPoolItemList = context.getPrizeDOList();
            String relationId = null;
            String scene = null;
            String sceneId = null;
            String extValue = context.getDrawParam().getExtValue();
            if (extValue != null) {
                JSONObject jsonObject = JSONObject.parseObject(extValue);
                relationId = jsonObject.getString("relationId");
                scene = jsonObject.getString("scene");
                sceneId = jsonObject.getString("sceneId");

            }
            for (DrawPoolItemDO item : drawPoolItemList) {
                astrologyCardCollectManager.userAddGift(BaseParam.ofMDC(), item.getItemKey(), item.getTargetTimes());
                Map<String, Object> params = Maps.newHashMap();
                params.put("times", item.getTargetTimes());
                params.put("pool_code", item.getPoolCode());
                params.put("from_room", relationId);
                params.put("award_key", item.getItemKey());
                params.put("award_amount", item.getItemValueGold() * item.getTargetTimes());
                params.put("from_entrance", scene);
                params.put("from_entrance_id", sceneId);
                yzKafkaProducerManager.dataRangerTrack(context.getDrawParam().getAppId(), user.getUserId(), "astrology_lottery", params, ServicesNameEnum.ump_services.getCode());

                try {
                    params = Maps.newHashMapWithExpectedSize(4);
                    params.put("room_id", relationId);
                    if (relationId != null) {
                        FamilyRoomRelationDTO familyRoomRelationDTO = familyRoomRelationFeignService.findByRoomIdAndStatus(Long.valueOf(relationId), CommonStatus.enable.getCode()).successData();
                        if (familyRoomRelationDTO != null) {
                            params.put("family_id", familyRoomRelationDTO.getFamilyId());
                        }
                    }
                    params.put("coin_amount", FAMILY_LUCKDRAW_TICKET_PRICE * drawParam.getTimes());
                    params.put("spend_type", "astrology");
                    yzKafkaProducerManager.dataRangerTrack(context.getDrawParam().getAppId(), user.getUserId(), "family_coin_spend", params, ServicesNameEnum.ump_services.getCode());
                } catch (Exception e) {
                    log.error("family_coin_spend track error roomId {} uid {}", relationId, user.getUserId(), e);
                }
            };

            log.debug("send TOPIC_LUCKY_BAG_DRAW_RESULT");
            JSONObject param = new JSONObject();
            param.put("times", context.getDrawParam().getTimes());
            param.put("prizeList", JSON.toJSONString(drawPoolItemList));
            rocketmqProducerManager.sendNormalMessage(TopicConstant.TOPIC_LUCKY_BAG.getTopicKey(), TopicTagEnum.TOPIC_LUCKY_BAG_DRAW_RESULT.getTagKey(), JSON.toJSONString(param), String.valueOf(System.currentTimeMillis()));

            //进行返奖率处理
            calReturnRate(context);
            luckyBagManager.incrUserDrawLeftCount(drawParam.getUid(), drawParam.getAppId(), drawParam.getTimes().longValue(), drawParam.getUnionId());

            long sum = drawPoolItemList.stream().mapToLong(one -> one.getItemValueGold() * one.getTargetTimes()).sum();
            incrUserLimitPriceCoin(user.getUserId(), (int) sum);

            AstrologyDrawMessage drawMessage = AstrologyDrawMessage.builder()
                    .uid(user.getUserId())
                    .drawTimes(context.getDrawParam().getTimes())
                    .consumeTimes(context.getDrawParam().getAllOpen() ? 5*context.getDrawParam().getTimes() : context.getDrawParam().getTimes())
                    .drawResult(context.getPrizeItemList()).build();
            // 占星完成事件 建议异步处理
            AstrologyDrawEvent event= new AstrologyDrawEvent(drawMessage);
            applicationEventPublisher.publishEvent(event);
            //占星奇旅活动后置处理
            astrologyAdventureManager.drawHandler(event);
            //入梦星河活动增加积分
            dreamingIntoTheGalaxyIndexManager.addPoints(user.getUserId(),context.getDrawParam().getTimes().longValue());

/*
            //增加占星奇旅次数
            String date = astrologyAdventureManager.getDate();
            redisManager.incrLong(String.format(AstrologyAdventureManager.ASTROLOGY_TIMES, date, drawParam.getUid()), drawParam.getTimes(), DateUtil.ONE_DAY_SECOND);
*/
        }
    }


    public Boolean checkAddGift() {
        if (1679155200000L <= System.currentTimeMillis() || System.currentTimeMillis() <= 1678896000000L) {
            return false;
        }
        return true;
    }

    public void drawAddGift(DrawContext context) {
        if (!checkAddGift() || !ServicesAppIdEnum.nuanliao.getUnionId().equals(context.getDrawParam().getUnionId())) {
            return;
        }
        try {
            //获取奖池配置
            String andCacheSysConfig = luckyBagConfigManager.getAndCacheSysConfig(ADD_GIFT_CONFIG_KEY, DateUtil.ONE_HOUR_SECOND);
            log.debug("drawAddGift config {}", andCacheSysConfig);
            Map<String, Integer> map = JSON.parseObject(andCacheSysConfig, Map.class);

            List<DrawPoolItemDO> drawList = getAddGift(context.getDrawParam(), map, context.getDrawParam().getTimes());
            if (CollectionUtils.isEmpty(drawList)) {
                return;
            }
            List<DrawPoolItemDO> drawPoolItemList = context.getDrawPoolItemDOS();
            drawPoolItemList = Objects.isNull(drawPoolItemList) ? new ArrayList<>() : drawPoolItemList;
            drawPoolItemList.addAll(drawList);
            context.setDrawPoolItemDOS(drawPoolItemList);
        } catch (Exception e) {
            log.error("drawAddGift error param {}", JSON.toJSONString(context.getDrawParam()), e);
        }
    }

    public static void main(String[] args) {
        Integer times = 100;
        Map<String, Integer> config = JSON.parseObject("{\"C_GIFT\": 7,\"N_GIFT\": 5,\"H_GIFT\": 2,\"K_GIFT\": 6}", Map.class);

        List<Integer> numsByLimit = RandomUtil.getInstance().getNumsByLimit(1000, times);
        Integer maxNum = config.values().stream().mapToInt(a -> a).sum();
        //获取中奖礼物和次数
        List<Integer> prizeNum = numsByLimit.stream().filter(a -> a <= maxNum).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(prizeNum)) {
            System.out.println("没有中奖");
            return;
        }
        Map<String, Integer> prizeData = Maps.newHashMap();
        Integer lastNum = 0;
        for (Map.Entry<String, Integer> stringIntegerEntry : config.entrySet()) {
            Integer value = stringIntegerEntry.getValue();
            //过滤符合条件的
            Integer finalLastNum = lastNum;
            List<Integer> collect = prizeNum.stream().filter(a -> a >= finalLastNum && a < (finalLastNum + value)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(collect)) {
                prizeData.put(stringIntegerEntry.getKey(), collect.size());
            }
            lastNum += value;
        }

        System.out.println(prizeData);
    }

    /**
     * 获取实际礼物个数
     *
     * @param config key：礼物Key value:权重 10代表 1%
     * @param times
     * @return
     */
    public List<DrawPoolItemDO> getAddGift(DrawParam drawParam, Map<String, Integer> config, Integer times) {
        List<DrawPoolItemDO> result = Lists.newArrayList();
        List<Integer> numsByLimit = RandomUtil.getInstance().getNumsByLimit(1000, times);
        Integer maxNum = config.values().stream().mapToInt(a -> a).sum();
        //获取中奖礼物和次数
        List<Integer> prizeNum = numsByLimit.stream().filter(a -> a <= maxNum).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(prizeNum)) {
            return result;
        }
        Map<String, Integer> prizeData = Maps.newHashMap();
        Integer lastNum = 0;
        for (Map.Entry<String, Integer> stringIntegerEntry : config.entrySet()) {
            Integer value = stringIntegerEntry.getValue();
            //过滤符合条件的
            Integer finalLastNum = lastNum;
            List<Integer> collect = prizeNum.stream().filter(a -> a >= finalLastNum && a < (finalLastNum + value)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(collect)) {
                prizeData.put(stringIntegerEntry.getKey(), collect.size());
            }
            lastNum += value;
        }
        //封装礼物和次数
        List<DrawPoolItemDO> collect = prizeData.entrySet().stream().map(one -> getDrawItemDOByAddGift(one.getKey(), one.getValue())).collect(Collectors.toList());
        log.debug("getAddGift uid {} times {} data {}", drawParam.getUid(), drawParam.getTimes(), JSON.toJSONString(collect));
        result.addAll(collect);

        //下发礼物和次数
        collect.forEach(item -> sendPrizeComponent.sendGift(drawParam.getAppId(), item.getItemKey(), drawParam.getUid(), item.getItemNum().longValue(), 15, String.format("%s_activity", ACTIVITY_CODE)));
        return result;
    }

    public DrawPoolItemDO getDrawItemDOByAddGift(String giftKey, Integer num) {
        DrawPoolItemDO.DrawPoolItemDOBuilder builder = DrawPoolItemDO.builder();
        LocalDateTime now = LocalDateTime.now();
        builder.itemNum(num).itemType("gift").itemEffectiveDay(15).itemValueGold(0L).status(0).createTime(now).updateTime(now).poolCode("ruleAddGift");
        switch (giftKey) {
            case "C_GIFT":
                builder.itemKey("C_GIFT")
                        .itemName("春")
                        .itemIcon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-03/1678778504596275.png");
                break;
            case "N_GIFT":
                builder.itemKey("N_GIFT")
                        .itemName("暖")
                        .itemIcon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-03/1678778555423522.png");
                break;
            case "H_GIFT":
                builder.itemKey("H_GIFT")
                        .itemName("花")
                        .itemIcon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-03/1678778589201275.png");
                break;
            case "K_GIFT":
                builder.itemKey("K_GIFT")
                        .itemName("开")
                        .itemIcon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-03/1678778628429466.png");
                break;
            default:
                return null;
        }
        DrawPoolItemDO build = builder.build();
        build.setTargetTimes(num);
        return build;
    }


    /**
     * 记录返奖率
     *
     * @param context
     */
    public void calReturnRate(DrawContext context) {
        try {
            DrawParam drawParam = context.getDrawParam();
            //计算本次的投入产出
            Integer times = drawParam.getTimes();
            Boolean allOpen = drawParam.getAllOpen();
            List<DrawPoolItemDO> prizeItemList = context.getPrizeDOList();
            long totalOut = prizeItemList.stream().filter(one -> PrizeTypeEnum.PRIZE_GIFT.getCode().equals(one.getItemType()))
                    .mapToLong(a -> (a.getTargetTimes() * a.getItemValueGold())).sum();
            Long totalInput = allOpen ? (10L * times * 5) : (10L * times);
            UserLuckDrawInfoDO userLuckDrawInfoDO = userLuckDrawInfoJpaDAO.findByUserIdAndAppId(drawParam.getUid(), drawParam.getAppId());
            if (null == userLuckDrawInfoDO) {
                userLuckDrawInfoDO = new UserLuckDrawInfoDO();
                userLuckDrawInfoDO.setAppId(drawParam.getAppId());
                userLuckDrawInfoDO.setUserId(drawParam.getUid());
                userLuckDrawInfoDO.setCreateTime(new Date());
                userLuckDrawInfoDO.setUpdateTime(new Date());
                userLuckDrawInfoDO.setTotalInput(0L);
                userLuckDrawInfoDO.setTotalOutput(0L);
                userLuckDrawInfoDO.setCurrentInput(0L);
                userLuckDrawInfoDO.setCurrentOutput(0L);
                userLuckDrawInfoDO.setNotBestGiftInput(0L);
                userLuckDrawInfoDO.setStatus(CommonStatus.enable);
            }
            //记录
            userLuckDrawInfoDO.setTotalInput(userLuckDrawInfoDO.getTotalInput() + totalInput);
            userLuckDrawInfoDO.setTotalOutput(userLuckDrawInfoDO.getTotalOutput() + totalOut);
            userLuckDrawInfoDO.setCurrentInput(userLuckDrawInfoDO.getCurrentInput() + totalInput);
            userLuckDrawInfoDO.setCurrentOutput(userLuckDrawInfoDO.getCurrentOutput() + totalOut);
            userLuckDrawInfoJpaDAO.save(userLuckDrawInfoDO);
        } catch (Exception e) {
            log.error("calReturnRate Error data {}", context, e);
        }
    }

    @Override
    protected void writeLog(DrawContext context) {
//        super.writeLog(context);
        //记录中奖记录
        List<DrawPoolItemDO> prizeDOList = context.getPrizeDOList();
        if (CollectionUtils.isEmpty(prizeDOList)) {
            return;
        }
        drawAsyncManager.savePrizeLog(context, prizeDOList);
    }

    /**
     * 获取7天内的返奖率
     */
    @Async
    public void calUserReturnRate() {
        String now = DateUtil.format(DateUtil.getStartOfDay(new Date()), DateUtil.YMDHMS);
        String start = DateUtil.format(DateUtil.getStartOfDay(DateUtil.getNDaysBefore(1)), DateUtil.YMDHMS);
        //查询购买过的
        List<Long> allUserIds = luckyBagUserBuyRecordJpaDAO.getDrawUserIds(start, now);
        log.info("calUserReturnRate data {}", allUserIds);
        List<List<Long>> partition = Lists.partition(allUserIds, 100);
        for (List<Long> drawUserIds : partition) {
            try {
                //查询这些用户使用了多少次
//                Map<Long, Integer> userUsedDrawByTime = userRemoteService.getUserUsedDrawByTime(drawUserIds, start, now);
                //本地查询中奖记录
//                List<Map<String, Object>> userPriceTotalCoin = userPrizeRecordDOJpaDAO.getUserPriceTotalCoin(drawUserIds, start, now);
//                Map<Long, Integer> userPrize = Maps.newHashMap();
//                userPriceTotalCoin.forEach(one -> {
//                    BigInteger userId = (BigInteger) one.get("userId");
//                    BigDecimal num = (BigDecimal) one.get("num");
//                    userPrize.put(userId.longValue(), num.intValue());
//                });

                //数据库更新
                List<UserLuckDrawInfoDO> byUserIdIn = userLuckDrawInfoJpaDAO.findByUserIdIn(drawUserIds);
                Map<Long, UserLuckDrawInfoDO> userDrawInfoMap = byUserIdIn.stream().collect(Collectors.toMap(UserLuckDrawInfoDO::getUserId, u -> u, (o1, o2) -> o1));
//                for (Long userId : userUsedDrawByTime.keySet()) {
                for (Long userId : userDrawInfoMap.keySet()) {
                    try {
//                        Integer input = userUsedDrawByTime.get(userId);
//                        Integer outPut = userPrize.get(userId);
//                        if (null == input || null == outPut) {
//                            continue;
//                        }
                        Integer input = 0;
                        Integer outPut = 0;
                        UserLuckDrawInfoDO userLuckDrawInfoDO = userDrawInfoMap.get(userId);
                        if (null == userLuckDrawInfoDO) {
                            userLuckDrawInfoDO = new UserLuckDrawInfoDO();
                            userLuckDrawInfoDO.setAppId(ServicesAppIdEnum.lanling.getAppId());
                            userLuckDrawInfoDO.setUserId(userId);
                            userLuckDrawInfoDO.setCreateTime(new Date());
                            userLuckDrawInfoDO.setUpdateTime(new Date());
                            userLuckDrawInfoDO.setTotalInput(input.longValue());
                            userLuckDrawInfoDO.setTotalOutput(outPut.longValue());
                            userLuckDrawInfoDO.setCurrentInput(0L);
                            userLuckDrawInfoDO.setCurrentOutput(0L);
                            userLuckDrawInfoDO.setNotBestGiftInput(0L);
                            userLuckDrawInfoDO.setStatus(CommonStatus.enable);
                        }
                        userLuckDrawInfoDO.setCurrentInput(input.longValue() * 10);
                        userLuckDrawInfoDO.setCurrentOutput(outPut.longValue());
                        log.info("calUserReturnRate one Data uid {} , input {} , output {}", userId, input, outPut);
                        userLuckDrawInfoJpaDAO.save(userLuckDrawInfoDO);
                    } catch (Exception e) {
                        log.error("calUserReturnRate save Error userId {}", userId);
                    }
                }
            } catch (Exception e) {
                log.error("calUserReturnRate Error ids {} ", drawUserIds, e);
            }
        }
    }

    /**
     * 检查用户是否通过检查
     *
     * @param userId
     * @return true 通过检查可以操作 false 达到限制不可以抽奖
     */
    public Boolean checkPassUserDrawLimit(Long userId) {
        Pair<Long, Long> userDrawLimit = getUserDrawLimit(userId);
        Long times = userDrawLimit.getFirst();
        Long coin = userDrawLimit.getSecond();
        if (times > dailyLimitTimes || coin > dailyLimitPriceCoin) {
            return false;
        }
        return true;
    }

    public Pair<Long, Long> getUserDrawLimit(Long userId) {
        String timesKey = getLimitKey(DAILY_LIMIT_TIMES_KEY, userId);
        String priceKey = getLimitKey(DAILY_LIMIT_PRICE_COIN_KEY, userId);
        Long times = redisManager.getLong(timesKey);
        Long coin = redisManager.getLong(priceKey);
        return Pair.of(Optional.ofNullable(times).orElse(0L), Optional.ofNullable(coin).orElse(0L));
    }

    /**
     * 添加用户的抽奖奖励记录
     *
     * @param userId
     * @param coin
     * @return
     */
    public Long incrUserLimitPriceCoin(Long userId, Integer coin) {
        String limitKey = getLimitKey(DAILY_LIMIT_PRICE_COIN_KEY, userId);
        Long aLong = redisManager.incrLong(limitKey, coin, RedisManager.ONE_DAY_SECONDS);
        return aLong;
    }


    /**
     * 添加用户抽奖次数记录
     *
     * @param userId
     * @param times
     * @return
     */
    public Long incrUserLimitTimes(Long userId, Integer times) {
        log.debug("incrUserLimitTimes uid {} times {}", userId, times);
        String limitKey = getLimitKey(DAILY_LIMIT_TIMES_KEY, userId);
        Long aLong = redisManager.incrLong(limitKey, times, RedisManager.ONE_DAY_SECONDS);
        return aLong;
    }

    /**
     * 获取每日限制的key
     *
     * @param key
     * @param userId
     * @return
     */
    public String getLimitKey(String key, Long userId) {
        int hour = LocalDateTime.now().getHour();
        String dateStr = DateUtil.getNowYyyyMMdd();
        if (hour < 4) {
            dateStr = DateUtil.format(DateUtil.yesterday(), DateUtil.YMD_WITHOUT_LINE);
        }
        return String.format(key, dateStr, userId);
    }

    @Override
    public String getActivityCode() {
        return ActivityCheckListEnum.LUCKY_BAG_HIGH_PROBABILITY.getCode();
    }

    @Override
    public BiFunction<CheckerContext, JSONObject, Boolean> getQueryAdSpaceChecker() {
        return (context, param) -> {
//            log.debug("getQueryAdSpaceChecker appId {} uid {} param {}", MDCUtil.getCurAppIdByMdc(), MDCUtil.getCurUserIdByMdc(), param.toJSONString());
            // check param
            Long uid = param.getLong("uid");
            String activityCode = param.getString("activityCode");
            log.debug("查询资源位回调检查 uid:{} activityCode {}", uid, activityCode);
            if (Objects.isNull(uid)) {
                return Boolean.FALSE;
            }

            // 如果不是所属活动直接返回
            if (!StringUtils.equalsIgnoreCase(activityCode, getActivityCode())) {
                return Boolean.TRUE;
            }

            // 1、检查活动状态
//            if (Boolean.FALSE.equals(activityStatusManager.activityIsEnable(BaseParam.ofMDC(), getActivityCode()))) {
//                return Boolean.FALSE;
//            }
            // 2、检查豪气值
            return checkHaughtyValue(uid);
        };
    }

    /**
     * 检查豪气值
     *
     * @param uid
     * @return
     */
    private Boolean checkHaughtyValue(Long uid) {
        UserCharmGenerosityVO userCharmGenerosityVO = feignSnsService.getUserCharmAndGenerosity(MDCUtil.getCurAppIdByMdc(), uid).successData();
        if (Objects.isNull(userCharmGenerosityVO)) {
            log.info("checkHaughtyValue getUserCharmAndGenerosity not existed or call fail");
            return Boolean.FALSE;
        }
        log.debug("LuckyBagRuleDrawManager checkHaughtyValue userCharmGenerosityVO {}", JSON.toJSONString(userCharmGenerosityVO));
        return userCharmGenerosityVO.getGenerosityLevel() >= 3;
    }

    public Boolean clearAdSpaceCache(BaseParam param,Long uid) {
        Boolean enable = activityStatusManager.activityIsEnable(param, getActivityCode());
        if (!enable) {
            return false;
        }
        redisManager.delete(String.format(AdSpaceConstant.AD_SPACE, uid));
        return Boolean.TRUE;
    }

}
