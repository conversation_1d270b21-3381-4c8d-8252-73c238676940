package cn.yizhoucp.ump.biz.project.web.rest.controller.activity.astrology;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.astrology.starWishCardCollectionGame.StarWishCardCollectionGameIndexManager;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
public class StarWishCardCollectionGameController {

    @Resource
    private StarWishCardCollectionGameIndexManager starWishCardCollectionGameIndexManager;

    @GetMapping("/api/inner/activity/star_wish_card_collection_game/exchange_card")
    public Result<List<Integer>> exchangeCard(BaseParam param, String cardCode) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> starWishCardCollectionGameIndexManager.exchangeCard(param, cardCode));
    }

    @GetMapping("/api/inner/activity/star_wish_card_collection_game/mission_receive")
    public Result<Boolean> missionReceive(BaseParam param, String taskCode) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> starWishCardCollectionGameIndexManager.missionReceive(param, taskCode));
    }

}
