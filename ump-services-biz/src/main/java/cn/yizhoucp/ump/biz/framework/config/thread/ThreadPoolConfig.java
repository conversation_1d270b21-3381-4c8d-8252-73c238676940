package cn.yizhoucp.ump.biz.framework.config.thread;

import cn.yizhoucp.ms.core.base.config.ContextCopyingDecorator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池配置
 *
 * @author: lianghu
 */
@Configuration
public class ThreadPoolConfig {

    /** 核心线程数 */
    private static final int CORE_POOL_SIZE = 50;
    /** 最大线程数 */
    private static final int MAX_POOL_SIZE = 100;
    /** 空闲线程最长等待时间 */
    private static final int KEEP_ALIVE_TIME = 50;
    /** 任务队列长度 */
    private static final int QUEUE_CAPACITY = 15000;
    /** 线程池名称前缀 */
    private static final String BIZ_THREAD_POOL = "%s-thread-pool-";

    /**
     * 活动业务线程池
     *
     * @param
     * @return org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
     */
    @Bean("redPacketHandleThreadPool")
    public ThreadPoolTaskExecutor redPacketHandleThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setTaskDecorator(new ContextCopyingDecorator());
        executor.setCorePoolSize(CORE_POOL_SIZE);
        executor.setMaxPoolSize(MAX_POOL_SIZE);
        executor.setQueueCapacity(QUEUE_CAPACITY);
        executor.setKeepAliveSeconds(KEEP_ALIVE_TIME);
        executor.setThreadNamePrefix(String.format(BIZ_THREAD_POOL, "redPacket-handle"));
        // 调用线程执行策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 等待任务完成
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 避免阻塞
        executor.setAwaitTerminationSeconds(60);
        executor.initialize();
        return executor;
    }

    /**
     * 活动业务线程池
     *
     * @param
     * @return org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
     */
    @Bean("activityHandleThreadPool")
    public ThreadPoolTaskExecutor activityHandleThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setTaskDecorator(new ContextCopyingDecorator());
        executor.setCorePoolSize(CORE_POOL_SIZE);
        executor.setMaxPoolSize(MAX_POOL_SIZE);
        executor.setQueueCapacity(QUEUE_CAPACITY);
        executor.setKeepAliveSeconds(KEEP_ALIVE_TIME);
        executor.setThreadNamePrefix(String.format(BIZ_THREAD_POOL, "activity-handle"));
        // 调用线程执行策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 等待任务完成
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 避免阻塞
        executor.setAwaitTerminationSeconds(60);
        executor.initialize();
        return executor;
    }

    /**
     * 异步日志线程池
     *
     * @param
     * @return org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
     */
    @Bean("logHandleThreadPool")
    public ThreadPoolTaskExecutor logHandleThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setTaskDecorator(new ContextCopyingDecorator());
        executor.setCorePoolSize(CORE_POOL_SIZE);
        executor.setMaxPoolSize(MAX_POOL_SIZE);
        executor.setQueueCapacity(QUEUE_CAPACITY);
        executor.setKeepAliveSeconds(KEEP_ALIVE_TIME);
        executor.setThreadNamePrefix(String.format(BIZ_THREAD_POOL, "biz-log"));
        // 调用线程执行策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 等待任务完成
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 避免阻塞
        executor.setAwaitTerminationSeconds(60);
        executor.initialize();
        return executor;
    }

    /**
     * 埋点上报日志线程池
     *
     * @param
     * @return org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
     */
    @Bean("reportHandleThreadPool")
    public ThreadPoolTaskExecutor reportHandleThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setTaskDecorator(new ContextCopyingDecorator());
        executor.setCorePoolSize(CORE_POOL_SIZE);
        executor.setMaxPoolSize(MAX_POOL_SIZE);
        executor.setQueueCapacity(QUEUE_CAPACITY);
        executor.setKeepAliveSeconds(KEEP_ALIVE_TIME);
        executor.setThreadNamePrefix(String.format(BIZ_THREAD_POOL, "data-report"));
        // 调用线程执行策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardPolicy());
        // 等待任务完成
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 避免阻塞
        executor.setAwaitTerminationSeconds(60);
        executor.initialize();
        return executor;
    }

    /**
     * 获取榜单线程池处理
     *
     * @param
     * @return org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
     */
    @Bean("rankHandleThreadPool")
    public ThreadPoolTaskExecutor rankHandleThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setTaskDecorator(new ContextCopyingDecorator());
        executor.setCorePoolSize(CORE_POOL_SIZE);
        executor.setMaxPoolSize(MAX_POOL_SIZE);
        executor.setQueueCapacity(QUEUE_CAPACITY);
        executor.setKeepAliveSeconds(KEEP_ALIVE_TIME);
        executor.setThreadNamePrefix(String.format(BIZ_THREAD_POOL, "rank-log"));
        // 调用线程执行策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 等待任务完成
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 避免阻塞
        executor.setAwaitTerminationSeconds(60);
        executor.initialize();
        return executor;
    }

}
