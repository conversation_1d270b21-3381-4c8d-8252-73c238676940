package cn.yizhoucp.ump.biz.project.web.rest.controller.goddess;

import cn.yizhoucp.ump.biz.project.biz.manager.goddessTrain.GoddessTrainSignUpManager;
import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ms.core.vo.CommonResultVO;
import cn.yizhoucp.ms.core.vo.landingservices.AdminPageVO;
import cn.yizhoucp.ump.api.vo.goddessTrain.BindWechatQrCodeVO;
import cn.yizhoucp.ump.api.vo.goddessTrain.UserSignUpInfoVO;
import cn.yizhoucp.ump.api.vo.goddessTrain.WechatQrCodeVO;
import com.alibaba.fastjson.JSONObject;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 女神扶持-报名
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/inner/activity/goddess-train/sign-up")
public class GoddessTrainSignUpAdminController {

    @Resource
    GoddessTrainSignUpManager goddessTrainSignUpManager;

    /**
     * 分页获取用户报名信息列表
     *
     * @param unionId   应用唯一标识
     * @param appId     应用id
     * @param pageIndex 页码
     * @param size      每页条数
     * @return AdminPageVO<UserSignUpInfoVO>
     */
    @GetMapping("/get-sign-up-list")
    public Result<AdminPageVO<UserSignUpInfoVO>> getUserSignUpInfoList(String unionId, Long appId, Integer pageIndex, Integer size) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> goddessTrainSignUpManager.getUserSignUpInfoList(unionId, appId, pageIndex, size));
    }

    /**
     * 培训活动绑定群二维码
     *
     * @param bindWechatQrCode 请求参数
     * @return CommonResultVO
     */
    @PostMapping("/save-or-update-qr")
    public Result<CommonResultVO> bindWechatQrCode(@RequestBody BindWechatQrCodeVO bindWechatQrCode) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> goddessTrainSignUpManager.bindWechatQrCode(bindWechatQrCode));
    }

    /**
     * 获取可用的培训活动 code 列表
     *
     * @param unionId   应用唯一标识
     * @param appId     应用id
     * @return List<JSONObject>
     */
    @GetMapping("/train-list")
    public Result<List<JSONObject>> getAvailableTrainList(String unionId, Long appId) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> goddessTrainSignUpManager.getAvailableTrainList(unionId, appId));
    }

    /**
     * 获取群微信二维码信息列表
     *
     * @param unionId   应用唯一标识
     * @param appId     应用id
     * @param pageIndex 当前页码
     * @param size      每页条数
     * @return AdminPageVO<WechatQrCodeVO>
     */
    @GetMapping("/wechat-qr-code-list")
    public Result<AdminPageVO<WechatQrCodeVO>> getWechatQrCodeList(String unionId, Long appId, Integer pageIndex, Integer size) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> goddessTrainSignUpManager.getWechatQrCodeList(unionId, appId, pageIndex, size));
    }

}
