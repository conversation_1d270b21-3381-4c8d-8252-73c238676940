package cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.strategy;

import cn.hutool.core.text.CharSequenceUtil;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.MainChineseNewYear2025TrackManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.common.MainChineseNewYear2025Constant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.common.MainChineseNewYear2025Enums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.common.MainChineseNewYear2025RedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.log.LogComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class ClaimCompletedDishStrategy implements MainChineseNewYear2025Strategy {
    @Resource
    private MainChineseNewYear2025RedisManager mainChineseNewYear2025RedisManager;
    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    private LogComponent logComponent;
    @Resource
    private MainChineseNewYear2025TrackManager mainChineseNewYear2025TrackManager;

    @Override
    public Boolean execute(ButtonEventParam buttonEventParam) {
        Long uid = buttonEventParam.getBaseParam().getUid();
        //获取当前制作的菜品
        String currentDish = mainChineseNewYear2025RedisManager.getCurrentDish(uid);
        //下发菜品
        if (CharSequenceUtil.isNotBlank(currentDish)) {
            mainChineseNewYear2025RedisManager.incrementDish(uid, currentDish, MainChineseNewYear2025Constant.DEFAULT_INCREMENT_NUM.intValue());
            //清空制作菜品状态
            mainChineseNewYear2025RedisManager.clearCurrentDish(uid);
            log.info("Dish creation successful uid: {},dishKey: {}", uid, currentDish);
            //记录日志
            recordSellItem(buttonEventParam.getBaseParam(), MainChineseNewYear2025Enums.DishsEnum.getByDishCode(currentDish));
            //埋点
            MainChineseNewYear2025Enums.DishsEnum dishsEnum = MainChineseNewYear2025Enums.DishsEnum.getByDishCode(currentDish);
            if (dishsEnum != null) {
                mainChineseNewYear2025TrackManager.allActivityTaskFinish(uid, dishsEnum.getTrackCode());
            }
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    private void recordSellItem(BaseParam baseParam, MainChineseNewYear2025Enums.DishsEnum dishsEnum) {
        List<DrawPoolItemDTO> prizeItemList = buildExchangeItemList(dishsEnum, MainChineseNewYear2025Constant.SELL_DISH_RECORD_CODE);
        logComponent.putDrawLog(baseParam, MainChineseNewYear2025Constant.ACTIVITY_CODE, prizeItemList);
    }

    private List<DrawPoolItemDTO> buildExchangeItemList(MainChineseNewYear2025Enums.DishsEnum dishsEnum, String recordCode) {
        String msg = MainChineseNewYear2025Constant.SELL_DISH_RECORD_MSG;
        msg = String.format(msg, dishsEnum.getDishName());
        //查询食材
        List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), MainChineseNewYear2025Constant.ACTIVITY_CODE, dishsEnum.getDishCode());
        StringBuilder ingredients = new StringBuilder();
        for (ScenePrizeDO scenePrizeDO : scenePrizeDOList) {
            if (ingredients.length() > 0) {
                ingredients.append("、");
            }
            ingredients.append(scenePrizeDO.getPrizeDesc()).append("*").append(scenePrizeDO.getPrizeNum());
        }
        msg = msg + "，消耗" + ingredients;
        DrawPoolItemDO drawPoolItemDO = DrawPoolItemDO.builder()
                .itemName(msg)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .itemKey(dishsEnum.getDishCode())
                .itemNum(1)
                .itemValueGold(0L)
                .status(1)
                .poolCode(recordCode)
                .build();
        DrawPoolItemDTO drawPoolItemDTO = DrawPoolItemDTO.builder()
                .targetTimes(1)
                .drawPoolItemDO(drawPoolItemDO).build();
        List<DrawPoolItemDTO> prizeItemList = new ArrayList<>();
        prizeItemList.add(drawPoolItemDTO);
        return prizeItemList;
    }

}
