package cn.yizhoucp.ump.biz.project.biz.manager.activity.galaxyAdventure;

import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.enums.ServicesNameEnum;
import cn.yizhoucp.starter.cassandra.base.kafka.YzKafkaProducerManager;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * 银河探险-抽奖处理类
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class GalaxyAdventureTrackManager {

    @Resource
    private YzKafkaProducerManager yzKafkaProducerManager;

    /**
     * 通用埋点_活动抽奖
     *
     * @param uid 用户id
     * @param poolCode 奖池
     * @param awardCount  获得奖励的个数
     * @param awardAmount 奖品对应的金币价值
     */
    public void allActivityLottery(Long uid, String poolCode, String boxName, String awardKey, Integer awardCount, Long awardAmount) {
        Map<String, Object> params = new HashMap<>();
        params.put("activity_type", "galaxy_adventure_box_openning");
        params.put("galaxy_adventure_box_name", boxName);
        params.put("attribute_type", "astrology_activity");
        params.put("pool_code", poolCode);
        params.put("award_key", awardKey);
        params.put("award_count", awardCount);
        params.put("award_amount", awardAmount);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_lottery", params, ServicesNameEnum.ump_services.getCode());
    }

    /**
     * 通用埋点_活动任务完成
     *
     * @param uid 用户id
     * @param taskType 任务类型
     * @param index 熔炼方案
     */
    public void allActivityTaskFinish(Long uid, String taskType, Long index) {
        Map<String, Object> params = Maps.newHashMapWithExpectedSize(4);
        params.put("activity_type", "galaxy_adventure");
        params.put("attribute_type", "astrology_activity");
        params.put("task_type", taskType);
        if (Objects.nonNull(index)) {
            params.put("fuel_schema_id", index);
        }
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_task_finish", params, ServicesNameEnum.ump_services.getCode());
    }

    /**
     * 通用埋点_ 活动领取奖励
     */
    public void allActivityReceiveAward(Long uid, String awardKey, Long awardAmount, Integer awardCount) {
        Map<String, Object> params = Maps.newHashMapWithExpectedSize(5);
        params.put("activity_type", "galaxy_adventure");
        params.put("attribute_type", "astrology_activity");
        params.put("award_key", awardKey);
        params.put("award_amount", awardAmount);
        params.put("award_count", awardCount);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_receive_award", params, ServicesNameEnum.ump_services.getCode());
    }

}
