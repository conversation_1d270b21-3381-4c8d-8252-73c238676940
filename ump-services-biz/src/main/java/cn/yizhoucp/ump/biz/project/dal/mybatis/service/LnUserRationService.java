package cn.yizhoucp.ump.biz.project.dal.mybatis.service;

import cn.yizhoucp.ump.biz.project.dal.mybatis.domain.LnUserRation;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ln_user_ration(传说中的梦幻岛用户-口粮表)】的数据库操作Service
* @createDate 2024-12-08 18:44:23
*/
public interface LnUserRationService extends IService<LnUserRation> {

    /**
     * 新增口粮
     */
    void addRation(Long uid, String rationKey, Long addRation);

    /**
     * 扣减食物
     */
    boolean deductRation(Long uid, String rationKey, Long deductRation);

    /**
     * 获取用户口粮
     */
    List<LnUserRation> getUserRationList(Long uid, Long limit);

}
