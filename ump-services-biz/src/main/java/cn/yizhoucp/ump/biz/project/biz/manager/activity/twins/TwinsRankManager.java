package cn.yizhoucp.ump.biz.project.biz.manager.activity.twins;

import cn.hutool.core.util.ObjectUtil;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.TwinsConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.AbstractRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.biz.manager.userActivity.ActivityManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ActivityJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.DrawPoolItemJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ActivityDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 榜单处理
 *
 * <AUTHOR>
 * @Date 2023/5/31 15:37
 * @Version 1.0
 */
@Slf4j
@Service
public class TwinsRankManager extends AbstractRankManager {

    private static final String SEND_PRIZE_IDEMPOTENT = "ump:twins:send_prize_idempotent:%s";

    @Resource
    private ActivityManager activityManager;
    @Lazy
    @Resource
    private TwinsPageManager twinsPageManager;
    @Resource
    private ActivityJpaDAO activityJpaDAO;
    @Resource
    private DrawPoolItemJpaDAO drawPoolItemJpaDAO;
    @Resource
    private NotifyComponent notifyComponent;
    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    private TwinsDrawV2Manager drawV2Manager;


    /**
     * 活动榜单累计
     *
     * @param uid
     * @param incrVal
     * @param activityCode
     */
    public void incrRankValueByActivityCode(Long uid, Long incrVal, String activityCode) {
        log.debug("incrRankValueByActivityCode uid {} incrVal {} activityCode {}", uid, incrVal, activityCode);
        if (Objects.isNull(uid) || Objects.isNull(incrVal) || StringUtils.isBlank(activityCode)) {
            throw new ServiceException(ErrorCode.MISS_PARAM);
        }
        if (incrVal < 0) {

            Double score = Optional.ofNullable(redisManager.score(this.getRankKey(LocalDateTime.now()), uid.toString())).orElse(0d);
            if (score + incrVal < 0) {
                redisManager.zSetAdd(this.getRankKey(LocalDateTime.now()), uid.toString(), 0d, DateUtil.ONE_MONTH_SECOND, TimeUnit.SECONDS);
            } else {
                this.incrRankValue(uid, incrVal, this.getRankKey(LocalDateTime.now()));
            }
        } else {
            this.incrRankValue(uid, incrVal, this.getRankKey(LocalDateTime.now()));
        }
    }

    /**
     * 下发榜单奖励
     */
    public Boolean twinsSendRankPrize(String param) {
        LocalDateTime now = LocalDateTime.now();
        now = now.minusDays(1L);
        TwinsConstant.ConstellationEnum constellationEnum;
        if (ObjectUtil.isNull(param)) {
            constellationEnum = drawV2Manager.getByMonthAndDay(now.getMonthValue(), now.getDayOfMonth());
        } else {
            constellationEnum = TwinsConstant.ConstellationEnum.getByName(param);
        }

        if (Boolean.TRUE.equals(redisManager.setnx(String.format(SEND_PRIZE_IDEMPOTENT, constellationEnum.name()), System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND))) {
            String activityCode = twinsPageManager.getActivityCode();
            log.info("saveActivityData activityCode {} rankKey {}", activityCode, String.format("ump:twins:board:%s", constellationEnum.name()));
            RankVO rank = this.getRank(RankContext.builder()
                    .innerInvoker(Boolean.TRUE)
                    .activityCode(activityCode)
                    .rankKey(String.format("ump:twins:board:%s", constellationEnum.name()))
                    .build());
            log.info("getRank result {}", JSON.toJSONString(rank.getRankList()));
            List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(1L, TwinsConstant.ACTIVITY_CODE, "rank");
            for (RankItem rankItem : rank.getRankList()) {
                Long rankIndex = rankItem.getRank();
                List<ScenePrizeDO> scenePrizeDOs = scenePrizeDOList.stream().filter(scenePrizeDO -> Objects.equals(rankIndex, scenePrizeDO.getPrizeBelongToRank())).collect(Collectors.toList());
                if (org.apache.commons.collections4.CollectionUtils.isEmpty(scenePrizeDOs)) {
                    continue;
                }
                sendPrizeManager.sendPrize(
                        BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(rankItem.getId()).build(),
                        scenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, rankItem.getId())).collect(Collectors.toList())
                );
                notifyComponent.npcNotify(ServicesAppIdEnum.lanling.getUnionId(), rankItem.getId(), String.format("恭喜您在“守护%s”活动中，荣获榜单第%s！礼物奖励已下发至您背包，请注意查收哦～", constellationEnum.getDesc(), rankItem.getRank()));
                if (rankItem.getRank() == 1) {
                    // 记录
                    saveRankTopOne(ServicesAppIdEnum.lanling.getAppId(), ServicesAppIdEnum.lanling.getUnionId(), activityCode, rankItem.getId());
                    saveRankTopOne(ServicesAppIdEnum.huayuan.getAppId(), ServicesAppIdEnum.huayuan.getUnionId(), activityCode, rankItem.getId());
                    saveRankTopOne(ServicesAppIdEnum.lianainiang.getAppId(), ServicesAppIdEnum.lianainiang.getUnionId(), activityCode, rankItem.getId());
                    saveRankTopOne(ServicesAppIdEnum.yumo.getAppId(), ServicesAppIdEnum.yumo.getUnionId(), activityCode, rankItem.getId());
                    saveRankTopOne(ServicesAppIdEnum.momogirl.getAppId(), ServicesAppIdEnum.yumo.getUnionId(), activityCode, rankItem.getId());
                    saveRankTopOne(ServicesAppIdEnum.couduis.getAppId(), ServicesAppIdEnum.yumo.getUnionId(), activityCode, rankItem.getId());
                }
            }
/*
            for (RankItem rankItem : rank.getRankList()) {
//                 log.info("saveActivityData uid {} rank {} prize {}", rankItem.getId(), rankItem.getRank(), RANK_REWARD.get(rankItem.getRank()));
                List<DrawPoolItemDO> drawPoolItemDOList = drawPoolItemJpaDAO.findByPoolCodeIn(Arrays.asList(TwinsConstant.BoxPoolEnum.HHXYLH_GIFT_H.name(), TwinsConstant.BoxPoolEnum.GJXYLH_GIFT_H.name(), TwinsConstant.BoxPoolEnum.CJXYLH_GIFT_H.name()));
                List<DrawPoolItemDTO> drawPoolItemDTOList = null;
                if (rankItem.getRank() == 1) {
                    drawPoolItemDTOList = DrawPoolItemDO.convert2DrawPoolItemDTO(drawPoolItemDOList.stream().filter(drawPoolItemDO -> drawPoolItemDO.getItemValueGold() == 30000L && TwinsConstant.BoxPoolEnum.HHXYLH_GIFT_H.name().equals(drawPoolItemDO.getPoolCode())).collect(Collectors.toList()));
                } else if (rankItem.getRank() == 2) {
                    drawPoolItemDTOList = DrawPoolItemDO.convert2DrawPoolItemDTO(drawPoolItemDOList.stream().filter(drawPoolItemDO -> drawPoolItemDO.getItemValueGold() == 10000L && TwinsConstant.BoxPoolEnum.GJXYLH_GIFT_H.name().equals(drawPoolItemDO.getPoolCode())).collect(Collectors.toList()));
                } else if (rankItem.getRank() == 3) {
                    drawPoolItemDTOList = DrawPoolItemDO.convert2DrawPoolItemDTO(drawPoolItemDOList.stream().filter(drawPoolItemDO -> drawPoolItemDO.getItemValueGold() == 6666L && TwinsConstant.BoxPoolEnum.HHXYLH_GIFT_H.name().equals(drawPoolItemDO.getPoolCode())).collect(Collectors.toList()));
                } else if (rankItem.getRank() == 4 || rankItem.getRank() == 5) {
                    drawPoolItemDTOList = DrawPoolItemDO.convert2DrawPoolItemDTO(drawPoolItemDOList.stream().filter(drawPoolItemDO -> drawPoolItemDO.getItemValueGold() == 3000L && TwinsConstant.BoxPoolEnum.HHXYLH_GIFT_H.name().equals(drawPoolItemDO.getPoolCode())).collect(Collectors.toList()));
                } else if (rankItem.getRank() == 6 || rankItem.getRank() == 7) {
                    drawPoolItemDTOList = DrawPoolItemDO.convert2DrawPoolItemDTO(drawPoolItemDOList.stream().filter(drawPoolItemDO -> drawPoolItemDO.getItemValueGold() == 1200L && TwinsConstant.BoxPoolEnum.HHXYLH_GIFT_H.name().equals(drawPoolItemDO.getPoolCode())).collect(Collectors.toList()));
                } else if (rankItem.getRank() == 8 || rankItem.getRank() == 9 || rankItem.getRank() == 10) {
                    drawPoolItemDTOList = DrawPoolItemDO.convert2DrawPoolItemDTO(drawPoolItemDOList.stream().filter(drawPoolItemDO -> drawPoolItemDO.getItemValueGold() == 520L && TwinsConstant.BoxPoolEnum.CJXYLH_GIFT_H.name().equals(drawPoolItemDO.getPoolCode())).collect(Collectors.toList()));
                }
                if (drawPoolItemDTOList != null) {
                    sendPrizeManager.sendPrize(
                            BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(rankItem.getId()).build(),
                            drawPoolItemDTOList.stream().map(drawPoolItemDTO -> {
                                SendPrizeDTO sendPrizeDTO = SendPrizeDTO.of(drawPoolItemDTO, activityCode);
                                sendPrizeDTO.setPrizeNum(1);
                                return sendPrizeDTO;
                            }).collect(Collectors.toList())
                    );
                    notifyComponent.npcNotify(ServicesAppIdEnum.lanling.getUnionId(), rankItem.getId(), String.format("恭喜您在“守护%s”活动中，荣获榜单第%s！礼物奖励已下发至您背包，请注意查收哦～", constellationEnum.getDesc(), rankItem.getRank()));
                }
*/
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    private void saveRankTopOne(Long appId, String unionId, String activity, Long top1) {
        log.info("saveRankTopOne appId  {} unionId {} activityCode {} top1 {}", appId, unionId, activity, top1);
        ActivityDO activityDO = activityManager.getActivityInfo(BaseParam.builder().appId(appId).unionId(unionId).build(), activity);
        if (Objects.isNull(activityDO)) {
            return;
        }
        String config = activityDO.getConfig();
        JSONObject jsonObject = JSON.parseObject(config);
        Map<String, Integer> endorsementInfoByConfig = twinsPageManager.getEndorsementInfoByConfig(config);
        LocalDateTime now = LocalDateTime.now();
        now = now.minusDays(1L);
        TwinsConstant.ConstellationEnum constellationEnum = drawV2Manager.getByMonthAndDay(now.getMonthValue(), now.getDayOfMonth());
        endorsementInfoByConfig.put(constellationEnum.name(), top1.intValue());
        jsonObject.put("bizConfig", JSON.toJSONString(endorsementInfoByConfig));
        activityDO.setConfig(jsonObject.toJSONString());
        activityJpaDAO.saveAndFlush(activityDO);
    }

    @Override
    protected void doPreProcess(RankContext rankContext) {
        // 设置榜单长度
        rankContext.setRankLen(20L);
        log.debug("doPreProcess context {}", JSON.toJSONString(rankContext));
    }

    @Override
    protected void postProcess(RankContext rankContext) {
        // 处理星运值问题
        dealWithShowValue(rankContext.getRankVO().getMyselfRank());
        if (Objects.isNull(rankContext.getRankVO()) || CollectionUtils.isEmpty(rankContext.getRankVO().getRankList())) {
            return;
        }
        rankContext.getRankVO().getRankList().forEach(this::dealWithShowValue);
    }

    private String getRankKey(LocalDateTime localDateTime) {
//        log.debug("getRankKeyByActivityCode activityCode {}",activityCode);
//        if (StringUtils.isBlank(activityCode)) {
//            throw new ServiceException(ErrorCode.INVALID_PARAM);
//        }
//        log.debug("getRankKeyByActivityCode localCache {}", JSON.toJSONString(RANK_KEY_CACHE));
//        // 本地缓存中获取数据
//        String cache = RANK_KEY_CACHE.get(activityCode);
//        if (StringUtils.isNotBlank(cache)) {
//            return cache;
//        }
//        RLock lock = redissonClient.getLock(INIT_RANK_KEY_LOCK);
//        lock.lock();
//        try {
//            ActivityDO activityDO = activityManager.getOnlineActivityInfo(BaseParam.ofMDC(), twinsPageManager.getActivityCode());
//            if (Objects.isNull(activityDO)) {
//                throw new ServiceException(ErrorCode.ACTIVITY_NONE);
//            }
//            String rankKey = activityDO.getRankKey();
//            if (StringUtils.isNotBlank(rankKey)) {
//                RANK_KEY_CACHE.put(activityCode, rankKey);
//                return rankKey;
//            }
//        } finally {
//            lock.unlock();
//        }
//        throw new ServiceException(ErrorCode.MISS_PARAM);
        TwinsConstant.ConstellationEnum constellationEnum = drawV2Manager.getByMonthAndDay(localDateTime.getMonthValue(), localDateTime.getDayOfMonth());
        return String.format("ump:twins:board:%s", constellationEnum.name());
    }

    private void dealWithShowValue(RankItem item) {
        if (Objects.isNull(item)) {
            return;
        }
        item.setDecimalVal(convertToStr(item.getValue()));
    }

    private String convertToStr(Long val) {
        if (Objects.isNull(val)) {
            return "0星座值";
        }
        Long wan = val / 10000;
        Long remainder = val % 10000;
        Long thousand = remainder / 1000;
        if (wan > 0 && thousand > 0) {
            return wan + "." + thousand + "万星座值";
        } else if (wan > 0 && thousand == 0) {
            return wan + ".0万星座值";
        } else {
            return remainder + "星座值";
        }
    }


}
