package cn.yizhoucp.ump.biz.project.biz.manager.activity.togetherIn2024;

import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.enums.ServicesNameEnum;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.starter.cassandra.base.kafka.YzKafkaProducerManager;
import cn.yizhoucp.ump.api.vo.jimu.rank.CpRankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.AbstractRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.TogetherIn2024Constant.ACTIVITY_CODE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.TogetherIn2024Constant.RANK;

@Service
@Slf4j
public class TogetherIn2024RankManager extends AbstractRankManager {

    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private NotifyComponent notifyComponent;
    @Resource
    private YzKafkaProducerManager yzKafkaProducerManager;

    @Override
    protected void doPreProcess(RankContext rankContext) {
        rankContext.setRankLen(7L);
    }

    @Override
    protected void postProcess(RankContext rankContext) {
    }

    @Override
    public Boolean sendPrize(RankContext rankContext) {
        if (!Boolean.TRUE.equals(redisManager.setnx("ump:together_in_2024:send_prize_idempotent", System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND))) {
            return Boolean.TRUE;
        }

        List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(1L, ACTIVITY_CODE, "rank");
        log.info("scenePrizeDOList {}", JSON.toJSONString(scenePrizeDOList));
        if (CollectionUtils.isEmpty(scenePrizeDOList)) {
            return Boolean.FALSE;
        }

        RankVO rankVO = this.getRank(RankContext.builder()
                .activityCode(ACTIVITY_CODE)
                .rankKey(RANK)
                .type(RankContext.RankType.cp)
                .build());
        log.info("rankVO {}", rankVO);
        if (rankVO == null) {
            return Boolean.FALSE;
        }

        List<CpRankItem> rankList = rankVO.getCpRankItemList();
        if (CollectionUtils.isEmpty(rankList)) {
            return Boolean.FALSE;
        }

        for (CpRankItem cpRankItem : rankList) {
            Long rank = cpRankItem.getRank();
            List<ScenePrizeDO> scenePrizeDOs = scenePrizeDOList.stream().filter(scenePrizeDO -> Objects.equals(rank, scenePrizeDO.getPrizeBelongToRank())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(scenePrizeDOs)) {
                log.info("maleUid {} femaleUid {} rank {} 没有奖励", cpRankItem.getMaleUid(), cpRankItem.getFemaleUid(), rank);
                continue;
            }
            sendPrizeManager.sendPrize(
                    BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(cpRankItem.getMaleUid()).build(),
                    scenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, cpRankItem.getMaleUid())).collect(Collectors.toList())
            );
            for (ScenePrizeDO scenePrizeDO : scenePrizeDOs) {
                notifyComponent.npcNotify(
                        ServicesAppIdEnum.lanling.getUnionId(),
                        cpRankItem.getMaleUid(),
                        String.format("恭喜您在“2024在一起”活动中获得真爱榜单第%s名，您可获得奖励为一个%s金币的礼物%s，奖励已下发，请注意查收~", rank, scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeDesc())
                );
                this.allActivityReceiveAwardTrack("on_list", scenePrizeDO.getPrizeValue(), scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeNum(), cpRankItem.getMaleUid());
            }
        }

        return Boolean.TRUE;
    }

    private void allActivityReceiveAwardTrack(String taskType, String awardKey, Long awardAmount, Integer awardCount, Long uid) {
        Map<String, Object> params = Maps.newHashMapWithExpectedSize(7);
        params.put("activity_type", "2024_together_activity");
        params.put("activity_attribute", "official_announcement_activity");
        params.put("task_type", taskType);
        params.put("award_key", awardKey);
        params.put("award_amount", awardAmount);
        params.put("award_count", awardCount);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_receive_award", params, ServicesNameEnum.ump_services.getCode());
    }

}
