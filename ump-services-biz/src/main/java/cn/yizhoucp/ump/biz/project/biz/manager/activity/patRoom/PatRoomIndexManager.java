package cn.yizhoucp.ump.biz.project.biz.manager.activity.patRoom;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.vo.activity.patRoom.PatRoomIndexVO;
import cn.yizhoucp.ump.api.vo.activity.patRoom.PatRoomRewardVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.patRoom.internal.PatRoomConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.patRoom.internal.PatRoomEnums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.patRoom.internal.PatRoomRedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.IndexManager;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PatRoomIndexManager implements IndexManager {
    @Resource
    private PatRoomRedisManager patRoomRedisManager;
    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    private SendPrizeManager sendPrizeManager;
    @Autowired
    private PatRoomTrackManager patRoomTrackManager;

    @Override
    public List<PatRoomIndexVO> getIndex(BaseParam param, Long toUid, String extData) {
        List<PatRoomIndexVO> list = new ArrayList<>();
        for (PatRoomEnums.PatRoomTask task : PatRoomEnums.PatRoomTask.values()) {
            PatRoomIndexVO indexVO = PatRoomIndexVO.builder().build();
            indexVO.setStatus(getTaskStatus(param, task));
            indexVO.setTaskId(task.name());
            indexVO.setRewardNum(getRewardNum(param, task));
            list.add(indexVO);
        }
        return list;
    }

    private Integer getRewardNum(BaseParam param, PatRoomEnums.PatRoomTask task) {
        Long rewardNum = patRoomRedisManager.getTaskProgress(task.name(), param.getUid());
        return rewardNum.intValue();
    }

    private Integer getTaskStatus(BaseParam param, PatRoomEnums.PatRoomTask task) {
        Long progress = patRoomRedisManager.getTaskProgress(task.name(), param.getUid());
        if (progress == 0) {
            return 0;
        }
/*
        Boolean status=patRoomRedisManager.getTaskRewarded(task.name(), param.getUid());
*/
/*
        if(status){
            return 2;
        }
*/
        return 1;
    }

    @Override
    public String getTemplateType() {
        return "";
    }

    @Override
    public String getActivityCode() {
        return "";
    }

    public List<PatRoomRewardVO> claimReward(String taskId) {
        PatRoomEnums.PatRoomTask task = PatRoomEnums.PatRoomTask.getByTaskName(taskId);
        if (task == null) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "任务不存在");
        }
        BaseParam param = BaseParam.ofMDC();
        List<ScenePrizeDO> list = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), PatRoomConstant.ACTIVITY_CODE, task.getRewardCode());
        if (list == null) {
            log.error("未配置活动奖励 {}", task.getRewardCode());
            return null;
        }
        //扣减进度
        Integer num = decrementProgress(param, task);
        sendPrizeManager.sendPrize(
                BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(param.getUid()).build(),
                list.stream().peek(scenePrizeDO -> scenePrizeDO.setPrizeNum(num)).map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, param.getUid())).collect(Collectors.toList())
        );
        //埋点
        for(ScenePrizeDO item:list){
            patRoomTrackManager.allActivityReceiveAward(item.getPrizeValue(), item.getPrizeValueGold(), item.getPrizeNum(), param.getUid());
        }
        return buildPatRoomRewardVO(list);
    }

    private List<PatRoomRewardVO> buildPatRoomRewardVO(List<ScenePrizeDO> list) {
        if (list == null || list.isEmpty()) {
            return null;
        }
        List<PatRoomRewardVO> rewardList = new ArrayList<>();
        for (ScenePrizeDO item : list) {
            PatRoomRewardVO rewardVO = PatRoomRewardVO
                    .builder()
                    .giftIcon(item.getPrizeIcon())
                    .giftName(item.getPrizeDesc())
                    .giftNum(item.getPrizeNum())
                    .giftValue(item.getPrizeValueGold())
                    .build();
            rewardList.add(rewardVO);
        }
        return rewardList;
    }

    private Integer decrementProgress(BaseParam param, PatRoomEnums.PatRoomTask task) {
        Long progress = patRoomRedisManager.getTaskProgress(task.name(), param.getUid());
        if (progress == 0) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "没有奖励可领取");
        }
        patRoomRedisManager.decrementTaskProgress(task.name(), progress, param.getUid());
        return progress.intValue();
    }
}
