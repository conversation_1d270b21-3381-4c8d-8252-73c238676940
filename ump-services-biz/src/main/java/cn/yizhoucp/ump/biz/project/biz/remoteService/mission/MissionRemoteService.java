package cn.yizhoucp.ump.biz.project.biz.remoteService.mission;


import cn.yizhoucp.ump.biz.project.biz.remoteService.AbstractRemoteService;
import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.enums.CommonStatus;
import cn.yizhoucp.ms.core.base.enums.mission.MissionTargetKey;
import cn.yizhoucp.ms.core.vo.missionservices.ActivityMissionVO;
import cn.yizhoucp.ms.core.vo.missionservices.UserReceiveRewardResultVO;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * mission 防腐层
 *
 * @author: lianghu
 */
@Slf4j
@Service
public class MissionRemoteService extends AbstractRemoteService {

    @Resource
    private FeignMissionService feignMissionService;

    /**
     * 活动任务列表
     *
     * @param appId
     * @param uid
     * @param activityKey
     * @return cn.yizhoucp.ms.core.base.Result<cn.yizhoucp.ms.core.vo.missionservices.MissionResultVO < java.util.List < cn.yizhoucp.ms.core.vo.missionservices.ActivityMissionVO>>>
     */
    public List<ActivityMissionVO> getUserMissionList(Long appId, Long uid, String activityKey) {
        Result<List<ActivityMissionVO>> result = null;
        try {
            log.info("调用 MissionRemoteService.getUserMissionList 入参 uid:{}, activityKey:{}", uid, activityKey);
            result = feignMissionService.getUserMissionList(appId, uid, activityKey);
            if (result.success()) {
                log.info("调用 MissionRemoteService.getUserMissionList 成功 result:{}", JSONObject.toJSONString(result));
                return result.successData();
            }
        } catch (Exception e) {
            log.error("调用 MissionRemoteService.getUserMissionList 异常", e);
        }
        log.error("调用 MissionRemoteService.getUserMissionList 失败 result:{}", JSONObject.toJSONString(result));
        return Lists.newArrayList();
    }


    /**
     * 主动领取任务奖励
     *
     * @param appId
     * @param uid
     * @param missionTargetKey
     * @return cn.yizhoucp.ms.core.base.Result<cn.yizhoucp.ms.core.vo.missionservices.MissionResultVO < java.util.List < cn.yizhoucp.ms.core.vo.missionservices.ActivityMissionVO>>>
     */
    public UserReceiveRewardResultVO userReceiveReward(Long appId, Long uid, MissionTargetKey missionTargetKey) {
        Result<UserReceiveRewardResultVO> result = null;
        try {
            log.info("调用 MissionRemoteService.userReceiveReward 入参 uid:{}, missionTargetKey:{}", uid, missionTargetKey);
            result = feignMissionService.userReceiveReward(appId, uid, missionTargetKey);
            if (result.success()) {
                log.info("调用 MissionRemoteService.userReceiveReward 成功 result:{}", JSONObject.toJSONString(result));
                return result.successData();
            }
        } catch (Exception e) {
            log.error("调用 MissionRemoteService.userReceiveReward 异常", e);
        }
        log.error("调用 MissionRemoteService.userReceiveReward 失败 result:{}", JSONObject.toJSONString(result));
        return null;
    }

    /**
     * 查询任务信息
     *
     * @param appId
     * @param uid
     * @param missionTargetKey
     * @return cn.yizhoucp.ms.core.base.Result<cn.yizhoucp.ms.core.vo.missionservices.MissionResultVO < java.util.List < cn.yizhoucp.ms.core.vo.missionservices.ActivityMissionVO>>>
     */
    public ActivityMissionVO getUserMissionInfo(Long appId, Long uid, MissionTargetKey missionTargetKey) {
        Result<ActivityMissionVO> result = null;
        try {
            log.info("调用 MissionRemoteService.getUserMissionInfo 入参 uid:{}, missionTargetKey:{}", uid, missionTargetKey);
            result = feignMissionService.getUserMissionInfo(appId, uid, missionTargetKey);
            if (result.success()) {
                log.info("调用 MissionRemoteService.getUserMissionInfo 成功 result:{}", JSONObject.toJSONString(result));
                return result.successData();
            }
        } catch (Exception e) {
            log.error("调用 MissionRemoteService.getUserMissionInfo 异常", e);
        }
        log.error("调用 MissionRemoteService.getUserMissionInfo 失败 result:{}", JSONObject.toJSONString(result));
        return null;
    }

    /**
     * 设置活动任务状态
     *
     * @param appId
     * @param activityKey
     * @param status
     * @return cn.yizhoucp.ms.core.base.Result<cn.yizhoucp.ms.core.vo.missionservices.MissionResultVO < java.util.List < cn.yizhoucp.ms.core.vo.missionservices.ActivityMissionVO>>>
     */
    public JSONObject setActivityMissionStatus(Long appId, String activityKey, CommonStatus status) {
        Result<JSONObject> result = null;
        try {
            log.info("调用 MissionRemoteService.setActivityMissionStatus 入参 activityKey:{}, status:{}", activityKey, status);
            result = feignMissionService.setActivityMissionStatus(appId, activityKey, status);
            if (result.success()) {
                log.info("调用 MissionRemoteService.setActivityMissionStatus 成功 result:{}", JSONObject.toJSONString(result));
                return result.successData();
            }
        } catch (Exception e) {
            log.error("调用 MissionRemoteService.setActivityMissionStatus 异常", e);
        }
        log.error("调用 MissionRemoteService.setActivityMissionStatus 失败 result:{}", JSONObject.toJSONString(result));
        return null;
    }


}
