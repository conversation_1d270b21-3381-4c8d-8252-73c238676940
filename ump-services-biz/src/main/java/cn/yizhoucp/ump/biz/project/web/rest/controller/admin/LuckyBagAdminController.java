package cn.yizhoucp.ump.biz.project.web.rest.controller.admin;

import cn.yizhoucp.ms.core.vo.landingservices.AnalysisDataByDayVO;
import cn.yizhoucp.ump.api.vo.appAdSpace.AppAdSpaceAdminVO;
import cn.yizhoucp.ump.api.vo.luckBag.DrawRoomConfigVO;
import cn.yizhoucp.ump.biz.project.biz.manager.luckyBag.LuckyBagManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.UserLuckDrawInfoDO;
import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.enums.LuckDrawPoolTypeEnum;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ms.core.dto.missionservices.coinservices.LuckDrawSysConfigDTO;
import cn.yizhoucp.ms.core.vo.coinservices.LuckDrawPrizeConfigVO;
import cn.yizhoucp.ms.core.vo.coinservices.LuckDrawPrizeVO;
import cn.yizhoucp.ms.core.vo.landingservices.AdminPageVO;
import cn.yizhoucp.ms.core.vo.landingservices.PrismPageVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collections;
import java.util.List;

/**
 * 手气福袋管理接口
 *
 * @author: lianghu
 */
@RestController
public class LuckyBagAdminController {

    @Resource
    private LuckyBagManager luckyBagManager;


    @RequestMapping("/api/inner/admin/ump/get-pool-config-by-type")
    public Result<String> getConfigByKey(String key){
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            return luckyBagManager.getConfigByKey(key);
        });
    }
    @RequestMapping("/api/inner/admin/ump/modify-pool-config-by-type")
    public Result<String> modifyConfigKey(String key,String content){
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            return luckyBagManager.modifyConfigByKey(key,content);
        });
    }

    /**
     * 一键替换当前奖池奖池
     *
     * @return
     */
    @RequestMapping("/api/inner/admin/lucky-bag/luck-draw-reset")
    public Result<Boolean> luckDrawReset(Long appId, LuckDrawPoolTypeEnum poolTypeEnum) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            return luckyBagManager.oneClickReplacePool(appId, poolTypeEnum);
        });
    }

    /**
     * 获取指定奖池的当前数据
     *
     * @return
     */
    @RequestMapping("/api/inner/admin/lucky-bag/current-pool-prizes")
    public Result<PrismPageVO<LuckDrawPrizeVO, LuckDrawPrizeConfigVO>> currentLuckDrawPool(Long appId, LuckDrawPoolTypeEnum poolTypeEnum) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            return luckyBagManager.getCurrentPoolAllPrize(appId, poolTypeEnum);
        });
    }

    /**
     * 修改当前抽奖礼物奖池
     *
     * @return
     */
    @RequestMapping("/api/inner/admin/lucky-bag/modify-current-pool-prizes")
    public Result<Boolean> modifyCurrentluckDrawPool(Long appId, LuckDrawPoolTypeEnum poolTypeEnum, String giftKey, Long num) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            return luckyBagManager.modifyCurrentPoolPrize(appId, poolTypeEnum, giftKey, num);
        });
    }

    /**
     * 获取指定奖池的配置属性
     *
     * @return
     */
    @RequestMapping("/api/inner/admin/lucky-bag/current-pool-prizes-config")
    public Result<PrismPageVO<LuckDrawPrizeVO, LuckDrawPrizeConfigVO>> currentLuckDrawPoolConfig(Long appId, LuckDrawPoolTypeEnum poolTypeEnum) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            return luckyBagManager.getPoolAllPrizeConfig(appId, poolTypeEnum);
        });
    }

    /**
     * 修改当前抽奖礼物奖池配置
     *
     * @return
     */
    @RequestMapping("/api/inner/admin/lucky-bag/modify-current-pool-config")
    public Result<Boolean> modifyCurrentluckDrawPoolConfig(Long appId, LuckDrawPoolTypeEnum poolTypeEnum, String giftKey, Long num) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            return luckyBagManager.modifyPoolPrizeConfig(appId, poolTypeEnum, giftKey, num);
        });
    }

    /**
     * 修改当前抽奖礼物奖池配置
     *
     * @return
     */
    @RequestMapping("/api/inner/admin/lucky-bag/luck-draw-config")
    public Result<LuckDrawSysConfigDTO> getLuckDrawConfig() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            return luckyBagManager.getLuckDrawConfig();
        });
    }

    /**
     * 修改当前抽奖礼物奖池配置
     *
     * @return
     */
    @PostMapping("/api/inner/admin/lucky-bag/modify-luck-draw-config")
    public Result<LuckDrawSysConfigDTO> modifyLuckDrawConfig(@RequestBody LuckDrawSysConfigDTO configDTO) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            return luckyBagManager.modifyLuckDrawConfig(configDTO);
        });
    }

    /**
     * 修改当前抽奖礼物奖池配置
     *
     * @return
     */
    @GetMapping("/api/inner/admin/lucky-bag/page-luck-draw-user-info")
    public Result<AdminPageVO<UserLuckDrawInfoDO>> getLuckDrawConfig(LuckDrawPoolTypeEnum poolTypeEnum, Integer pageSize, Integer pageNo) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            return luckyBagManager.getUserLuckDrawInfoPage(poolTypeEnum, pageSize, pageNo);
        });
    }

    @GetMapping("/api/admin/ump/lucky-bag/page-room-draw-info")
    public Result<AdminPageVO<DrawRoomConfigVO>> getLuckDrawRoomConfig() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            List<DrawRoomConfigVO> luckDrawRoomConfig = luckyBagManager.getLuckDrawRoomConfig();
            AdminPageVO<DrawRoomConfigVO> data = new AdminPageVO<>();
            data.setPageIndex(1);
            data.setPageSize(10);
            data.setItems(luckDrawRoomConfig);
            return data;
        });
    }

    @PostMapping("/api/admin/ump/lucky-bag/save-room-draw-info")
    public Result<DrawRoomConfigVO> saveLuckDrawRoomConfig(@Valid @RequestBody DrawRoomConfigVO param) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            return luckyBagManager.saveRoomConfig(param);
        });
    }

}
