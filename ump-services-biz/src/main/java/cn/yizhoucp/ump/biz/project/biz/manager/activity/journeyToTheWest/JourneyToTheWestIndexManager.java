package cn.yizhoucp.ump.biz.project.biz.manager.activity.journeyToTheWest;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.PageVO;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.util.AppUtil;
import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.snsservices.relation.WrapUserVO;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.activity.journeyToTheWest.JourneyToTheWestExchangeGift;
import cn.yizhoucp.ump.api.vo.activity.journeyToTheWest.JourneyToTheWestIndexVO;
import cn.yizhoucp.ump.api.vo.activity.journeyToTheWest.ThemeGiftInfo;
import cn.yizhoucp.ump.api.vo.activity.journeyToTheWest.ThemeLevel;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.log.LogComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawLogParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.journeyToTheWest.claimRewardStrategy.ChooseRewardStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.journeyToTheWest.claimRewardStrategy.JourneyRewardStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.IndexManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.lanling.FeignLanlingService;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import cn.yizhoucp.user.manager.UserFeignManager;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class JourneyToTheWestIndexManager implements IndexManager {

    @Resource
    private JourneyToTheWestConstant journeyToTheWestConstant;
    @Resource
    private UserFeignManager userFeignManager;
    @Resource
    private ChooseRewardStrategy chooseRewardStrategy;
    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    private FeignLanlingService feignLanlingService;
    @Resource
    private RedisManager redisManager;
    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private LogComponent logComponent;
    @Resource
    private JourneyToTheWestDrawManager journeyToTheWestDrawManager;
    @Resource
    private JourneyToTheWestRankManager journeyToTheWestRankManager;
    @Resource
    private JourneyToTheWestTrackManager journeyToTheWestTrackManager;


    @ActivityCheck(activityCode = JourneyToTheWestConstant.ACTIVITY_CODE, isThrowException = false)
    @Override
    public JourneyToTheWestIndexVO getIndex(BaseParam param, Long toUid, String extData) {
        JourneyToTheWestIndexVO indexVO = new JourneyToTheWestIndexVO();
        Long purpleGoldBellNum = journeyToTheWestConstant.getPurpleGoldBellNum(param.getUid());
        indexVO.setPurpleGoldBellNum(purpleGoldBellNum);
        indexVO.setIsFirst(getUserIsFirst(param.getUid()));
        indexVO.setHeroicDream(buildHeroicDream(param, toUid));
        indexVO.setTowerView(buildTowerView(param, toUid));
        indexVO.setRainBell(buildRainBell(param, toUid));
        return indexVO;
    }

    private Boolean getUserIsFirst(Long uid) {
        return journeyToTheWestConstant.getUserIsFirst(uid);
    }

    private JourneyToTheWestIndexVO.RainBell buildRainBell(BaseParam param, Long toUid) {
        if (ObjectUtil.isNull(toUid)) {
            JourneyToTheWestIndexVO.UserVO topScoreFriend = getTopScoreFriend(param);
            if (topScoreFriend != null) {
                toUid = topScoreFriend.getUid();
            }
        }
        String bindKey = AppUtil.splicUserId(toUid, param.getUid());
        JourneyToTheWestIndexVO.RainBell rainBell = new JourneyToTheWestIndexVO.RainBell();
        rainBell.setPartnerInfo(buildUserInfo(toUid));
        rainBell.setUserInfo(buildUserInfo(param.getUid()));
        rainBell.setProgressBar(buildProgressBar(bindKey));
        rainBell.setPuzzles(buildPuzzles(bindKey));
        rainBell.setButton(buildButton(bindKey, rainBell.getPuzzles()));
        return rainBell;
    }

    private JourneyToTheWestIndexVO.Button buildButton(String bindKey, List<JourneyToTheWestIndexVO.Puzzle> puzzles) {
        Boolean isReceivable = Boolean.FALSE;
        for (JourneyToTheWestIndexVO.Puzzle puzzle : puzzles) {
            if (puzzle.getId() == 0L) {
                continue;
            }
            Boolean isClaimed = journeyToTheWestConstant.getPuzzleRewardIsClaimed(puzzle.getId(), bindKey);
            if (puzzle.getIsLit() && !isClaimed) {
                isReceivable = Boolean.TRUE;
            }
        }
        return JourneyToTheWestIndexVO.Button.builder()
                .isReceivable(getButtonTaskStatus(isReceivable))
                .build();
    }

    private Integer getButtonTaskStatus(Boolean isReceivable) {
        if (isReceivable) {
            return JourneyToTheWestConstant.ButtonStatusEnum.LIT.getStatus();
        }
        return JourneyToTheWestConstant.ButtonStatusEnum.UNLIT.getStatus();
    }

    private List<JourneyToTheWestIndexVO.Puzzle> buildPuzzles(String bindKey) {
        List<JourneyToTheWestIndexVO.Puzzle> puzzles = new ArrayList<>();
        for (JourneyToTheWestConstant.PuzzleEnum puzzleEnum : JourneyToTheWestConstant.PuzzleEnum.values()) {
            JourneyToTheWestIndexVO.Puzzle puzzle = new JourneyToTheWestIndexVO.Puzzle();
            puzzle.setId(puzzleEnum.getId());
            puzzle.setIsLit(journeyToTheWestConstant.getPuzzleIsLit(puzzleEnum, bindKey));
            puzzle.setIsReceivable(journeyToTheWestConstant.getPuzzleRewardIsClaimed(puzzleEnum.getId(), bindKey));
            puzzles.add(puzzle);
        }
        return puzzles;
    }

    private JourneyToTheWestIndexVO.ProgressBar buildProgressBar(String bindKey) {
        JourneyToTheWestIndexVO.ProgressBar progressBar = new JourneyToTheWestIndexVO.ProgressBar();
        Long currentValue = journeyToTheWestConstant.getRainBellScore(bindKey);
        progressBar.setCurrentValue(currentValue >= JourneyToTheWestConstant.RAIN_BELL_MAX_SCORE ? JourneyToTheWestConstant.RAIN_BELL_MAX_SCORE : currentValue);
        progressBar.setMaxValue(JourneyToTheWestConstant.RAIN_BELL_MAX_SCORE);
        return progressBar;
    }

    private JourneyToTheWestIndexVO.TowerView buildTowerView(BaseParam param, Long toUid) {
        JourneyToTheWestIndexVO.TowerView towerView = new JourneyToTheWestIndexVO.TowerView();
        towerView.setRewardGift(buildRewardGift(param, toUid));
        towerView.setTasks(buildTasks(param, toUid));
        towerView.setIsComplete(getTowerViewThemeCompleteStatus(param.getUid()));
        return towerView;
    }

    private Integer getTowerViewThemeCompleteStatus(Long uid) {
        Boolean isComplete = Boolean.TRUE;
        for (JourneyToTheWestConstant.TaskCodeEnum task : JourneyToTheWestConstant.TaskCodeEnum.values()) {
            isComplete = journeyToTheWestConstant.getToViewTaskIsComplete(uid, task) && isComplete;
        }
        if (!isComplete) {
            return JourneyToTheWestConstant.ToViewTaskEnum.LOCKED.getStatus();
        }
        Boolean isClaimed = journeyToTheWestConstant.isFinalRewardClaimed(uid);
        if (!isClaimed) {
            return JourneyToTheWestConstant.ToViewTaskEnum.COMPLETED.getStatus();
        }
        return JourneyToTheWestConstant.ToViewTaskEnum.RECEIVED.getStatus();
    }

    private List<JourneyToTheWestIndexVO.Task> buildTasks(BaseParam param, Long toUid) {
        List<JourneyToTheWestIndexVO.Task> tasks = new ArrayList<>();
        for (JourneyToTheWestConstant.TaskCodeEnum task : JourneyToTheWestConstant.TaskCodeEnum.values()) {
            JourneyToTheWestIndexVO.Task taskItem = new JourneyToTheWestIndexVO.Task();
            Long progress = journeyToTheWestConstant.getTaskProgress(task.getTaskId(), param.getUid());
            taskItem.setId(task.getTaskId());
            taskItem.setCurrentProgress(progress > task.getMaxProgress() ? task.getMaxProgress() : progress);
            taskItem.setMaxProgress(task.getMaxProgress());
            taskItem.setStatus(getTaskStatus(param.getUid(), task));
            tasks.add(taskItem);
        }
        return tasks;
    }

    private Integer getTaskStatus(Long uid, JourneyToTheWestConstant.TaskCodeEnum task) {
        //上一个任务是否完成
        JourneyToTheWestConstant.TaskCodeEnum nextTask = JourneyToTheWestConstant.TaskCodeEnum.getPreTask(task);
        Boolean isComplete;
        if (nextTask == null) {
            isComplete = Boolean.TRUE;
        } else {
            isComplete = journeyToTheWestConstant.getToViewTaskIsComplete(uid, nextTask);
        }
        if (!isComplete) {
            return JourneyToTheWestConstant.ToViewTaskEnum.LOCKED.getStatus();
        }
        //任务是否完成
        isComplete = journeyToTheWestConstant.getToViewTaskIsComplete(uid, task);
        if (!isComplete) {
            return JourneyToTheWestConstant.ToViewTaskEnum.UNLOCKED.getStatus();
        }
        //任务是否领取
        Boolean isClaimed = journeyToTheWestConstant.getToViewTaskIsClaimed(uid, task);
        if (!isClaimed) {
            return JourneyToTheWestConstant.ToViewTaskEnum.COMPLETED.getStatus();
        }
        return JourneyToTheWestConstant.ToViewTaskEnum.RECEIVED.getStatus();
    }

    private JourneyToTheWestIndexVO.Gift buildRewardGift(BaseParam param, Long toUid) {
        List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), JourneyToTheWestConstant.ACTIVITY_CODE, JourneyToTheWestConstant.TOWER_VIEW_REWARD);
        if (CollectionUtil.isEmpty(scenePrizeDOList)) {
            return null;
        }
        ScenePrizeDO scenePrizeDO = scenePrizeDOList.get(0);
        return JourneyToTheWestIndexVO.Gift.builder()
                .giftIcon(scenePrizeDO.getPrizeIcon())
                .giftName(scenePrizeDO.getPrizeDesc())
                .giftValue(scenePrizeDO.getPrizeValueGold())
                .build();
    }

    private JourneyToTheWestIndexVO.UserVO getTopScoreFriend(BaseParam param) {
        List<JourneyToTheWestIndexVO.UserVO> list = getFriends(param);
        JourneyToTheWestIndexVO.UserVO maxFriend = list.stream()
                .max(Comparator.comparing(userVO -> journeyToTheWestConstant.getToViewScore(param.getUid(), userVO.getUid())))
                .orElse(null);

        if (maxFriend == null) {
            return null;
        }
        String bindKey = String.format(JourneyToTheWestConstant.BIND_FRIEND_KEY, AppUtil.splicUserId(maxFriend.getUid(), param.getUid()));
        Boolean isBind = redisManager.hasKey(bindKey);
        if (!isBind) {
            return null;
        }
        return maxFriend;
    }

    private JourneyToTheWestIndexVO.HeroicDream buildHeroicDream(BaseParam param, Long toUid) {
        JourneyToTheWestIndexVO.HeroicDream heroicDream = new JourneyToTheWestIndexVO.HeroicDream();
        JourneyToTheWestIndexVO.Theme currentTheme = getCurrentTheme(param.getUid());
        heroicDream.setCurrentThemeId(currentTheme.getThemeId());
        Long currentLevel = journeyToTheWestConstant.getLevel(param.getUid());
        if (currentLevel == 0L) {
            journeyToTheWestConstant.incrementLevel(param.getUid(), 1L);
        }
        heroicDream.setIsClaimedUpgradeReward(journeyToTheWestConstant.getMaxLevelIsComplete(param.getUid()));
        heroicDream.setCurrentLevel(currentLevel == 0L ? 1L : currentLevel);
        heroicDream.setUserInfo(buildUserInfo(param.getUid()));
        heroicDream.setThemes(buildThemes(param));
        return heroicDream;
    }


    public JourneyToTheWestIndexVO.Theme getCurrentTheme(Long uid) {
        JourneyToTheWestIndexVO.Theme theme = null;
        JourneyToTheWestIndexVO.Theme lastEnabledTheme = new JourneyToTheWestIndexVO.Theme();
        for (JourneyToTheWestConstant.ThemesEnum themesEnum : JourneyToTheWestConstant.ThemesEnum.values()) {
            Boolean isEnabled = getThemeIsEnabled(themesEnum);
            if (!isEnabled) {
                continue;
            }
            lastEnabledTheme = JourneyToTheWestIndexVO.Theme.builder()
                    .themeId(themesEnum.getThemeId())
                    .themeName(themesEnum.getThemeName())
                    .isEnabled(Boolean.TRUE)
                    .build();
            for (JourneyToTheWestConstant.ThemeLevelEnum levelEnum : JourneyToTheWestConstant.ThemeLevelEnum.values()) {
                Boolean isComplete = journeyToTheWestConstant.getThemeLevelIsComplete(uid, themesEnum, levelEnum);
                if (!isComplete) {
                    if (theme == null) {
                        theme = JourneyToTheWestIndexVO.Theme.builder()
                                .themeId(themesEnum.getThemeId())
                                .themeName(themesEnum.getThemeName())
                                .currentLevel(levelEnum.getLevelId())
                                .isEnabled(Boolean.TRUE)
                                .build();
                    }
                }
            }
        }
        if (theme == null) {
            return lastEnabledTheme;
        }
        return theme;
    }


    private List<JourneyToTheWestIndexVO.Theme> buildThemes(BaseParam param) {
        List<JourneyToTheWestIndexVO.Theme> themes = new ArrayList<>();
        for (JourneyToTheWestConstant.ThemesEnum themesEnum : JourneyToTheWestConstant.ThemesEnum.values()) {
            JourneyToTheWestIndexVO.Theme theme = new JourneyToTheWestIndexVO.Theme();
            theme.setThemeId(themesEnum.getThemeId());
            theme.setThemeName(themesEnum.getThemeName());
            theme.setCurrentLevel(getCurrentLevel(param.getUid(), themesEnum));
            theme.setLevels(buildLevels(param, themesEnum));
            theme.setIsEnabled(getThemeIsEnabled(themesEnum));
            theme.setThemeCompleteStatus(getThemeCompleteStatus(param, themesEnum));
            themes.add(theme);
        }
        return themes;
    }

    private Integer getThemeCompleteStatus(BaseParam param, JourneyToTheWestConstant.ThemesEnum themesEnum) {
        Boolean allLevelComplete = Boolean.TRUE;
        for (JourneyToTheWestConstant.ThemeLevelEnum levelEnum : JourneyToTheWestConstant.ThemeLevelEnum.values()) {
            allLevelComplete = journeyToTheWestConstant.getThemeLevelIsComplete(param.getUid(), themesEnum, levelEnum) && allLevelComplete;
        }
        if (!allLevelComplete) {
            return JourneyToTheWestConstant.ButtonStatusEnum.UNLIT.getStatus();
        }
        Boolean isFinalRewardClaimed = journeyToTheWestConstant.isThemeRewardClaimed(param.getUid(), themesEnum);
        if (!isFinalRewardClaimed) {
            return JourneyToTheWestConstant.ButtonStatusEnum.LIT.getStatus();
        }
        return JourneyToTheWestConstant.ButtonStatusEnum.RECEIVED.getStatus();
    }

    private Long getCurrentLevel(Long uid, JourneyToTheWestConstant.ThemesEnum themesEnum) {
        for (JourneyToTheWestConstant.ThemeLevelEnum levelEnum : JourneyToTheWestConstant.ThemeLevelEnum.values()) {
            if (!journeyToTheWestConstant.getThemeLevelIsComplete(uid, themesEnum, levelEnum)) {
                return levelEnum.getLevelId();
            }
        }
        return JourneyToTheWestConstant.ThemeLevelEnum.LEVEL_1.getLevelId();
    }

    public Boolean getThemeIsEnabled(JourneyToTheWestConstant.ThemesEnum themesEnum) {
        LocalDate startTime = journeyToTheWestConstant.getStartTime();
        LocalDate now = LocalDate.now();
        LocalDate themeTime = startTime.plusDays(themesEnum.getThemeId() - 1);
        if (themeTime.isBefore(now) || themeTime.equals(now)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    private List<JourneyToTheWestIndexVO.Level> buildLevels(BaseParam param, JourneyToTheWestConstant.ThemesEnum themesEnum) {
        List<JourneyToTheWestIndexVO.Level> levels = new ArrayList<>();
        for (JourneyToTheWestConstant.ThemeLevelEnum levelEnum : JourneyToTheWestConstant.ThemeLevelEnum.values()) {
            JourneyToTheWestIndexVO.Level level = new JourneyToTheWestIndexVO.Level();
            level.setLevel(levelEnum.getLevelId());
            Boolean isComplete = journeyToTheWestConstant.getThemeLevelIsComplete(param.getUid(), themesEnum, levelEnum);
            level.setIsComplete(getLevelCompleteStatus(param, themesEnum, levelEnum, isComplete));
            levels.add(level);
        }
        return levels;
    }

    private Integer getLevelCompleteStatus(BaseParam param, JourneyToTheWestConstant.ThemesEnum themesEnum, JourneyToTheWestConstant.ThemeLevelEnum levelEnum, Boolean isComplete) {
        if (!isComplete) {
            return JourneyToTheWestConstant.ButtonStatusEnum.UNLIT.getStatus();
        }
        Boolean isRewardClaimed = journeyToTheWestConstant.getThemeLevelIsClaimed(param.getUid(), themesEnum, levelEnum);
        if (!isRewardClaimed) {
            return JourneyToTheWestConstant.ButtonStatusEnum.LIT.getStatus();
        }
        return JourneyToTheWestConstant.ButtonStatusEnum.RECEIVED.getStatus();
    }

    private JourneyToTheWestIndexVO.UserInfo buildUserInfo(Long uid) {
        if (ObjectUtil.isNull(uid)) {
            return JourneyToTheWestIndexVO.UserInfo.builder().build();
        }
        UserVO userVO = userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), uid);
        JourneyToTheWestIndexVO.UserInfo userInfo = new JourneyToTheWestIndexVO.UserInfo();
        userInfo.setUserAvatar(userVO.getAvatar());
        userInfo.setSex(userVO.getSex());
        userInfo.setScore(journeyToTheWestConstant.getScore(uid));
        userInfo.setItemNum(journeyToTheWestConstant.getItemNum(uid));
        userInfo.setUid(userVO.getId());
        return userInfo;
    }

    @Override
    public String getTemplateType() {
        return "";
    }

    @Override
    public String getActivityCode() {
        return "";
    }

    public ThemeGiftInfo getThemeReward(BaseParam param, Long themeId) {
        JourneyToTheWestConstant.ThemesEnum themesEnum = JourneyToTheWestConstant.ThemesEnum.getThemeEnumByThemeId(themeId);
        if (themesEnum == null) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "error theme code!");
        }
        return buildThemeReward(param, themesEnum);
    }

    private ThemeGiftInfo buildThemeReward(BaseParam param, JourneyToTheWestConstant.ThemesEnum themesEnum) {
        ThemeGiftInfo result = new ThemeGiftInfo();
        List<ThemeGiftInfo.ThemeLevel> levelList = new ArrayList<>();
        List<ScenePrizeDO> taskList = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), JourneyToTheWestConstant.ACTIVITY_CODE, themesEnum.name() + "_TASK");
        List<ScenePrizeDO> rewardList = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), JourneyToTheWestConstant.ACTIVITY_CODE, themesEnum.name() + "_REWARD");
        List<ScenePrizeDO> themeRewardList = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), JourneyToTheWestConstant.ACTIVITY_CODE, themesEnum.name() + "_THEME_REWARD");
        for (ThemeLevel level : themesEnum.getLevel()) {
            ThemeGiftInfo.ThemeLevel themeLevel = new ThemeGiftInfo.ThemeLevel();
            themeLevel.setLevel(level.getLevel());
            themeLevel.setTaskGiftList(buildTaskGiftList(param, taskList, level, themesEnum));
            themeLevel.setRewardGiftList(buildThemeLevelGiftList(level, rewardList));
            themeLevel.setThemeReward(buildThemeRewardGift(themeRewardList));
            levelList.add(themeLevel);
        }
        result.setLevelList(levelList);
        return result;
    }

    private List<JourneyToTheWestIndexVO.TaskGift> buildTaskGiftList(BaseParam param, List<ScenePrizeDO> taskList, ThemeLevel level, JourneyToTheWestConstant.ThemesEnum themesEnum) {
        if (CollectionUtil.isEmpty(taskList)) {
            return Collections.emptyList();
        }
        List<JourneyToTheWestIndexVO.TaskGift> giftList = new ArrayList<>();
        for (ScenePrizeDO scenePrizeDO : taskList) {
            JourneyToTheWestIndexVO.TaskGift gift = new JourneyToTheWestIndexVO.TaskGift();
            gift.setGiftIcon(scenePrizeDO.getPrizeIcon());
            gift.setGiftValue(scenePrizeDO.getPrizeValueGold());
            gift.setGiftName(scenePrizeDO.getPrizeDesc());
            Boolean isReceivable = journeyToTheWestConstant.getThemeLevelTaskIsReceivable(param.getUid(), level.getLevel(), themesEnum, scenePrizeDO.getPrizeValue());
            gift.setIsReceivable(isReceivable);
            giftList.add(gift);
        }
        return giftList;
    }

    private List<JourneyToTheWestIndexVO.RewardGift> buildThemeLevelGiftList(ThemeLevel level, List<ScenePrizeDO> rewardList) {
        List<JourneyToTheWestIndexVO.RewardGift> giftList = new ArrayList<>();
        for (ScenePrizeDO item : rewardList) {
            String extData = item.getExtData();
            Map<String, String> extDataMap = JSONUtil.toBean(extData, Map.class);
            if (extDataMap.get("level").equals(String.valueOf(level.getLevel()))) {
                JourneyToTheWestIndexVO.RewardGift gift = new JourneyToTheWestIndexVO.RewardGift();
                gift.setGiftIcon(item.getPrizeIcon());
                gift.setGiftValue(item.getPrizeValueGold());
                gift.setGiftName(item.getPrizeDesc());
                gift.setType(item.getPrizeType());
                giftList.add(gift);
            }
        }
        return giftList;
    }

    private JourneyToTheWestIndexVO.Gift buildThemeRewardGift(List<ScenePrizeDO> themeRewardList) {
        if (CollectionUtil.isEmpty(themeRewardList)) {
            return new JourneyToTheWestIndexVO.Gift();
        }
        ScenePrizeDO themeReward = themeRewardList.get(0);
        JourneyToTheWestIndexVO.Gift gift = new JourneyToTheWestIndexVO.Gift();
        gift.setGiftName(themeReward.getPrizeDesc());
        gift.setGiftIcon(themeReward.getPrizeIcon());
        gift.setGiftValue(themeReward.getPrizeValueGold());
        return gift;
    }

    private List<JourneyToTheWestIndexVO.Gift> buildGiftList(List<ScenePrizeDO> list) {
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<JourneyToTheWestIndexVO.Gift> giftList = new ArrayList<>();
        for (ScenePrizeDO scenePrizeDO : list) {
            JourneyToTheWestIndexVO.Gift gift = new JourneyToTheWestIndexVO.Gift();
            gift.setGiftIcon(scenePrizeDO.getPrizeIcon());
            gift.setGiftValue(scenePrizeDO.getPrizeValueGold());
            gift.setGiftName(scenePrizeDO.getPrizeDesc());
            giftList.add(gift);
        }
        return giftList;
    }


    private List<JourneyToTheWestIndexVO.Gift> buildGiftList(JourneyToTheWestConstant.ThemesEnum themesEnum, String scene) {
        List<JourneyToTheWestIndexVO.Gift> taskGiftList = new ArrayList<>();
        List<ScenePrizeDO> list = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), JourneyToTheWestConstant.ACTIVITY_CODE, themesEnum.name() + "_" + scene);
        for (ScenePrizeDO scenePrizeDO : list) {
            JourneyToTheWestIndexVO.Gift gift = new JourneyToTheWestIndexVO.Gift();
            gift.setGiftIcon(scenePrizeDO.getPrizeIcon());
            gift.setGiftValue(scenePrizeDO.getPrizeValueGold());
            gift.setGiftName(scenePrizeDO.getPrizeDesc());
            taskGiftList.add(gift);
        }
        return taskGiftList;
    }


    public Boolean upgrade(BaseParam param) {
        Long currentLevel = journeyToTheWestConstant.getLevel(param.getUid());
        Long uid = param.getUid();
        JourneyToTheWestConstant.LevelEnum levelEnum = JourneyToTheWestConstant.LevelEnum.getNextLevelEnum(currentLevel);
        JourneyToTheWestConstant.LevelEnum currentLevelEnum = JourneyToTheWestConstant.LevelEnum.getLevelEnumByLevelId(currentLevel);
        if (levelEnum == null && currentLevelEnum == null) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "已是最高级");
        }
        Long score = journeyToTheWestConstant.getScore(param.getUid());
        Long itemNum = journeyToTheWestConstant.getItemNum(param.getUid());
        if (currentLevelEnum == null) {
            currentLevelEnum = JourneyToTheWestConstant.LevelEnum.LEVEL_1;
        }
        Boolean scoreRequired = score >= currentLevelEnum.getRequireScore();
        Boolean itemNumRequired = itemNum >= currentLevelEnum.getRequireItemNum();
        if (!scoreRequired && !itemNumRequired) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "当前月华值和乾坤袋不足");
        }
        if (!scoreRequired) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "当前月华值不足");
        }
        if (!itemNumRequired) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "当前乾坤袋不足");
        }
        journeyToTheWestConstant.decrementScore(uid, currentLevelEnum.getRequireScore());
        journeyToTheWestConstant.decrementItemNum(uid, currentLevelEnum.getRequireItemNum());
        Long level;
        if (levelEnum == null) {
            level = currentLevelEnum.getLevelId() + 1;
        } else {
            level = levelEnum.getLevelId();
        }
        journeyToTheWestConstant.incrementLevel(uid, level);
        return Boolean.TRUE;
    }


    public Object claimReward(BaseParam param, String scene, String rewardKey, String extData, Long toUid) {
        JourneyRewardStrategy strategy = chooseRewardStrategy.getStrategy(scene);
        return strategy.claimReward(param.getUid(), toUid, rewardKey, extData);
    }

    public List<JourneyToTheWestIndexVO.UserVO> getFriends(BaseParam param) {
        Long start = 0L;
        Integer num = 100;
        PageVO<WrapUserVO> friendList = feignLanlingService.getFriendList(start, num).getData();
        List<WrapUserVO> list = new ArrayList<>();
        list.addAll(friendList.getList());
        log.info("获取好友列表：{}", list);
        return list.stream().map(this::convertUserVO)
                .sorted(Comparator.comparing(userVO -> journeyToTheWestConstant.getToViewScore(param.getUid(), userVO.getUid())))
                .collect(Collectors.toList());
    }

    private JourneyToTheWestIndexVO.UserVO convertUserVO(WrapUserVO wrapUserVO) {
        JourneyToTheWestIndexVO.UserVO userInfo = new JourneyToTheWestIndexVO.UserVO();
        userInfo.setUserAvatar(wrapUserVO.getUser().getAvatar());
        userInfo.setUserName(wrapUserVO.getUser().getName());
        userInfo.setUid(wrapUserVO.getUser().getId());
        return userInfo;
    }


    public Boolean bindFriend(Long userId, Long toUid) {
        if (ObjectUtil.isNull(toUid)) {
            return true;
        }
        redisManager.setnx(String.format(JourneyToTheWestConstant.BIND_FRIEND_KEY, AppUtil.splicUserId(userId, toUid)), System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND);
        return true;
    }

    public List<JourneyToTheWestExchangeGift> getExchangeGift(BaseParam param) {
        List<ScenePrizeDO> list = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), JourneyToTheWestConstant.ACTIVITY_CODE, JourneyToTheWestConstant.EXCHANGE_GIFT);
        List<JourneyToTheWestExchangeGift> giftList = new ArrayList<>();
        Long purpleGoldBellNum = journeyToTheWestConstant.getPurpleGoldBellNum(param.getUid());
        for (ScenePrizeDO scenePrizeDO : list) {
            JourneyToTheWestExchangeGift gift = new JourneyToTheWestExchangeGift();
            String extData = scenePrizeDO.getExtData();
            Map<String, String> extDataMap = JSON.parseObject(extData, Map.class);
            gift.setGiftName(scenePrizeDO.getPrizeDesc());
            gift.setGiftValue(scenePrizeDO.getPrizeValueGold());
            gift.setGiftIcon(scenePrizeDO.getPrizeIcon());
            gift.setGiftKey(scenePrizeDO.getPrizeValue());
            Long needNum = Long.parseLong(extDataMap.get("purpleGoldBellNum"));
            gift.setNeedNum(needNum.intValue());
            Boolean available = needNum <= purpleGoldBellNum;
            gift.setAvailable(available);
            giftList.add(gift);
        }
        return giftList;
    }

    public Boolean exchangeGift(String giftKey) {
        BaseParam baseParam = BaseParam.ofMDC();
        //资源检查
        Long purpleGoldBellNum = journeyToTheWestConstant.getPurpleGoldBellNum(baseParam.getUid());
        List<ScenePrizeDO> scenePrizeDOS = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), JourneyToTheWestConstant.ACTIVITY_CODE, JourneyToTheWestConstant.EXCHANGE_GIFT);
        ScenePrizeDO result = scenePrizeDOS.stream().filter(scenePrize -> (Objects.equals(giftKey, scenePrize.getPrizeValue()))).findFirst().orElse(null);
        if (result == null) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "礼物不存在");
        }
        String extData = result.getExtData();
        if (StrUtil.isBlank(extData)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "配置错误");
        }
        Map<String, String> extDataMap = JSONUtil.toBean(extData, Map.class);
        Long exchangeNum = Long.valueOf(extDataMap.get("purpleGoldBellNum"));
        if (purpleGoldBellNum < exchangeNum) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "兑换道具不足");
        }
        journeyToTheWestConstant.decrementPurpleGoldBellNum(baseParam.getUid(), exchangeNum);
        sendPrizeManager.sendPrize(
                baseParam,
                Collections.singletonList(SendPrizeDTO.of(result, baseParam.getUid()))
        );
        //兑换记录
        List<DrawPoolItemDTO> prizeItemList = buildExchangeItemList(result);
        logComponent.putDrawLog(BaseParam.ofMDC(), JourneyToTheWestConstant.ACTIVITY_CODE, prizeItemList);
        //埋点
        journeyToTheWestTrackManager.allActivityReceiveAward(JourneyToTheWestConstant.AwardTypeEnum.SHOP_EXCHANGE.getCode(), giftKey, result.getPrizeValueGold(), result.getPrizeNum(), baseParam.getUid());
        return Boolean.TRUE;
    }

    private List<DrawPoolItemDTO> buildExchangeItemList(ScenePrizeDO scenePrizeDO) {
        DrawPoolItemDO drawPoolItemDO = DrawPoolItemDO.builder()
                .itemName(scenePrizeDO.getPrizeDesc())
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .itemKey(scenePrizeDO.getPrizeValue())
                .itemNum(1)
                .itemValueGold(scenePrizeDO.getPrizeValueGold())
                .status(1)
                .poolCode(JourneyToTheWestConstant.EXCHANGE_GIFT_CODE)
                .build();
        DrawPoolItemDTO drawPoolItemDTO = DrawPoolItemDTO.builder()
                .targetTimes(1)
                .drawPoolItemDO(drawPoolItemDO).build();
        List<DrawPoolItemDTO> prizeItemList = new ArrayList<>();
        prizeItemList.add(drawPoolItemDTO);
        return prizeItemList;
    }

    public Object getDrawLog(BaseParam param, String activityCode, String poolCode) {
        Long uid = param.getUid();
        return journeyToTheWestDrawManager.drawLogNew(DrawLogParam.builder()
                .appId(param.getAppId())
                .unionId(param.getUnionId())
                .uid(uid)
                .activityCode(activityCode)
                .build(), poolCode);
    }

    public RankVO getRank(Long toUid) {
        return journeyToTheWestRankManager.getRank(RankContext.builder()
                .param(BaseParam.ofMDC())
                .otherUid(toUid)
                .activityCode(JourneyToTheWestConstant.ACTIVITY_CODE)
                .rankKey(JourneyToTheWestConstant.CP_RANK_KEY)
                .rankLen(10L)
                .supportDiff(Boolean.TRUE)
                .type(RankContext.RankType.cp)
                .build());
    }
}
