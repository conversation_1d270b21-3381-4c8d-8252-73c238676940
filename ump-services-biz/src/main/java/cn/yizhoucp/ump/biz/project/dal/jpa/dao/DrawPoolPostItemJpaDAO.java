package cn.yizhoucp.ump.biz.project.dal.jpa.dao;

import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolPostItemDO;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 奖池公示信息礼物项
 *
 * @author: lianghu
 */
@Repository
public interface DrawPoolPostItemJpaDAO extends JpaSpecificationExecutor<DrawPoolPostItemDO>, CrudRepository<DrawPoolPostItemDO, Long> {

    /**
     * 根据奖池公示信息编号查询
     *
     * @param poolCode
     * @return java.util.List<api.project.dal.jpa.dataobject.DrawPoolPostItemDO>
     */
    @Query(value = "select * from draw_pool_post_info_item where activity_code = ?1 and pool_code = ?2 and status = 1", nativeQuery = true)
    List<DrawPoolPostItemDO> getByPoolCode(String activityCode, String poolCode);

    List<DrawPoolPostItemDO> findByIdIn(List<Long> ids);
}
