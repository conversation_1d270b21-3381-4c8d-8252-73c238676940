package cn.yizhoucp.ump.biz.project.biz.manager.activity.legendary_star_park;

import cn.yizhoucp.ms.core.base.enums.GiftFrom;
import cn.yizhoucp.ms.core.base.enums.GiftWay;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.legendary_star_park.internal.LegendaryStarParkConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.legendary_star_park.internal.LegendaryStarParkEnums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.legendary_star_park.internal.LegendaryStarParkRedisManager;
import cn.yizhoucp.ump.biz.project.common.ActivityComponent;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class LegendaryStarParkBizManager implements ActivityComponent {

    @Resource
    private LegendaryStarParkRedisManager legendaryStarParkRedisManager;

    @Resource
    private LegendaryStarParkRankManager legendaryStarParkRankManager;
    @Resource
    private LegendaryStarParkIndexManager legendaryStarParkIndexManager;
    @Resource
    private LegendaryStarParkTrackManager legendaryStarParkTrackManager;


    @Override
    public String getActivityCode() {
        return LegendaryStarParkConstant.ACTIVITY_CODE;
    }

    @ActivityCheck(activityCode = LegendaryStarParkConstant.ACTIVITY_CODE, isThrowException = false)
    public Boolean sendGiftHandle(BaseParam param, List<CoinGiftGivedModel> coinGiftGivedModelList) {
        for (CoinGiftGivedModel coinGiftGivedModel : coinGiftGivedModelList) {
            //检查是否来自面板和聊天室
            if (Boolean.FALSE.equals(checkGift(coinGiftGivedModel))) {
                return Boolean.FALSE;
            }
            //每日任务
            dailyTask(param, coinGiftGivedModel);
        }
        return Boolean.TRUE;
    }

    public void calculateRankValues(BaseParam param, String poolCode, Long productCount) {
        LegendaryStarParkEnums.LegendaryStarParkPoolType poolType = LegendaryStarParkEnums.LegendaryStarParkPoolType.getPoolTypeByPoolCode(poolCode);
        if (poolType == null) {
            return;
        }
        legendaryStarParkRankManager.incrRankValue(param.getUid(), productCount, legendaryStarParkRedisManager.getWeeklyKey(poolType.getPoolRankKey()));
    }

    public void isGloryMomentEnabled(Long uid, String poolCode, Long roomId, Long num) {
        if (roomId == null || poolCode == null) {
            return;
        }
        LegendaryStarParkEnums.LegendaryStarParkPoolType poolType = LegendaryStarParkEnums.LegendaryStarParkPoolType.getPoolTypeByPoolCode(poolCode);
        if (poolType == null) {
            return;
        }
        Long drawTimes = legendaryStarParkRedisManager.incrementRoomDrawTimes(roomId, poolCode, num);
        if (drawTimes >= poolType.getGloryMomentDrawStarNumber()) {
            Boolean isGloryMoment = legendaryStarParkRedisManager.startGloryMoment(roomId, poolCode);
            if (Boolean.TRUE.equals(isGloryMoment)) {
                legendaryStarParkRedisManager.deleteRoomDrawTimes(roomId, poolCode);
            }
            //埋点
            legendaryStarParkTrackManager.allActivityTaskFinish(uid, poolType.getTrackCode());
        }

    }

    private Boolean checkGift(CoinGiftGivedModel coinGiftGivedModel) {
        boolean isPanel = Boolean.FALSE;
        // 面板礼物
        if (!StringUtils.isEmpty(coinGiftGivedModel.getGiftWay())) {
            isPanel = StringUtils.equalsIgnoreCase(coinGiftGivedModel.getGiftWay(), GiftWay.NORMAL.getCode());
            if (!isPanel) {
                return Boolean.FALSE;
            }
        }
        //聊天室礼物
        if (!StringUtils.isEmpty(coinGiftGivedModel.getFrom())) {
            isPanel = StringUtils.equalsIgnoreCase(coinGiftGivedModel.getFrom(), GiftFrom.live_room.getCode())
                    || StringUtils.equalsIgnoreCase(coinGiftGivedModel.getFrom(), GiftFrom.room.getCode());
        }
        return isPanel;
    }

    /**
     * 奖池收集任务
     *
     * @param param
     * @param targetTimes
     */
    public void collectedTask(BaseParam param, String poolCode, String giftKey, Integer targetTimes) {
        Integer taskId = legendaryStarParkRedisManager.getCollectedTaskId(poolCode, giftKey);
        LegendaryStarParkEnums.CollectedTaskEnum collectedTaskEnum = LegendaryStarParkEnums.CollectedTaskEnum.getTaskById(taskId);
        if (collectedTaskEnum == null) {
            return;
        }
        legendaryStarParkRedisManager.incrementCollectedTaskProgress(param.getUid(), collectedTaskEnum.getTaskId(), targetTimes.longValue(), poolCode);
    }

    /**
     * 每日任务
     *
     * @param param
     * @param coinGiftGivedModel
     */
    private void dailyTask(BaseParam param, CoinGiftGivedModel coinGiftGivedModel) {
        LegendaryStarParkEnums.DailyTaskEnum dailyTaskEnum = LegendaryStarParkEnums.DailyTaskEnum.getTaskByGiftKey(coinGiftGivedModel.getGiftKey());
        if (dailyTaskEnum == null) {
            return;
        }
        legendaryStarParkRedisManager.incrementDailyTaskProgress(param.getUid(), dailyTaskEnum.name(), dailyTaskEnum.getTaskKey(), coinGiftGivedModel.getProductCount().intValue());
    }


}
