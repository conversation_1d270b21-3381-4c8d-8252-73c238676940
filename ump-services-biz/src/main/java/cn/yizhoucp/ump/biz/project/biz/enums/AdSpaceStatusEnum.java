package cn.yizhoucp.ump.biz.project.biz.enums;

import cn.yizhoucp.ms.core.base.jpa.IBaseJpaEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 广告位状态
 * <AUTHOR>
 */
@Deprecated
public enum AdSpaceStatusEnum implements IBaseJpaEnum {

  /** 待测试 */
  to_be_test(0,"to_be_test", "待测试"),
  /** 测试中 */
  testing(1,"testing", "测试中"),
  /** 上架中 */
  put_on_ing(2,"put_on_ing", "上架中"),
  /** 已上架 */
  put_on(3,"put_on", "已上架"),
  /** 已下架 */
  put_off(4,"put_off", "已下架"),
  /** 已删除 */
  deleted(5,"deleted", "已删除"),
  ;

  private Integer index;
  private String code;

  private String desc;

  AdSpaceStatusEnum(String code, String desc) {
    this.code = code;
    this.desc = desc;
  }

  AdSpaceStatusEnum(Integer index, String code, String desc) {
    this.index = index;
    this.code = code;
    this.desc = desc;
  }

  /**
   * 根据 code 获取枚举
   * @param code  code
   * @return  AdSpaceTypeEnum
   */
  public static AdSpaceStatusEnum findByCode(String code) {
    if (StringUtils.isBlank(code)) {
      return null;
    }
    for (AdSpaceStatusEnum type : values()) {
      if (StringUtils.equals(type.getCode(), code)) {
        return type;
      }
    }
    return null;
  }

  /**
   * 获取所有枚举键值对
   * @return  List<Map<String, String>>
   */
  public static List<Map<String, String>> getList() {
    List<Map<String, String>> result = new ArrayList<>();
    Map<String, String> map = new HashMap<>();
    for (AdSpaceStatusEnum type : values()) {
      map.put("code", type.getCode());
      map.put("desc", type.getDesc());
    }
    result.add(map);
    return result;
  }

  public String getCode() {
    return code;
  }

  public String getDesc() {
    return desc;
  }

  public Integer getIndex() {
    return index;
  }


  @Override
  public Integer getIdx() {
    return this.index;
  }

}
