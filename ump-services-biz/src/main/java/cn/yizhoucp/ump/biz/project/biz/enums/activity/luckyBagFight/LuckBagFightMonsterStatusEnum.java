package cn.yizhoucp.ump.biz.project.biz.enums.activity.luckyBagFight;

/**
 * <AUTHOR>
 * @Description 怪兽状态枚举
 * @date 2022-12-12 12:11
 */
public enum LuckBagFightMonsterStatusEnum {
    /**
     * 穿戴中
     * 进行中
     * 已击杀
     */
    lock("lock","未解锁"),
    processing("processing","进行中"),
    killed("killed","已击杀"),

    ;

    private String code;
    private String desc;

    LuckBagFightMonsterStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static LuckBagFightMonsterStatusEnum getByCode(String type){
        for (LuckBagFightMonsterStatusEnum item: LuckBagFightMonsterStatusEnum.values()){
            if (item.code.equals(type)){
                return item;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
