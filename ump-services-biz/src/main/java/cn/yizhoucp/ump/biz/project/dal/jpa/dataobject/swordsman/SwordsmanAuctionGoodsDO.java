package cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.swordsman;

import lombok.Data;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @description 拍卖古董表
 * @createDate 2024/11/20
 */
@Entity
@Table(name = "swordsman_auction_goods")
@Data
public class SwordsmanAuctionGoodsDO {
    /**
     * 主键 ID
     */

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 拍卖品对应的礼物 key（只有古董才有此属性）
     */
    private String auctionKey;

    /**
     * 拍品名称
     */
    private String auctionName;

    /**
     * 拍品图标
     */
    private String auctionIcon;

    /**
     * 拍品价值
     */
    private Integer auctionPrice;

    /**
     * 拍品起拍价格
     */
    private Integer auctionStartPrice;

    /**
     * 拍品类型（拍卖、古董）
     */
    private String auctionType;

    /**
     * 库存数量（古董专用）
     */
    private Integer stock;

    /**
     * 拍卖日期，距离活动开始日期的天数(1,2,3...10)
     */
    private String auctionDate;


}
