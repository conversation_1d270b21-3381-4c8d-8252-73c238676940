package cn.yizhoucp.ump.biz.project.biz.manager.jimu;

import cn.yizhoucp.ump.api.vo.jimu.draw.DrawLogNewVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.DrawLogVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.DrawReturn;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawLogParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;

/**
 * 抽奖玩法
 *
 * @author: lianghu
 */
public interface DrawManager {

    /**
     * 抽奖
     *
     * @param drawParam
     * @return cn.yizhoucp.ms.core.vo.umpServices.activity.common.jimu.DrawReturn
     */
    DrawReturn draw(DrawParam drawParam);

    /**
     * 抽奖记录
     *
     * @param drawLogParam
     * @return
     */
    default DrawLogVO drawLog(DrawLogParam drawLogParam) {
        return null;
    }

    /**
     * 抽奖记录（分页）
     *
     * @param drawLogParam
     * @return
     */
    default DrawLogVO drawLogByPage(DrawLogParam drawLogParam) {
        return null;
    }

    default DrawLogNewVO drawLogNew(DrawLogParam param) {
        return null;
    }

    /**
     * 抽奖记录 - 根据活动code + 奖池code
     * @param param
     * @return
     */
    default DrawLogNewVO drawLogLatest(DrawLogParam param) {
        return null;
    }

}
