package cn.yizhoucp.ump.biz.project.biz.manager.activity.music_for_heart;

import cn.yizhoucp.ms.core.base.config.ThreadPoolConfig;
import cn.yizhoucp.ms.core.base.enums.GraphIdEnum;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.enums.ServicesNameEnum;
import cn.yizhoucp.ms.core.base.util.RiskUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.risk.api.client.RiskFeignService;
import cn.yizhoucp.risk.api.constant.RiskParam;
import cn.yizhoucp.risk.api.constant.RiskScene;
import cn.yizhoucp.risk.api.dto.RiskParamDTO;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.music_for_heart.common.MusicForHeartConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.music_for_heart.common.MusicForHeartEnums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.music_for_heart.common.MusicForHeartRedisManager;
import cn.yizhoucp.ump.biz.project.common.ActivityComponent;
import cn.yizhoucp.ump.biz.project.common.event.GiftGiveEvent;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 随悦而动业务类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 14:07 2025/4/7
 */
@Slf4j
@Service
public class MusicForHeartBizManager implements ActivityComponent {

    @Resource
    private MusicForHeartRedisManager musicForHeartRedisManager;
    @Resource
    private RiskFeignService riskFeignService;
    @Resource
    private MusicForHeartTrackManager musicForHeartTrackManager;


    @Override
    public String getActivityCode() {
        return MusicForHeartConstant.ACTIVITY_CODE;
    }

    @Async(ThreadPoolConfig.ASYNC_EXECUTOR_NAME)
    @EventListener(GiftGiveEvent.class)
    @ActivityCheck(activityCode = MusicForHeartConstant.ACTIVITY_CODE, isThrowException = false)
    public void eventSendGiftHandle(GiftGiveEvent event) {
        log.info("sendGiftHandle event:{}", event);
        sendGiftHandle(event.getBaseParam(), event.getCoinGiftGivedModels());
    }

    public Boolean sendGiftHandle(BaseParam param, List<CoinGiftGivedModel> coinGiftGivedModelList) {
        // 实现具体的送礼逻辑
        return null;
    }

    @Async(ThreadPoolConfig.ASYNC_EXECUTOR_NAME)
    public void containsProhibitedWords(Long uid, Long workId, String workName, String workUrl) {
        Map<String, Object> extend = new HashMap<>();
        extend.put("workId", workId);
        extend.put("uid", uid);

        Map<String, Object> detection = RiskUtil.getDetectionMap(workName, null,replaceDomain(workUrl) , null, null, null);
        RiskParamDTO build = RiskParamDTO.builder().scene(RiskScene.ACTIVITY)
                .services(ServicesNameEnum.ump_services)
                .baseUrl(RiskParam.baseRiskUrl).basePath(RiskParam.asyncScanUrl)
                .appId(ServicesAppIdEnum.lanling.getAppId())
                .callbackUrl(RiskParam.callbackProperties)
                .userId(uid)
                .graphId(GraphIdEnum.CP_AUDIO_FILE)
                .detection(detection)
                .extend(extend)
                .build();
        String riskResultString = null;
        try {
            riskResultString = riskFeignService.sendRisk(build).successData();
        } catch (Exception e) {
            log.error("sendRiskError {} err : ", build, e);
            handleRiskFail(uid, workId);
            return;
        }
        log.info("riskResultString {}", riskResultString);
        if(riskResultString!=null){
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                JsonNode root = objectMapper.readTree(riskResultString);
                JsonNode dataNode = root.path("data");
                String id = dataNode.path("_id").asText();
                log.info("解析到_id: {}", id); // 记录日志
                musicForHeartRedisManager.processRiskResult(id, workId, uid);
            } catch (JsonProcessingException e) {
                log.error("JSON解析失败: {}", riskResultString, e);
            }
        }
        //上线前暂时直接通过，上线后走风控
        if(musicForHeartRedisManager.isTest()){
            handleRiskPass(uid, workId);
        }
    }

    public String replaceDomain(String originalUrl) {
        String newDomain = "https://nuanchat.oss-cn-hangzhou.aliyuncs.com/";
        String oldDomain = "https://res-cdn.nuan.chat/";
        if (originalUrl != null && originalUrl.startsWith(oldDomain)) {
            String path = originalUrl.substring(oldDomain.length());
            return newDomain + path;
        } else {
            log.warn("The original URL does not start with the expected domain.");
            return originalUrl;
        }
    }

    // 新增方法：处理风控通过的逻辑
    @ActivityCheck(activityCode = MusicForHeartConstant.ACTIVITY_CODE, isThrowException = false)
    public void handleRiskPass(Long uid, Long workId) {
        log.info("风险检测通过，uid:{}, workId:{}", uid, workId);
        if(uid==null||workId==null){
            log.warn("uid or workId is null, uid:{}, workId:{}", uid, workId);
            return;
        }
        musicForHeartRedisManager.updateWorkStatus(uid, workId, MusicForHeartEnums.AuditStatusEnum.AUDIT_SUCCESS.getStatus());
        musicForHeartRedisManager.addToRank(uid, workId);
        musicForHeartTrackManager.allActivityTaskFinish(uid, "upload_work_sucessfully");
    }

    // 新增方法：处理风控未通过的逻辑
    @ActivityCheck(activityCode = MusicForHeartConstant.ACTIVITY_CODE, isThrowException = false)
    public void handleRiskFail(Long uid, Long workId) {
        log.warn("风险检测未通过，uid:{}, workId:{}", uid, workId);
        if(uid==null||workId==null){
            log.warn("uid or workId is null, uid:{}, workId:{}", uid, workId);
            return;
        }
        musicForHeartRedisManager.updateWorkStatus(uid, workId, MusicForHeartEnums.AuditStatusEnum.AUDIT_FAIL.getStatus());
    }

}