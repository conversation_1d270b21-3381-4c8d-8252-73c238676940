package cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

public class StarWishCardCollectionGameConstant {

    /** 活动 code */
    public static final String ACTIVITY_CODE = "star_wish_card_collection_game";

    /** 替换的礼物 */
    /** uid */
    public static final String REPLACE_GIFT = "ump:star_wish_card_collection_game2:replace_gift:%s";

    /** 占星次数 */
    /** uid yyyyMMdd */
    public static final String ASTROLOGY_TIMES = "ump:star_wish_card_collection_game2:astrology_times:%s:%s";

    /** 水晶球是否祈愿 */
    /** uid yyyyMMdd CrystalBall.name() */
    public static final String CRYSTAL_PRAY = "ump:star_wish_card_collection_game2:crystal_pray:%s:%s:%s";

    /** 当前的卡 */
    /** uid */
    public static final String CARD_COLLECTION_CUR = "ump:star_wish_card_collection_game2:card_collection_cur:%s";
    /** 累积的卡，榜单和任务用 */
    public static final String CARD_COLLECTION_WITH_EXCHANGE = "ump:star_wish_card_collection_game2:card_collection_with_exchange:%s";

    /** 星愿榜 */
    /** yyyyMMdd */
    public static final String STAR_WISH_BOARD = "ump:star_wish_card_collection_game2:star_wish_board:%s";
    /** 集卡榜 */
    public static final String CARD_COLLECTION_BOARD = "ump:star_wish_card_collection_game2:card_collection_board";

    /** 中奖轮播 */
    public static final String BROADCAST = "ump:star_wish_card_collection_game2:broadcast";
    public static final Set<String> BROADCAST_GIFT = Sets.newHashSet("ADWJ_GIFT","XKC_GIFT","YSZL_GIFT","LYBF_GIFT","TXDG_GIFT","TKZY_GIFT","YSSA_GIFT","QNZY_GIFT","SLDD_GIFT");

    @AllArgsConstructor
    @Getter
    public enum CrystalBall {
        CRYSTAL_BALL_1(50, 85, 15, 0, 0, 0),
        CRYSTAL_BALL_2(299, 15, 85, 0, 0, 0),
        CRYSTAL_BALL_3(1314, 0, 20, 80, 0, 0),
        CRYSTAL_BALL_4(2588, 0, 0, 20, 80, 0),
        CRYSTAL_BALL_5(5999, 0, 0, 0, 55, 45),
        ;

        private final Integer astrologyTimes;
        private final Integer xingCardProb;
        private final Integer yuanCardProb;
        private final Integer jiCardProb;
        private final Integer kaCardProb;
        private final Integer leCardProb;
    }

    @AllArgsConstructor
    @Getter
    public enum ReplaceGift {
        REPLACE_GIFT_1(1, 1, 0, 0, 0),
        REPLACE_GIFT_2(1, 1, 1,  0, 0),
        REPLACE_GIFT_3(1, 1, 1,  1, 0),
        REPLACE_GIFT_4(1, 1, 1,  1, 1),
        ;

        private final Integer xingCardNeed;
        private final Integer yuanCardNeed;
        private final Integer jiCardNeed;
        private final Integer kaCardNeed;
        private final Integer leCardNeed;
    }

    public static final Map<String, String> CARD_CODE_OLD_NEW_MAP = Maps.newHashMapWithExpectedSize(5);
    public static final Map<String, String> CARD_CODE_NEW_OLD_MAP = Maps.newHashMapWithExpectedSize(5);
    static {
        CARD_CODE_OLD_NEW_MAP.put("XING", "XING");
        CARD_CODE_OLD_NEW_MAP.put("YUAN", "YUN");
        CARD_CODE_OLD_NEW_MAP.put("JI", "TA");
        CARD_CODE_OLD_NEW_MAP.put("KA", "LUO");
        CARD_CODE_OLD_NEW_MAP.put("LE", "PAI");
        CARD_CODE_NEW_OLD_MAP.put("XING", "XING");
        CARD_CODE_NEW_OLD_MAP.put("YUN", "YUAN");
        CARD_CODE_NEW_OLD_MAP.put("TA", "JI");
        CARD_CODE_NEW_OLD_MAP.put("LUO", "KA");
        CARD_CODE_NEW_OLD_MAP.put("PAI", "LE");
    }

}
