package cn.yizhoucp.ump.biz.project.biz.manager.activity.loveInProgress.strategy;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.loveInProgress.common.LoveInProgressEnums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.loveInProgress.common.LoveInProgressRedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class LoveInProgressTaskTakenStrategy implements ExecutableStrategy {
    @Resource
    private LoveInProgressRedisManager loveInProgressRedisManager;

    @Override
    public Object execute(ButtonEventParam buttonEventParam) {
        // 任务领取事件
        BaseParam baseParam = buttonEventParam.getBaseParam();
        Long uid = baseParam.getUid();
        String taskCode = buttonEventParam.getBizKey();
        LoveInProgressEnums.TaskEnum taskEnum = LoveInProgressEnums.TaskEnum.getByTaskCode(taskCode);
        if (taskEnum==null) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "任务不存在");
        }
        // 任务领取逻辑
        loveInProgressRedisManager.setTaskClaimed(uid, taskCode);
        return Boolean.TRUE;
    }
}
