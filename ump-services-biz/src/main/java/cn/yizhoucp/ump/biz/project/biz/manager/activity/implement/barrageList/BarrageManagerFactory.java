package cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.barrageList;


import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.ActivityInfoKeyEnum;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityInfoUtil;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.beans.Introspector;
import java.util.List;
import java.util.Map;

/**
 * 弹幕玩法工厂
 *
 * @author: lianghu
 */
@Component
public class BarrageManagerFactory {

    @Autowired
    private List<BarrageManager> barrageManagerList;

    private static final Map<String, BarrageManager> instanceMap = Maps.newConcurrentMap();

    @PostConstruct
    public void init() {
        for (BarrageManager barrageManager : barrageManagerList) {
            //获取实现类的beanName
            String simpleName = AopUtils.getTargetClass(barrageManager).getSimpleName();
            String beanName = Introspector.decapitalize(simpleName);
            //放入缓存
            instanceMap.put(beanName,barrageManager);
        }
    }

    public BarrageManager getStrategy() {
        String strategy = (String) ActivityInfoUtil.getConfig().get(ActivityInfoKeyEnum.BARRAGE_LIST_STRATEGY.getCode());
        if (StringUtils.isBlank(strategy)) {
            return null;
        }
        return instanceMap.get(strategy);
    }

}
