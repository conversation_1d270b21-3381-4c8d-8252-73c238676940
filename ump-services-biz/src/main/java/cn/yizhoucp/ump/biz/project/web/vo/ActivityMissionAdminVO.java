package cn.yizhoucp.ump.biz.project.web.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityMissionAdminVO implements Serializable {

    private static final long serialVersionUID = 39643211637615864L;

    private Long id;
    /** 应用 ID */
    private String unionId;
    /** 应用 ID */
    private Long appId;
    /** 任务编号 */
    private String code;
    /** 所属活动 */
    private String belongActivityCode;
    /** 标题 */
    private String title;
    /** 描述 */
    private String missionDesc;
    /** icon */
    private String icon;
    /** 路由 */
    private String route;
    /** 完成次数上限 */
    private Integer limitTimes;
    /** 分组 */
    private String groupId;
    /** 任务状态 */
    private String status;
    /** 任务类型 */
    private String type;
    /** 优先级 */
    private Integer priority;
    /** 活动板块 */
    private String activityPlate;
    /** 完成类型 */
    private String finishType;
    /** 周期 */
    private String cycleType;
    /** 下发类型 */
    private String distributeType;
    /** 礼物 key */
    private String giftKey;
    /** 数量 */
    private Integer giftCount;
    private List<PrizeItemAdminVO> prizeItemAdminVOList;

}
