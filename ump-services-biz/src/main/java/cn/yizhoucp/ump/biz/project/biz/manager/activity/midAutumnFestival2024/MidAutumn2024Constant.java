package cn.yizhoucp.ump.biz.project.biz.manager.activity.midAutumnFestival2024;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

public class MidAutumn2024Constant {

    public static final String ACTIVITY_CODE_PRE = "mid_autumn_festival_2024_pre";

    public static final String ACTIVITY_CODE = "mid_autumn_festival_2024";


    public static final String MOON_TOUR_RANKING = "activity:" + ACTIVITY_CODE + ":moon_tour_ranking";
    public static final String LIGHT_UP_RECORD = "light_up_record";
    public static final String STARRY_NIGHT_WINE_RECORD = "starry_night_wine_record";
    public static final String STARRY_NIGHT_WINE_TASK_COMPLETION_RECORD = "night_wine_task_record";
    public static final String GIFT_POOL = "gift_pool";
    public static final List<String> POOL_CODES = Arrays.asList("mid_autumn_pool_1", "mid_autumn_pool_2", "mid_autumn_pool_3");
    //礼盒包含的礼物
    public static final String QYJQ_BOX_GIFT = "QYJQ_GIFT";
    public static final String YMXC_GIFT = "YMXC_GIFT";
    public static final String YHXS_GIFT = "YHXS_GIFT";

    public static final String DRAW_POOL_CODE = "longing_reward_pool";
    //酒杯奖池
    public static final String WINE_REWARD_POOL = "wine_reward_pool";
    public static final String AUTUMN_MOON_TOUR_TASK_COMPLETION_RECORD = "moon_tour_task_record";

    /**
     * 所有参加活动的用户
     */
    public static final String ALL_USER_KEY = "activity:" + ACTIVITY_CODE + ":all_user";
    public static final String BIZ_LOCK_KEY = "activity:"+ ACTIVITY_CODE + ":biz_lock";


    public static String createPairingKey(Long fromUid) {
        return "activity:" + ACTIVITY_CODE_PRE + ":pairing:user:" + fromUid;
    }

    public static String createAllPairingKey() {
        return "activity:" + ACTIVITY_CODE_PRE + ":pairing:user:all";
    }

    /**
     * 任务进度
     *
     * @param id
     * @param taskCode
     * @return
     */
    public static String createTaskKey(String id, String taskCode) {
        return "activity:" + ACTIVITY_CODE + ":task:" + id + ":" + taskCode;
    }

    public static String createTaskIsCompleteKey(String id, String taskCode) {
        return "activity:" + ACTIVITY_CODE + ":task:isComplete:" + id + ":" + taskCode;
    }

    /**
     * 相思雀数量
     */
    public static String createDrawItemKey(Long uid) {
        return "activity:" + ACTIVITY_CODE + ":draw:item:" + uid;
    }

    public static String createMoonLightKey(String id) {
        return String.format("activity:%s:moon_light_value:%s", ACTIVITY_CODE, id);
    }

    /**
     * 拼图收集
     */
    public static String createPuzzlePiecesKey(String id, String collectedTimes) {
        return String.format("activity:%s:puzzle_pieces:%s:%s", ACTIVITY_CODE, id, collectedTimes);
    }

    public static String createPuzzlePiecesCollectedTimesKey(String id) {
        return String.format("activity:%s:puzzle_pieces:%s", ACTIVITY_CODE, id);
    }

    public static String createWineCollectedTimesKey(Long uid) {
        return String.format("activity:%s:wine:collected:times:%s", ACTIVITY_CODE, uid);
    }

    public static String createGiftBoxKey(Long uid, Long id) {
        return String.format("activity:%s:gift_box:%s:%s", ACTIVITY_CODE, uid, id);
    }

    public static String createGiftPoolKey(Long uid) {
        return String.format("activity:%s:gift_pool:%s", ACTIVITY_CODE, uid);
    }

    public static String createTaskIsReceiveKey(String id, String taskCode) {
        return String.format("activity:%s:task:isReceive:%s:%s", ACTIVITY_CODE, id, taskCode);
    }

    public static String createPoolCodeKey(Long userId) {
        return String.format("activity:%s:pool_code:%s", ACTIVITY_CODE, userId);
    }

    public static String createLightCountKey(String s) {
        return String.format("activity:%s:light_count:%s", ACTIVITY_CODE, s);
    }

    @AllArgsConstructor
    @Getter
    public enum AutumnMoonTourTasks {

        TASK_1(1, "moon_tour_task_1", "1个【秋夜寄情】", 2L, 1L, false),
        TASK_2(2, "moon_tour_task_2", "1个【月满乡愁】", 20L, 1L, false),
        TASK_3(3, "moon_tour_task_3", "1个【银辉相思】", 50L, 1L, false),
        TASK_4(4, "moon_tour_task_4", "集齐【秋夜寄情】全部3个礼物", 6L, 3L, true),
        TASK_5(5, "moon_tour_task_5", "集齐【月满乡愁】全部3个礼物", 60L, 3L, true),
        TASK_6(6, "moon_tour_task_6", "集齐【银辉相思】全部3个礼物", 150L, 3L, true);


        private final Integer taskId;
        private final String taskCode;
        private final String taskDesc;
        private final Long reward;
        private final Long totalProgress;
        private final Boolean isReceive;

        public static AutumnMoonTourTasks getByTaskCode(String taskCode) {
            for (AutumnMoonTourTasks value : values()) {
                if (value.taskCode.equals(taskCode)) {
                    return value;
                }
            }
            return null;
        }

    }

    @AllArgsConstructor
    @Getter
    public enum GiftBox {
        /**
         * 月影礼
         */
        MOON_SHADOW(1L, "月影礼", "moon_shadow", 10),
        /**
         * 逐月礼
         */
        CHASE_MOON(2L, "逐月礼", "chase_moon", 30),
        /**
         * 流光礼
         */
        FLOW_LIGHT(3L, "流光礼", "flow_light", 60);
        private final Long id;
        private final String name;
        private final String key;
        private final Integer needNum;

        public static GiftBox getById(Long id) {
            for (GiftBox value : values()) {
                if (value.id.equals(id)) {
                    return value;
                }
            }
            return null;
        }
    }

    @AllArgsConstructor
    @Getter
    public enum SendGiftBox {
        /**
         * 秋夜寄情
         */
        QYJQ_BOX(1L, "秋夜寄情", "QYJQ_GIFT", Arrays.asList("SJMG_GIFT", "BBX_GIFT", "GBQQ_GIFT")),
        /**
         * 逐月礼
         */
        YMXC_GIFT(2L, "月满乡愁", "YMXC_GIFT", Arrays.asList("XST_GIFT", "JBGY_GIFT", "YHJS_GIFT")),
        /**
         * 流光礼
         */
        YHSX_GIFT(3L, "银辉相思", "YHXS_GIFT", Arrays.asList("YSCA_GIFT", "XYXY_GIFT", "LMRQQ_GIFT"));
        private final Long id;
        private final String name;
        private final String boxKey;
        private final List<String> giftKeys;
    }

    @AllArgsConstructor
    @Getter
    public enum StarryNightWineTask {
        ZQYB_GIFT(1, "ZQYB_GIFT", "starry_night_wine_task_1"),
        DYFZ_GIFT(2, "DYFZ_GIFT", "starry_night_wine_task_2"),
        THSB_GIFT(3, "THSB_GIFT", "starry_night_wine_task_3"),
        DQYP_GIFT(4, "DQYP_GIFT", "starry_night_wine_task_4"),
        ZAXC_GIFT(5, "ZAXC_GIFT", "starry_night_wine_task_5"),
        XYYH_GIFT(6, "XYYH_GIFT", "starry_night_wine_task_6"),
        XQQL_GIFT(7, "XQQL_GIFT", "starry_night_wine_task_7");

        private final Integer taskId;
        private final String giftKey;
        private final String rewardPoolCode;
    }


    @AllArgsConstructor
    @Getter
    public enum StarryNightWine {
        TS(1L, "天枢", "TS_GIFT"),
        TX(2L, "天璇", "TX_GIFT"),
        TJ(3L, "天玑", "TJ_GIFT"),
        TQ(4L, "天权", "TQ_GIFT"),
        YH(5L, "玉衡", "YH_GIFT"),
        KY(6L, "开阳", "KY_GIFT"),
        YG(7L, "摇光", "YG_GIFT");
        private final Long id;
        private final String name;
        private final String giftCode;

        public static StarryNightWine getByGiftCode(String giftCode) {
            for (StarryNightWine value : values()) {
                if (value.giftCode.equals(giftCode)) {
                    return value;
                }
            }
            return null;
        }
    }


}
