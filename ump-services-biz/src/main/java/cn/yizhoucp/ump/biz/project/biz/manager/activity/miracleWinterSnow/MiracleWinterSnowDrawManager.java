package cn.yizhoucp.ump.biz.project.biz.manager.activity.miracleWinterSnow;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ump.api.vo.jimu.draw.DrawLogNewVO;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw.inner.PrizeTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.log.LogComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.AbstractDrawTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawContext;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawLogParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.doDrawStrategy.common.ProbStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.manager.activity.miracleWinterSnow.MiracleWinterSnowConstant.*;

@Service
@Slf4j
public class MiracleWinterSnowDrawManager extends AbstractDrawTemplate {

    @Resource
    private MiracleWinterSnowConstant constant;

    @Resource
    private ProbStrategy probStrategy;

    @Resource
    private LogComponent logComponent;

    @Resource
    private MiracleWinterSnowTrackManager trackManager;

    @Override
    protected void resourceCheck(DrawContext context) {
        DrawParam drawParam = context.getDrawParam();
        String poolCode = drawParam.getPoolCode();
        Integer poolTimes = drawParam.getTimes();
        if (Objects.isNull(poolCode) || Objects.isNull(poolTimes)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        if (!GM_POOL_CODE.equals(poolCode) && !CB_POOL_CODE.equals(poolCode)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "奖池错误");
        }
        if (GM_POOL_CODE.equals(poolCode)) {
            // 姜饼人
            resourceCheckGM(drawParam);
        } else {
            // 水晶球
            resourceCheckCB(drawParam);
        }
    }

    @Override
    protected void deductResource(DrawContext context) {
        DrawParam drawParam = context.getDrawParam();
        String poolCode = drawParam.getPoolCode();
        Integer poolTimes = drawParam.getTimes();

        if (GM_POOL_CODE.equals(poolCode)) {
            // 姜饼人
            constant.deductGingerbread(drawParam.getUid(), poolTimes * 10L);
        } else {
            // 水晶球碎片
            SHARD_LIST.forEach(shardKey -> constant.deductShardNum(drawParam.getUid(), shardKey, (long) poolTimes));
        }

    }

    @Override
    protected void draw(DrawContext context) {
        // 这里水晶球抽奖是获得两个奖励
        DrawParam drawParam = context.getDrawParam();
        drawParam.setTimes(CB_POOL_CODE.equals(drawParam.getPoolCode()) ? drawParam.getTimes() * 2 : drawParam.getTimes());

        List<DrawPoolItemDTO> drawPoolItemDTOList = probStrategy.getDrawPoolItems(context.getDrawPoolItemDOS(),
                drawParam.getTimes(),
                Boolean.TRUE);
        context.setPrizeItemList(drawPoolItemDTOList);
    }

    @Override
    protected void sendPrize(DrawParam drawParam, List<DrawPoolItemDTO> prizeList) {
        if (Boolean.TRUE.equals(drawParam.getNoSendPrize())) {
            return;
        }
        prizeList.forEach(drawPoolItemDTO -> {
            String itemKey = drawPoolItemDTO.getDrawPoolItemDO().getItemKey();
            if (SHARD_LIST.contains(itemKey)) {
                constant.addShardNum(drawParam.getUid(), itemKey, Long.valueOf(drawPoolItemDTO.getTargetTimes()));
            } else {
                SendPrizeDTO sendPrizeDTO = SendPrizeDTO.of(drawPoolItemDTO);
                sendPrizeManager.sendPrize(drawParam.getBaseParam(), Collections.singletonList(sendPrizeDTO));
            }
        });
    }


    @Override
    protected void doCallback(DrawContext context) {
        DrawParam drawParam = context.getDrawParam();
        // 抽奖结果留存
        logComponent.putDrawLog(drawParam.getBaseParam(), drawParam.getActivityCode(), context.getPrizeItemList());

        // 用户在活动中抽奖开奖时 埋点
        for (DrawPoolItemDTO drawPoolItemDTO : context.getPrizeItemList()) {
            DrawPoolItemDO drawPoolItemDO = drawPoolItemDTO.getDrawPoolItemDO();
            Long awardAmount = drawPoolItemDO.getItemValueGold() * drawPoolItemDTO.getTargetTimes();
            // 一次抽奖多个参数分别传递
            if (Objects.nonNull(drawPoolItemDO.getItemType()) && PrizeTypeEnum.PRIZE_DRESS.getCode().equals(drawPoolItemDO.getItemType())) {
                // 头像框埋点价值处理
                awardAmount = 0L;
            }
            String poolCode = GM_POOL_CODE.equals(drawPoolItemDO.getPoolCode()) ? "gashapon-machine_pool" : "wishes_pool";
            trackManager.allActivityLottery(drawParam.getUid(), poolCode, drawPoolItemDO.getItemKey(), awardAmount, drawPoolItemDTO.getTargetTimes());
        }
    }

    /**
     * 姜饼人资源检查
     *
     * @param drawParam
     */
    public void resourceCheckGM(DrawParam drawParam) {
        Long gingerBreadNum = constant.getGingerBreadNum(drawParam.getUid());
        if (gingerBreadNum < drawParam.getTimes() * 10) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "当前姜饼人数量不够哦～去完成任务获得更多姜饼人继续参加扭蛋吧！");
        }
    }

    /**
     * 水晶球资源检查
     *
     * @param drawParam
     */
    public void resourceCheckCB(DrawParam drawParam) {
        if (!constant.checkShardNum(drawParam.getUid())) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "碎片不足");
        }
    }

    @Override
    public DrawLogNewVO drawLogLatest(DrawLogParam param) {
        String poolCode = param.getPoolCode();
        if (StringUtils.isNotBlank(poolCode)) {
            if (GM_POOL_CODE.equals(poolCode) || CB_POOL_CODE.equals(poolCode)) {
                return super.drawLogLatest(param);
            } else {
                String key = String.format(TASK_LOG_KEY, param.getUid());
                return constant.predictLog(key);
            }
        }
        return new DrawLogNewVO();
    }
}
