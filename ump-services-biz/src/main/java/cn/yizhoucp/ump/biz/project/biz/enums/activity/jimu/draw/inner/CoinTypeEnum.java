package cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw.inner;

import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 下发金币类型
 * <p>
 * 用于 {@link ScenePrizeDO} 中的 coin 类型
 *
 * @author: lianghu
 */
@Getter
@AllArgsConstructor
public enum CoinTypeEnum {

    FEE("fee", "付费金币"),
    FREE("free", "免费金币"),
    ;

    private static final Map<String, CoinTypeEnum> instanceMap = new HashMap<>();

    static {
        for (CoinTypeEnum item : values()) {
            instanceMap.put(item.getCode(), item);
        }
    }

    public static CoinTypeEnum getInstanceByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        return instanceMap.get(code);
    }

    private String code;
    private String desc;
}
