package cn.yizhoucp.ump.biz.project.biz.manager.activity.hallowmasDayHD;

import cn.yizhoucp.ms.core.base.util.AppUtil;
import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.activity.hallowmasDayHD.HallowmasDayHDIndexVO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class HallowmasDayHDConstant {

    @Resource
    private RedisManager redisManager;

    @Resource
    private HallowmasDayHDTrackManager trackManager;

    public static final String ACTIVITY_CODE = "hallowmas_day_hd";
    public static final String HD_POLL = "hallowmas_day_hd_poll";

    public static final Long MIN_SUM_RANK_VALUE = 13140L;
    /**
     * 礼盒
     */
    public static final List<String> GIFT_BOX = Arrays.asList("BSNGT_GIFT", "XYXYL_GIFT", "KAYTG_GIFT");


    /**
     * 糖果数量
     */
    public static final String USER_CANDY_COUNT_KEY = "ump:hallowmas_day_hd:user:%s:candy_count";

    /**
     * 任务赠送数量Key
     */
    public static final String USER_TASK_GIFT_COUNT_KEY = "ump:hallowmas_day_hd:user:%s:task_gift_count:%s";

    /**
     * 任务结果Key
     */
    public static final String TASK_KEY = "ump:hallowmas_day_hd:user:%s:task:%s";

    /**
     * 任务领取限制Key
     */
    public static final String RESTRICTED_TASK_KEY = "ump:hallowmas_day_hd:user:%s:restrict_task:%s";

    /**
     * 结对留存 key
     */
    public static final String PAIR_RETAIN_KEY = "ump:hallowmas_day_hd:user:%s:pair_retain";

    /**
     * 用户结对（自己的榜单）key
     */
    public static final String USER_OTHER_RANK_KEY = "ump:hallowmas_day_hd:user_other_rank:user:%s";

    /**
     * 捣蛋派对
     */
    public static final String DANCE_PARTY_KEY = "ump:hallowmas_day_hd:dance_party";

    /**
     * 捣蛋派对 活动限时礼物
     */
    public static final List<String> DANCE_PARTY_GIFT_LIST = Arrays.asList("MGJX_GIFT", "WSXYL_GIFT", "XZSV_GIFT", "JZMV_GIFT");

    /**
     * 化妆舞会搭档进度
     */
    public static final String MAKEUP_PARTY_KEY = "ump:hallowmas_day_hd:user_other:%s:makeup_party:%s";

    /**
     * 活动任务key集合
     */
    public static final List<String> TASK_LIST = Arrays.asList("taskHD1", "taskHD2", "taskHD3", "taskHD4", "taskHD5", "taskHD6", "taskHD7");

    /**
     * 活动可重复任务
     */
    public static final List<String> RESTRICTED_TASK_LIST = Arrays.asList("taskHD2", "taskHD3", "taskHD4");

    /**
     * 舞会奖励sceneCode
     */
    public static final String MAKEUP_PARTY_SCENE_CODE = "makeup_party_%s_%s";

    /**
     * 两人是否已经到达童话舞会
     */
    public static final String MAKEUP_PARTY_KEY_FORMAT = "ump:hallowmas_day_hd:fairy_ball:%s";


    /**
     * 获取糖果数量
     *
     * @param uid
     * @return
     */
    public Long getCandyCount(Long uid) {
        return Optional.ofNullable(redisManager.getLong(String.format(USER_CANDY_COUNT_KEY, uid))).orElse(0L);
    }

    /**
     * 判断任务是否完成
     *
     * @param taskKey
     * @param uid
     * @return
     */
    public Boolean isTaskFinished(String taskKey, Long uid) {
        return redisManager.hasKey(String.format(TASK_KEY, uid, taskKey));
    }

    public void finishTask(String taskKey, Long uid) {
        redisManager.setnx(String.format(TASK_KEY, uid, taskKey), System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND);
    }

    public void rewardTask(String taskKey, Long uid) {
        redisManager.delete(String.format(TASK_KEY, uid, taskKey));
    }


    public void restrictRewardTask(String taskKey, Long uid) {
        redisManager.setnx(String.format(RESTRICTED_TASK_KEY, uid, taskKey), System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND);
    }

    public Boolean isRestrictedRewardTask(String taskKey, Long uid) {
        return redisManager.hasKey(String.format(RESTRICTED_TASK_KEY, uid, taskKey));
    }


    @AllArgsConstructor
    @Getter
    public enum CandyGiftEnum {
        BSNGT_GIFT("BSNGT_GIFT", 6L, 5L, "taskHD2", "悲伤南瓜头", "party_quest-2"),
        XYXYL_GIFT("XYXYL_GIFT", 3L, 28L, "taskHD3", "巡夜小幽灵", "party_quest-3"),
        KAYTG_GIFT("KAYTG_GIFT", 2L, 34L, "taskHD4", "可爱摇头鬼", "party_quest-4");

        private String codeKey;
        private Long sendCount;
        private Long awardCount;
        private String taskKey;
        private String codeName;
        private String taskType;

        public static CandyGiftEnum getByCode(String item) {
            for (CandyGiftEnum action : CandyGiftEnum.values()) {
                if (action.getCodeKey().equals(item)) {
                    return action;
                }
            }
            return null;
        }

        public static CandyGiftEnum getByTask(String item) {
            for (CandyGiftEnum candyGiftEnum : CandyGiftEnum.values()) {
                if (candyGiftEnum.getTaskKey().equals(item)) {
                    return candyGiftEnum;
                }
            }
            return null;
        }

    }


    /**
     * 化妆舞会
     */
    @AllArgsConstructor
    @Getter
    public enum BallEnum {
        // TODO 礼物Key待定
        ballHD1("ballHD1", 999L),
        ballHD2("ballHD2", 1999L),
        ballHD3("ballHD3", 5200L),
        ballHD4("ballHD4", 9999L),
        ballHD5("ballHD5", 15999L);

        private String ballHDKey;
        private Long satrtCount;

        public static BallEnum getByCode(String item) {
            for (BallEnum ballEnum : BallEnum.values()) {
                if (ballEnum.getBallHDKey().equals(item)) {
                    return ballEnum;
                }
            }
            return null;
        }
    }

    /**
     * 获取搭档的 化妆舞会 进度
     * @return
     */
    public List<HallowmasDayHDIndexVO.BallHD> getTaskList(Long uid, Long toUid) {
        refreshTaskList(uid, toUid);
        List<HallowmasDayHDIndexVO.BallHD> res = new ArrayList<>();
        Long pairRetain = getPairRetain(uid, toUid);
        log.info("cheer value between two users:{}", pairRetain);
        for (BallEnum ballEnum : BallEnum.values()) {
            if (pairRetain < ballEnum.getSatrtCount()) {
                HallowmasDayHDIndexVO.BallHD ballHD = new HallowmasDayHDIndexVO.BallHD();
                ballHD.setBallHDKey(ballEnum.getBallHDKey());
                ballHD.setBallHDStatus(0);
                res.add(ballHD);
            } else {
                HallowmasDayHDIndexVO.BallHD ballHD = new HallowmasDayHDIndexVO.BallHD();
                ballHD.setBallHDKey(ballEnum.getBallHDKey());
                String makeupKey = String.format(MAKEUP_PARTY_KEY, AppUtil.splicUserId(uid, toUid), ballEnum.getBallHDKey());
                String orElse = Optional.ofNullable(redisManager.getString(makeupKey)).orElse(StatusEnum.NOT_FINISHED.getStatus());
                ballHD.setBallHDStatus(Optional.ofNullable(Objects.requireNonNull(StatusEnum.getByCode(orElse)).getStatusValue()).orElse(0));
                res.add(ballHD);
            }
        }
        return res;
    }

    /**
     * 刷新搭档的 化妆舞会 进度
     */
    public void refreshTaskList(Long uid, Long toUid) {
        for (BallEnum ballEnum : BallEnum.values()) {
            String makeupKey = String.format(MAKEUP_PARTY_KEY, AppUtil.splicUserId(uid, toUid), ballEnum.getBallHDKey());
            String orElse = Optional.ofNullable(redisManager.getString(makeupKey)).orElse(StatusEnum.NOT_FINISHED.getStatus());
            Integer status = Optional.ofNullable(Objects.requireNonNull(StatusEnum.getByCode(orElse)).getStatusValue()).orElse(0);
            Long pairRetain = getPairRetain(uid, toUid);
            if (pairRetain >= ballEnum.getSatrtCount() && status == 0) {
                redisManager.set(makeupKey, StatusEnum.REWARDED_AVAILABLE.getStatus(), DateUtil.ONE_MONTH_SECOND);
                if (BallEnum.ballHD5.equals(ballEnum)){
                    // 记录两个人开启童话舞会
                    if (!redisManager.hasKey(String.format(MAKEUP_PARTY_KEY_FORMAT, AppUtil.splicUserId(uid, toUid)))) {
                        trackManager.allActivityTaskFinish(uid, "fairy_tale_dance", 1);
                        trackManager.allActivityTaskFinish(toUid, "fairy_tale_dance", 1);
                        redisManager.set(String.format(MAKEUP_PARTY_KEY_FORMAT, AppUtil.splicUserId(uid, toUid)), 1, DateUtil.ONE_MONTH_SECOND);
                    }
                }
            }
        }
    }

    /**
     * 初始化 搭档的 化妆舞会 进度
     */
    public void initTaskList(Long uid, Long toUid) {
        String uidTargetRank = String.format(USER_OTHER_RANK_KEY, uid);
        String toUidTargetRank = String.format(USER_OTHER_RANK_KEY, toUid);
        String splicUserId = AppUtil.splicUserId(uid, toUid);

        // 男生女生都要初始化
        redisManager.zSetAdd(uidTargetRank, String.valueOf(splicUserId), 0d, DateUtil.ONE_MONTH_SECOND, TimeUnit.SECONDS);
        redisManager.zSetAdd(toUidTargetRank, String.valueOf(splicUserId), 0d, DateUtil.ONE_MONTH_SECOND, TimeUnit.SECONDS);

        for (BallEnum ballEnum : BallEnum.values()) {
            String makeupKey = String.format(MAKEUP_PARTY_KEY, splicUserId, ballEnum.getBallHDKey());
            redisManager.set(makeupKey, StatusEnum.NOT_FINISHED.getStatus(), DateUtil.ONE_MONTH_SECOND);
        }
    }

    /**
     * 获取两个用户欢呼值
     */
    public Long getPairRetain(Long uid, Long toUid) {
        String key = String.format(USER_OTHER_RANK_KEY, uid);
        if (!redisManager.hasKey(key)) {
            return -1L;
        }
        Double score = redisManager.score(key, AppUtil.splicUserId(uid, toUid));

        try {
            return Optional.ofNullable(score)
                    .map(Double::longValue)
                    .orElse(-1L);
        } catch (Exception e) {
            log.warn("Failed to convert score to Long for key: {}, value: {}", key, score, e);
            return -1L;
        }}


    /**
     * 获取用户当前化妆舞会阶段
     */
    public BallEnum getBallEnum(Long uid, Long toUid) {
        Long pairRetain = getPairRetain(uid, toUid);
        if (pairRetain == -1 || pairRetain < 999) {
            return null;
        }
        BallEnum res;
        for (BallEnum ballEnum : BallEnum.values()) {
            res = ballEnum;
            if (pairRetain < ballEnum.getSatrtCount()) {
                return res;
            }
        }
        return null;
    }

    @AllArgsConstructor
    @Getter
    public enum StatusEnum {
        /**
         * 未开启
         */
        NOT_FINISHED("NOT_FINISHED", 0),
        /**
         * 可领取
         */
        REWARDED_AVAILABLE("REWARDED_AVAILABLE", 1),
        /**
         * 已领取
         */
        COMPLETED("COMPLETED", 2);
//        /**
//         * 未领取（已过期）
//         */
//        EXPIRED("EXPIRED", 3);

        private final String status;
        private final Integer statusValue;


        public static StatusEnum getByCode(String item) {
            for (StatusEnum statusEnum : StatusEnum.values()) {
                if (statusEnum.getStatus().equals(item)) {
                    return statusEnum;
                }
            }
            return null;
        }
    }
}

