package cn.yizhoucp.ump.biz.project.biz.manager.activity.goddess_pageant;

import cn.yizhoucp.ms.core.base.config.ThreadPoolConfig;
import cn.yizhoucp.ms.core.base.convert.SexType;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.goddess_pageant.common.GoddessPageantConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.goddess_pageant.common.GoddessPageantRedisManager;
import cn.yizhoucp.ump.biz.project.common.ActivityComponent;
import cn.yizhoucp.ump.biz.project.common.event.GiftGiveEvent;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.user.manager.UserFeignManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 女神评选业务类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 15:19 2025/3/18
 */
@Slf4j
@Service
public class GoddessPageantBizManager implements ActivityComponent {

    @Resource
    private RedisManager redisManager;

    @Resource
    private UserFeignManager userFeignManager;

    @Resource
    private GoddessPageantRankManager goddessPageantRankManager;

    @Resource
    private GoddessPageantRedisManager goddessPageantRedisManager;

    @Override
    public String getActivityCode() {
        return GoddessPageantConstant.ACTIVITY_CODE;
    }

    @Async(ThreadPoolConfig.ASYNC_EXECUTOR_NAME)
    @EventListener(GiftGiveEvent.class)
    @ActivityCheck(activityCode = GoddessPageantConstant.ACTIVITY_CODE, isThrowException = false)
    public void eventSendGiftHandle(GiftGiveEvent event) {
        log.info("sendGiftHandle event:{}", event);
        sendGiftHandle(event.getBaseParam(), event.getCoinGiftGivedModels());
    }

    public Boolean sendGiftHandle(BaseParam param, List<CoinGiftGivedModel> coinGiftGivedModelList) {
        for (CoinGiftGivedModel coinGiftGivedModel : coinGiftGivedModelList) {
            log.info("sendGiftHandle param:{}, coinGiftGivedModel:{}", param, coinGiftGivedModel);
            // 榜单处理
            rankHandler(param, coinGiftGivedModel);
        }
        return Boolean.TRUE;
    }

    private void rankHandler(BaseParam param, CoinGiftGivedModel coinGiftGivedModel) {
        String giftKey = coinGiftGivedModel.getGiftKey();
        ScenePrizeDO targetPrizeDO = goddessPageantRedisManager.getTargetPrizeScene(giftKey);
        if (targetPrizeDO == null) {
            return;
        }
        Double rankValue = GoddessPageantConstant.RANK_BASE_VALUE * coinGiftGivedModel.getCoin();
        // 女生榜单处理
        Long toUid = coinGiftGivedModel.getToUid();
        UserVO toUserVO =
                userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), toUid);
        if (toUserVO != null && SexType.WOMAN.getCode().equals(toUserVO.getSex().getCode())) {
            goddessPageantRankManager.incrRankValue(
                    toUid,
                    rankValue,
                    goddessPageantRedisManager.getDailyRankKey(GoddessPageantConstant.WOMAN_DAILY_RANK_CODE));
            goddessPageantRankManager.incrRankValue(
                    toUid,
                    rankValue,
                    goddessPageantRedisManager.getTotalRankKey(GoddessPageantConstant.WOMAN_TOTAL_RANK_CODE));
        }
        // 男生榜单处理
        Long uid = param.getUid();
        UserVO userVO = userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), uid);
        if (userVO != null && toUserVO != null && SexType.MAN.getCode().equals(userVO.getSex().getCode())
                && SexType.WOMAN.getCode().equals(toUserVO.getSex().getCode())) {
            goddessPageantRankManager.incrRankValue(
                    uid,
                    rankValue,
                    goddessPageantRedisManager.getDailyRankKey(GoddessPageantConstant.MAN_DAILY_RANK_CODE));
            goddessPageantRankManager.incrRankValue(
                    uid,
                    rankValue,
                    goddessPageantRedisManager.getTotalRankKey(GoddessPageantConstant.MAN_TOTAL_RANK_CODE));
        }
    }
}
