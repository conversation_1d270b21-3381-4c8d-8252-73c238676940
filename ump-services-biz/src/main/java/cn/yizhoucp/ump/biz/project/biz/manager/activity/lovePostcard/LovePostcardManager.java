package cn.yizhoucp.ump.biz.project.biz.manager.activity.lovePostcard;

import cn.yizhoucp.ms.core.base.Constant;
import cn.yizhoucp.ms.core.base.enums.*;
import cn.yizhoucp.ms.core.base.util.AppUtil;
import cn.yizhoucp.ms.core.base.util.JsonUtil;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.base.util.WeightRandomUtil;
import cn.yizhoucp.ms.core.vo.CommonListVO;
import cn.yizhoucp.ms.core.vo.CommonResultVO;
import cn.yizhoucp.ms.core.vo.imservices.BottomButtonVO;
import cn.yizhoucp.ms.core.vo.imservices.ImChatInfoVO;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.ms.core.vo.userservices.UserBasicVO;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.enums.ActivityCheckListEnum;
import cn.yizhoucp.ump.api.vo.activity.lovePostcard.*;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.AbstractManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.inner.WindowParam;
import cn.yizhoucp.ump.biz.project.biz.remoteService.FeignImService;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityTimeUtil;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.biz.util.LanlingActivityUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ActivityDO;
import cn.yizhoucp.ump.biz.project.dal.service.ActivityService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.util.Pair;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 国庆活动-爱的明信片
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class LovePostcardManager extends AbstractManager {

    @Resource
    ActivityService activityService;

    @Resource
    FeignImService feignImService;

    @Resource
    RedissonClient redissonClient;

    /** 用户活动期间邮票数记录 redis-key hash（uid） */
    private static final String USER_STAMP_COUNT = "ump:lovePostcard:stamp";
    /** 用户活动期间明信片数记录 redis-key hash（uid） */
    private static final String USER_POSTCARD_COUNT = "ump:lovePostcard:postcard";
    /** 用户寄出的明信片内容 */
    private static final String USER_POSTCARD_CONTENT = "ump:lovePostcard:postcardContent";
    /** 用户寄出的明信片 id */
    private static final String USER_POSTCARD_ID = "ump:lovePostcard:postcardId";
    /** 用户寄出的明信片 list */
    private static final String USER_POSTCARD_SEND_CONTENT = "ump:lovePostcard:sendPostcard_%s";
    /** 用户收到的明信片 list */
    private static final String USER_POSTCARD_RECEIVE_CONTENT = "ump:lovePostcard:receivePostcard_%s";
    /** 用户寄出的明信片 hash */
    private static final String USER_POSTCARD_SEND_CONTENT_MAP = "ump:lovePostcard:sendPostcardMap_%s";
    /** 用户活动期间各城市解锁情况 redis-key hash（uid） */
    private static final String USER_CITY_CACHE = "ump:lovePostcard:city";
    /** 用户活动期间获得的头像框天数 */
    public static final String USER_HEAD_FRAME_CACHE = "ump:lovePostcard:headFrame";
    /** 任务值记录 redis-key hash（uid、date） */
    private static final String USER_TASK_LOG = "ump:lovePostcard:taskLog_%s_%s";
    /** 任务完成获得邮票数记录 redis-key hash（uid、date） */
    private static final String USER_TASK_STAMP_COUNT = "ump:lovePostcard:taskStamp_%s_%s";
    /** 任务完成获得金邮票数记录 redis-key hash（uid、date） */
    private static final String USER_TASK_GOLD_POSTCARD_COUNT = "ump:lovePostcard:taskGoldStamp_%s_%s";
    /** 用户当前获取邮票概率 redis-key hash（uid、日期） */
    private static final String USER_GET_PRIZE_PROB = "ump:lovePostcard:prob_%s_%s";
    /** 用户基金数值 redis-key(hash) */
    private static final String USER_BONUS = "ump:lovePostcard:userBonus";
    /** 记录活动期间积分增长 redis-key hash */
    private static final String POINT_DURING_ACTIVITY = "ump:lovePostcard:point";
    /** 记录活动期间已处理积分增长 redis-key hash */
    private static final String POINT_DONE_DURING_ACTIVITY = "ump:lovePostcard:pointDone";
    /** 记录活动期间金币增长 redis-key hash */
    private static final String COIN_DURING_ACTIVITY = "ump:lovePostcard:coin";
    /** 记录活动期间已处理金币增长 redis-key hash */
    private static final String COIN_DONE_DURING_ACTIVITY = "ump:lovePostcard:coinDone";
    /** 金币处理 lock */
    private static final String COIN_HANDLE_LOCK = "ump:lovePostcard:coinHandleLock_%s";
    /** 积分处理 lock */
    private static final String POINT_HANDLE_LOCK = "ump:lovePostcard:pointHandleLock_%s";
    /** 任务处理 lock */
    private static final String COMPLETE_TASK_LOCK = "ump:lovePostcard:completeTaskLock_%s";
    /** 明信片锁 */
    private static final String REFRESH_POSTCARD_LOCK = "ump:lovePostcard:postcardLock_%s";
    /** 用户 chatId 缓存 */
    public static final String USER_CHAT_ID_CACHE = "ump:lovePostcard:chatId_%s";
    private static final String ACTIVITY_START_ACTION_CACHE = "ump_activity_love_postcard_switch";

    /** 弹幕数 */
    private static final Integer BARRAGE_LIMIT = 30;
    /** 奖励数 */
    private static final Integer PRIZE_NUM = 1;
    /** 视频任务限制 */
    private static final Integer VIDEO_DURATION_LIMIT = 15;
    /** 私聊任务礼物：爱的旅行 */
    private static final String ADLX_GIFT = "ADLX_GIFT";
    /** 家族任务礼物：捏捏脸 */
    private static final String NNL_GIFT = "NNL_GIFT";
    /** 聊天室任务礼物：爱情鱼 */
    private static final String AQY_GIFT = "AQY_GIFT";
    /** 邮票 */
    private static final String STAMP = "stamp";
    /** 金邮票 */
    private static final String GOLD_STAMP = "goldStamp";
    /** 活动路由 */
    private static final String ACTIVITY_URL = "love-postcard-2022";
    private static final String ACTIVITY_URL2 = "postcard-detail-2022";

    /** 获取邮票小助手消息 */
    private static final String GET_STAMP_MSG = "恭喜您在“爱情明信片”活动中获得一张邮票，快给您的好友寄张明信片吧！集齐7张地区明信片还可参与华为P50 pro抽奖哦～<a href=\"%s\">点击查看邮票数量</a>";
    private static final String GET_GOLD_STAMP_MSG = "恭喜您在“爱情明信片”活动中获得一张金邮票，快给您的好友寄张明信片吧！集齐7张地区明信片还可参与华为P50 pro抽奖哦～<a href=\"%s\">点击查看邮票数量</a>";

    /** 发送明信片系统消息 */
    private static final String SEND_POSTCARD_MSG = "%s 从 %s 给您寄了一张明信片，<a href=\"%s\">点击查看～</a>";

    private static final String POSTCARD_RECORD = "%s 从 %s 寄给 %s 一张明信片";

    /** 奖励类型 埋点 */
    private static final String GENERAL_AWARD = "general_stamps";
    private static final String GOLD_AWARD = "gold_stamps";
    private static final String NO_AWARD = "no_award";

    /**
     * 主页信息
     *
     * @return MidAutumn2024PreIndexVO
     */
    public IndexVO index(Long uid) {
        LocalDateTime activityEndTime = this.getActivityEndTime();
        if (Objects.isNull(activityEndTime)) {
            log.error("活动截止时间错误");
            return null;
        }
        long endTime = activityEndTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        // 获取城市明信片数据
        List<CityPostcardVO> cityList = this.getUserPostcard(uid);
        // 获取弹幕数据
        List<String> barrageList = this.getBarrageList();
        // 获取邮票数据
        JSONObject stampJson = this.getUserStamp(uid);
        // 按数量顺序排列，最少的数量就是抽奖次数
        List<Integer> numList = cityList.stream().sorted(Comparator.comparing(CityPostcardVO::getPostcardNum)).map(CityPostcardVO::getPostcardNum).collect(Collectors.toList());
        return IndexVO.builder()
                .cityList(cityList)
                .barrageList(barrageList)
                .stampNum(stampJson.getInteger(STAMP))
                .goldStampNum(stampJson.getInteger(GOLD_STAMP))
                .drawCount(numList.get(0))
                .activityEndTime(endTime)
                .build();
    }

    /**
     * 获取最近 30 条告白弹幕
     *
     * @return List<String>
     */
    private List<String> getBarrageList() {
        long size = redisManager.lGetListSize(USER_POSTCARD_CONTENT);
        List<String> resultList = new ArrayList<>();
        List<Object> list;
        if (size > BARRAGE_LIMIT) {
            list = redisManager.lGet(USER_POSTCARD_CONTENT, size - BARRAGE_LIMIT, size - 1);
        } else {
            list = redisManager.lGet(USER_POSTCARD_CONTENT, 0, -1);
        }
        if (!CollectionUtils.isEmpty(list)) {
            for (int i = list.size() - 1; i >= 0; i--) {
                resultList.add(String.valueOf(list.get(i)));
            }
        }
        return resultList;
    }

    /**
     * 获取寄明信片页面信息
     *
     * @param uid 用户id
     * @return CommonListVO
     */
    public SendPostcardIndexVO sendPostcardIndex(Long uid) {
        Object cache = redisManager.hget(USER_CITY_CACHE, String.valueOf(uid));
        String unlockStr = Objects.isNull(cache) ? "0000000" : String.valueOf(cache);
        char unlockFlag = '1';
        List<JSONObject> list = new ArrayList<>();
        for (int i = 0; i < unlockStr.length(); i++) {
            char curUnlockFlag = unlockStr.charAt(i);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("cityCode", CityEnum.getCityNameByOrdinal(i));
            jsonObject.put("unlock", unlockFlag == curUnlockFlag);
            list.add(jsonObject);
        }
        JSONObject stampJson = this.getUserStamp(uid);
        return SendPostcardIndexVO.builder()
                .list(list)
                .stampNum(stampJson.getInteger(STAMP))
                .goldStampNum(stampJson.getInteger(GOLD_STAMP))
                .build();
    }

    /**
     * 寄出明信片
     *
     * @param context 发送明信片内容
     * @return CommonResultVO
     */
    public CommonResultVO sendPostcard(SendPostcardVO context) {
        log.info("lovePostcard 发送明信片 {}", JSON.toJSONString(context));
        if (!this.getActivityStartAction()) {
            return CommonResultVO.fail("活动已下线～");
        }
        Long uid = context.getLoginUid();
        Long otherUid = context.getUid();
        Boolean stamp = context.getStamp();
        String cityCode = context.getCityCode();
        String content = context.getContent();
        JSONObject userStamp = this.getUserStamp(uid);
        // 邮票数校验
        CommonResultVO checkResult = this.stampCheck(userStamp, stamp, cityCode);
        if (!(Boolean) checkResult.getResult()) {
            return checkResult;
        }
        CityEnum cityEnum = CityEnum.getCityNum(cityCode);
        if (Objects.isNull(cityEnum)) {
            log.error("context {}", JSON.toJSONString(context));
            return CommonResultVO.fail("城市代码错误");
        }
        // 校验并解锁城市
        if (!Boolean.TRUE.equals(this.checkCityUnlock(uid, cityEnum))) {
            log.error("context {}", JSON.toJSONString(context));
            return CommonResultVO.fail("请先解锁前一个城市");
        }
        // 扣除用户邮票数量
        this.deductStamp(uid, userStamp, stamp, cityEnum);
        // 增加对方明信片数量
        this.increasePostcard(otherUid, cityCode);
        // 明信片id
        long id = redisManager.incrLong(USER_POSTCARD_ID, 1, DateUtil.ONE_MONTH_SECOND);
        // 发送系统消息
        Map<Long, UserVO> userMap = userRemoteService.acquireUsersInBulk(Arrays.asList(uid, otherUid), Boolean.FALSE);
        UserVO user = userMap.get(uid);
        if (Objects.nonNull(user)) {
            notifyComponent.chatNotify(uid, otherUid, String.format(SEND_POSTCARD_MSG, user.getName(), cityEnum.getDesc(), LanlingActivityUtil.getH5BaseUrl(env) + ACTIVITY_URL2 + "?from=postcard&uid=" + uid + "&id=" + id));
        }
        // 获取会话 id
        Long chatId = this.getUserChatId(context.getAppId(), uid, otherUid);
        // 记录双方明信片信息
        this.recordPostcard(id, uid, otherUid, cityEnum, content, Boolean.TRUE, chatId);
        this.recordPostcard(id, otherUid, uid, cityEnum, content, Boolean.FALSE, chatId);
        // 缓存内容
        if (!CollectionUtils.isEmpty(userMap) && userMap.size() == 2) {
            String fromName = this.getUserName(user.getName());
            String toName = this.getUserName(userMap.get(otherUid).getName());
            String record = String.format(POSTCARD_RECORD, fromName, cityEnum.getDesc(), toName);
            redisManager.lSet(USER_POSTCARD_CONTENT, record, DateUtil.ONE_MONTH_SECOND);
        }
        // 埋点
        this.postcardTrack(uid, otherUid, cityEnum.getTraceCode(), content, stamp);
        return CommonResultVO.success("投递成功，小暖已快马加鞭给您送达～");
    }

    /**
     * 获取用户昵称
     *
     * @param name 昵称
     * @return String
     */
    private String getUserName(String name) {
        if (StringUtils.isEmpty(name) || name.length() <= 1) {
            return name;
        }
        StringBuilder sb = new StringBuilder(name);
        if (name.length() == 2) {
            return sb.replace(1, 2, "*").toString();
        }
        return sb.replace(1, name.length() - 1, "*").toString();
    }

    /**
     * 获取明信片记录
     *
     * @param uid       用户id
     * @param pageIndex 页码
     * @param pageSize  每页条数
     * @param send      true-寄出明信片；false-收到明信片
     * @return CommonListVO
     */
    public CommonListVO postcardRecord(Long uid, int pageIndex, int pageSize, Boolean send) {
        String key = Boolean.FALSE.equals(send) ? String.format(USER_POSTCARD_RECEIVE_CONTENT, uid) : String.format(USER_POSTCARD_SEND_CONTENT, uid);
        List<PostcardRecordDetailVO> list = this.getUserPostcardRecord(key, pageIndex, pageSize);
        if (!CollectionUtils.isEmpty(list)) {
            list = list.stream().sorted(Comparator.comparing(detail -> detail.getPostcard().getId(), Comparator.reverseOrder())).collect(Collectors.toList());
        }
        return CommonListVO.success(list);
    }

    /**
     * 查看明信片详情
     *
     * @param loginUid 登陆用户id
     * @param uid      对方id
     * @param id       明信片id
     * @return PostcardIndexVO
     */
    public PostcardIndexVO postcardInfo(Long loginUid, Long uid, Long id) {
        // 是否是寄出用户
        log.debug("loginUid {} uid {} id {}", loginUid, uid, id);
        boolean send = loginUid.equals(uid);
        String key = String.format(USER_POSTCARD_SEND_CONTENT_MAP, uid);
        Object cache = redisManager.hget(key, String.valueOf(id));
        UserBasicVO sendUser = new UserBasicVO();
        UserBasicVO receiveUser = new UserBasicVO();
        PostcardVO postcard = Objects.isNull(cache) ? null : JSONObject.parseObject(String.valueOf(cache), PostcardVO.class);
        if (Objects.nonNull(postcard)) {
            Map<Long, UserVO> userMap = userFeignService.acquireUsersInBulkPost2(postcard.getSendUid(), postcard.getReceiveUid()).successData();
            if (!CollectionUtils.isEmpty(userMap)) {
                UserVO sendUserVo = userMap.get(postcard.getSendUid());
                UserVO receiveUserVo = userMap.get(postcard.getReceiveUid());
                if (Objects.nonNull(sendUserVo)) {
                    sendUser.setId(sendUserVo.getId());
                    sendUser.setName(sendUserVo.getName());
                    sendUser.setAvatar(sendUserVo.getAvatar());
                }
                if (Objects.nonNull(receiveUserVo)) {
                    receiveUser.setId(receiveUserVo.getId());
                    receiveUser.setName(receiveUserVo.getName());
                    receiveUser.setAvatar(receiveUserVo.getAvatar());
                }
            }
        }
        return PostcardIndexVO.builder()
                .send(send)
                .sendUser(sendUser)
                .receiveUser(receiveUser)
                .postcard(Objects.isNull(cache) ? null : JSONObject.parseObject(String.valueOf(cache), PostcardVO.class))
                .build();
    }

    /**
     * 发送定时小助手消息
     *
     * @param uid 用户id
     */
    public void sendTimingMsg(Long uid) {
        long time = System.currentTimeMillis();
        Object timeCache = redisManager.get("test_love_postcard_time");
        if (!EnvType.PROD.getEnv().equals(env) && Objects.nonNull(timeCache)) {
            time = Long.parseLong(String.valueOf(timeCache));
        }
        NpcMsgEnum npcMsg = NpcMsgEnum.getNpcMsg(time);
        if (Objects.isNull(npcMsg) || Objects.nonNull(redisManager.hget(npcMsg.getCacheKey(), String.valueOf(uid)))) {
            return;
        }
        notifyComponent.npcNotify(uid, String.format(npcMsg.getMsg(), this.getActivityUrl(npcMsg.getFrom())));
        redisManager.hset(npcMsg.getCacheKey(), String.valueOf(uid), uid, DateUtil.ONE_MONTH_SECOND);
    }

    /**
     * 获取用户明信片记录
     *
     * @param key       对应key
     * @param pageIndex 页码
     * @param pageSize  条数
     * @return List<PostcardRecordDetailVO>
     */
    private List<PostcardRecordDetailVO> getUserPostcardRecord(String key, int pageIndex, int pageSize) {
        List<PostcardRecordDetailVO> result = new ArrayList<>();
        int start = (pageIndex - 1) * pageSize;
        int end = pageIndex * pageSize - 1;
        long num = redisManager.lGetListSize(key);
        log.debug("key {} pageIndex {} pageSize {} start {} num {}", key, pageIndex, pageSize, start, num);
        List<Object> cacheList = null;
        if (num > 0 && num <= end) {
            cacheList = redisManager.lGet(key, start, Math.min(num - 1, end));
        } else if (num > end) {
            cacheList = redisManager.lGet(key, start, end);
        }
        if (!CollectionUtils.isEmpty(cacheList)) {
            List<PostcardVO> postcardList = cacheList.stream().map(postcard -> JSONObject.parseObject(String.valueOf(postcard), PostcardVO.class))
                    .collect(Collectors.toList());
            result = this.fillWithUser(postcardList);
            Collections.reverse(result);
        }
        return result;
    }


    /**
     * 填充用户信息
     *
     * @param postcardList 明信片信息
     * @return List<PostcardRecordDetailVO
     */
    private List<PostcardRecordDetailVO> fillWithUser(List<PostcardVO> postcardList) {
        List<PostcardRecordDetailVO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(postcardList)) {
            return result;
        }
        List<Long> sendUidList = postcardList.stream().map(PostcardVO::getSendUid).collect(Collectors.toList());
        List<Long> receiveUidList = postcardList.stream().map(PostcardVO::getReceiveUid).collect(Collectors.toList());
        sendUidList.addAll(receiveUidList);
        Map<Long, UserVO> userMap = userRemoteService.acquireUsersInBulk(sendUidList, Boolean.FALSE);
        for (PostcardVO postcard : postcardList) {
            PostcardRecordDetailVO detail = new PostcardRecordDetailVO();
            UserVO sendUser = userMap.get(postcard.getSendUid());
            UserVO receiveUser = userMap.get(postcard.getReceiveUid());
            UserBasicVO sendUserBasic = new UserBasicVO();
            UserBasicVO receiveUserBasic = new UserBasicVO();
            if (Objects.nonNull(sendUser)) {
                sendUserBasic.setId(sendUser.getId());
                sendUserBasic.setAvatar(sendUser.getAvatar());
                sendUserBasic.setName(sendUser.getName());
            }
            if (Objects.nonNull(receiveUser)) {
                receiveUserBasic.setId(receiveUser.getId());
                receiveUserBasic.setAvatar(receiveUser.getAvatar());
                receiveUserBasic.setName(receiveUser.getName());
            }
            detail.setSendUser(sendUserBasic);
            detail.setReceiveUser(receiveUserBasic);
            detail.setPostcard(postcard);
            result.add(detail);
        }
        return result;
    }

    /**
     * 获取用户的会话 id
     *
     * @param appId    应用id
     * @param uid      用户id
     * @param otherUid 对方id
     * @return Long
     */
    private Long getUserChatId(Long appId, Long uid, Long otherUid) {
        String key = String.format(USER_CHAT_ID_CACHE, AppUtil.splicUserId(uid, otherUid));
        Object cache = redisManager.get(key);
        if (Objects.nonNull(cache)) {
            return Long.parseLong(String.valueOf(cache));
        } else {
            ImChatInfoVO chatInfo = feignImService.getChat(appId, uid, otherUid).successData();
            if (Objects.nonNull(chatInfo)) {
                redisManager.set(key, chatInfo.getId(), DateUtil.ONE_MONTH_SECOND);
                return chatInfo.getId();
            }
        }
        return null;
    }

    /**
     * 创建有效会话任务
     *
     * @param uid      用户id
     * @param otherUid 对方id
     * @return Boolean
     */
    public Boolean createDialogTask(Long uid, Long otherUid) {
        Map<Long, String> userUnionIdMap = userRemoteService.getUserUnionIdMap(JsonUtil.getJsonStrFromLongArr(uid, otherUid));
        if (CollectionUtils.isEmpty(userUnionIdMap)) {
            return Boolean.FALSE;
        }
        log.debug("userUnionIdMap {}", JSON.toJSONString(userUnionIdMap));
        String unionId = userUnionIdMap.get(uid);
        if (ServicesAppIdEnum.lanling.getUnionId().equals(unionId)) {
            this.completeTask(uid, TaskEnum.TASK_CREATE_DIALOG);
        }
        String otherUnionId = userUnionIdMap.get(otherUid);
        if (ServicesAppIdEnum.lanling.getUnionId().equals(otherUnionId)) {
            this.completeTask(otherUid, TaskEnum.TASK_CREATE_DIALOG);
        }
        return Boolean.TRUE;
    }

    /**
     * 视频任务
     *
     * @param unionId 应用唯一标识
     * @param param   消息内容
     */
    public void videoTask(String unionId, JSONObject param) {
        if (!this.getActivityStartAction() || !ServicesAppIdEnum.lanling.getUnionId().equals(unionId)) {
            return;
        }
        Long durationSeconds = param.getLong("durationSeconds");
        Long fromUid = param.getLong("fromUserId");
        Long toUid = param.getLong("toUserId");
        if (Objects.nonNull(durationSeconds) && durationSeconds >= VIDEO_DURATION_LIMIT) {
            completeTask(fromUid, TaskEnum.TASK_VIDEO);
            completeTask(toUid, TaskEnum.TASK_VIDEO);
        }
    }

    /**
     * 送礼任务
     *
     * @param modelList 送礼
     */
    public void giveGiftTask(List<CoinGiftGivedModel> modelList) {
        if (!this.getActivityStartAction() || CollectionUtils.isEmpty(modelList)) {
            return;
        }
        Long fromUid = modelList.get(0).getFromUid();
        String unionId = userRemoteService.getUserUnionId(fromUid);
        if (!ServicesAppIdEnum.lanling.getUnionId().equals(unionId)) {
            return;
        }
        for (CoinGiftGivedModel model : modelList) {
            GiftFrom giftFrom = GiftFrom.findByCode(model.getFrom());
            boolean familyGiftTask = (GiftFrom.family_gift.equals(giftFrom) || GiftFrom.gift_in_return_family.equals(giftFrom)) && model.getGiftKey().equals(NNL_GIFT);
            if (familyGiftTask) {
                this.completeTask(fromUid, TaskEnum.TASK_FAMILY_GIVE_GIFT);
            }
            boolean roomGiftTask = (GiftFrom.room.equals(giftFrom) || GiftFrom.gift_in_return_voice_room.equals(giftFrom)) && model.getGiftKey().equals(AQY_GIFT);
            if (roomGiftTask) {
                this.completeTask(fromUid, TaskEnum.TASK_ROOM_GIVE_GIFT);
            }
            if (model.getGiftKey().equals(ADLX_GIFT)) {
                this.completeTask(fromUid, TaskEnum.TASK_GIVE_GIFT);
            }
        }
    }

    /**
     * "十一浪漫旅行"领取奖励任务
     *
     * @param uid 用户id
     * @return Boolean
     */
    public Boolean receiveRewardTask(Long uid) {
        return this.completeTask(uid, TaskEnum.TASK_ACTIVITY_PRIZE);
    }

    /**
     * 完成任务
     *
     * @param uid  用户id
     * @param task 任务
     * @return Boolean
     */
    public Boolean completeTask(Long uid, TaskEnum task) {
        if (!this.getActivityStartAction()) {
            return false;
        }
        log.info("lovePostcard 完成任务 uid:{}, task:{}", uid, task.getCode());
        redisManager.hincr(String.format(USER_TASK_LOG, uid, ActivityTimeUtil.getToday(getActivityEnum().getCode())), task.getCode(), 1, RedisManager.ONE_DAY_SECONDS * 3);
        RLock lock = redissonClient.getLock(String.format(COMPLETE_TASK_LOCK, uid));
        try {
            lock.lock();
            // 过期时间
            LocalDateTime endTime = this.getActivityEndTime();
            if (Objects.isNull(endTime) || endTime.isBefore(LocalDateTime.now())) {
                log.error("lovePostcard 活动已经结束");
                return Boolean.FALSE;
            }
            // 本次完成是否中金邮票
            if (this.jackpot(uid, task)) {
                log.info("lovePostcard 用户本次任务中金邮票了 uid {} task {}", uid, task);
                this.taskTrack(uid, task.getType(), GOLD_AWARD);
                return Boolean.FALSE;
            }
            // 判断当前任务获得邮票数是否达到今日上限
            String rabbitCountKey = String.format(USER_TASK_STAMP_COUNT, uid, ActivityTimeUtil.getToday(getActivityEnum().getCode()));
            Integer curTaskNum = Optional.ofNullable((Integer) redisManager.hget(rabbitCountKey, task.getCode())).orElse(0);
            if (curTaskNum >= task.getMaxCount()) {
                log.info("{} 当前任务 {} 获得邮票已达上限", uid, task.getDesc());
                this.taskTrack(uid, task.getType(), NO_AWARD);
                return Boolean.FALSE;
            }
            // 获取本次中奖概率
            Integer prob = this.getUserTaskProb(uid, task);
            if (prob <= 0) {
                log.info("{} 当前任务 {} 获得邮票概率为 0 ", uid, task.getDesc());
                this.taskTrack(uid, task.getType(), NO_AWARD);
                return Boolean.FALSE;
            }
            // 校验本次是否得邮票
            if (!this.hitPrize(curTaskNum, prob)) {
                log.info("计算概率本次未能获得邮票 {} {}", uid, task.getDesc());
                this.taskTrack(uid, task.getType(), NO_AWARD);
                return Boolean.FALSE;
            }
            // 下发邮票
            this.sendPrize(uid, task.getPrizeKey());
            // 更新当日获得邮票数量
            redisManager.hincr(rabbitCountKey, task.getCode(), PRIZE_NUM, RedisManager.ONE_DAY_SECONDS * 3);
            // 刷新任务概率
            if (curTaskNum > 0) {
                this.refreshUserTaskProb(uid, task, prob);
            }
            // 埋点
            this.taskTrack(uid, task.getType(), STAMP.equals(task.getPrizeKey()) ? GENERAL_AWARD : GOLD_AWARD);
        } finally {
            lock.unlock();
        }
        return Boolean.TRUE;
    }

    /**
     * 用户获得积分处理
     *
     * @param uid   用户id
     * @param value 增加积分数
     */
    public void pointHandle(Long uid, Long value) {
        if (!this.getActivityStartAction()) {
            return;
        }
        log.info("lovePostcard 积分处理 uid:{}, value:{}", uid, value);
        RLock lock = redissonClient.getLock(String.format(POINT_HANDLE_LOCK, uid));
        try {
            lock.lock();
            // 获取当前积分: 初始化
            this.getBonus(uid);
            // 记录活动期间总积分
            Double pointTotal = redisManager.hincr(POINT_DURING_ACTIVITY, uid.toString(), value, RedisManager.ONE_DAY_SECONDS * 30);
            // 获取已处理积分
            Object pointDoneObj = redisManager.hget(POINT_DONE_DURING_ACTIVITY, uid.toString());
            Double pointDone = Objects.isNull(pointDoneObj) ? 0d : Double.parseDouble(String.valueOf(pointDoneObj));
            double pointDiff = pointTotal - pointDone;
            log.info("total:{}, done:{}, diff:{}", pointTotal, pointDone, pointDiff);
            // 处理积分
            if (pointDiff >= 220) {
                // 记录基金值
                long bonus = (long) pointDiff / 220;
                redisManager.hincr(USER_BONUS, uid.toString(), bonus, RedisManager.ONE_DAY_SECONDS * 30);
                // 更新已处理积分
                redisManager.hincr(POINT_DONE_DURING_ACTIVITY, uid.toString(), bonus * 220, RedisManager.ONE_DAY_SECONDS * 30);
            }
        } catch (Exception ex) {
            log.error("pointHandle ", ex);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 消耗金币处理
     *
     * @param uid   用户id
     * @param value 消耗金币数
     */
    public void coinHandle(Long uid, Long value) {
        if (!this.getActivityStartAction()) {
            return;
        }
        log.info("lovePostcard 金币处理 uid:{}, value:{}", uid, value);
        RLock lock = redissonClient.getLock(String.format(COIN_HANDLE_LOCK, uid));
        try {
            lock.lock();
            // 获取当前金币: 初始化
            this.getBonus(uid);
            Double coinTotal = redisManager.hincr(COIN_DURING_ACTIVITY, uid.toString(), value, RedisManager.ONE_DAY_SECONDS * 30);
            // 获取已处理金币
            Object coinDoneObj = redisManager.hget(COIN_DONE_DURING_ACTIVITY, uid.toString());
            Double coinDone = Objects.isNull(coinDoneObj) ? 0d : Double.parseDouble(String.valueOf(coinDoneObj));
            double coinDiff = coinTotal - coinDone;
            log.info("total:{}, done:{}, diff:{}", coinTotal, coinDone, coinDiff);
            // 处理积分
            if (coinDiff >= 10) {
                // 记录基金值
                long bonus = (long) coinDiff / 10;
                redisManager.hincr(USER_BONUS, uid.toString(), bonus, RedisManager.ONE_DAY_SECONDS * 30);
                // 更新已处理金币
                redisManager.hincr(COIN_DONE_DURING_ACTIVITY, uid.toString(), bonus * 10, RedisManager.ONE_DAY_SECONDS * 30);
            }
        } finally {
            lock.unlock();
        }
    }

    /**
     * 获取用户基金数
     *
     * @param uid 用户id
     * @return Long
     */
    public Long getBonus(Long uid) {
        if (!this.getActivityStartAction()) {
            return 0L;
        }
        Object bonusCache = redisManager.hget(USER_BONUS, uid.toString());
        if (Objects.isNull(bonusCache)) {
            redisManager.hset(USER_BONUS, uid.toString(), 30, DateUtil.ONE_MONTH_SECOND);
            return 30L;
        }
        return Long.parseLong(String.valueOf(bonusCache));
    }

    /**
     * 使用基金
     *
     * @param uid   用户id
     * @param value 扣减值
     */
    public void decrBonus(Long uid, Long value) {
        if (!this.getActivityStartAction()) {
            return;
        }
        redisManager.hdecr(USER_BONUS, uid.toString(), value.doubleValue());
    }

    /**
     * 本次任务是否中了大奖-金邮票
     *
     * @param uid  用户id
     * @param task 任务
     * @return boolean
     */
    private boolean jackpot(Long uid, TaskEnum task) {
        String goldKey = String.format(USER_TASK_GOLD_POSTCARD_COUNT, uid, ActivityTimeUtil.getToday(getActivityEnum().getCode()));
        Object goldCache = redisManager.hget(goldKey, task.getCode());
        int goldNum = Objects.isNull(goldCache) ? 0 : Integer.parseInt(String.valueOf(goldCache));
        if (goldNum >= task.getGoldMaxCount()) {
            return Boolean.FALSE;
        }
        if (this.hitPrizeAction(task.getGoldProbability())) {
            log.info("jackpot uid {} task {}", uid, task);
            // 中大奖，刷新任务获得金邮票数和用户邮票信息
            redisManager.hincr(goldKey, task.getCode(), 1, DateUtil.ONE_MONTH_SECOND);
            this.sendPrize(uid, GOLD_STAMP);
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 获取当前任务本次中奖励概率
     *
     * @param uid  用户id
     * @param task 任务
     * @return Integer
     */
    private Integer getUserTaskProb(Long uid, TaskEnum task) {
        String prizeKey = String.format(USER_GET_PRIZE_PROB, uid, ActivityTimeUtil.getToday(getActivityEnum().getCode()));
        Integer prob = (Integer) redisManager.hget(prizeKey, task.getCode());
        if (Objects.isNull(prob)) {
            redisManager.hset(prizeKey, task.getCode(), task.getSecondProbability(), RedisManager.ONE_DAY_SECONDS * 3);
            prob = 100;
        }
        return prob;
    }

    /**
     * 刷新概率
     *
     * @param uid  用户id
     * @param task 任务
     * @param prob 当前概率
     */
    private void refreshUserTaskProb(Long uid, TaskEnum task, Integer prob) {
        int nextProd = prob > task.getDecrProb() ? (prob - task.getDecrProb()) : 0;
        redisManager.hset(String.format(USER_GET_PRIZE_PROB, uid, ActivityTimeUtil.getToday(getActivityEnum().getCode())), task.getCode(), nextProd, RedisManager.ONE_DAY_SECONDS * 3);
    }

    /**
     * 是否命中奖励
     *
     * @param curTaskNum 完成任务数
     * @param prob       prob
     * @return Boolean
     */
    private Boolean hitPrize(Integer curTaskNum, Integer prob) {
        if (curTaskNum <= 0) {
            return Boolean.TRUE;
        }
        return this.hitPrizeAction(prob);
    }

    /**
     * 是否中奖
     *
     * @param prob 概率
     * @return Boolean
     */
    private Boolean hitPrizeAction(int prob) {
        if (prob >= 100) {
            return Boolean.TRUE;
        } else if (prob <= 0) {
            return Boolean.FALSE;
        }
        List<Pair> pairs = Lists.newArrayList(Pair.create(Boolean.TRUE, prob), Pair.create(Boolean.FALSE, 100 - prob));
        return (Boolean) new WeightRandomUtil(pairs).random();
    }

    /**
     * 获取用户邮票数据
     *
     * @param uid 用户id
     * @return JSONObject
     */
    private JSONObject getUserStamp(Long uid) {
        Object stampCache = redisManager.hget(USER_STAMP_COUNT, String.valueOf(uid));
        JSONObject result;
        if (Objects.isNull(stampCache)) {
            result = new JSONObject();
            result.put(STAMP, 0);
            result.put(GOLD_STAMP, 0);
        } else {
            result = JSONObject.parseObject(String.valueOf(stampCache));
        }
        return result;
    }

    /**
     * 获取用户明信片数据
     *
     * @param uid 用户id
     * @return List<CityPostcardVO>
     */
    private List<CityPostcardVO> getUserPostcard(Long uid) {
        Object postcardCache = redisManager.hget(USER_POSTCARD_COUNT, String.valueOf(uid));
        if (Objects.nonNull(postcardCache)) {
            return JSONArray.parseArray(String.valueOf(postcardCache), CityPostcardVO.class);
        }
        List<CityPostcardVO> result = new ArrayList<>();
        for (CityEnum city : CityEnum.values()) {
            result.add(CityPostcardVO.builder().cityCode(city.getCode()).postcardNum(0).build());
        }
        redisManager.hset(USER_POSTCARD_COUNT, String.valueOf(uid), JSON.toJSONString(result), DateUtil.ONE_MONTH_SECOND);
        return result;
    }

    /**
     * 下发奖励
     *
     * @param uid   用户 id
     * @param prize 奖励
     */
    private void sendPrize(Long uid, String prize) {
        JSONObject stampJson = this.getUserStamp(uid);
        stampJson.put(prize, stampJson.getInteger(prize) + PRIZE_NUM);
        log.info("uid {} prize {} stampJson {}", uid, prize, stampJson.toJSONString());
        redisManager.hset(USER_STAMP_COUNT, String.valueOf(uid), stampJson.toJSONString(), DateUtil.ONE_MONTH_SECOND);
        // 发送弹窗通知
        notifyComponent.commonClientPopPush(this.buildPopParam(uid, prize));
        // 发送小助手消息
        notifyComponent.npcNotify(uid, STAMP.equals(prize) ? String.format(GET_STAMP_MSG, this.getActivityUrl("YP")) : String.format(GET_GOLD_STAMP_MSG, this.getActivityUrl("JYP")));
    }

    /**
     * 校验用户邮票是否充足
     *
     * @param myStampJson 用户邮票数据
     * @param stamp       是否使用普通邮票
     * @param cityCode    城市代码
     * @return int 0-邮票足够；1-普通邮票不足；2-金邮票不足
     */
    private CommonResultVO stampCheck(JSONObject myStampJson, boolean stamp, String cityCode) {
        int stampNum = myStampJson.getIntValue(STAMP);
        int goldStampNum = myStampJson.getIntValue(GOLD_STAMP);
        Integer costStamp = CityEnum.getCostStampNum(cityCode);
        if (Objects.isNull(costStamp)) {
            return CommonResultVO.fail("城市代码错误");
        }
        if (stamp && stampNum < costStamp) {
            return goldStampNum > 0 ? CommonResultVO.fail("您的邮票不足，可手动选择金邮票邮寄哦～") : CommonResultVO.fail("您的邮票不足，可通过完成集邮任务来获得哦～");
        } else if (!stamp && goldStampNum <= 0) {
            return stampNum >= costStamp ? CommonResultVO.fail("您的金邮票不足，可手动选择邮票邮寄哦～") : CommonResultVO.fail("您的邮票不足，可通过完成集邮任务来获得哦～");
        }
        return CommonResultVO.success();
    }

    /**
     * 扣除用户邮票
     *
     * @param uid       用户id
     * @param stampJson 用户邮票数据
     * @param stamp     是否使用普通邮票
     * @param city      城市
     */
    private void deductStamp(Long uid, JSONObject stampJson, boolean stamp, CityEnum city) {
        String stampKey = stamp ? STAMP : GOLD_STAMP;
        // 当前拥有邮票数和本次扣除邮票数
        int existNum = stampJson.getIntValue(stampKey);
        int realCostStamp = stamp ? city.getCostStampNum() : 1;
        stampJson.put(stampKey, existNum - realCostStamp);
        redisManager.hset(USER_STAMP_COUNT, String.valueOf(uid), stampJson.toJSONString(), DateUtil.ONE_MONTH_SECOND);
    }

    /**
     * 增加用户明信片
     *
     * @param uid      用户id
     * @param cityCode 城市代码
     */
    private void increasePostcard(Long uid, String cityCode) {
        log.info("increasePostcard uid {} cityCode {}", uid, cityCode);
        RLock lock = redissonClient.getLock(String.format(REFRESH_POSTCARD_LOCK, uid));
        try {
            lock.lock(3, TimeUnit.SECONDS);
            List<CityPostcardVO> userPostcard = this.getUserPostcard(uid);
            userPostcard.forEach(postcard -> {
                if (cityCode.equals(postcard.getCityCode())) {
                    postcard.setPostcardNum(postcard.getPostcardNum() + 1);
                }
            });
            redisManager.hset(USER_POSTCARD_COUNT, String.valueOf(uid), JSON.toJSONString(userPostcard), DateUtil.ONE_MONTH_SECOND);
        } catch (Exception e) {
            log.error("increasePostcard error uid {} cityCode {}", uid, cityCode, e);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 扣除用户明信片
     *
     * @param uid 用户id
     */
    public void deductPostcard(Long uid) {
        log.info("deductPostcard uid {} ", uid);
        RLock lock = redissonClient.getLock(String.format(REFRESH_POSTCARD_LOCK, uid));
        try {
            lock.lock(3, TimeUnit.SECONDS);
            List<CityPostcardVO> userPostcard = this.getUserPostcard(uid);
            userPostcard.forEach(postcard -> postcard.setPostcardNum(postcard.getPostcardNum() - 1));
            redisManager.hset(USER_POSTCARD_COUNT, String.valueOf(uid), JSON.toJSONString(userPostcard), DateUtil.ONE_MONTH_SECOND);
        } catch (Exception e) {
            log.error("deductPostcard error uid {}", uid, e);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 获取用户抽奖次数
     *
     * @param uid 用户id
     * @return Integer
     */
    public Integer getDrawCount(Long uid) {
        // 获取城市明信片数据
        List<CityPostcardVO> cityList = this.getUserPostcard(uid);
        // 按数量顺序排列，最少的数量就是抽奖次数
        List<Integer> numList = cityList.stream().sorted(Comparator.comparing(CityPostcardVO::getPostcardNum)).map(CityPostcardVO::getPostcardNum).collect(Collectors.toList());
        return numList.get(0);
    }

    /**
     * 校验并解锁城市
     *
     * @param uid      用户id
     * @param cityEnum 城市枚举
     */
    private Boolean checkCityUnlock(Long uid, CityEnum cityEnum) {
        Object cache = redisManager.hget(USER_CITY_CACHE, String.valueOf(uid));
        String unlockStr = Objects.isNull(cache) ? "0000000" : String.valueOf(cache);
        StringBuilder sbStr = new StringBuilder(unlockStr);
        // 不是发杭州明信片，需要校验前一个地区是否解锁
        if (cityEnum.getUnlockSort() != 0 && '1' != sbStr.charAt(cityEnum.getUnlockSort() - 1)) {
            return Boolean.FALSE;
        }
        sbStr.replace(cityEnum.getUnlockSort(), cityEnum.getUnlockSort() + 1, "1");
        redisManager.hset(USER_CITY_CACHE, String.valueOf(uid), sbStr.toString(), DateUtil.ONE_MONTH_SECOND);
        return Boolean.TRUE;
    }

    /**
     * 记录 寄出/收到 明信片信息
     *
     * @param id       明信片id
     * @param uid      用户id
     * @param otherUid 对方id
     * @param cityEnum 城市
     * @param content  明信片内容
     * @param send     是否是寄出明信片
     * @param chatId   会话id
     */
    private void recordPostcard(Long id, Long uid, Long otherUid, CityEnum cityEnum, String content, boolean send, Long chatId) {
        PostcardVO postcard = PostcardVO.builder()
                .id(id)
                .name(cityEnum.getPostcardName())
                .cityName(cityEnum.getDesc())
                .img(cityEnum.getImg())
                .content(content)
                .sendUid(send ? uid : otherUid)
                .receiveUid(send ? otherUid : uid)
                .chatId(chatId).build();
        redisManager.lSet(String.format(send ? USER_POSTCARD_SEND_CONTENT : USER_POSTCARD_RECEIVE_CONTENT, uid), JSON.toJSONString(postcard), DateUtil.ONE_MONTH_SECOND);
        if (send) {
            redisManager.hset(String.format(USER_POSTCARD_SEND_CONTENT_MAP, uid), String.valueOf(id), JSON.toJSONString(postcard), DateUtil.ONE_MONTH_SECOND);
        }
    }

    /**
     * 获取活动结束时间
     *
     * @return LocalDateTime
     */
    private LocalDateTime getActivityEndTime() {
        ActivityDO activityDO = activityService.getByCode(MDCUtil.getCurAppIdByMdc(), MDCUtil.getCurUnionIdByMdc(), getActivityEnum().getCode());
        if (Objects.isNull(activityDO)) {
            return null;
        }
        return activityDO.getEndTime();
    }

    /**
     * 构建获得弹窗参数
     *
     * @param uid   目标用户
     * @param prize 奖励
     * @return WindowParam
     */
    private WindowParam buildPopParam(Long uid, String prize) {
        boolean stamp = STAMP.equals(prize);
        String background = stamp ? TaskEnum.TASK_CREATE_DIALOG.getPrizeIcon() : TaskEnum.TASK_GIVE_GIFT.getPrizeIcon();
        String leftBtn = stamp ? "https://res-cdn.nuan.chat/res/activity/postcard2022/stamp_left_btn.png" : "https://res-cdn.nuan.chat/res/activity/postcard2022/gold_stamp_left_btn.png";
        String rightBtn = stamp ? "https://res-cdn.nuan.chat/res/activity/postcard2022/stamp_right_btn.png" : "https://res-cdn.nuan.chat/res/activity/postcard2022/gold_stamp_right_btn.png";
        return WindowParam.builder()
                .toUid(uid)
                .type("activity")
                .from("love-postcard")
                .backgroundImage(background)
                .actionImage("https://res-cdn.nuan.chat/res/activity/postcard2022/old_btn.png")
                .renderType(ServerPushRenderType.fixed.getCode())
                .priority(80)
                .needCloseIcon(Boolean.TRUE)
                .bottomButton(
                        BottomButtonVO.builder()
                                .leftButtonImg(leftBtn)
                                .leftButtonActionUrl(this.getActivityUrl("pop"))
                                .rightButtonImg(rightBtn)
                                .build()
                )
                .build();
    }

    /**
     * 获取活动链接
     *
     * @return String
     */
    private String getActivityUrl(String from) {
        return LanlingActivityUtil.getH5BaseUrl(env) + ACTIVITY_URL + "?from=" + from;
    }

    /**
     * 活动开关设置
     *
     * @return Boolean
     */
    public Boolean getActivityStartAction() {
        Object back = redisManager.get(ACTIVITY_START_ACTION_CACHE);
        if (Objects.isNull(back)) {
            LocalDateTime now = LocalDateTime.now();
            ActivityDO activity = activityService.getByCode(MDCUtil.getCurAppIdByMdc(), MDCUtil.getCurUnionIdByMdc(), ActivityCheckListEnum.LOVE_POSTCARD.getCode());
            if (Objects.isNull(activity)) {
                return false;
            }
            if (now.isBefore(activity.getStartTime())) {
                // 还未开始
                long duration = activity.getStartTime().atZone(ZoneId.systemDefault()).toEpochSecond() - now.atZone(ZoneId.systemDefault()).toEpochSecond();
                redisManager.set(ACTIVITY_START_ACTION_CACHE, Constant.CLOSE, duration);
            } else if (now.isAfter(activity.getEndTime())) {
                // 已经结束
                redisManager.set(ACTIVITY_START_ACTION_CACHE, Constant.CLOSE, DateUtil.ONE_MONTH_SECOND);
            } else {
                // 活动期间
                long duration = activity.getEndTime().atZone(ZoneId.systemDefault()).toEpochSecond() - now.atZone(ZoneId.systemDefault()).toEpochSecond();
                redisManager.set(ACTIVITY_START_ACTION_CACHE, Constant.OPEN, duration);
                return true;
            }
            return false;
        }
        return Constant.OPEN.equals(back.toString());
    }

    @Override
    protected ActivityCheckListEnum getActivityEnum() {
        return ActivityCheckListEnum.LOVE_POSTCARD;
    }

    /**
     * 完成任务埋点
     *
     * @param uid      用户id
     * @param taskType 任务名
     * @param award    奖励
     */
    public void taskTrack(Long uid, String taskType, String award) {
        Map<String, Object> params = new HashMap<>();
        params.put("task_type", taskType);
        params.put("award", award);
        yzKafkaProducerManager.dataRangerTrack(MDCUtil.getCurAppIdByMdc(), uid, "22national_day_travel_postcard_collect_stamps_task_finish", params, ServicesNameEnum.ump_services.getCode());
    }

    /**
     * 寄出明信片埋点
     *
     * @param uid     用户id
     * @param toUid   目标用户id
     * @param toCity  城市
     * @param content 内容
     * @param stamp   邮票类型
     */
    public void postcardTrack(Long uid, Long toUid, String toCity, String content, boolean stamp) {
        Map<String, Object> params = new HashMap<>();
        params.put("target_user_id", toUid);
        params.put("target_region", toCity);
        params.put("content", content);
        params.put("stamps_type", stamp ? "general_stamps" : "gold_stamps");
        yzKafkaProducerManager.dataRangerTrack(MDCUtil.getCurAppIdByMdc(), uid, "22national_day_travel_postcard_send_postcard", params, ServicesNameEnum.ump_services.getCode());
    }

    @Getter
    @AllArgsConstructor
    public enum TaskEnum {
        /** 任务 */
        TASK_DRAW_LUCKY_BAG("drawLuckyBagTask", "lucky_bag_lottery", STAMP, "https://res-cdn.nuan.chat/res/activity/postcard2022/stamp_background.png", 10, 30, 5, 1, 2, "抽取福袋任务"),
        TASK_CREATE_DIALOG("createDialogTask", "chat_with_new_firend", STAMP, "https://res-cdn.nuan.chat/res/activity/postcard2022/stamp_background.png", 5, 30, 5, 1, 2, "1v1 创建有效会话"),
        TASK_VIDEO("videoTask", "15s_video_call_or_match", STAMP, "https://res-cdn.nuan.chat/res/activity/postcard2022/stamp_background.png", 10, 75, 9, 2, 1, "15s视频通话"),
        TASK_FAMILY_GIVE_GIFT("familyGiveGiftTask", "send_nienielian_gift_in_family", STAMP, "https://res-cdn.nuan.chat/res/activity/postcard2022/stamp_background.png", 10, 75, 9, 1, 2, "家族送礼任务"),
        TASK_ROOM_GIVE_GIFT("roomGiveGiftTask", "send_aiqingyu_gift_in_chatroom", STAMP, "https://res-cdn.nuan.chat/res/activity/postcard2022/stamp_background.png", 20, 50, 0, 2, 2, "聊天室送礼任务"),
        TASK_GIVE_GIFT("giveGiftTask", "send_aidelvxing_gift", GOLD_STAMP, "https://res-cdn.nuan.chat/res/activity/postcard2022/gold_stamp_background.png", Integer.MAX_VALUE, 100, 0, 100, Integer.MAX_VALUE, "送礼任务（爱的旅行）"),
        TASK_ACTIVITY_PRIZE("activityPrizeTask", "get_award_in_shiyilangmanlvxing", STAMP, "https://res-cdn.nuan.chat/res/activity/postcard2022/stamp_background.png", 5, 50, 15, 4, 2, "完成十一浪漫旅行任务"),
        ;
        private String code;
        /** 类型 埋点用 */
        private String type;
        /** 奖励key */
        private String prizeKey;
        /** 奖励icon */
        private String prizeIcon;
        /** 每天获得奖励最大上限 */
        private Integer maxCount;
        /** 第二次获得奖励概率 */
        private Integer secondProbability;
        /** 第二次之后每次概率递减数 */
        private Integer decrProb;
        /** 获取金邮票概率 */
        private Integer goldProbability;
        /** 任务金邮票上限 */
        private Integer goldMaxCount;
        /** 描述 */
        private String desc;

        public String getCode() {
            return code;
        }

        public String getPrizeKey() {
            return prizeKey;
        }

        public String getPrizeIcon() {
            return prizeIcon;
        }

        public Integer getMaxCount() {
            return maxCount;
        }

        public Integer getSecondProbability() {
            return secondProbability;
        }

        public Integer getDecrProb() {
            return decrProb;
        }

        public Integer getGoldMaxCount() {
            return goldMaxCount;
        }

        public String getDesc() {
            return desc;
        }
    }

    @Getter
    @AllArgsConstructor
    public enum CityEnum {
        /** 城市 */
        xj("xj", 4, "XinJiang", 10, "新疆明信片", "https://res-cdn.nuan.chat/res/activity/postcard2022/xj_background.png", "新疆"),
        xa("xa", 3, "XiAn", 5, "西安明信片", "https://res-cdn.nuan.chat/res/activity/postcard2022/xa_background.png", "西安"),
        bj("bj", 2, "BeiJing", 3, "北京明信片", "https://res-cdn.nuan.chat/res/activity/postcard2022/bj_background.png", "北京"),
        hz("hz", 0, "HangZhou", 1, "杭州明信片", "https://res-cdn.nuan.chat/res/activity/postcard2022/hz_background.png", "杭州"),
        gz("gz", 1, "GuangZhou", 2, "广州明信片", "https://res-cdn.nuan.chat/res/activity/postcard2022/gz_background.png", "广州"),
        yn("yn", 6, "YunNan", 6, "云南明信片", "https://res-cdn.nuan.chat/res/activity/postcard2022/yn_background.png", "云南"),
        xz("xz", 5, "XiZang", 8, "西藏明信片", "https://res-cdn.nuan.chat/res/activity/postcard2022/xz_background.png", "西藏"),
        ;

        public static final Map<String, CityEnum> CITY_MAP = new HashMap<>();
        public static final Map<Integer, CityEnum> CITY_SORT_MAP = new HashMap<>();

        static {
            for (CityEnum city : values()) {
                CITY_MAP.put(city.getCode(), city);
                CITY_SORT_MAP.put(city.getUnlockSort(), city);
            }
        }

        private String code;
        /** 解锁顺序 */
        private Integer unlockSort;
        /** 埋点代码 */
        private String traceCode;
        /** 使用邮票数 */
        private Integer costStampNum;
        /** 明信片名称 */
        private String postcardName;
        /** 城市背景图 */
        private String img;
        /** 描述 */
        private String desc;

        /**
         * 获取城市
         *
         * @param cityCode 城市
         * @return CityEnum
         */
        public static CityEnum getCityNum(String cityCode) {
            return CITY_MAP.get(cityCode);
        }

        /**
         * 根据位置获取城市名
         *
         * @param sort 位置
         * @return String
         */
        public static String getCityNameByOrdinal(int sort) {
            return CITY_SORT_MAP.get(sort).getCode();
        }

        /**
         * 获取需要消耗的邮票数
         *
         * @param cityCode 城市
         * @return Integer
         */
        public static Integer getCostStampNum(String cityCode) {
            CityEnum city = CITY_MAP.get(cityCode);
            return Objects.isNull(city) ? null : city.getCostStampNum();
        }

        public String getCode() {
            return code;
        }

        public Integer getCostStampNum() {
            return costStampNum;
        }

        public String getPostcardName() {
            return postcardName;
        }

        public String getDesc() {
            return desc;
        }
    }

    @Getter
    @AllArgsConstructor
    public enum NpcMsgEnum {
        /** 城市 */
        first("first", "ump:lovePostcard:npcMsgFirst", 1664535600000L, 1664550000000L,
                "国庆活动“爱情明信片”上线啦，给你的好友寄张明信片，助力集齐还可参与抽奖赢华为手机哦～<a href=\"%s\">点击查看>>></a>", "0930", "9月30号消息"),
        second("second", "ump:lovePostcard:npcMsgSecond", 1664622000000L, 1664636400000L,
                "国庆节快乐！快来参与“爱情明信片”活动，互寄明信片可参与抽奖赢华为手机哦～<a href=\"%s\">点击查看>>></a>", "1001", "10月01号消息"),
        third("third", "ump:lovePostcard:npcMsgThird", 1664794800000L, 1664809200000L,
                "国庆节快乐！快来参与“爱情明信片”活动，互寄明信片可参与抽奖赢华为手机哦～<a href=\"%s\">点击查看>>></a>", "1003", "10月03号消息"),
        four("four", "ump:lovePostcard:npcMsgFour", 1664967600000L, 1664982000000L,
                "国庆节快乐！快来参与“爱情明信片”活动，互寄明信片可参与抽奖赢华为手机哦～<a href=\"%s\">点击查看>>></a>", "1005", "10月05号消息"),
        five("five", "ump:lovePostcard:npcMsgFive", 1665140400000L, 1665154800000L,
                "国庆最后1天！现在参与“爱情明信片”活动，抽奖可赢取华为手机哦～<a href=\"%s\">点击查看>>></a>", "1007", "10月07号消息"),
        ;

        private String code;
        /** 缓存key */
        private String cacheKey;
        /** 开始时间 */
        private Long startTime;
        /** 截止时间 */
        private Long endTime;
        /** 消息内容 */
        private String msg;
        /** 消息来源 */
        private String from;
        /** 描述 */
        private String desc;

        /**
         * 获取当前时间段可以发送的消息
         *
         * @return NpcMsgEnum
         */
        public static NpcMsgEnum getNpcMsg(long time) {
            for (NpcMsgEnum npcMsg : values()) {
                if (npcMsg.getStartTime() <= time && npcMsg.getEndTime() >= time) {
                    return npcMsg;
                }
            }
            return null;
        }
    }

}
