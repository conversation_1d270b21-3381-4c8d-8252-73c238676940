package cn.yizhoucp.ump.biz.project.web.rest.controller.admin;


import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.vo.landingservices.AdminPageVO;
import cn.yizhoucp.ump.api.enums.ActivityTemplateEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.userActivity.admin.LuckyBagCallBackAdminManager;
import cn.yizhoucp.ump.biz.project.biz.util.PageUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ActivityJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ActivityDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import cn.yizhoucp.ump.biz.project.dto.activity.config.ActivityConfigDTO;
import cn.yizhoucp.ump.biz.project.web.vo.LuckyBagCallBackAdminVO;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.common.checker.CheckerChainEnum.JOIN_USER_ACTIVITY;
import static cn.yizhoucp.ump.biz.project.common.handler.HandlerChainEnum.JOIN_ACTIVITY;

/**
 * 福袋换回管理后台
 *
 * @author: lianghu
 */
@RestController
@RequestMapping("/api/admin/ump/lucky-bag-call-back")
public class LuckyBagCallBackAdminController {

    @Resource
    private LuckyBagCallBackAdminManager luckyBagCallBackAdminManager;
    @Resource
    private ActivityJpaDAO activityJpaDAO;

    @GetMapping("/query")
    public Result<AdminPageVO<LuckyBagCallBackAdminVO>> query(LuckyBagCallBackAdminVO param, Integer page, Integer perPage) {
        Page<ActivityDO> pageResult = activityJpaDAO.findAll(getPageDetailInfoSql(param), PageUtil.defaultPage(page - 1, perPage, "updateTime"));
        if (CollectionUtils.isEmpty(pageResult.getContent())) {
            return Result.successResult(AdminPageVO.getDefault(page, perPage, pageResult));
        }
        List<LuckyBagCallBackAdminVO> result = pageResult.getContent().stream().map(this::convert2VO).collect(Collectors.toList());
        // 补充活动信息
        return Result.successResult(AdminPageVO.getDefault(page, perPage, pageResult, result));
    }

    /**
     * 添加唤回活动
     *
     * @return
     */
    @PostMapping("/update")
    public Result<Boolean> create(@RequestBody LuckyBagCallBackAdminVO param) {
        return Result.successResult(luckyBagCallBackAdminManager.createActivity(param));
    }

    private Specification<ActivityDO> getPageDetailInfoSql(LuckyBagCallBackAdminVO param) {
        return (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            // 限制福袋唤回类型
            predicates.add(criteriaBuilder.equal(root.get("templateType"), ActivityTemplateEnum.LUCKY_BAG_CALL_BACK_DAILY.getCode()));
            if (StringUtils.isNotBlank(param.getActivityCode())) {
                predicates.add(criteriaBuilder.equal(root.get("activityCode"), param.getActivityCode()));
            }
            if (StringUtils.isNotBlank(param.getActivityName())) {
                predicates.add(criteriaBuilder.equal(root.get("activityName"), param.getActivityName()));
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    private LuckyBagCallBackAdminVO convert2VO(ActivityDO activityDO) {
        ActivityConfigDTO config = JSON.parseObject(activityDO.getConfig(), ActivityConfigDTO.class);
        Integer idempotentDays = config.getCheckerContext().get(JOIN_USER_ACTIVITY.getConfigKey()).getIdempotentDuring();
        Long cohortId = config.getCheckerContext().get(JOIN_USER_ACTIVITY.getConfigKey()).getCohortId();
        List<SendPrizeDTO> prizes = config.getHandlerContext().get(JOIN_ACTIVITY.getConfigKey()).getSendPrizeConfig();
        return LuckyBagCallBackAdminVO.builder()
                .id(activityDO.getId())
                .appId(activityDO.getAppId())
                .unionId(activityDO.getUnionId())
                .activityName(activityDO.getActivityName())
                .activityCode(activityDO.getActivityCode())
                .priority(activityDO.getPriority())
                .startTime(activityDO.getStartTimeString())
                .endTime(activityDO.getEndTimeString())
                .idempotentDays(idempotentDays)
                .cohortId(cohortId)
                .prizes(prizes)
                .build();
    }

}
