package cn.yizhoucp.ump.biz.project.biz.event.astrologydraw;

import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AstrologyDrawMessage {
    /**
     * 用户id
     */
    private Long uid;

    /**
     * 抽奖的次数
     */
    private Integer drawTimes;

    /**
     * 消耗的次数
     */
    private Integer consumeTimes;

    /**
     * 抽奖结果
     */
    private List<DrawPoolItemDTO> drawResult;
}
