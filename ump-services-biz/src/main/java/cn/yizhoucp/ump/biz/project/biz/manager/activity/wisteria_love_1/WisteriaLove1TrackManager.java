package cn.yizhoucp.ump.biz.project.biz.manager.activity.wisteria_love_1;


import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.enums.ServicesNameEnum;
import cn.yizhoucp.starter.cassandra.base.kafka.YzKafkaProducerManager;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 紫藤萝之恋 埋点类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 18:05 2025/4/18
 */
@Service
public class WisteriaLove1TrackManager {

    @Resource
    private YzKafkaProducerManager yzKafkaProducerManager;

    /**
     * 通用埋点_活动抽奖
     *
     * @param uid
     * @param poolCode
     * @param awardKey
     * @param awardAmount
     */
    public void allActivityLottery(Long uid, String poolCode, Long awardCount, String awardKey, Long awardAmount) {
        Map<String, Object> params = Maps.newHashMapWithExpectedSize(6);
        params.put("activity_type", "wisteria_love_1");
        params.put("attribute_type", "platform_activity");
        params.put("pool_code", poolCode);
        params.put("award_key", awardKey);
        params.put("award_count", awardCount);
        params.put("award_amount", awardAmount);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_lottery", params, ServicesNameEnum.ump_services.getCode());
    }

    /**
     * 通用埋点_活动任务完成
     *
     * @param uid
     * @param taskType
     */
    public void allActivityTaskFinish(Long uid, String taskType) {
        Map<String, Object> params = Maps.newHashMapWithExpectedSize(6);
        params.put("activity_type", "wisteria_love_1");
        params.put("attribute_type", "platform_activity");
        params.put("task_type", taskType);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_task_finish", params, ServicesNameEnum.ump_services.getCode());
    }

    /**
     * 通用埋点_ 活动领取奖励
     */
    public void allActivityReceiveAward(String awardKey, Long awardAmount, Integer awardCount, String rewardType, Long uid) {
        Map<String, Object> params = Maps.newHashMapWithExpectedSize(6);
        params.put("activity_type", "wisteria_love_1");
        params.put("attribute_type", "platform_activity");
        params.put("award_type", rewardType);
        params.put("award_key", awardKey);
        params.put("award_amount", awardAmount);
        params.put("award_count", awardCount);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_receive_award", params, ServicesNameEnum.ump_services.getCode());
    }
}
