package cn.yizhoucp.ump.biz.project.biz.manager.activity.qixiActivity;

import cn.hutool.core.util.ObjectUtil;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.vo.jimu.rank.CpRankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.AbstractRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import org.springframework.stereotype.Service;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ActivityJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ActivityDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dal.service.ScenePrizeService;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Example;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 七夕活动排名管理
 *
 * <AUTHOR>
 * @version V1.0
 * @since 下午6:46 2024/7/24
 */
@Service
@Slf4j
public class QiXiFestivalRankManager extends AbstractRankManager {

    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;

    @Resource
    private ActivityJpaDAO activityJpaDAO;

    @Resource
    private SendPrizeManager sendPrizeManager;

    @Resource
    private NotifyComponent notifyComponent;
    @Resource
    private ScenePrizeService scenePrizeService;

    @Resource
    private QiXiFestivalTrackManager qiXiFestivalTrackManager;


    @Override
    protected void postProcess(RankContext rankContext) {
        try {
            RankVO rankVO = rankContext.getRankVO();
            List<CpRankItem> cpRankItemList = rankVO.getCpRankItemList();
            CpRankItem myCpRankItem = rankVO.getMyCpRankItem();
            Boolean isNull = ObjectUtil.isEmpty(myCpRankItem);
            log.info("QiXiConfessionRankManager#postProcess cpRankItemList:{}", JSON.toJSONString(cpRankItemList));
            for (CpRankItem cpRankItem : cpRankItemList) {
                if (isNull || (!cpRankItem.getMaleUid().equals(myCpRankItem.getMaleUid()) || !cpRankItem.getFemaleUid().equals(myCpRankItem.getFemaleUid()))) {
                    cpRankItem.setMaleAvatar("https://res-cdn.nuan.chat/nuanliao-fronted/activity/guanxuan-jun/default-avatar.png");
                    cpRankItem.setFemaleAvatar("https://res-cdn.nuan.chat/nuanliao-fronted/activity/guanxuan-jun/default-avatar.png");
                    cpRankItem.setMaleUserName("神秘嘉宾");
                    cpRankItem.setFemaleUserName("神秘嘉宾");
                }
            }
        } catch (Exception e) {
            log.warn("QiXiConfessionRankManager#postProcess error {}", e.getMessage());
        }
    }

    @Override
    protected void doPreProcess(RankContext rankContext) {
        Long rankLen = rankContext.getRankLen();
        if (rankLen == null || rankLen <= 0) {
            rankContext.setRankLen(10L);
        }
    }

    public Boolean sendPrize() {
        if (!Boolean.TRUE.equals(redisManager.setnx("ump:qixi_festival:send_prize_idempotent:qiluo_lantern_pool", System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND))) {
            return Boolean.TRUE;
        }

        List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(1L, QiXiFestivalConstant.ACTIVITY_CODE, "qiluo_lantern_pool");
        log.info("scenePrizeDOList {}", JSON.toJSONString(scenePrizeDOList));
        if (CollectionUtils.isEmpty(scenePrizeDOList)) {
            return Boolean.FALSE;
        }

        RankVO rankVO = this.getRank(RankContext.builder()
                .activityCode(QiXiFestivalConstant.ACTIVITY_CODE)
                .rankKey(QiXiFestivalConstant.WISH_RANK)
                .rankLen(7L)
                .type(RankContext.RankType.cp)
                .build());
        log.info("rankVO {}", rankVO);
        if (rankVO == null) {
            return Boolean.FALSE;
        }

        List<CpRankItem> rankList = rankVO.getCpRankItemList();
        if (CollectionUtils.isEmpty(rankList)) {
            return Boolean.FALSE;
        }

        for (CpRankItem cpRankItem : rankList) {
            Long rank = cpRankItem.getRank();
            List<ScenePrizeDO> manScenePrizeDOs = scenePrizeDOList.stream().filter(scenePrizeDO -> (Objects.equals(rank, scenePrizeDO.getPrizeBelongToRank()) || Objects.equals(0L, scenePrizeDO.getPrizeBelongToRank())) && Objects.equals("man", scenePrizeDO.getSex())).collect(Collectors.toList());
            List<ScenePrizeDO> womanScenePrizeDOs = scenePrizeDOList.stream().filter(scenePrizeDO -> (Objects.equals(rank, scenePrizeDO.getPrizeBelongToRank()) || Objects.equals(0L, scenePrizeDO.getPrizeBelongToRank())) && Objects.equals("woman", scenePrizeDO.getSex())).collect(Collectors.toList());
            log.info("QiXiFestivalRankManager#sendPrize cpRankItem {} manScenePrizeDOs {} womanScenePrizeDOs {}", cpRankItem, manScenePrizeDOs, womanScenePrizeDOs);
            if (CollectionUtils.isEmpty(manScenePrizeDOs) || CollectionUtils.isEmpty(womanScenePrizeDOs)) {
                log.info("maleUid {} femaleUid {} rank {} 没有奖励", cpRankItem.getMaleUid(), cpRankItem.getFemaleUid(), rank);
                continue;
            }
            //埋点
            for (ScenePrizeDO gift : manScenePrizeDOs) {
                qiXiFestivalTrackManager.allActivityReceiveAward("destiny_on_qixi", gift.getPrizeValue(), gift.getPrizeValueGold().longValue(), gift.getPrizeNum(), null, cpRankItem.getMaleUid());
            }
            for (ScenePrizeDO gift : womanScenePrizeDOs) {
                if(Objects.equals("coin", gift.getPrizeType())){
                    qiXiFestivalTrackManager.allActivityReceiveAward("destiny_on_qixi", gift.getPrizeValue(),Long.valueOf(gift.getPrizeValue()), gift.getPrizeNum(), null, cpRankItem.getFemaleUid());
                }else{
                    qiXiFestivalTrackManager.allActivityReceiveAward("destiny_on_qixi", gift.getPrizeValue(), gift.getPrizeValueGold().longValue(), gift.getPrizeNum(), null, cpRankItem.getFemaleUid());
                }
            }
            sendPrizeManager.sendPrize(
                    BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(cpRankItem.getMaleUid()).build(),
                    manScenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, cpRankItem.getMaleUid())).collect(Collectors.toList())
            );
            sendPrizeManager.sendPrize(
                    BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(cpRankItem.getFemaleUid()).build(),
                    womanScenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, cpRankItem.getFemaleUid())).collect(Collectors.toList())
            );
            notifyComponent.npcNotify(
                    ServicesAppIdEnum.lanling.getUnionId(),
                    cpRankItem.getMaleUid(),
                    String.format("亲爱的有情人，恭喜在”七夕“活动中排名第%s，奖励已经下发至背包，快去查看吧", cpRankItem.getRank())
            );
            notifyComponent.npcNotify(
                    ServicesAppIdEnum.lanling.getUnionId(),
                    cpRankItem.getFemaleUid(),
                    String.format("亲爱的有情人，恭喜在”七夕“活动中排名第%s，奖励已经下发至账户，快去查看吧", cpRankItem.getRank())
            );
        }

        return Boolean.TRUE;
    }

    public Boolean sendLoveRank(String date) {
        if (!Boolean.TRUE.equals(redisManager.setnx(String.format("ump:qixi_festival:send_prize_idempotent:%s", DateUtil.getNowYyyyMMdd()), System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND))) {
            return Boolean.TRUE;
        }
        List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(1L, QiXiFestivalConstant.ACTIVITY_CODE, "love_star_river_pool");
        log.info("scenePrizeDOList {}", JSON.toJSONString(scenePrizeDOList));
        if (CollectionUtils.isEmpty(scenePrizeDOList)) {
            return Boolean.FALSE;
        }
        if (Objects.isNull(date)) {
            LocalDateTime yesterday = LocalDateTime.now().minusDays(1);
            date = yesterday.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        }
        String rankKey = String.format(QiXiFestivalConstant.CP_RANK_KEY, date);
        RankVO rankVO = this.getRank(RankContext.builder()
                .activityCode(QiXiFestivalConstant.ACTIVITY_CODE)
                .rankKey(rankKey)
                .rankLen(4L)
                .supportDiff(Boolean.TRUE)
                .type(RankContext.RankType.cp)
                .build());
        log.info("rankVO {}", rankVO);
        if (rankVO == null) {
            return Boolean.FALSE;
        }

        List<CpRankItem> rankList = rankVO.getCpRankItemList();
        if (CollectionUtils.isEmpty(rankList)) {
            return Boolean.FALSE;
        }

        for (CpRankItem cpRankItem : rankList) {
            Long rank = cpRankItem.getRank();
            List<ScenePrizeDO> scenePrizeDOs = scenePrizeDOList.stream().filter(scenePrizeDO -> Objects.equals(rank, scenePrizeDO.getPrizeBelongToRank())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(scenePrizeDOs)) {
                log.info("maleUid {} femaleUid {} rank {} 没有奖励", cpRankItem.getMaleUid(), cpRankItem.getFemaleUid(), rank);
                continue;
            }
            for (ScenePrizeDO gift : scenePrizeDOs) {
                qiXiFestivalTrackManager.allActivityReceiveAward("destiny_on_qixi", gift.getPrizeValue(), gift.getPrizeValueGold().longValue(), gift.getPrizeNum(), null, cpRankItem.getMaleUid());
            }
            for (ScenePrizeDO gift : scenePrizeDOs) {
                qiXiFestivalTrackManager.allActivityReceiveAward("destiny_on_qixi", gift.getPrizeValue(), gift.getPrizeValueGold().longValue(), gift.getPrizeNum(), null, cpRankItem.getFemaleUid());
            }
            sendPrizeManager.sendPrize(
                    BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(cpRankItem.getMaleUid()).build(),
                    scenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, cpRankItem.getMaleUid())).collect(Collectors.toList())
            );
            sendPrizeManager.sendPrize(
                    BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(cpRankItem.getFemaleUid()).build(),
                    scenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, cpRankItem.getFemaleUid())).collect(Collectors.toList())
            );
        }

        return Boolean.TRUE;
    }

    public Boolean sendSignAllPrize() {
        if (!Boolean.TRUE.equals(redisManager.setnx(String.format("ump:qixi_festival:send_prize_idempotent:signAll"), System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND))) {
            return Boolean.TRUE;
        }
        Long start = 0L;
        Long batchSize = 1000L;
        Long end = batchSize - 1;
        String rankKey = String.format(QiXiFestivalConstant.SIGN_ALL_LIST, getLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        while (true) {
            Set<ZSetOperations.TypedTuple<Object>> typedTuples = redisManager.reverseRangeByScoreWithScores(rankKey,
                    0, Double.MAX_VALUE, start, end);
            log.info("QiXiFestivalRankManager#sendRankPrize typedTuples size {}", typedTuples.size());
            if (org.springframework.util.CollectionUtils.isEmpty(typedTuples)) {
                break;
            }
            //发送礼物
            sendSignPrize(typedTuples);
            start += batchSize;
            end = end + batchSize - 1;
        }
        return Boolean.TRUE;
    }

    private void sendSignPrize(Set<ZSetOperations.TypedTuple<Object>> typedTuples) {
        for (ZSetOperations.TypedTuple<Object> typedTuple : typedTuples) {
            String cpId = typedTuple.getValue().toString();
            List<String> uids = Arrays.asList(cpId.split("_"));
/*
            String signAllKey = QiXiFestivalConstant.createSignAllRedisKey(cpId);
*/
/*
            if (redisManager.hasKey(signAllKey)) {
*/
            for (int i = 0; i < uids.size(); i++) {
                Long uid = Long.valueOf(uids.get(i));
                List<ScenePrizeDO> scenePrizeDOList = scenePrizeService.getListBySceneCode(QiXiFestivalConstant.ACTIVITY_CODE, "sign_all");
                for (ScenePrizeDO gift : scenePrizeDOList) {
                    qiXiFestivalTrackManager.allActivityReceiveAward("destiny_on_qixi", gift.getPrizeValue(), gift.getPrizeValueGold().longValue(), gift.getPrizeNum(), null, uid);
                }
                sendPrizeManager.sendPrize(
                        BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(uid).build(),
                        scenePrizeDOList.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, uid)).collect(Collectors.toList())
                );
                for (ScenePrizeDO scenePrizeDO : scenePrizeDOList) {
                    notifyComponent.npcNotify(
                            ServicesAppIdEnum.lanling.getUnionId(),
                            uid,
                            String.format("恭喜有情人在七夕活动中活动中获得“%s”一个，礼物下发至背包，快去查收吧~", scenePrizeDO.getPrizeValue())
                    );
                }
            }
        }
/*
        }
*/
    }

    public LocalDate getLocalDate() {
        Optional<ActivityDO> opt = activityJpaDAO.findOne(Example.of(ActivityDO.builder()
                .unionId(ServicesAppIdEnum.lanling.getUnionId())
                .activityCode(QiXiFestivalConstant.ACTIVITY_CODE).build()));
        ActivityDO activityDO;
        if(opt.isPresent()){
             activityDO = opt.get();
        }else{
            return LocalDate.now();
        }
        LocalDate startTime = activityDO.getStartTime().toLocalDate();
        LocalDate secondStartTime = startTime.plusDays(7);
        LocalDate now = LocalDate.now();
        if (now.isAfter(secondStartTime) || now.equals(secondStartTime)) {
            return secondStartTime;
        }
        log.info("QiXiFestivalIndexManager#getLocalDate startTime {}", startTime);
        return startTime;
    }
}
