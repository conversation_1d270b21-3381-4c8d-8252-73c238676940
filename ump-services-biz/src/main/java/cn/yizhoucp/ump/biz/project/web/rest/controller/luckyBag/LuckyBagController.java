package cn.yizhoucp.ump.biz.project.web.rest.controller.luckyBag;

import cn.yizhoucp.ump.biz.project.biz.manager.luckyBag.LuckyBagManager;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.auth.SecurityUser;
import cn.yizhoucp.ms.core.base.auth.SecurityUtils;
import cn.yizhoucp.ms.core.base.enums.LuckyActivityEnum;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ms.core.vo.Page;
import cn.yizhoucp.ms.core.vo.coinservices.UserPrizeRecordVO;
import cn.yizhoucp.ump.api.vo.luckBag.DrawGiftProductVO;
import cn.yizhoucp.ump.api.vo.redPacket.LuckyBagIndexVO;
import cn.yizhoucp.ump.api.vo.redPacket.LuckyDrawResultVO;
import com.alibaba.fastjson.JSONObject;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 手气福袋
 *
 * @author: lianghu
 */
@Deprecated
@RestController
public class LuckyBagController {

    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private LuckyBagManager luckyBagManager;

    /**
     * 购买抽奖次数
     *
     * @param count
     * @return
     */
    @RequestMapping("/api/inner/family/buy-lucky-draw-count")
    public Result<JSONObject> bugLuckDrawCount(Long count) {
        if (Objects.isNull(count) || count < 0L) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        return RestBusinessTemplate.executeWithoutTransaction(() -> luckyBagManager.buyLuckyDrawCount(LuckyActivityEnum.FAMILY_LUCKDRAW.getCode(), count, null, null, null, null));
    }

    /**
     * 进行幸运抽奖
     *
     * @param count        抽奖次数
     * @param luckDrawType 抽奖类型
     * @return LuckyDrawResultVO
     */
    @RequestMapping("/api/inner/family/open-luck-draw")
    public Result<LuckyDrawResultVO> openLuckDraw(Long count, @RequestParam(value = "luckDrawType", required = false) String luckDrawType) {
        if (Objects.isNull(count) || count < 0L) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        return RestBusinessTemplate.execute(() -> luckyBagManager.openLuckDraw(LuckyActivityEnum.FAMILY_LUCKDRAW.getCode(), count, luckDrawType), transactionTemplate);
    }

    /**
     * 获取用户抽奖首页
     *
     * @return LuckyBagIndexVO
     */
    @RequestMapping("/api/inner/family/luck-draw-index")
    public Result<LuckyBagIndexVO> luckyDrawIndex() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> luckyBagManager.luckDrawIndex());
    }

    /***
     * 3 查看当前登录账户的J金币流水 分页
     * @return Page<UserPrizeRecordVO>
     */
    @RequestMapping(value = "/api/inner/user-prize-record", method = RequestMethod.GET)
    public Result<Page<UserPrizeRecordVO>> getUserPrizeRecord(Integer pageIndex, Integer pageSize, Long activityId, @RequestParam(value = "bussinessType", required = false) String bussinessType) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            SecurityUser securityUser = SecurityUtils.getCurrentUser();
            return luckyBagManager.pageQueryUserPrizeRecord(securityUser.getUserId(), activityId, pageIndex, pageSize, securityUser.getAppId(), bussinessType);
        });
    }

    /**
     * 手气福袋奖池奖品
     *
     * @param luckDrawType 抽奖类型
     * @return List<CoinGiftProductVO>
     */
    @RequestMapping("/api/inner/lucky-bag-draw-pool")
    public Result<List<DrawGiftProductVO>> luckDrawPool(String luckDrawType) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> luckyBagManager.luckDrawPool(luckDrawType));
    }
}
