package cn.yizhoucp.ump.biz.project.biz.manager.navigation;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.vo.landingservices.GiftProductModel;
import cn.yizhoucp.ms.core.vo.productServices.CoinGiftProductVO;
import cn.yizhoucp.product.client.CoinGiftProductFeignService;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.GiftBaseInfoDTO;
import cn.yizhoucp.ump.api.vo.navigation.ExchangeGiftPublicItemVO;
import cn.yizhoucp.ump.api.vo.navigation.ExchangeGiftPublicVO;
import cn.yizhoucp.ump.biz.project.biz.enums.navigation.BoatEnum;
import cn.yizhoucp.ump.biz.project.biz.enums.navigation.MileEnum;
import cn.yizhoucp.ump.biz.project.dal.mp.dataobject.navigation.UserBoatDO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class NavigationGiftManager {
    @Resource
    private NavigationUserBoatManager userBoatManager;

    @Resource
    private RedisManager redisManager;
    // 没有单独更换礼物的需求，时间不够，先这样吧
    private static final Map<String, Map<String, String>> EXCHANGE_GIFT_MAP = new HashMap<>();
    static {
        Map<String, String> boatAGiftMap = new HashMap<>();
        boatAGiftMap.put(MileEnum.ONE.name(), "HD_GIFT_NAVIGATION");
        boatAGiftMap.put(MileEnum.TWO.name(), "JGPY_GIFT_NAVIGATION");
        boatAGiftMap.put(MileEnum.THREE.name(), "FCM_GIFT_NAVIGATION");
        boatAGiftMap.put(MileEnum.FOUR.name(), "TTBQL_GIFT_NAVIGATION");
        boatAGiftMap.put(MileEnum.FIVE.name(), "CCZX_GIFT_NAVIGATION");
        boatAGiftMap.put(MileEnum.SIX.name(), "SJX_GIFT_NAVIGATION");
        boatAGiftMap.put(MileEnum.SEVEN.name(), "MYALS_GIFT_NAVIGATION");
        Map<String, String> boatBGiftMap = new HashMap<>();
        boatBGiftMap.put(MileEnum.ONE.name(), "HD_GIFT_NAVIGATION");
        boatBGiftMap.put(MileEnum.TWO.name(), "YDMJ_GIFT_NAVIGATION");
        boatBGiftMap.put(MileEnum.THREE.name(), "TTBQL_GIFT_NAVIGATION");
        boatBGiftMap.put(MileEnum.FOUR.name(), "CCZX_GIFT_NAVIGATION");
        boatBGiftMap.put(MileEnum.FIVE.name(), "SJX_GIFT_NAVIGATION");
        boatBGiftMap.put(MileEnum.SIX.name(), "MYALS_GIFT_NAVIGATION");
        boatBGiftMap.put(MileEnum.SEVEN.name(), "XCMR_GIFT_NAVIGATION");
        Map<String, String> boatCGiftMap = new HashMap<>();
        boatCGiftMap.put(MileEnum.ONE.name(), "HD_GIFT_NAVIGATION");
        boatCGiftMap.put(MileEnum.TWO.name(), "YDMJ_GIFT_NAVIGATION");
        boatCGiftMap.put(MileEnum.THREE.name(), "CCZX_GIFT_NAVIGATION");
        boatCGiftMap.put(MileEnum.FOUR.name(), "MZJX_GIFT_NAVIGATION");
        boatCGiftMap.put(MileEnum.FIVE.name(), "MGLC_GIFT_NAVIGATION");
        boatCGiftMap.put(MileEnum.SIX.name(), "RYGD_GIFT_NAVIGATION");
        boatCGiftMap.put(MileEnum.SEVEN.name(), "XRKQL_GIFT_NAVIGATION");
        Map<String, String> boatDGiftMap = new HashMap<>();
        boatDGiftMap.put(MileEnum.ONE.name(), "HD_GIFT_NAVIGATION");
        boatDGiftMap.put(MileEnum.TWO.name(), "YDMJ_GIFT_NAVIGATION");
        boatDGiftMap.put(MileEnum.THREE.name(), "CCZX_GIFT_NAVIGATION");
        boatDGiftMap.put(MileEnum.FOUR.name(), "MZJX_GIFT_NAVIGATION");
        boatDGiftMap.put(MileEnum.FIVE.name(), "MGLC_GIFT_NAVIGATION");
        boatDGiftMap.put(MileEnum.SIX.name(), "KYSH_GIFT_NAVIGATION");
        boatDGiftMap.put(MileEnum.SEVEN.name(), "HYDK_GIFT_NAVIGATION");
        Map<String, String> boatEGiftMap = new HashMap<>();
        boatEGiftMap.put(MileEnum.ONE.name(), "HD_GIFT_NAVIGATION");
        boatEGiftMap.put(MileEnum.TWO.name(), "TTBQL_GIFT_NAVIGATION");
        boatEGiftMap.put(MileEnum.THREE.name(), "XDGD_GIFT_NAVIGATION");
        boatEGiftMap.put(MileEnum.FOUR.name(), "MYALS_GIFT_NAVIGATION");
        boatEGiftMap.put(MileEnum.FIVE.name(), "RYGD_GIFT_NAVIGATION");
        boatEGiftMap.put(MileEnum.SIX.name(), "CMAY_GIFT_NAVIGATION");
        boatEGiftMap.put(MileEnum.SEVEN.name(), "YSZL_GIFT_NAVIGATION");
        EXCHANGE_GIFT_MAP.put(BoatEnum.A.name(), boatAGiftMap);
        EXCHANGE_GIFT_MAP.put(BoatEnum.B.name(), boatBGiftMap);
        EXCHANGE_GIFT_MAP.put(BoatEnum.C.name(), boatCGiftMap);
        EXCHANGE_GIFT_MAP.put(BoatEnum.D.name(), boatDGiftMap);
        EXCHANGE_GIFT_MAP.put(BoatEnum.E.name(), boatEGiftMap);
    }


    @Resource
    private CoinGiftProductFeignService coinGiftProductFeignService;

    public String getCanExchangeGift(BoatEnum curBoat, MileEnum curMile) {
        String chooseGiftKey = getExchangeGift(curBoat, curMile);
        if (chooseGiftKey == null) {
            throw new ServiceException(ErrorCode.SERVER_EXCEPTION, "礼物不存在");
        }
        return chooseGiftKey;
    }

    private static String getExchangeGift(BoatEnum curBoat, MileEnum curMile) {
        return EXCHANGE_GIFT_MAP.getOrDefault(curBoat.name(), new HashMap<>()).getOrDefault(curMile.name(), null);
    }

    public String getCanExchangeGift(String curBoat, Integer curMile) {
        BoatEnum boatEnum = BoatEnum.valueOf(curBoat);
        MileEnum mileEnum = MileEnum.ofByValue(curMile);
        return this.getCanExchangeGift(boatEnum, mileEnum);
    }

    public GiftBaseInfoDTO getGiftBaseInfo(String giftKey) {
        if (giftKey == null) {
            throw new ServiceException(ErrorCode.SERVER_EXCEPTION, "礼物不存在");
        }
        GiftProductModel giftProductModel = getGiftModelByGiftKey(giftKey);
        if (giftProductModel == null) {
            throw new ServiceException(ErrorCode.SERVER_EXCEPTION, "礼物不存在");
        }
        return GiftBaseInfoDTO.ofByModel(giftProductModel);
    }

    private GiftProductModel getGiftModelByGiftKey(String giftKey) {
        Map<String, GiftProductModel> giftProductModels = this.getGiftModelByGiftKeyBatch(Collections.singletonList(giftKey));
        return giftProductModels.get(giftKey);
    }

    public Map<String, GiftProductModel> getGiftModelByGiftKeyBatch(List<String> giftKeyList) {
        if (CollectionUtils.isEmpty(giftKeyList)) {
            log.error("传入的key list为空");
            return new HashMap<>(0);
        }
        List<Object> objects = redisManager.hMultiGet(NavigationConstant.GIFT_CACHE_KEY, Collections.unmodifiableCollection(giftKeyList));
        List<GiftProductModel> giftProductModels;
        if (CollectionUtils.isEmpty(objects) || objects.contains(null)) {
            // 有没有命中的，就调product服务查询
            giftProductModels = coinGiftProductFeignService.getGiftInfoByKeyList(MDCUtil.getCurAppIdByMdc(), JSON.toJSONString(giftKeyList)).successData();
            redisManager.hmset(NavigationConstant.GIFT_CACHE_KEY,
                    giftProductModels.stream().collect(Collectors.toMap(GiftProductModel::getGiftKey, x->JSON.toJSONString(x), (x1, x2)->x1)),
                    RedisManager.ONE_DAY_SECONDS
            );
        } else {
            giftProductModels = objects.stream()
                    .map(x->JSONObject.parseObject(String.valueOf(x), GiftProductModel.class))
                    .collect(Collectors.toList());
        }
        return giftProductModels.stream().collect(Collectors.toMap(GiftProductModel::getGiftKey, Function.identity(), (x1, x2)->x1));
    }

    /**
     * 另一个方案是把整个返回结果放在缓存里，查询结构之后再处理当前的船只及可以兑换的礼物 TODO
     * @param uid
     * @return
     */
    public List<ExchangeGiftPublicVO> giftPublic(Long uid) {
        UserBoatDO userBoat = userBoatManager.getCurBoat(uid);
        MileEnum curMile = MileEnum.ofByValue(userBoat.getCurMile());
        BoatEnum curBoat = BoatEnum.valueOf(userBoat.getBoat());

        // 查询所有礼物信息
        List<String> keyList = Arrays.stream(BoatEnum.values()).flatMap(x -> EXCHANGE_GIFT_MAP.get(x.name()).values().stream().distinct()).collect(Collectors.toList());
        Map<String, GiftProductModel> giftAndModel = this.getGiftModelByGiftKeyBatch(keyList);
        log.debug("keyList -> {}, giftAndModel -> {}", keyList, JSON.toJSONString(giftAndModel));

        // 遍历每个船
        List<ExchangeGiftPublicVO> result = new ArrayList<>();
        for (BoatEnum value : BoatEnum.values()) {
            ExchangeGiftPublicVO item = new ExchangeGiftPublicVO();
            item.setBoat(value.name());
            item.setCurBoat(userBoat.getId() != null && curBoat.equals(value));
            // 遍历每个船的所有海里
            Map<String, String> stringStringMap = EXCHANGE_GIFT_MAP.get(item.getBoat());
            log.debug("stringStringMap: {}", stringStringMap);
            stringStringMap.forEach((mile, giftKey) -> {
                log.debug("mile: {}, giftKey: {}", mile, giftKey);
                MileEnum mileEnum = MileEnum.valueOf(mile);
                ExchangeGiftPublicItemVO exchangeGiftPublicItemVO = new ExchangeGiftPublicItemVO();
                exchangeGiftPublicItemVO.setNeedMile(mileEnum.getMileValue());
                exchangeGiftPublicItemVO.setGiftInfo(GiftBaseInfoDTO.ofByModel(giftAndModel.get(giftKey)));
                exchangeGiftPublicItemVO.setCanExchange(curBoat.equals(value) && curMile.equals(mileEnum));
                if (item.getItems() == null) {
                    item.setItems(new ArrayList<>(8));
                }
                item.getItems().add(exchangeGiftPublicItemVO);
            });
            result.add(item);
        }
        return result;
    }
}
