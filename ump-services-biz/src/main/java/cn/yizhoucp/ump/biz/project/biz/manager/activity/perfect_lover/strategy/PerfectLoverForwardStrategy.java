package cn.yizhoucp.ump.biz.project.biz.manager.activity.perfect_lover.strategy;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.annotation.NoRepeatSubmit;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.util.AppUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.log.LogComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.perfect_lover.PerfectLoverTrackManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.perfect_lover.common.PerfectLoverConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.perfect_lover.common.PerfectLoverRedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.perfect_lover.vo.PerfectLoveForwardResponse;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.perfect_lover.vo.PerfectLoverIndexVO;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import cn.yizhoucp.user.manager.UserFeignManager;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 满分恋人前进策略
 *
 * <AUTHOR>
 * @version V1.0
 * @since 20:18 2025/5/13
 */
@Service
@Slf4j
public class PerfectLoverForwardStrategy implements ExecutableStrategy {

    @Resource
    private PerfectLoverRedisManager perfectLoverRedisManager;

    @Resource
    private cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager sendPrizeManager;

    @Resource
    private NotifyComponent notifyComponent;
    @Resource
    private LogComponent logComponent;

    @Resource
    private UserFeignManager userFeignManager;

    @Resource
    private PerfectLoverTrackManager perfectLoverTrackManager;

    @Override
    @NoRepeatSubmit(time = 3)
    public PerfectLoveForwardResponse execute(ButtonEventParam buttonEventParam) {
        BaseParam baseParam = buttonEventParam.getBaseParam();
        Long uid = baseParam.getUid();
        Long friendId = buttonEventParam.getOtherId();
/*
        // 获取用户选择的好友
        Long friendId = perfectLoverRedisManager.getSelectedFriend(uid);
*/
        if (friendId == null) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "请先选择好友");
        }

        String bindId = AppUtil.splicUserId(uid, friendId);
        // 获取用户当前进度
        Integer currentProgress = perfectLoverRedisManager.getUserProgress(uid, bindId);
        if (currentProgress >= PerfectLoverConstant.MAX_NODE) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "已经到达终点啦");
        }

        // 获取甜蜜值
        Integer sweetness = perfectLoverRedisManager.getSweetness(uid, friendId);

        // 计算前进所需的甜蜜值（每前进一步需要10点甜蜜值）
        int requiredSweetness = 520;

        // 检查甜蜜值是否足够
        if (sweetness < requiredSweetness) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "甜蜜值不足，需要继续赠送礼物获取甜蜜值哦");
        }

        // 减少甜蜜值
        perfectLoverRedisManager.decrementSweetness(uid, friendId, (long) requiredSweetness);

        // 增加用户进度
        Long newProgress = perfectLoverRedisManager.incrementUserProgress(uid, bindId);
        Integer friendProgress = perfectLoverRedisManager.getUserProgress(friendId, bindId);
        //奖励列表
        List<PerfectLoverIndexVO.Reward> rewards = new ArrayList<>();

        PerfectLoveForwardResponse response = PerfectLoveForwardResponse.builder()
                .build();
        // 处理终点奖励
        if (newProgress.intValue() == PerfectLoverConstant.FINAL_NODE) {
            handleFinalReward(baseParam, uid, friendId, rewards, response);
        } else {
            // 处理节点奖励
            handleNodeReward(baseParam, uid, newProgress.intValue(), bindId, rewards);
        }
        //构建返回结果
        UserVO userVO = userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), uid);
        UserVO friendVO = userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), friendId);
        List<PerfectLoverIndexVO.Route> routes = new ArrayList<>();
        String userSex = null;
        String friendSex = null;
        if (userVO != null) {
            userSex = userVO.getSex().getCode();
        }
        if (friendVO != null) {
            friendSex = friendVO.getSex().getCode();
        }
        routes.add(PerfectLoverIndexVO.Route.builder()
                .sex(userSex)
                .currentNodeId(newProgress.intValue())
                .build());
        routes.add(PerfectLoverIndexVO.Route.builder()
                .sex(friendSex)
                .currentNodeId(friendProgress)
                .build());
        response.setRoutes(routes);
        response.setRewardList(rewards);
        //记录日志
        recordItem(baseParam, rewards, friendId, newProgress);
        //上报埋点
        for(PerfectLoverIndexVO.Reward reward:rewards){
            if(newProgress==PerfectLoverConstant.FINAL_NODE){
                perfectLoverTrackManager.allActivityReceiveAward("sweet_travel",reward.getGiftKey(),reward.getGiftValue(),1,uid);
                perfectLoverTrackManager.allActivityReceiveAward("sweet_travel",reward.getGiftKey(),reward.getGiftValue(),1,friendId);
            }else{
                perfectLoverTrackManager.allActivityReceiveAward("sweet_travel",reward.getGiftKey(),reward.getGiftValue(),1,uid);
            }
        }
        return response;

    }

    /**
     * 处理节点奖励
     *
     * @param baseParam 基础参数
     * @param uid       用户ID
     * @param nodeId    节点ID
     * @param bindId
     * @param rewards
     */
    private void handleNodeReward(BaseParam baseParam, Long uid, Integer nodeId, String bindId, List<PerfectLoverIndexVO.Reward> rewards) {

        // 获取节点奖励
        List<ScenePrizeDO> nodeRewards = perfectLoverRedisManager.getNodeRewards();
        Map<Integer, ScenePrizeDO> nodeRewardMap = nodeRewards.stream()
                .collect(Collectors.toMap(
                        scenePrizeDO -> {
                            JSONObject jsonObject = JSON.parseObject(scenePrizeDO.getExtData());
                            return jsonObject.getInteger("nodeId");
                        },
                        scenePrizeDO -> scenePrizeDO
                ));

        ScenePrizeDO reward = nodeRewardMap.get(nodeId);
        if (reward != null) {
            // 发放奖励
            sendPrizeManager.sendPrize(
                    BaseParam.builder().appId(baseParam.getAppId()).unionId(baseParam.getUnionId()).uid(uid).build(),
                    Collections.singletonList(SendPrizeDTO.of(reward, uid))
            );

            if (nodeId == 4) {
                // 发送通知
                notifyComponent.npcNotify(
                        baseParam.getUnionId(),
                        uid,
                        "恭喜在「满分恋人」活动中成功到达过山车，奖励已经下发，快去查看吧～"
                );
            }

            // 添加到奖励列表
            rewards.add(PerfectLoverIndexVO.Reward.builder()
                    .giftName(reward.getPrizeDesc())
                    .giftValue(reward.getPrizeValueGold())
                    .giftIcon(reward.getPrizeIcon())
                    .giftKey(reward.getPrizeValue())
                    .build());
        }


    }

    private void recordItem(BaseParam baseParam, List<PerfectLoverIndexVO.Reward> rewards, Long friendId, Long newProgress) {
        List<DrawPoolItemDTO> prizeItemList = buildExchangeItemList(rewards, PerfectLoverConstant.MOVE_RECORD, friendId, newProgress);
        logComponent.putDrawLog(BaseParam.builder()
                        .unionId(ServicesAppIdEnum.lanling.getUnionId())
                        .appId(ServicesAppIdEnum.lanling.getAppId())
                        .uid(baseParam.getUid())
                .build(), PerfectLoverConstant.ACTIVITY_CODE, prizeItemList);
        if (newProgress == PerfectLoverConstant.FINAL_NODE&& !rewards.isEmpty()) {
            List<DrawPoolItemDTO> otherRecord = new ArrayList<>();
            DrawPoolItemDO drawPoolItemDO = DrawPoolItemDO.builder()
                    .itemName(String.format("获取%s礼物", rewards.get(0).getGiftName()))
                    .createTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .itemKey("无")
                    .itemNum(1)
                    .itemValueGold(0L)
                    .status(1)
                    .poolCode(PerfectLoverConstant.MOVE_RECORD)
                    .build();
            DrawPoolItemDTO drawPoolItemDTO = DrawPoolItemDTO.builder()
                    .targetTimes(1)
                    .drawPoolItemDO(drawPoolItemDO).build();
            otherRecord.add(drawPoolItemDTO);
            logComponent.putDrawLog(BaseParam.builder()
                    .appId(ServicesAppIdEnum.lanling.getAppId())
                    .unionId(ServicesAppIdEnum.lanling.getUnionId())
                    .uid(friendId)
                    .build(), PerfectLoverConstant.ACTIVITY_CODE, otherRecord);

        }
    }

    private List<DrawPoolItemDTO> buildExchangeItemList(List<PerfectLoverIndexVO.Reward> rewards, String recordCode, Long friendId, Long newProgress) {
        String recordMsg = "消耗520甜蜜值前进1步";
        List<DrawPoolItemDTO> prizeItemList = new ArrayList<>();
        if (rewards == null || rewards.isEmpty()) {
            DrawPoolItemDO drawPoolItemDO = DrawPoolItemDO.builder()
                    .itemName(recordMsg)
                    .createTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .itemKey("无")
                    .itemNum(1)
                    .itemValueGold(0L)
                    .status(1)
                    .poolCode(recordCode)
                    .build();
            DrawPoolItemDTO drawPoolItemDTO = DrawPoolItemDTO.builder()
                    .targetTimes(1)
                    .drawPoolItemDO(drawPoolItemDO).build();
            prizeItemList.add(drawPoolItemDTO);
            return prizeItemList;
        }
        for (PerfectLoverIndexVO.Reward reward : rewards) {
            recordMsg = recordMsg + "，获取%s礼物";
            DrawPoolItemDO drawPoolItemDO = DrawPoolItemDO.builder()
                    .itemName(String.format(recordMsg, reward.getGiftName()))
                    .createTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .itemKey(reward.getGiftKey())
                    .itemNum(1)
                    .itemValueGold(reward.getGiftValue())
                    .status(1)
                    .poolCode(recordCode)
                    .build();
            DrawPoolItemDTO drawPoolItemDTO = DrawPoolItemDTO.builder()
                    .targetTimes(1)
                    .drawPoolItemDO(drawPoolItemDO).build();
            prizeItemList.add(drawPoolItemDTO);
        }
        return prizeItemList;
    }


    /**
     * 处理终点奖励
     *
     * @param baseParam 基础参数
     * @param uid       用户ID
     * @param friendId  好友ID
     * @param rewards
     * @param response
     */
    private void handleFinalReward(BaseParam baseParam, Long uid, Long friendId, List<PerfectLoverIndexVO.Reward> rewards, PerfectLoveForwardResponse response) {
        String bindId = AppUtil.splicUserId(uid, friendId);
        // 检查好友是否也到达终点
        Integer friendProgress = perfectLoverRedisManager.getUserProgress(friendId, bindId);
        if (friendProgress < PerfectLoverConstant.FINAL_NODE) {
            String message = "等待对方到达吧～";
            response.setMessage(message);
            return;
        }

        // 双方都到达终点，检查是否已经领取过终点奖励
/*
        if (perfectLoverRedisManager.isFinalRewardReceived(uid, friendId)) {
            return;
        }
*/

        // 获取终点奖励
        List<ScenePrizeDO> nodeRewards = perfectLoverRedisManager.getNodeRewards();
        Map<Integer, ScenePrizeDO> nodeRewardMap = nodeRewards.stream()
                .collect(Collectors.toMap(
                        scenePrizeDO -> {
                            JSONObject jsonObject = JSON.parseObject(scenePrizeDO.getExtData());
                            return jsonObject.getInteger("nodeId");
                        },
                        scenePrizeDO -> scenePrizeDO
                ));

        ScenePrizeDO finalReward = nodeRewardMap.get(PerfectLoverConstant.FINAL_NODE);
        if (finalReward != null) {
            // 发放奖励给双方
            sendPrizeManager.sendPrize(
                    BaseParam.builder().appId(baseParam.getAppId()).unionId(baseParam.getUnionId()).uid(uid).build(),
                    Collections.singletonList(SendPrizeDTO.of(finalReward, uid))
            );
            sendPrizeManager.sendPrize(
                    BaseParam.builder().appId(baseParam.getAppId()).unionId(baseParam.getUnionId()).uid(friendId).build(),
                    Collections.singletonList(SendPrizeDTO.of(finalReward, friendId))
            );

/*
            // 标记终点奖励已领取
            perfectLoverRedisManager.markFinalRewardReceived(uid, friendId);
*/

            // 发送通知给双方
            String message = "恭喜在「满分恋人」活动中成功登上花车巡游，奖励已经下发至背包，快去查看吧～";
            notifyComponent.npcNotify(baseParam.getUnionId(), uid, message);
            notifyComponent.npcNotify(baseParam.getUnionId(), friendId, message);
            // 添加到奖励列表
            rewards.add(PerfectLoverIndexVO.Reward.builder()
                    .giftName(finalReward.getPrizeDesc())
                    .giftValue(finalReward.getPrizeValueGold())
                    .giftIcon(finalReward.getPrizeIcon())
                    .giftKey(finalReward.getPrizeValue())
                    .build());
        }
    }
}
