package cn.yizhoucp.ump.biz.project.biz.manager.redPacket;

import cn.yizhoucp.ms.core.base.*;
import cn.yizhoucp.ms.core.base.enums.coin.UseCoinScene;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.starter.cassandra.base.kafka.YzKafkaProducerManager;
import cn.yizhoucp.starter.cassandra.base.rocketmq.RocketmqCompatibleProducerManager;
import cn.yizhoucp.starter.cassandra.base.enums.MqDelayLevelEnum;
import cn.yizhoucp.starter.cassandra.base.rocketmq.RocketmqProducerManager;
import cn.yizhoucp.ump.api.vo.redPacket.OpenRedPacketResult;
import cn.yizhoucp.order.api.enums.OrderTypeEnum;
import cn.yizhoucp.order.api.param.addOrder.RedPacketParam;
import cn.yizhoucp.order.api.vo.OrderVO;
import cn.yizhoucp.ump.biz.project.biz.manager.VestChannelManager;
import cn.yizhoucp.ump.biz.project.biz.manager.userCoinAccount.UserCoinAccountManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.FeignAvService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.lanling.FeignLanlingService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.order.AddOrderRemoteService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.FeignUserService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.userAccount.UserAccountFeignService;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.RedPacketInfoJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.RedPacketRecordJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.RedPacketInfoDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.RedPacketRecordDO;
import cn.yizhoucp.ump.biz.project.biz.remoteService.FeignRoomService;
import cn.yizhoucp.ms.core.RedisKey;
import cn.yizhoucp.ms.core.RedissonKey;
import cn.yizhoucp.ms.core.base.config.ThreadPoolConfig;
import cn.yizhoucp.ms.core.base.enums.*;
import cn.yizhoucp.ms.core.base.international.InternationalManager;
import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.vo.CommonResultVO;
import cn.yizhoucp.ms.core.vo.CycleIntervalSetting;
import cn.yizhoucp.ms.core.vo.avservices.AgoraRedPacketAttrs;
import cn.yizhoucp.ms.core.vo.avservices.AgoraRestfulMessageData;
import cn.yizhoucp.ms.core.vo.coinservices.*;
import cn.yizhoucp.ms.core.vo.userservices.UserBaseVO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.biz.project.dto.redPacket.OpenCheckDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static cn.yizhoucp.ump.biz.project.biz.constant.redPacket.RedPacketConstant.*;

/**
 * 红包
 *
 * @author: lianghu
 */
@Slf4j
@Service
public class RedPacketManager {

    private static final String HAS_SEND_PACKET = "ump:redPacket:has:send";

    @Resource
    private RedissonClient redissonClient;
    @Resource
    @Lazy
    private RedPacketInfoJpaDAO redPacketInfoJpaDAO;
    @Resource
    @Lazy
    private RedPacketRecordJpaDAO redPacketRecordJpaDAO;
    @Resource
    private RedisManager redisManager;
    @Resource
    private UserAccountFeignService userAccountFeignService;

    @Resource
    private UserCoinAccountManager userCoinAccountManager;

    @Resource
    @Lazy
    FeignRoomService feignRoomService;

    @Resource
    @Lazy
    FeignAvService feignAvService;

    @Resource
    @Lazy
    private TransactionTemplate transactionTemplate;
    @Resource
    @Lazy
    private InternationalManager internationalManager;
    @Resource
    @Lazy
    private FeignUserService feignUserService;
    @Resource
    @Lazy
    private VestChannelManager vestChannelManager;

    @Resource
    @Lazy
    RocketmqCompatibleProducerManager rocketmqCompatibleProducerManager;

    @Resource
    private RocketmqProducerManager rocketmqProducerManager;

    @Resource
    private YzKafkaProducerManager yzKafkaProducerManager;

    @Resource
    @Lazy
    FeignLanlingService feignLanlingService;

    @Resource
    private AddOrderRemoteService addOrderRemoteService;
    @Resource
    private RedPacketAsyncManager redPacketAsyncManager;

    /**
     * 用户发送的指定类型红包次数
     *
     * @param userId
     * @param type
     * @return
     */
    public Integer userSendRedPacketNum(Long userId, Integer type) {
        if (null == userId || null == type) {
            return null;
        }
        return redPacketInfoJpaDAO.countByUserIdAndType(userId, type);
    }

    /**
     * 检查是否发送过免费红包
     * true 发送过
     * false 没有发送过
     *
     * @param userId
     * @param type
     * @param deviceId
     * @return
     */
    public Boolean hasSendFreshManRedPacket(Long userId, Integer type, String deviceId) {
        log.debug("hasSendFreshManRedPacket Enter userId : {}, type : {}, deviceId : {}", userId, type, deviceId);

        if (null == userId || null == type || StringUtils.isEmpty(deviceId)) {
            return true;
        }
        String itemKey = deviceId.concat("_").concat(userId.toString());
        if (redisManager.hHasKey(HAS_SEND_PACKET, itemKey)) {
            return true;
        }

        RedPacketInfoDO item = redPacketInfoJpaDAO.findByUserIdAndTypeAndDeviceId(userId, type, deviceId);
        log.debug("hasSendFreshManRedPacket item : {}", item);
        if (Objects.isNull(item)) {
            return false;
        }
        redisManager.hset(HAS_SEND_PACKET, itemKey, Boolean.TRUE);
        return true;
    }

    /**
     * 发红包
     *
     * @param type                 红包类型   1-家族红包 2-广场红包
     * @param title                红包主题
     * @param num                  红包总个数
     * @param coinAmount           红包金额
     * @param toOtherId            发送场景id，可以是familyId,userId
     * @param canGrabGroupType     可抢人群类型，all - 所有人可抢 follow - 关注
     * @param canGrabTimeType      可抢时间类型，immediately - 立即可抢 delay - 延迟可抢
     * @param canGrabTimestamp     可抢时间戳
     * @param cycleIntervalSetting 循环间隔红包配置参数(间隔时间:interval（Integer, 3、5、10、20表示对应间隔分钟数,默认5分钟）,发送金币数：coinNum(Long, 最低10金币),红包个数:redPacketNum(Long, 最多为金币填写的数量),发送次数:sendCount(Long, 默认50次))
     * @return SendRedPacketResultd
     */
    public SendRedPacketResult sendRedPacket(BaseParam param, Integer type, String title, Integer num, Integer coinAmount, Long toOtherId,
                                             String canGrabGroupType, String canGrabTimeType, Long canGrabTimestamp,
                                             String cycleIntervalSetting, AppScene appScene) {
        appScene = Objects.isNull(appScene) ? AppScene.family : appScene;
        UserBaseVO user = feignUserService.getUserWithoutStatusBaseVO(param.getAppId(), param.getUid()).successData();

        boolean needLock = RedPacketType.family_join.getType().equals(type);
        RLock lock = null;
        if (needLock) {
            lock = redissonClient.getLock("family_join_red_packet_lock_" + param.getUid());
        }

        OrderVO orderVO = null;
        RedPacketInfoDO redPacketDO;
        try {
            if (lock != null) {
                lock.lock();
            }
            // 判断是否为免费类型
            Boolean free = isFree(param, type, user.getDeviceId());

            // 下单
            if (!free) {
                OrderTypeEnum orderType = RedPacketType.family_join.getType().equals(type) ? OrderTypeEnum.FAMILY_JOIN_RED_PACKET_ORDER : OrderTypeEnum.RED_PACKET_ORDER;
                orderVO = addOrderRemoteService.addRedPacketOrder(RedPacketParam.builder()
                        .appId(param.getAppId())
                        .unionId(param.getUnionId())
                        .uid(param.getUid())
                        .orderType(orderType.getType())
                        .coin(coinAmount.longValue())
                        .memo(getSendPacketMemo(toOtherId, type))
                        .appScene(appScene)
                        .appFunction(AppFunctionEnum.red_envelope).build(), null);
                // 金币不足
                if (Objects.isNull(orderVO) || StringUtils.isEmpty(orderVO.getWalletMsgId())) {
                    // 余额 & 充值弹窗
                    UserAccountVO userAccount = userAccountFeignService.getUserAccountByUid(param.getAppId(), param.getUid()).successData();
                    return SendRedPacketResult.builder()
                            .result(Boolean.FALSE)
                            .from(getSendPacketFrom(type))
                            .flag(RedPacketFlag.COIN_NOT_ENOUGH.getCode())
                            .userBalance(UserBalance.builder()
                                    .allBalance(userAccount.getBalance() + userAccount.getFreeBalance())
                                    .feeBalance(userAccount.getBalance())
                                    .freeBalance(userAccount.getFreeBalance()).build())
                            .build();
                }
            }

            // 保存红包记录
            Date now = new Date();
            redPacketDO = redPacketInfoJpaDAO.save(RedPacketInfoDO.builder().appId(param.getAppId()).userId(param.getUid()).amount(coinAmount).createTimestamp(System.currentTimeMillis())
                    .num(num).remainingAmount(coinAmount).remainingPacket(num).title(title).type(type).toOtherId(toOtherId).canGrabGroupType(canGrabGroupType)
                    .canGrabTimeType(canGrabTimeType).canGrabTimestamp(canGrabTimestamp).status(RedPacketStatus.IN_PROGRESS.getCode()).deviceId(user.getDeviceId())
                    .cycleIntervalSetting(cycleIntervalSetting).createTime(now).updateTime(now).build());

        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        // 生成红包奖金池
        List<Object> pool = generateAmountPool(redPacketDO.getAmount(), redPacketDO.getNum());
        redisManager.lSet(String.format(RED_PACKET_COIN_POOL, redPacketDO.getId()), pool, RedisManager.ONE_DAY_SECONDS * 3, TimeUnit.SECONDS);
        log.info("sendRedPacket 生成红包池 redPacketId:{}, pool:{}", redPacketDO.getId(), pool);


        // 设置红包业务缓存
        redisManager.set(String.format(RED_PACKET_SIZE, redPacketDO.getId()), num, DateUtils.MILLIS_PER_DAY / 1000);
        if (Objects.nonNull(orderVO)) {
            redisManager.set(String.format(RED_PACKAGE_ORDER_MSG_KEY, redPacketDO.getId()), orderVO.getWalletMsgId(), RedisManager.ONE_DAY_SECONDS + RedisManager.ONE_HOUR_SECONDS);
        }

        // 发送发红包事件
        JSONObject eventParam = new JSONObject();
        eventParam.put("uid", param.getUid());
        rocketmqProducerManager.sendNormalMessage(TopicConstant.TOPIC_TRADE_EVENT.getTopicKey(), TopicTagEnum.TOPIC_SEND_RED_PACKET.getTagKey(), JSON.toJSONString(eventParam), System.currentTimeMillis() + "");

        // 添加发红包埋点
        trackSendRedPacket(param, type, coinAmount, toOtherId, orderVO, appScene);

        return SendRedPacketResult.builder()
                .result(Boolean.TRUE)
                .title(title)
                .redPacketId(redPacketDO.getId()).build();
    }

    /**
     * 判断红包状态
     * <p>
     *
     * @param redPacketId 红包id
     * @return
     */
    public CheckRedPacketResult checkRedPacket(Long redPacketId) {
        CheckRedPacketResult checkRedPacketResult = new CheckRedPacketResult();
        checkRedPacketResult.setResult(true);
        BaseParam param = BaseParam.ofMDC();

        // 当前用户是否已抢过（cache）
        Double coin = redisManager.score(String.format(OPEN_RED_PACKET_RANK, redPacketId), param.getUid().toString());
        if (Objects.nonNull(coin)) {
            checkRedPacketResult.setResult(false);
            checkRedPacketResult.setFlag(RedPacketFlag.ALREADY_GRABBED.getCode());
            Object[] params = new Object[]{String.valueOf(coin)};
            String text = internationalManager.changeCommonText(params, "splice.get.coin");
            checkRedPacketResult.setMessage(text);
            checkRedPacketResult.setAmount(coin.intValue());
            return checkRedPacketResult;
        }

        // 验证红包是否过期
        Integer leftTimes = redisManager.getInteger(String.format(RED_PACKET_SIZE, redPacketId));
        if (Objects.isNull(leftTimes) && expired(redPacketId)) {
            RedPacketFlag expired = RedPacketFlag.EXPIRED;
            checkRedPacketResult.setResult(false);
            checkRedPacketResult.setFlag(expired.getCode());
            String text = internationalManager.changeCommonText(null, "simple.red.packet.expired.desc");
            String expiredDesc = ServicesAppIdEnum.chatie.getAppId().equals(param.getAppId()) ? text : expired.getDesc();
            checkRedPacketResult.setMessage(expiredDesc);
            return checkRedPacketResult;
        }

        // 当前红包是否已抢完（缓存异常清空时进入该流程兜底）
        if (Optional.ofNullable(leftTimes).orElse(0) < 1) {
            RedPacketFlag lootAll = RedPacketFlag.LOOT_ALL;
            checkRedPacketResult.setResult(false);
            checkRedPacketResult.setFlag(lootAll.getCode());
            String text = internationalManager.changeCommonText(null, "simple.red.packet.loot.all.desc");
            String lootAllDesc = ServicesAppIdEnum.chatie.getAppId().equals(param.getAppId()) ? text : lootAll.getDesc();
            checkRedPacketResult.setMessage(lootAllDesc);
            return checkRedPacketResult;
        }
        return checkRedPacketResult;
    }

    private Boolean expired(Long redPacketId) {
        // 验证红包是否过期
        Optional<RedPacketInfoDO> opt = redPacketInfoJpaDAO.findById(redPacketId);
        if (!opt.isPresent()) {
            throw new ServiceException(ErrorCode.COIN_PARAM_ERROR);
        }
        if (Objects.equals(RedPacketStatus.EXPIRED_RETURNED.getCode(), opt.get().getStatus())
                || Objects.equals(RedPacketStatus.EXPIRED_NOT_RETURNED.getCode(), opt.get().getStatus())) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 开红包
     *
     * @param redPacketId 红包id
     * @param toOtherId   发送场景id，可以是familyId,userId
     * @return OpenRedPacketResult
     */
    public OpenRedPacketResult openRedPacket(BaseParam param, Long redPacketId, Long toOtherId, AppScene appScene) {
        log.info("uid {} redPacketId {} toOtherId {} appScene {}", param.getUid(), redPacketId, toOtherId, appScene);
        if (Objects.isNull(redPacketId) || Objects.isNull(toOtherId)) {
            throw new ServiceException(ErrorCode.MISS_PARAM);
        }

        // 业务验证
        OpenCheckDTO openCheckDTO = openRedPacketCheck(param, redPacketId);
        if (Objects.nonNull(openCheckDTO.getResult())) {
            return openCheckDTO.getResult();
        }
        RedPacketInfoDO redPacket = Optional.ofNullable(openCheckDTO.getRedPacketInfoDO()).orElse(redPacketInfoJpaDAO.findById(redPacketId).get());

        // 获取本次红包金额
        Integer coin = (Integer) redisManager.listLeftPop(String.format(RED_PACKET_COIN_POOL, redPacketId));
        if (Objects.isNull(coin)) {
            log.error("redPacket 异常分支 redPacketId:{} uid:{}", redPacketId, param.getUid());
            return finishResult(param);
        }
        redisManager.zIncrby(String.format(OPEN_RED_PACKET_RANK, redPacketId), param.getUid().toString(), coin.doubleValue(), RedisManager.ONE_DAY_SECONDS * 3);
        log.info("openRedPacket 抢到红包金额 redPacketId:{}, uid:{}, coin:{}", redPacketId, param.getUid(), coin);

        // 关闭红包
        Long luckyUid = null;
        Integer status = redPacket.getStatus();
        if (openCheckDTO.getLeftTimes() < 1) {
            status = RedPacketStatus.GRAB_FINISH.getCode();
            luckyUid = getLuckyUid(redPacketId);
            log.info("openRedPacket 红包已抢完 redPacketId:{}, luckyUid:{}", redPacketId, luckyUid);
        }

        // 异步处理
        redPacketAsyncManager.openRedPacketAsyncHandle(param, redPacket, coin, status, toOtherId, appScene);

        redPacketAsyncManager.openRedPacketInSecond(param.getAppId(), param.getUid(), RedPacketCanGrabTimeType.immediately.getCode().equals(redPacket.getCanGrabTimeType()) ? redPacket.getCreateTimestamp() : redPacket.getCanGrabTimestamp());

        // 添加开红包埋点
        trackOpenRedPacket(param, redPacket, coin, toOtherId, appScene);

        // 返回结果
        return successResult(param, redPacket.getUserId(), coin, luckyUid);
    }

    /**
     * 计算本次红包金额
     * 红包取值随机数，不超过最大金额
     *
     * @param amount          剩余金额
     * @param maxMoney        最大金额
     * @param num             剩余数量
     * @param redPacketInfoDO 红包信息
     * @return int
     */
    private int calculateRedAmount(int amount, int maxMoney, int num, RedPacketInfoDO redPacketInfoDO) {
        int randomAmount;
        if (num > 1) {
            int chose = new Random().nextInt(100);
            if (chose < 90) {
                randomAmount = avgRedPacket(maxMoney, num, redPacketInfoDO.getAmount(), redPacketInfoDO.getNum());
            } else {
                randomAmount = randomRedPacket(maxMoney, redPacketInfoDO.getAmount());
            }
        } else {
            // 如果是最后一个红包，金额就是剩下的所有钱
            randomAmount = amount;
        }
        if (randomAmount < 0) {
            randomAmount = 0;
        }
        return randomAmount;
    }


    /**
     * 获取红包领取记录
     *
     * @param redPacketId
     * @return
     */
    public List<RedPacketRecordVO> redPacketRecordDetails(Long redPacketId) {
        List<RedPacketRecordVO> result = Lists.newArrayList();
        Optional<RedPacketInfoDO> optional = redPacketInfoJpaDAO.findById(redPacketId);
        if (optional == null) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "simple.red.packet.no.exist");
        }
        //查询红包领取记录 转换VO
        List<RedPacketRecordDO> recordDOS = redPacketRecordJpaDAO.findByRedPacketIdOrderByCreateTimeDesc(redPacketId);
        if (CollectionUtils.isEmpty(recordDOS)) {
            return result;
        }
        recordDOS.stream().forEach(redPacketRecordDO -> {
            RedPacketRecordVO redPacketRecordVO = new RedPacketRecordVO();
            BeanUtils.copyProperties(redPacketRecordDO, redPacketRecordVO);
            result.add(redPacketRecordVO);
        });
        return result;
    }


    /**
     * 查询语音房的红包房间(进行中 )
     *
     * @param type            红包类型
     * @param status          1-进行中 2-已抢完 3-过期
     * @param canGrabTimeType delay or immediately
     * @return List<RedPacketInfoVO>
     * @see RedPacketType
     * @see RedPacketCanGrabGroupType
     */
    public List<RedPacketInfoVO> batchRedPacketListOfVoiceRoom(Integer type, Integer status, String canGrabTimeType) {
        if (StringUtils.isEmpty(status) || StringUtils.isEmpty(type) || StringUtils.isEmpty(canGrabTimeType)) {
            return null;
        }
        List<RedPacketInfoVO> redPacketInfoVOS = new ArrayList<>();
        List<RedPacketInfoDO> redPacketInfoDOS = redPacketInfoJpaDAO.findByTypeAndStatusAndCanGrabTimeTypeAndCurrentTime(type, status, canGrabTimeType, System.currentTimeMillis(), System.currentTimeMillis());
        log.debug("打印时间 ---- {}", System.currentTimeMillis());
        log.debug("打印相关的参数 --- 语音房红包列表 type: {} 语音房红包的类型 status: {} 语音房红包可抢的时机 canGrabTimeType：{} ", type, status, canGrabTimeType);
        log.debug("打印相关的结果  --- > {}", redPacketInfoDOS);
        if (!CollectionUtils.isEmpty(redPacketInfoDOS)) {
            for (RedPacketInfoDO redPacketInfoDO : redPacketInfoDOS) {
                RedPacketInfoVO redPacketInfoVO = new RedPacketInfoVO();
                BeanUtils.copyProperties(redPacketInfoDO, redPacketInfoVO);
                redPacketInfoVOS.add(redPacketInfoVO);
            }
        }
        // 如果是语音房还得获取有自动循环间隔红包的房间列表
        if (RedPacketType.room.getType().equals(type)) {
            List<RedPacketInfoDO> redPacketInfoDOS2 = redPacketInfoJpaDAO.findByTypeAndStatusAndCanGrabTimeTypeAndCurrentTime(RedPacketType.room_cycle_interval.getType(), status, canGrabTimeType, System.currentTimeMillis(), System.currentTimeMillis());
            if (!CollectionUtils.isEmpty(redPacketInfoDOS2)) {
                for (RedPacketInfoDO redPacketInfoDO : redPacketInfoDOS2) {
                    RedPacketInfoVO redPacketInfoVO = new RedPacketInfoVO();
                    BeanUtils.copyProperties(redPacketInfoDO, redPacketInfoVO);
                    redPacketInfoVOS.add(redPacketInfoVO);
                }
            }
        }

        return redPacketInfoVOS;
    }

    /**
     * 获取单个红包的VO
     *
     * @param redPacketId
     * @return
     */
    public RedPacketInfoVO getRedPacketInfoVO(Long redPacketId) {
        List<RedPacketRecordVO> result = Lists.newArrayList();
        Optional<RedPacketInfoDO> optional = redPacketInfoJpaDAO.findById(redPacketId);
        if (!optional.isPresent()) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "simple.red.packet.no.exist");
        }
        RedPacketInfoDO redPacketInfoDO = optional.get();
        RedPacketInfoVO redPacketInfoVO = new RedPacketInfoVO();
        BeanUtils.copyProperties(redPacketInfoDO, redPacketInfoVO);
        return redPacketInfoVO;
    }


    /**
     * 处理过期且没有抢完的红包
     * <p>
     * todo: @梁胡
     *
     * @return Boolean
     */
    @Async(ThreadPoolConfig.ASYNC_EXECUTOR_NAME)
    public Boolean handleExpireRedPacket(Long appId) {
        if (appId == null) {
            appId = ServicesAppIdEnum.lanling.getAppId();
        }
        long time = DateUtil.getNDaysBefore(1).getTime();
        log.info("RedPacketManager handleExpireRedPacket start. time {}", time);
        List<RedPacketInfoDO> statusRedPacketByBeforeTime = redPacketInfoJpaDAO.findStatusRedPacketByBeforeTime(RedPacketStatus.IN_PROGRESS.getCode(), time);
        log.debug("RedPacketManager handleExpireRedPacket list size {}. time {}", statusRedPacketByBeforeTime.size(), time);
        if (CollectionUtils.isEmpty(statusRedPacketByBeforeTime)) {
            log.debug("RedPacketManager handleExpireRedPacket list is null. time {}", time);
            return true;
        }


        //过滤 saka 新人红包
        List<RedPacketInfoDO> redPacketInfoDOS = filterFreeFreshManRedPacket(appId, statusRedPacketByBeforeTime);

        Long finalAppId = appId;
        redPacketInfoDOS.forEach(redPacketInfoDO -> {
            Long redPacketId = redPacketInfoDO.getId();
            String lockKey = RedissonKey.COIN_GRAB_RED_PACKET.getKey() + redPacketId;
            RLock lock = redissonClient.getLock(lockKey);
            try {
                lock.lock();
                transactionTemplate.execute(status -> {
                    Integer remainingAmount = redPacketInfoDO.getRemainingAmount();
                    Long userId = redPacketInfoDO.getUserId();
                    log.debug("RedPacketManager handleExpireRedPacket handle one info.redPacketInfoDO {} time {}", JSON.toJSONString(redPacketInfoDO), time);
                    if (remainingAmount > 0) {
                        // 给用户加钱
                        UserAccountVO userAccount = userAccountFeignService.getUserAccountByUid(finalAppId, userId).successData();

                        if (Objects.isNull(userAccount) || Objects.isNull(userAccount.getId())) {
                            log.error("handleExpireRedPacket error , appId : {}, userId : {}, redPacket : {}", finalAppId, userId, JSON.toJSONString(redPacketInfoDO));
                            throw new ServiceException(ErrorCode.COIN_ACCOUNT_NOT_EXITS);
                        }
                        // 退钱之后的余额
                        Long afterCoin = userAccount.getBalance() + userAccount.getFreeBalance() + remainingAmount;
                        Long accountId = userAccount.getId();
                        AppScene appScene = RedPacketType.findAppSceneByType(redPacketInfoDO.getType());
//                        userAccountRemoteService.refreshUserAccount(null, finalAppId, userId, accountId, remainingAmount.longValue(), 0L,
//                                AccountHandleType.COIN_BACK, appScene, AppFunctionEnum.red_envelope_return);
                        String msgIdKey = String.format(RED_PACKAGE_ORDER_MSG_KEY, redPacketId);
                        Object o = redisManager.get(msgIdKey);
                        String walletmsgId = null;
                        if (null != o) {
                            walletmsgId = o.toString();
                        }
                        Boolean returnResult = userCoinAccountManager.coinBack(null, finalAppId, userId,
                                appScene, AppFunctionEnum.red_envelope_return.getCode(),
                                remainingAmount.longValue(), 0L, "", "", "红包退款 " + remainingAmount.longValue() + " 金币", walletmsgId, null);
                        // 生成金币流水
                        Long randomAmountLong = Long.parseLong(remainingAmount + "");
                        Object[] params = new Object[]{String.valueOf(redPacketId)};

                        vestChannelManager.setUserInternational(userId, finalAppId);

                        String memo = internationalManager.changeCommonText(params, "splice.red.packet.expired");

                        UseCoinScene scene = getUseCoinSceneByType(redPacketInfoDO.getType());
                        log.debug("RedPacketManager saveCoinLog userId : {}, redPacketId : {}, memo : {} scene {}", userId, redPacketId, memo, scene);
                    }

                    clearRedPacketCache(redPacketInfoDO.getId().toString());
                    redPacketInfoDO.setStatus(RedPacketStatus.EXPIRED_RETURNED.getCode());
                    redPacketInfoJpaDAO.save(redPacketInfoDO);
                    log.debug("RedPacketManager handleExpireRedPacket handle one update success.redPacketInfoDO {} time {}", JSON.toJSONString(redPacketInfoDO), time);
                    return true;
                });
            } catch (Exception e) {
                log.error("RedPacketManager handleExpireRedPacket is error. redPacketInfoDO {} ,e {}", JSON.toJSONString(redPacketInfoDO), e);
            } finally {
                lock.unlock();
            }
        });
        log.info("RedPacketManager handleExpireRedPacket had finish. time {}", time);
        return true;
    }

    private void clearRedPacketCache(String packetId) {
        if (!StringUtils.isEmpty(packetId)) {
            redisManager.delete(String.format(RED_PACKET_SIZE, packetId));
        }
    }

    private List<RedPacketInfoDO> filterFreeFreshManRedPacket(Long appId, List<RedPacketInfoDO> statusRedPacketByBeforeTime) {
        List<RedPacketInfoDO> freeFreshManRedPackets = new ArrayList<>();
        List<RedPacketInfoDO> redPacketInfoDOS;

        if (ServicesAppIdEnum.chatie.getAppId().equals(appId)) {

            redPacketInfoDOS = new ArrayList<>(statusRedPacketByBeforeTime.size());
            //处理 saka 免费的新人红包
            for (RedPacketInfoDO item : statusRedPacketByBeforeTime) {
                if (item.getAmount() == 3) {
                    item.setStatus(RedPacketStatus.EXPIRED_RETURNED.getCode());
                    freeFreshManRedPackets.add(item);
                } else {
                    redPacketInfoDOS.add(item);
                }
            }

            log.info("RedPacketManager handleExpireRedPacket save freeFreshManRedPackets : {}", JSON.toJSONString(freeFreshManRedPackets));
            freeFreshManRedPackets.forEach(item -> {
                clearRedPacketCache(item.getId().toString());
            });

            redPacketInfoJpaDAO.saveAll(freeFreshManRedPackets);
        } else {
            redPacketInfoDOS = statusRedPacketByBeforeTime;
        }

        return redPacketInfoDOS;
    }

    /**
     * 获取多长时间以前的红包数量
     *
     * @param type      红包类型
     * @param toOtherId 场景id
     * @param time      多少时间之前的数量(毫秒)
     * @return
     * @see RedPacketType
     */
    public Integer getNumBeforeTime(Integer type, Long toOtherId, Long time) {

        if (type == null || toOtherId == null || time == null) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }

        long timeBefore = System.currentTimeMillis() - time;
        SimpleDateFormat format = new SimpleDateFormat(DateUtil.YMDHMS);
        String nowFormat = format.format(new Date());
        String beforeFormat = format.format(new Date(timeBefore));

        List<RedPacketInfoDO> redPacketInfoDOS = redPacketInfoJpaDAO.findByTypeAndToOtherIdAndCreateTime(type, toOtherId, nowFormat, beforeFormat);
        if (CollectionUtils.isEmpty(redPacketInfoDOS)) {
            return 0;
        }

        return redPacketInfoDOS.size();
    }

    /**
     * 红包列表查询 - 目前只有语音房使用
     *
     * @param status           1-进行中 2-已抢完 3-过期
     * @param type             红包类型 @see cn.yizhoucp.ms.core.base.enums.RedPacketType
     * @param toOtherId        家族id、用户id、语音房id
     * @param canGrabTimeType  可抢时间类型 @see RedPacketCanGrabTimeType
     * @param canGrabTimestamp 可抢时间戳（毫秒）
     * @return
     */
    public List<RedPacketInfoVO> redPacketList(Integer status, Integer type, Long toOtherId, String canGrabTimeType, Long canGrabTimestamp) {

        if (StringUtils.isEmpty(status) || StringUtils.isEmpty(type) || toOtherId == null || StringUtils.isEmpty(canGrabTimeType) || StringUtils.isEmpty(canGrabTimestamp)) {
            return null;
        }

        List<RedPacketInfoVO> redPacketInfoVOS = new ArrayList<>();
        List<RedPacketInfoDO> redPacketInfoDOS = redPacketInfoJpaDAO.findByStatusAndTypeAndToOtherIdAndCanGrabTimeTypeAndCanGrabTimestampGreaterThanEqual(status, type, toOtherId, canGrabTimeType, canGrabTimestamp);
        if (!CollectionUtils.isEmpty(redPacketInfoDOS)) {
            for (RedPacketInfoDO redPacketInfoDO : redPacketInfoDOS) {
                RedPacketInfoVO redPacketInfoVO = new RedPacketInfoVO();
                BeanUtils.copyProperties(redPacketInfoDO, redPacketInfoVO);
                redPacketInfoVOS.add(redPacketInfoVO);
            }
        }
        // 如果是语音房还得获取有自动循环间隔红包的房间列表
        if (RedPacketType.room.getType().equals(type)) {
            List<RedPacketInfoDO> redPacketInfoDOS2 = redPacketInfoJpaDAO.findByStatusAndTypeAndToOtherIdAndCanGrabTimeTypeAndCanGrabTimestampGreaterThanEqual(status, RedPacketType.room_cycle_interval.getType(), toOtherId, canGrabTimeType, canGrabTimestamp);
            if (!CollectionUtils.isEmpty(redPacketInfoDOS2)) {
                for (RedPacketInfoDO redPacketInfoDO : redPacketInfoDOS2) {
                    RedPacketInfoVO redPacketInfoVO = new RedPacketInfoVO();
                    BeanUtils.copyProperties(redPacketInfoDO, redPacketInfoVO);
                    redPacketInfoVOS.add(redPacketInfoVO);
                }
            }
        }

        return redPacketInfoVOS;
    }

    /**
     * 红包列表 根据otherId、type、status查询
     *
     * @param otherId 家族id、用户id、语音房id
     * @param type    红包类型 @see cn.yizhoucp.ms.core.base.enums.RedPacketType
     * @param status  1-进行中 2-已抢完 3-过期
     * @return List<RedPacketInfoVO>
     */
    public List<RedPacketInfoVO> redPacketListByOtherIdAndTypeAndStatus(Long otherId, Integer type, Integer status) {
        log.debug("otherId: {} type: {} status: {}", otherId, type, status);
        List<RedPacketInfoVO> packetInfoList = new ArrayList<>();
        if (null == otherId || null == type || null == status) {
            return packetInfoList;
        }
        List<RedPacketInfoDO> redPacketInfoDOList = redPacketInfoJpaDAO.findByToOtherIdAndTypeAndStatus(otherId, type, status);
        if (!CollectionUtils.isEmpty(redPacketInfoDOList)) {
            for (RedPacketInfoDO redPacketInfoDO : redPacketInfoDOList) {
                RedPacketInfoVO redPacketInfoVO = new RedPacketInfoVO();
                BeanUtils.copyProperties(redPacketInfoDO, redPacketInfoVO);
                packetInfoList.add(redPacketInfoVO);
            }
        }
        // 如果是语音房还得获取有自动循环间隔红包的房间列表
        if (RedPacketType.room.getType().equals(type)) {
            List<RedPacketInfoDO> redPacketInfoDOList2 = redPacketInfoJpaDAO.findByToOtherIdAndTypeAndStatus(otherId, RedPacketType.room_cycle_interval.getType(), status);
            if (!CollectionUtils.isEmpty(redPacketInfoDOList2)) {
                for (RedPacketInfoDO redPacketInfoDO : redPacketInfoDOList2) {
                    RedPacketInfoVO redPacketInfoVO = new RedPacketInfoVO();
                    BeanUtils.copyProperties(redPacketInfoDO, redPacketInfoVO);
                    packetInfoList.add(redPacketInfoVO);
                }
            }
        }
        return packetInfoList;
    }

    /**
     * 批量获取红包列表 根据otherId、type、status查询
     *
     * @param otherIds 家族id、用户id、语音房id(JSON格式)
     * @param type     红包类型 @see cn.yizhoucp.ms.core.base.enums.RedPacketType
     * @param status   1-进行中 2-已抢完 3-过期
     * @return List<RedPacketInfoVO>
     */
    public List<RedPacketInfoVO> batchRedPacketList(String otherIds, Integer type, Integer status, String canGrabTimeType, Long canGrabTimestamp) {
        log.debug("otherIds: {} type: {} status: {} canGrabTimeType:{} canGrabTimestamp:{}", otherIds, type, status, canGrabTimeType, canGrabTimestamp);
        List<RedPacketInfoVO> packetInfoList = new ArrayList<>();
        if (null == otherIds || null == type || null == status) {
            return packetInfoList;
        }
        List<Long> idList = JSON.parseArray(otherIds, Long.class);
        List<RedPacketInfoDO> redPacketInfoDOList = redPacketInfoJpaDAO.findByToOtherIdInAndTypeAndStatus(idList, type, status, canGrabTimeType, canGrabTimestamp);
        if (!CollectionUtils.isEmpty(redPacketInfoDOList)) {
            for (RedPacketInfoDO redPacketInfoDO : redPacketInfoDOList) {
                RedPacketInfoVO redPacketInfoVO = new RedPacketInfoVO();
                BeanUtils.copyProperties(redPacketInfoDO, redPacketInfoVO);
                packetInfoList.add(redPacketInfoVO);
            }
        }
        // 如果是语音房还得获取有自动循环间隔红包的房间列表
        if (RedPacketType.room.getType().equals(type)) {
            List<RedPacketInfoDO> redPacketInfoDOList2 = redPacketInfoJpaDAO.findByToOtherIdInAndTypeAndStatus(idList, RedPacketType.room_cycle_interval.getType(), status, canGrabTimeType, canGrabTimestamp);
            if (!CollectionUtils.isEmpty(redPacketInfoDOList2)) {
                for (RedPacketInfoDO redPacketInfoDO : redPacketInfoDOList2) {
                    RedPacketInfoVO redPacketInfoVO = new RedPacketInfoVO();
                    BeanUtils.copyProperties(redPacketInfoDO, redPacketInfoVO);
                    packetInfoList.add(redPacketInfoVO);
                }
            }
        }
        return packetInfoList;
    }

    /**
     * 红包列表 根据type、status查询
     *
     * @param type   红包类型 @see cn.yizhoucp.ms.core.base.enums.RedPacketType
     * @param status 1-进行中 2-已抢完 3-过期
     * @return List<RedPacketInfoVO>
     */
    public List<RedPacketInfoVO> redPacketListByTypeAndStatus(Integer type, Integer status, String canGrabTimeType, Long canGrabTimestamp) {
        log.debug("type: {} status: {} canGrabTimeType:{} canGrabTimestamp:{}", type, status, canGrabTimeType, canGrabTimestamp);
        List<RedPacketInfoVO> packetInfoList = new ArrayList<>();
        if (null == type || null == status) {
            return packetInfoList;
        }
        List<RedPacketInfoDO> redPacketInfoDOList = redPacketInfoJpaDAO.findByTypeAndStatusAndCanGrabTimeType(type, status, canGrabTimeType, canGrabTimestamp);
        if (!CollectionUtils.isEmpty(redPacketInfoDOList)) {
            for (RedPacketInfoDO redPacketInfoDO : redPacketInfoDOList) {
                RedPacketInfoVO redPacketInfoVO = new RedPacketInfoVO();
                BeanUtils.copyProperties(redPacketInfoDO, redPacketInfoVO);
                packetInfoList.add(redPacketInfoVO);
            }
        }
        // 如果是语音房还得获取有自动循环间隔红包的房间列表
        if (RedPacketType.room.getType().equals(type)) {
            List<RedPacketInfoDO> redPacketInfoDOList2 = redPacketInfoJpaDAO.findByTypeAndStatusAndCanGrabTimeType(RedPacketType.room_cycle_interval.getType(), status, canGrabTimeType, canGrabTimestamp);
            if (!CollectionUtils.isEmpty(redPacketInfoDOList2)) {
                for (RedPacketInfoDO redPacketInfoDO : redPacketInfoDOList2) {
                    RedPacketInfoVO redPacketInfoVO = new RedPacketInfoVO();
                    BeanUtils.copyProperties(redPacketInfoDO, redPacketInfoVO);
                    packetInfoList.add(redPacketInfoVO);
                }
            }
        }
        return packetInfoList;
    }


    /**
     * 通过 ${redPacketId} 获取红包
     *
     * @param redPacketId
     * @return
     */
    public RedPacketInfoVO getById(Long redPacketId) {

        if (redPacketId == null) {
            return null;
        }

        RedPacketInfoVO redPacketInfoVO = new RedPacketInfoVO();
        RedPacketInfoDO redPacketInfoDO = redPacketInfoJpaDAO.findById(redPacketId).get();
        BeanUtils.copyProperties(redPacketInfoDO, redPacketInfoVO);

        return redPacketInfoVO;
    }

    /**
     * 发送语音房循环间隔自动红包
     *
     * @param type                 红包类型   1-家族红包 2-广场红包(@See cn.yizhoucp.ms.core.base.enums.RedPacketType)
     * @param title                红包主题
     * @param num                  红包总个数
     * @param coinAmount           红包金额
     * @param userId               房主ID
     * @param toOtherId            发送场景id，可以是familyId,userId,roomId
     * @param canGrabGroupType     可抢人群类型，all - 所有人可抢 follow - 关注
     * @param canGrabTimeType      可抢时间类型，immediately - 立即可抢 delay - 延迟可抢
     * @param canGrabTimestamp     可抢时间戳
     * @param cycleIntervalSetting 循环间隔红包配置参数(间隔时间:interval（Integer, 3、5、10、20表示对应间隔分钟数,默认5分钟）,发送金币数：coinNum(Long, 最低10金币),红包个数:redPacketNum(Long, 最多为金币填写的数量),发送次数:sendCount(Long, 默认50次),startId(起始ID）)
     * @return
     */
    public CommonResultVO sendCycleIntervalRedPacket(Integer type, String title, Integer num, Integer coinAmount, Long userId, Long toOtherId,
                                                     String canGrabGroupType, String canGrabTimeType, Long canGrabTimestamp, String cycleIntervalSetting) {
        CommonResultVO commonResultVO = new CommonResultVO();
        Long appId = MDCUtil.getCurAppIdByMdc();
        // 判断该配置的自动发送红包是否达到总次数限制
        int size = 0;
        List<RedPacketInfoDO> redPacketInfoDOList = redPacketInfoJpaDAO.findByTypeAndUserIdAndToOtherIdAndCycleIntervalSetting(type, userId, toOtherId, cycleIntervalSetting);
        if (!CollectionUtils.isEmpty(redPacketInfoDOList)) {
            size = redPacketInfoDOList.size();
        }
        Long sendCountLimit = Long.parseLong(JSONObject.parseObject(cycleIntervalSetting).get("sendCount") + "");
        if (size < sendCountLimit.intValue()) {
            // 判断房主账户的金币数是否充足够发下次红包，如果够发且未达到总次数限制就发送下轮自动红包延时消息
            UserAccountVO userAccountVO = feignUserService.getUserAccountByUid(appId, userId).successData();
            if (Objects.nonNull(userAccountVO)) {
                Long userCoin = userAccountVO.getBalance() + userAccountVO.getFreeBalance();
                // 判断房主账户的金币数是否足够发本次红包
                if (userCoin >= coinAmount) {
                    // 发送语音房循环间隔自动红包并将红包配置信息落库
                    SendRedPacketResult sendRedPacketResult = feignLanlingService.sendRedPacket(title, num, coinAmount, toOtherId, canGrabGroupType, canGrabTimeType, cycleIntervalSetting, type).successData();
                    log.info("sendCycleIntervalRedPacket userId : {} num : {} coinAmount : {} toOtherId : {} cycleIntervalSetting : {} sendRedPacketResult : {}",
                            userId, num, coinAmount, toOtherId, JSONObject.toJSONString(cycleIntervalSetting), JSONObject.toJSONString(sendRedPacketResult));
                    this.sendChannelMessages(sendRedPacketResult, AgoraRestfulMessageData.TYPE_ACTION, RoomAgoraType.ROOM_RED_PACKET.getSubtype(), userId, String.valueOf(toOtherId));
                    boolean sendRedPacketResultResult = sendRedPacketResult.isResult();
                    if (sendRedPacketResultResult) {
                        commonResultVO.setResult(true);
                        // 成功发送语音房自动循环间隔红包埋点
                        Map<String, Object> params = new HashMap<>();
                        params.put("type", "automatic");
                        params.put("room_id", toOtherId);
                        yzKafkaProducerManager.dataRangerTrack(MDCUtil.getCurAppIdByMdc(), userId, "red_packet_send_success", params, ServicesNameEnum.ump_services.getCode());
                        // 房主发送关注红包之后，如果用户未关注，则自下而上弹出浮窗，才用发送特定RTM消息提示客户端弹出弹窗
                        feignRoomService.sendGuideAttentionMessage(appId, toOtherId);
                        // 判断房主账户的金币数以及总次数限制是否足够发下次红包
                        if ((userCoin >= (2L * coinAmount)) && (size + 1) < sendCountLimit) {
                            // 发送下一轮红包消息
                            JSONObject parseObject = JSONObject.parseObject(cycleIntervalSetting);
                            Integer interval = parseObject.getInteger("interval");
                            Long coinNum = parseObject.getLong("coinNum");
                            Long redPacketNum = parseObject.getLong("redPacketNum");
                            Long sendCount = parseObject.getLong("sendCount");
                            String startId = parseObject.getString("startId");
                            // 自动红包延时 MQ 消息的延时设置为 interval（自动红包延时消息消费时，需判断是否需要继续发送下一轮自动红包延时消息）
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("appId", appId);
                            jsonObject.put("roomOwnerUserId", userId);
                            jsonObject.put("roomId", toOtherId);
                            jsonObject.put("interval", interval);
                            jsonObject.put("coinNum", coinNum);
                            jsonObject.put("redPacketNum", redPacketNum);
                            jsonObject.put("sendCount", sendCount);
                            jsonObject.put("startId", startId);
                            String jsonString = JSON.toJSONString(jsonObject);
                            TopicTagEnum topicTagEnum = TopicTagEnum.TOPIC_DELAY_ROOM_CYCLE_INTERVAL_RED_PACKET;
                            String tagKey = topicTagEnum.getTagKey();
                            MqDelayLevelEnum mqDelayLevelEnum = MqDelayLevelEnum.findBySecond(60L * interval);
                            rocketmqCompatibleProducerManager.sendDelayMessage(topicTagEnum.getTopicKey().getTopicKey(), topicTagEnum.getTagKey(), jsonString, tagKey, mqDelayLevelEnum);
                            log.info("sendCycleIntervalRedPacket 发送循环间隔自动红包的延时MQ roomId: {} startId: {} interval: {} coinNum: {} redPacketNum: {} sendCount: {} tagKey: {} delayTime: {}",
                                    toOtherId, startId, interval, coinNum, redPacketNum, sendCount, tagKey, mqDelayLevelEnum.getDesc());
                        }
                    }
                }
            }
        }
        return commonResultVO;
    }

    /**
     * 发送声网频道消息(该频道内所有用户都可以收到)
     *
     * @param redPacketResult 发红包返回信息
     * @param type            消息类型(常量:AgoraRestfulMessageData 必传)
     * @param subType         具体动作(枚举:RoomAgoraType 必传)
     * @param senderUid       发送者用户id
     * @param channelId       声网频道id(必传)
     */
    public void sendChannelMessages(SendRedPacketResult redPacketResult, String type, String subType, Long senderUid, String channelId) {
        log.debug("sendChannelMessages redPacketResult : {} type : {} subType : {} senderUid : {} channelId : {}", redPacketResult, type, subType, senderUid, channelId);
        if (!StringUtils.isEmpty(channelId)) {
            AgoraRedPacketAttrs redPacketAttrs = new AgoraRedPacketAttrs();
            redPacketAttrs.setRedPacket(redPacketResult);
            AgoraRestfulMessageData messageData = new AgoraRestfulMessageData();
            messageData.setType(type);
            messageData.setSubtype(subType);
            messageData.setSenderUid(senderUid);
            messageData.setAttrs(redPacketAttrs);
            String message = JSONObject.toJSONString(messageData);
            log.debug("sendChannelMessages message : {}", message);
            feignAvService.sendChannelMessages(SystemNPC.LANLING_LITTLE.getUserId(), channelId, message);
        }
        log.debug("sendChannelMessages success");
    }

    /**
     * 查询最新的一次语音房自动红包配置
     *
     * @param status           1-进行中 2-已抢完 3-过期
     * @param type             红包类型 @see cn.yizhoucp.ms.core.base.enums.RedPacketType
     * @param userId           发送红包的用户ID
     * @param toOtherId        家族id、用户id、语音房id
     * @param canGrabTimeType  可抢时间类型 @see RedPacketCanGrabTimeType
     * @param canGrabTimestamp 可抢时间戳（毫秒）
     * @return
     */
    public CycleIntervalSetting getLatestRoomCycleIntervalRedPacket(Integer status, Integer type, Long userId, Long toOtherId, String canGrabTimeType, Long canGrabTimestamp) {
        CycleIntervalSetting setting = new CycleIntervalSetting();
        if (status != null && type != null && userId != null && toOtherId != null && canGrabTimeType != null && canGrabTimestamp != null) {
            RedPacketInfoDO redPacketInfoDO = redPacketInfoJpaDAO.findTopByStatusAndTypeAndUserIdAndToOtherIdAndCanGrabTimeTypeAndCanGrabTimestampGreaterThanEqualOrderByCreateTimeDesc(status,
                    type, userId, toOtherId, canGrabTimeType, canGrabTimestamp);
            if (Objects.nonNull(redPacketInfoDO)) {
                String cycleIntervalSetting = redPacketInfoDO.getCycleIntervalSetting();
                if (!org.apache.commons.lang3.StringUtils.isEmpty(cycleIntervalSetting)) {
                    setting = JSONObject.parseObject(cycleIntervalSetting, CycleIntervalSetting.class);
                }
            }
            // 查询缓存的语音房自动发送红包参数
            String cycleRedPacketKey = String.format(RedisKey.CHAT_ROOM_CYCLE_INTERVAL_REDPACKET_KEY.getKey(), toOtherId, userId);
            Object o = redisManager.get(cycleRedPacketKey);
            if (Objects.nonNull(o)) {
                // 如果自动红包发送过程未达到原总次数限制，房主重置了红包属性，则原属性红包停止循环发送，开始发送新设置红包（总次数限制重置）
                if (!(o + "").equals(setting.toString())) {
                    setting = JSONObject.parseObject(o + "", CycleIntervalSetting.class);
                }
            }
        }
        return setting;
    }

    /**
     * 根据红包类型获取金币消耗场景
     *
     * @param type 红包类型 1-家族红包 2-交友大厅红包 3-家族新成员红包 4-聊天室(语音房)红包
     * @return UseCoinScene
     */
    public UseCoinScene getUseCoinSceneByType(Integer type) {
        UseCoinScene scene = UseCoinScene.family;
        Integer chatRoom = 2;
        Integer voiceRoom = 4;
        if (chatRoom.equals(type)) {
            scene = UseCoinScene.chatRoom;
        } else if (voiceRoom.equals(type)) {
            scene = UseCoinScene.voiceRoom;
        }
        return scene;
    }

    private OpenRedPacketResult expiredResult(BaseParam param) {
        return OpenRedPacketResult.builder()
                .result(false)
                .flag(RedPacketFlag.EXPIRED.getCode())
                .message(ServicesAppIdEnum.chatie.getAppId().equals(param.getAppId()) ? internationalManager.changeCommonText(null, "simple.red.packet.expired.desc") : RedPacketFlag.EXPIRED.getDesc()).build();
    }

    private OpenRedPacketResult repeatResult(BaseParam param, Long coin) {
        return OpenRedPacketResult.builder()
                .result(false)
                .flag(RedPacketFlag.ALREADY_GRABBED.getCode())
                .message(ServicesAppIdEnum.chatie.getAppId().equals(param.getAppId()) ? internationalManager.changeCommonText(new Object[]{String.valueOf(coin.intValue())}, "splice.get.coin") : RedPacketFlag.ALREADY_GRABBED.getDesc()).build();
    }

    private OpenRedPacketResult finishResult(BaseParam param) {
        OpenRedPacketResult result = new OpenRedPacketResult();
        result.setResult(false);
        result.setFlag(RedPacketFlag.LOOT_ALL.getCode());
        String text = internationalManager.changeCommonText(null, "simple.red.packet.loot.all.desc");
        String lootAllDesc = ServicesAppIdEnum.chatie.getAppId().equals(param.getAppId()) ? text : RedPacketFlag.LOOT_ALL.getDesc();
        result.setMessage(lootAllDesc);
        return result;
    }

    private OpenRedPacketResult successResult(BaseParam param, Long sendUid, Integer coin, Long luckyUid) {
        OpenRedPacketResult result = new OpenRedPacketResult();
        result.setResult(true);
        result.setAmount(coin + "");
        Object[] params = new Object[]{String.valueOf(coin)};
        String text = internationalManager.changeCommonText(params, "splice.get.coin");
        result.setMessage(text);
        result.setSendUserId(sendUid);
        result.setFeelingLuckyUserId(luckyUid);
        return result;
    }

    private Long getLuckyUid(Long redPacketId) {
        Set<ZSetOperations.TypedTuple<Object>> typedTuples = redisManager.reverseRangeByScoreWithScores(String.format(OPEN_RED_PACKET_RANK, redPacketId), 1, Double.MAX_VALUE, 0L, 1L);
        if (typedTuples != null) {
            Iterator<ZSetOperations.TypedTuple<Object>> iterator = typedTuples.iterator();
            while (iterator.hasNext()) {
                ZSetOperations.TypedTuple<Object> next = iterator.next();
                return Long.parseLong(next.getValue() + "");
            }
        }
        return null;
    }

    private OpenCheckDTO openRedPacketCheck(BaseParam param, Long redPacketId) {
        OpenCheckDTO openCheckDTO;
        try {
            openCheckDTO = openRedPacketCheckWithCache(param, redPacketId);
        } catch (Throwable e) {
            log.warn("openRedPacket 业务缓存失效 redPacketId:{}, uid:{}", redPacketId, param.getUid(), e);
            openCheckDTO = openRedPacketCheckWithDB(param, redPacketId);
        }
        if (Objects.isNull(openCheckDTO)) {
            throw new ServiceException(ErrorCode.MISS_PARAM);
        }
        return openCheckDTO;
    }

    private OpenCheckDTO openRedPacketCheckWithDB(BaseParam param, Long redPacketId) {
        Optional<RedPacketInfoDO> opt = redPacketInfoJpaDAO.findById(redPacketId);
        if (!opt.isPresent()) {
            throw new ServiceException(ErrorCode.COIN_PARAM_ERROR);
        }
        RedPacketInfoDO redPacket = opt.get();
        // 当前用户是否已抢过
        RedPacketRecordDO record = redPacketRecordJpaDAO.findByAppIdAndUserIdAndRedPacketId(param.getAppId(), param.getUid(), redPacketId);
        if (Objects.nonNull(record)) {
            return OpenCheckDTO.builder().result(repeatResult(param, record.getAmount().longValue())).build();
        }
        // 红包状态验证
        if (Objects.equals(RedPacketStatus.EXPIRED_RETURNED.getCode(), redPacket.getStatus())
                || Objects.equals(RedPacketStatus.EXPIRED_NOT_RETURNED.getCode(), redPacket.getStatus())) {
            // 红包已过期
            return OpenCheckDTO.builder().result(expiredResult(param)).build();
        } else if (Objects.equals(RedPacketStatus.GRAB_FINISH.getCode(), redPacket.getStatus())) {
            // 红包已抢完
            return OpenCheckDTO.builder().result(finishResult(param)).build();
        }
        return OpenCheckDTO.builder().leftTimes(redPacket.getRemainingPacket().longValue()).build();
    }

    private OpenCheckDTO openRedPacketCheckWithCache(BaseParam param, Long redPacketId) {
        // 当前用户是否已抢过（cache）
        Double coin = redisManager.score(String.format(OPEN_RED_PACKET_RANK, redPacketId), param.getUid().toString());
        if (Objects.nonNull(coin)) {
            return OpenCheckDTO.builder().result(repeatResult(param, coin.longValue())).build();
        }
        // 判断红包是否过期（cache）
        if (!redisManager.hasKey(RED_PACKET_SIZE) && expired(redPacketId)) {
            return OpenCheckDTO.builder().result(expiredResult(param)).build();
        }
        // 当前红包是否已抢完（cache）
        Long leftTimes;
        if ((leftTimes = redisManager.decrLong(String.format(RED_PACKET_SIZE, redPacketId), 1, RedisManager.ONE_DAY_SECONDS * 3)) < 0L) {
            return OpenCheckDTO.builder().result(finishResult(param)).build();
        }
        log.info("openRedPacket 剩余红包数量 redPacketId:{}, leftTimes:{}, uid:{}", redPacketId, leftTimes, param.getUid());
        // 标记当前用户已抢（cache）
        redisManager.zSetAdd(String.format(OPEN_RED_PACKET_RANK, redPacketId), param.getUid().toString(), 0, RedisManager.ONE_DAY_SECONDS * 3, TimeUnit.SECONDS);
        return OpenCheckDTO.builder().leftTimes(leftTimes).build();
    }

    public List<Object> generateAmountPool(int totalAmount, Integer totalSize) {
        List<Object> result = Lists.newArrayList();
        // 每轮最低金额
        int minAmount = 1;
        for (int i = 0; i < totalSize; i++) {
            // 本轮开启金额
            int currAmount;
            // 计算剩余数量：总数量 - 已开数量
            int leftSize = totalSize - i;
            if (leftSize > 1) {
                // 计算本轮上限：总金额 - 剩余人数 + 每轮最低金额
                int maxAmount = totalAmount - leftSize + minAmount;
                if (new Random().nextInt(100) < 90) {
                    currAmount = avgRedPacket(maxAmount, leftSize, totalAmount, totalSize);
                } else {
                    currAmount = randomRedPacket(maxAmount, totalAmount);
                }
            } else {
                // 最后一轮取剩余金额
                currAmount = totalAmount;
            }
            result.add(currAmount);
            // 更新红包金额与红包数量
            totalAmount -= currAmount;
        }
        return result;
    }

    private String getSendPacketFrom(Integer type) {
        if (RedPacketType.family.getType().equals(type)) {
            return FROM_FAMILY;
        } else if (RedPacketType.chatroom.getType().equals(type)) {
            return FROM_CHAT_ROOM;
        } else if (RedPacketType.room.getType().equals(type)) {
            return RedPacketType.room.getCode();
        }
        return null;
    }

    private String getSendPacketMemo(Long toOtherId, Integer type) {
        RedPacketType typeEnum = RedPacketType.convertFormType(type);
        if (Objects.isNull(typeEnum)) {
            log.warn("redPacket 未知红包类型 type:{}", type);
            return "";
        }
        switch (typeEnum) {
            case family:
                return internationalManager.format("splice.send.red.packet.in.family", toOtherId);
            case chatroom:
                return internationalManager.format("splice.send.red.packet.in.chatroom", toOtherId);
            case room:
                return internationalManager.format("splice.send.red.packet.in.voice.room", toOtherId);
            default:
                return internationalManager.format("splice.send.red.packet", toOtherId);
        }
    }

    /**
     * 随机均分算法
     *
     * @param maxMoney 可分配的最大金额
     * @param num      剩余待分配红包数量
     * @param amount   红包总金额
     * @param totalnum 红包总个数
     * @return
     */
    private static int avgRedPacket(int maxMoney, int num, int amount, int totalnum) {
        int maxAmount = maxMoney;//设定最大值
        if ((double) (maxMoney / num) > (double) amount / totalnum) {
            //限制边界
            maxAmount = maxMoney < 2 * (maxMoney / num) ? maxAmount : 2 * (maxMoney / num);
            return getRandom(1, maxAmount < 1 ? 1 : maxAmount);
        }
        maxAmount = maxMoney < 2 * (amount / totalnum) ? maxAmount : 2 * (amount / totalnum);
        return getRandom(1, maxAmount < 1 ? 1 : maxAmount);
    }

    /**
     * 完全随机金额
     *
     * @param maxMoney 最大可分配金额
     * @param amount   红包的总金额
     * @return
     */
    private static int randomRedPacket(Integer maxMoney, int amount) {
        int randomAmount = 1;
        boolean passFlag = false;
        int times = 0;
        while (!passFlag || times < 3) {
            randomAmount = new Random().nextInt(maxMoney) + 1;
            if (randomAmount <= (amount / 2)) {
                return randomAmount;
            }
            times++;
            //金额越大 概率越小
            int passRandom = 100 - (randomAmount * 100 / amount);
            passFlag = new Random().nextInt(101) <= passRandom;
        }
        return randomAmount;
    }

    /***
     * 两个数之间的随机值
     * @param x
     * @param y
     * @return
     */
    private static int getRandom(int x, int y) {
        int num = -1;
        //说明：两个数在合法范围内，并不限制输入的数哪个更大一些
        if (x < 0 || y < 0) {
            return num;
        } else {
            int max = Math.max(x, y);
            int min = Math.min(x, y);
            int mid = max - min;//求差
            //产生随机数
            num = (int) (Math.random() * (mid + 1)) + min;
        }
        return num;
    }

    private Boolean isFree(BaseParam param, Integer type, String deviceId) {
        if (RedPacketType.family_join.getType().equals(type)) {
            // chatie
            if (ServicesAppIdEnum.chatie.getAppId().equals(param.getAppId())) {
                if (!hasSendFreshManRedPacket(param.getUid(), type, deviceId)) {
                    return Boolean.TRUE;
                }
            } else {
                // default
                Integer sendJoinNum = redPacketInfoJpaDAO.countByUserIdAndType(param.getUid(), RedPacketType.family_join.getType());
                if (sendJoinNum < 1) {
                    return Boolean.TRUE;
                }
            }
        }
        return Boolean.FALSE;
    }

    /**
     *
     * @param param 基础参数
     * @param type 红包类型
     * @param coinAmount 红包金额
     * @param toOtherId 场景Id
     * @param orderVO 订单信息
     * @param appScene 场景
     */
    private void trackSendRedPacket(BaseParam param, Integer type, Integer coinAmount, Long toOtherId, OrderVO orderVO, AppScene appScene) {
        log.info("发红包埋点开始 - uid:{}, type:{}, coinAmount:{}, toOtherId:{}, appScene:{}",
                param.getUid(), type, coinAmount, toOtherId, appScene);
        try {
            Map<String, Object> params = new HashMap<>();
            // 根据场景判断填充room_id和family_id
            if (AppScene.voiceRoom.equals(appScene)) {
                // 语音房场景，toOtherId为房间ID
                params.put("room_id", toOtherId.toString());
            } else if(AppScene.family.equals(appScene)) {
                // 家族场景，toOtherId为家族ID
                params.put("family_id", toOtherId.floatValue());
            }
            // 红包场景来源 appScene
            params.put("from", appScene.getCode());
            // 总金币数
            params.put("amount", coinAmount.floatValue());

            // 计算免费、付费金币、平台手续费
            if (orderVO != null && orderVO.getOrderAmountVO() != null) {
                // 免费金币数
                params.put("free_count", orderVO.getOrderAmountVO().getCoinFree().floatValue());
                // 付费金币数
                params.put("pay_count", coinAmount.floatValue());
                // 计算平台手续费 = 订单金币总额 - 红包金额
                Long serviceCharge = orderVO.getOrderAmountVO().getCoinCount() - coinAmount;
                params.put("service_charge", serviceCharge.floatValue());
            } else {
                // 免费红包场景
                params.put("free_count", coinAmount.floatValue());
                params.put("pay_count", 0f);
                params.put("service_charge", 0f);
            }

            // 发送埋点数据
            yzKafkaProducerManager.dataRangerTrack(
                    MDCUtil.getCurAppIdByMdc(),
                    param.getUid(),
                    "send_red_envelope",
                    params,
                    ServicesNameEnum.ump_services.getCode()
            );

            log.info("发红包埋点完成 - uid:{}, type:{}, coinAmount:{}, toOtherId:{}",
                    param.getUid(), type, coinAmount, toOtherId);

        } catch (Exception e) {
            log.error("发红包埋点异常 - uid:{}, type:{}, coinAmount:{}, toOtherId:{}",
                    param.getUid(), type, coinAmount, toOtherId, e);
        }
    }

    /**
     *
     * @param param 基础参数
     * @param redPacket 红包信息
     * @param coin 抢到的金币数
     * @param toOtherId 场景ID
     * @param appScene 场景
     */
    private void trackOpenRedPacket(BaseParam param, RedPacketInfoDO redPacket, Integer coin, Long toOtherId, AppScene appScene) {
        log.info("开红包埋点开始 - uid:{}, redPacketId:{}, coin:{}, toOtherId:{}, appScene:{}",
                param.getUid(), redPacket.getId(), coin, toOtherId, appScene);
        try {
            Map<String, Object> params = new HashMap<>();
            // 根据场景判断填充room_id和family_id
            if (AppScene.voiceRoom.equals(appScene)) {
                // 语音房场景，toOtherId为房间ID
                params.put("room_id", toOtherId.toString());
            } else if(AppScene.family.equals(appScene)) {
                // 家族场景，toOtherId为家族ID
                params.put("family_id", toOtherId.floatValue());
            }
            // 开出的金币总数
            params.put("amount", coin.floatValue());
            // 红包类型
            Integer type = redPacket.getType();
            // 根据红包类型判断是否免费
            if(RedPacketType.family_join.getType().equals(type)) {
                // 红包类型为家族新成员红包
                params.put("free_count", coin.floatValue());
                params.put("pay_count", 0f);
            }else{
                params.put("free_count", 0f);
                params.put("pay_count", coin.floatValue());
            }

            // 发红包的用户ID
            params.put("from_user_id", redPacket.getUserId().floatValue());
            // 发送埋点数据
            yzKafkaProducerManager.dataRangerTrack(
                    MDCUtil.getCurAppIdByMdc(),
                    param.getUid(),
                    "open_red_envelope",
                    params,
                    ServicesNameEnum.ump_services.getCode()
            );

            log.info("开红包埋点完成 - uid:{}, redPacketId:{}, coin:{}, toOtherId:{}",
                    param.getUid(), redPacket.getId(), coin, toOtherId);

        } catch (Exception e) {
            log.error("开红包埋点异常 - uid:{}, redPacketId:{}, coin:{}, toOtherId:{}",
                    param.getUid(), redPacket.getId(), coin, toOtherId, e);
        }
    }
}
