package cn.yizhoucp.ump.biz.project.biz.util;

import java.io.UnsupportedEncodingException;
import java.security.GeneralSecurityException;
import java.security.MessageDigest;

/**
 * @Description:
 * @Author: dingqiankun
 * @Date: 2018/7/16 下午2:40
 * @Version: V
 */
public class MD5Util {

    public static String encode(String str) {
        try {
            byte[] bytes = null;
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(str.getBytes("UTF-8"));
            bytes = md.digest();
            StringBuilder stringBuilder = new StringBuilder();

            for(int i = 0; i < bytes.length; ++i) {
                int v = bytes[i] & 255;
                String hv = Integer.toHexString(v);
                if (hv.length() < 2) {
                    stringBuilder.append(0);
                }

                stringBuilder.append(hv);
            }

            return stringBuilder.toString();
        } catch (GeneralSecurityException var7) {
            return "";
        } catch (UnsupportedEncodingException var8) {
            throw new RuntimeException(var8);
        }
    }
}
