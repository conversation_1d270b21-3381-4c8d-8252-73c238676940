package cn.yizhoucp.ump.biz.project.biz.manager.luckyBag;

import cn.yizhoucp.ump.biz.project.dal.jpa.dao.SysConfigJpaDAO;
import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 福袋配置管理
 *
 * @author: lianghu
 */
@Slf4j
@Component
public class LuckyBagConfigManager {

    @Resource
    private RedisManager redisManager;
    @Resource
    private SysConfigJpaDAO sysConfigJpaDAO;

    public String getSysConfig(String sysKey) {
        String result = null;
        if (redisManager.hasKey(sysKey)) {
            result = (String) redisManager.get(sysKey);
        }
        log.debug("[SysConfigManager-getSysConfig] sysValue -> {}", JSON.toJSON(result));
        if (result == null || "".equals(result)) {
            result = sysConfigJpaDAO.findByConfigKey(sysKey);
            //将redis中对应的数据进行修改或者新增
            boolean setKey = redisManager.set(sysKey, result, DateUtil.ONE_MONTH_SECOND);
            log.debug("[SysConfigManager-getSysConfig] setKey -> {}", setKey);
        }
        return result;
    }


    /**
     * 查询配置并缓存  注意要求Key一定要规范，防止和Redis重复！！！ 时间不传默认15分钟
     *
     * @param key
     * @param time
     * @return
     */
    public String getAndCacheSysConfig(String key, Long time) {
        time = null == time ? 60 * 15 : time;
        String configValue = (String) redisManager.get(key);
        if (StringUtils.isEmpty(configValue)) {
            configValue = sysConfigJpaDAO.findByConfigKey(key);
            if (!StringUtils.isEmpty(configValue)) {
                redisManager.set(key, configValue, time);
            }
        }
        if (StringUtils.isEmpty(configValue)) {
            return null;
        }
        return configValue;
    }
}
