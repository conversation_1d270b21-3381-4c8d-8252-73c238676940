package cn.yizhoucp.ump.biz.project.dal.mp.dataobject.navigation;

import cn.yizhoucp.ump.api.vo.navigation.SailRecordVO;
import cn.yizhoucp.ump.api.vo.navigation.SailVO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@TableName("ng_sail_record")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SailRecordDO {
    @TableId(type = IdType.AUTO)
    private Long id;

    private Long uid;

    private Long userBoatId;

    /** 用户船只 */
    private String boat;

    /** 原始变更海里数 */
    private Integer originMileChange;

    /** 普通/海神 */
    private String sailType;

    /** 海里实际变化 */
    private Integer mileChange;

    /** 前行后海里变化 */
    private Integer afterChange;

    private Date sailDate;

    /** ---以下统计数据需要--- */
    /** 航行房间id */
    private Long roomId;

    /** 消耗汽油数 */
    private Integer consumerPetrol;

    /** 航行前后礼物差价 */
    private Integer giftCoinDiff;

    public SailRecordVO convertToVO() {
        return SailRecordVO.builder()
                .id(id)
                .boatName(boat)
                .sailType(sailType)
                .appendDate(sailDate)
                .sailMile(mileChange)
                .build();
    }
}
