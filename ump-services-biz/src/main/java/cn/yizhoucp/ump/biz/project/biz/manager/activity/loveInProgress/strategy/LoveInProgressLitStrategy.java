package cn.yizhoucp.ump.biz.project.biz.manager.activity.loveInProgress.strategy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.annotation.NoRepeatSubmit;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.log.LogComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.loveInProgress.LoveInProgressTrackManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.loveInProgress.common.LoveInProgressConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.loveInProgress.common.LoveInProgressRedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.loveInProgress.vo.LoveInProgressLitResponse;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LoveInProgressLitStrategy implements ExecutableStrategy {

    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    private LoveInProgressRedisManager loveInProgressRedisManager;
    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private LogComponent logComponent;
    @Resource
    private LoveInProgressTrackManager loveInProgressTrackManager;

    @NoRepeatSubmit(time=3)
    @Override
    public LoveInProgressLitResponse execute(ButtonEventParam buttonEventParam) {
        BaseParam baseParam = buttonEventParam.getBaseParam();
        Long uid = baseParam.getUid();
        String timesStr = buttonEventParam.getBizKey();
        if (timesStr == null) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "参数错误");
        }
        Long drawItem = loveInProgressRedisManager.getLitItems(uid);
        Long times = Long.parseLong(timesStr);
        if (drawItem < times) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "恋爱真心不足哦～");
        }
        //初始化奖池
        Boolean poolIsFull = loveInProgressRedisManager.isPoolInitialized(uid);
        if (Boolean.FALSE.equals(poolIsFull)) {
            List<ScenePrizeDO> listBySceneCode = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), LoveInProgressConstant.ACTIVITY_CODE, LoveInProgressConstant.GIFT_SCENE_CODE);
            if (CollUtil.isEmpty(listBySceneCode)) {
                log.error("恋爱进行时首页礼物列表为空");
                return null;
            }
            loveInProgressRedisManager.initPool(uid, listBySceneCode);
        }
        //抽取
        List<String> drawItems = loveInProgressRedisManager.drawPool(uid, times);
        if (CollUtil.isEmpty(drawItems)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "奖池已抽空");
        }
        //扣除抽奖道具
        loveInProgressRedisManager.decrementLitItems(uid, times);
        for (String itemKey : drawItems) {
            //点亮
            Long litNum=loveInProgressRedisManager.litGift(uid, itemKey);
            if(litNum>=25){
                loveInProgressTrackManager.allActivityTaskFinish(uid,"light_all_icon");
            }
        }
        //查看是否有完成点亮
        List<List<String>> giftList = loveInProgressRedisManager.getLitGiftList();
        List<String> points = checkIfAllLit(uid, giftList);
        List<LoveInProgressLitResponse.Reward> litGiftList = buildGfitList(drawItems, uid);
        List<LoveInProgressLitResponse.Reward> litReward = buildRewardList(points, uid);
        //点亮记录
        litRecord(uid, litGiftList, litReward);
        List<LoveInProgressLitResponse.Reward> res = new ArrayList<>();
        if (litGiftList != null && CollUtil.isNotEmpty(litGiftList)) {
            res.addAll(litGiftList);
        }
        if (litReward != null && CollUtil.isNotEmpty(litReward)) {
            res.addAll(litReward);
        }
        return LoveInProgressLitResponse.builder()
                .giftList(res)
                .build();
    }

    private void litRecord(Long uid, List<LoveInProgressLitResponse.Reward> litGift, List<LoveInProgressLitResponse.Reward> litReward) {
        if (litGift == null) {
            return;
        }
        if (CollUtil.isNotEmpty(litGift)) {
            List<DrawPoolItemDTO> drawPoolItemDTOList = buildExchangeItemList(litGift, LoveInProgressConstant.LIT_GIFT_MSG);
            logComponent.putDrawLog(BaseParam.ofMDC(), LoveInProgressConstant.ACTIVITY_CODE, drawPoolItemDTOList);
        }
        if (CollUtil.isNotEmpty(litReward)) {
            List<DrawPoolItemDTO> drawPoolItemDTOList = buildExchangeItemList(litReward, LoveInProgressConstant.LIT_REWARD_MSG);
            logComponent.putDrawLog(BaseParam.ofMDC(), LoveInProgressConstant.ACTIVITY_CODE, drawPoolItemDTOList);
        }
    }

    private List<LoveInProgressLitResponse.Reward> buildRewardList(List<String> points, Long uid) {
        if (CollUtil.isEmpty(points)) {
            return CollUtil.newArrayList();
        }
        List<ScenePrizeDO> scenePrizeDOS = points.stream().map(point -> loveInProgressRedisManager.getRewardScenePrizeDO(point)).collect(Collectors.toList());
        if (CollUtil.isEmpty(scenePrizeDOS)) {
            return CollUtil.newArrayList();
        }
        //过滤已领取奖励
        scenePrizeDOS = scenePrizeDOS.stream().filter(scenePrizeDO -> {
            Integer row = getRow(scenePrizeDO.getExtData());
            Integer column = getColumn(scenePrizeDO.getExtData());
            return !loveInProgressRedisManager.litRewardIsReceive(uid, row.toString() + column);
        }).collect(Collectors.toList());
        //下发奖励
        for (ScenePrizeDO scenePrizeDO : scenePrizeDOS) {
            if ("ZAJZ_GIFT".equals(scenePrizeDO.getPrizeValue())) {
                loveInProgressRedisManager.incrementDrawItems(uid, scenePrizeDO.getPrizeNum().longValue());
            } else {
                sendPrizeManager.sendPrize(
                        BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(uid).build(),
                        Collections.singletonList(SendPrizeDTO.of(scenePrizeDO, uid))
                );
            }
            Integer row = getRow(scenePrizeDO.getExtData());
            Integer column = getColumn(scenePrizeDO.getExtData());
            loveInProgressRedisManager.setLitRewardIsReceive(uid, row.toString() + column);
        }

        return scenePrizeDOS.stream().map(scenePrizeDO -> LoveInProgressLitResponse.Reward.builder()
                .giftIcon(scenePrizeDO.getPrizeIcon())
                .giftName(scenePrizeDO.getPrizeDesc())
                .giftNum(scenePrizeDO.getPrizeNum().longValue())
                .giftValue(scenePrizeDO.getPrizeValueGold())
                .build()).collect(Collectors.toList());
    }

    private List<String> checkIfAllLit(Long uid, List<List<String>> giftList) {
        List<String> mapPoints = CollUtil.newArrayList();
        int rows = giftList.size();
        if (rows == 0) {
            return mapPoints;
        }
        int cols = giftList.get(0).size();
        Boolean allLit = Boolean.TRUE;
        //检查每一个行
        for (int i = 0; i < rows; i++) {
            allLit = Boolean.TRUE;
            for (int j = 0; j < cols; j++) {
                Boolean isLit = loveInProgressRedisManager.isLit(uid, giftList.get(i).get(j));
                if (Boolean.FALSE.equals(isLit)) {
                    allLit = Boolean.FALSE;
                    break;
                }
            }
            if (Boolean.TRUE.equals(allLit)) {
                mapPoints.add(String.valueOf(i) + "5");
            }
        }
        //检查每一列
        for (int i = 0; i < cols; i++) {
            allLit = Boolean.TRUE;
            for (int j = 0; j < rows; j++) {
                Boolean isLit = loveInProgressRedisManager.isLit(uid, giftList.get(j).get(i));
                if (Boolean.FALSE.equals(isLit)) {
                    allLit = Boolean.FALSE;
                    break;
                }
            }
            if (Boolean.TRUE.equals(allLit)) {
                mapPoints.add("5" + i);
            }
        }
        //检查对角线
        for (int i = 0; i < rows; i++) {
            allLit = Boolean.TRUE;
            Boolean isLit = loveInProgressRedisManager.isLit(uid, giftList.get(i).get(i));
            if (Boolean.FALSE.equals(isLit)) {
                allLit = Boolean.FALSE;
                break;
            }
        }
        if (Boolean.TRUE.equals(allLit)) {
            mapPoints.add("55");
        }
        return mapPoints;
    }

    private List<LoveInProgressLitResponse.Reward> buildGfitList(List<String> drawItems, Long uid) {
        List<ScenePrizeDO> scenePrizeDOS = new ArrayList<>();
        for (String drawItem : drawItems) {
            ScenePrizeDO scenePrizeDO = loveInProgressRedisManager.getLitGiftScenePrizeDO(drawItem);
            if (scenePrizeDO != null) {
                scenePrizeDOS.add(scenePrizeDO);
                loveInProgressTrackManager.allActivityReceiveAward("love_icon",scenePrizeDO.getPrizeValue(),scenePrizeDO.getPrizeValueGold(),scenePrizeDO.getPrizeNum(),uid);
            }
        }
        if (CollUtil.isEmpty(scenePrizeDOS)) {
            return CollUtil.newArrayList();
        }
        //下发奖励
        sendPrizeManager.sendPrize(
                BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(uid).build(),
                scenePrizeDOS.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, uid)).collect(Collectors.toList())
        );
        return scenePrizeDOS.stream().map(scenePrizeDO -> LoveInProgressLitResponse.Reward.builder()
                        .giftName(scenePrizeDO.getPrizeDesc())
                        .giftIcon(scenePrizeDO.getPrizeIcon())
                        .giftNum(scenePrizeDO.getPrizeNum().longValue())
                        .giftValue(scenePrizeDO.getPrizeValueGold())
                        .build())
                .collect(Collectors.toList());
    }

    private List<DrawPoolItemDTO> buildExchangeItemList(List<LoveInProgressLitResponse.Reward> litGift, String msg) {
        List<DrawPoolItemDTO> prizeItemList = new ArrayList<>();
        for (LoveInProgressLitResponse.Reward litGiftItem : litGift) {
            DrawPoolItemDO drawPoolItemDO = DrawPoolItemDO.builder()
                    .itemName(String.format(msg, litGiftItem.getGiftName()))
                    .createTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .itemKey(" ")
                    .itemNum(1)
                    .itemValueGold(0L)
                    .status(1)
                    .poolCode(LoveInProgressConstant.LIT_RECORD)
                    .build();
            DrawPoolItemDTO drawPoolItemDTO = DrawPoolItemDTO.builder()
                    .targetTimes(1)
                    .drawPoolItemDO(drawPoolItemDO).build();
            prizeItemList.add(drawPoolItemDTO);
        }
        return prizeItemList;
    }

    private Integer getRow(String extData) {
        if (extData == null) {
            return 0;
        }
        Map<String, String> map = JSON.parseObject(extData, new TypeReference<Map<String, String>>() {
        });
        String row = map.get("row");
        return CharSequenceUtil.isBlank(row) ? 0 : Integer.parseInt(row);
    }

    private Integer getColumn(String extData) {
        if (extData == null) {
            return 0;
        }
        Map<String, String> map = JSON.parseObject(extData, new TypeReference<Map<String, String>>() {
        });
        String column = map.get("column");
        return CharSequenceUtil.isBlank(column) ? 0 : Integer.parseInt(column);
    }
}
