package cn.yizhoucp.ump.biz.project.web.rest.controller.activity;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.auth.SecurityUser;
import cn.yizhoucp.ms.core.base.auth.SecurityUtils;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ms.core.vo.CommonResultVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.worldAnnounceLoveYou.WorldAnnounceLoveYouIndexManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

@Slf4j
@RestController
public class WorldAnnounceLoveYouController {


    @Resource
    private WorldAnnounceLoveYouIndexManager worldAnnounceLoveYouIndexManager;

    @GetMapping("/api/inner/activity/world-announce-love-you/receive-gift")
    public Result<Boolean> receiveGift() {
        SecurityUser user = SecurityUtils.getCurrentUser();
        if (Objects.isNull(user)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                worldAnnounceLoveYouIndexManager.receiveGift(user.getUserId())
        );
    }
}
