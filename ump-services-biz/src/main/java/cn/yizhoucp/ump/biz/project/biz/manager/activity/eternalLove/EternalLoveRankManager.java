package cn.yizhoucp.ump.biz.project.biz.manager.activity.eternalLove;

import cn.yizhoucp.family.api.client.FamilyFeignService;
import cn.yizhoucp.family.api.dto.family.FamilyInfoDTO;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.vo.activity.eternalLove.TeamVO;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.AbstractRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.lanling.FeignLanlingService;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.ACTIVITY_CODE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.FAMILY_RANK;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.GIRL_RANK;

@Service
@Slf4j
public class EternalLoveRankManager extends AbstractRankManager {

    public static final List<Long> FAMILY_RANK_1_COIN = Arrays.asList(18888L, 12000L, 8000L, 6000L, 4000L, 3000L);
    public static final List<Long> FAMILY_RANK_2_COIN = Arrays.asList(25000L, 15000L, 10000L);
    public static final List<Long> FAMILY_RANK_3_COIN = Arrays.asList(20000L, 15000L, 10000L, 5000L);

    @Resource
    private EternalLoveBizManager eternalLoveBizManager;
    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private NotifyComponent notifyComponent;
    @Resource
    private FeignLanlingService feignLanlingService;
    @Resource
    private FamilyFeignService familyFeignService;
    @Resource
    private EternalLoveTrackManager eternalLoveTrackManager;

    @Override
    protected void doPreProcess(RankContext rankContext) {
        rankContext.setRankLen(10L);
        if (RankContext.RankType.family.equals(rankContext.getType())) {
            rankContext.setRankLen(12L);
        }
    }

    @Override
    protected void postProcess(RankContext rankContext) {
    }

    @Override
    public Boolean sendPrize(RankContext rankContext) {
        if (GIRL_RANK.equals(rankContext.getRankKey())) {
            if (!Boolean.TRUE.equals(redisManager.setnx(String.format("ump:eternal_love:send_prize_idempotent:girl_rank:%s", eternalLoveBizManager.stage() - 1), System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND))) {
                return Boolean.TRUE;
            }

            List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(1L, ACTIVITY_CODE, "overload_rank");
            log.info("scenePrizeDOList {}", JSON.toJSONString(scenePrizeDOList));
            if (CollectionUtils.isEmpty(scenePrizeDOList)) {
                return Boolean.FALSE;
            }

            RankVO rankVO = this.getRank(RankContext.builder()
                    .activityCode(ACTIVITY_CODE)
                    .rankKey(String.format(GIRL_RANK, eternalLoveBizManager.stage() - 1))
                    .type(RankContext.RankType.user)
                    .build());
            log.info("rankVO {}", rankVO);
            if (rankVO == null) {
                return Boolean.FALSE;
            }

            List<RankItem> rankList = rankVO.getRankList();
            if (CollectionUtils.isEmpty(rankList)) {
                return Boolean.FALSE;
            }

            for (RankItem rankItem : rankList) {
                Long rank = rankItem.getRank();
                List<ScenePrizeDO> scenePrizeDOs = scenePrizeDOList.stream().filter(scenePrizeDO -> Objects.equals(rank, scenePrizeDO.getPrizeBelongToRank())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(scenePrizeDOs)) {
                    log.info("uid {} rank {} 没有奖励", rankItem.getId(), rank);
                    continue;
                }
                sendPrizeManager.sendPrize(
                        BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(rankItem.getId()).build(),
                        scenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, rankItem.getId())).collect(Collectors.toList())
                );
                notifyComponent.npcNotify(
                        ServicesAppIdEnum.lanling.getUnionId(),
                        rankItem.getId(),
                        String.format("亲爱的花神，恭喜你在“三生三世永生恋”中获得第%s名，奖励已下发至您的背包，快去查看吧！", rank)
                );
                for (ScenePrizeDO scenePrizeDO : scenePrizeDOs) {
                    eternalLoveTrackManager.allActivityReceiveAward("on_list", scenePrizeDO.getPrizeValue(), scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeNum(), rankItem.getId());
                }
            }
        }

        if ("ump:eternal_love:family_rank:1".equals(rankContext.getRankKey())) {
            if (!Boolean.TRUE.equals(redisManager.setnx("ump:eternal_love:send_prize_idempotent:family_rank:1", System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND))) {
                return Boolean.TRUE;
            }

            RankVO rankVO = this.getRank(RankContext.builder()
                    .activityCode(ACTIVITY_CODE)
                    .rankKey(String.format(FAMILY_RANK, 1))
                    .type(RankContext.RankType.user)
                    .build());
            log.info("rankVO {}", rankVO);
            if (rankVO == null) {
                return Boolean.FALSE;
            }

            List<RankItem> rankList = rankVO.getRankList();
            if (CollectionUtils.isEmpty(rankList)) {
                return Boolean.FALSE;
            }

            for (RankItem rankItem : rankList) {
                Long rank = rankItem.getRank();

                if (rank <= FAMILY_RANK_1_COIN.size()) {
                    Boolean updated = feignLanlingService.updateFamilyBoxOriginalCoin(rankItem.getId(), FAMILY_RANK_1_COIN.get(Math.toIntExact(rank) - 1), null, null, null).successData();
                    log.info("id {} rank {} updated {}", rankItem.getId(), rank, updated);
                    eternalLoveTrackManager.allActivityReceiveAward("on_list", null, FAMILY_RANK_1_COIN.get(Math.toIntExact(rank) - 1), Math.toIntExact(FAMILY_RANK_1_COIN.get(Math.toIntExact(rank) - 1)), rankItem.getId());
                }

                FamilyInfoDTO familyInfoDTO = familyFeignService.findFamilyInfoById(rankItem.getId(), ServicesAppIdEnum.lanling.getAppId()).successData();
                if (familyInfoDTO != null) {
                    notifyComponent.npcNotify(
                            ServicesAppIdEnum.lanling.getUnionId(),
                            familyInfoDTO.getOwnerUid(),
                            String.format("亲爱的%s家族，恭喜家族在第一阶段活动中获得第%s名，奖励已下发至家族奖励中，快去查看吧！", familyInfoDTO.getName(), rank)
                    );
                }
            }
        }

        if ("ump:eternal_love:family_rank:2".equals(rankContext.getRankKey())) {
            if (!Boolean.TRUE.equals(redisManager.setnx("ump:eternal_love:send_prize_idempotent:family_rank:2", System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND))) {
                return Boolean.TRUE;
            }

            RankVO rank1 = this.getRank(RankContext.builder()
                    .rankKey(String.format(FAMILY_RANK, 1))
                    .activityCode(ACTIVITY_CODE)
                    .type(RankContext.RankType.family)
                    .build());
            RankVO rank2 = this.getRank(RankContext.builder()
                    .rankKey(String.format(FAMILY_RANK, 2))
                    .activityCode(ACTIVITY_CODE)
                    .type(RankContext.RankType.family)
                    .build());

            List<List<TeamVO>> teamListList = new ArrayList<>();
            List<Long> teamValue = new ArrayList<>();
            for (int i = 0; i < 4; i++) {
                teamListList.add(new ArrayList<>());
                teamValue.add(0L);
            }

            List<RankItem> rankList1 = rank1.getRankList();
            Map<Long, Long> collect = rank2.getRankList().stream().collect(Collectors.toMap(RankItem::getId, RankItem::getValue, (oldValue, newValue) -> oldValue));
            for (int i = 0; i < rankList1.size(); i++) {
                TeamVO teamVO = TeamVO.builder().id(rankList1.get(i).getId()).name(rankList1.get(i).getName()).icon(rankList1.get(i).getIcon()).value(collect.getOrDefault(rankList1.get(i).getId(), 0L)).build();
                if (i == 0 || i == 7 || i == 9) {
                    teamListList.get(0).add(teamVO);
                    teamValue.set(0, teamValue.get(0) + teamVO.getValue());
                } else if (i == 1 || i == 6 || i == 8) {
                    teamListList.get(1).add(teamVO);
                    teamValue.set(1, teamValue.get(0) + teamVO.getValue());
                } else if (i == 2 || i == 5 || i == 10) {
                    teamListList.get(2).add(teamVO);
                    teamValue.set(2, teamValue.get(0) + teamVO.getValue());
                } else if (i == 3 || i == 4 || i == 11) {
                    teamListList.get(3).add(teamVO);
                    teamValue.set(3, teamValue.get(0) + teamVO.getValue());
                }
            }

            Integer maxIndex = maxIndex(teamValue);
            log.info("maxIndex {}", maxIndex);
            if (maxIndex != null) {
                List<TeamVO> teamList = teamListList.get(maxIndex);
                teamList.sort((o1, o2) -> Math.toIntExact(o2.getValue() - o1.getValue()));
                for (int i = 0; i < teamList.size(); i++) {
                    Boolean updated = feignLanlingService.updateFamilyBoxOriginalCoin(teamList.get(i).getId(), FAMILY_RANK_2_COIN.get(i), null, null, null).successData();
                    log.info("id {} rank {} updated {}", teamList.get(i).getId(), FAMILY_RANK_2_COIN.get(i), updated);
                    eternalLoveTrackManager.allActivityReceiveAward("on_list", null, FAMILY_RANK_2_COIN.get(i), Math.toIntExact(FAMILY_RANK_2_COIN.get(i)), teamList.get(i).getId());
                    FamilyInfoDTO familyInfoDTO = familyFeignService.findFamilyInfoById(teamList.get(i).getId(), ServicesAppIdEnum.lanling.getAppId()).successData();
                    if (familyInfoDTO != null) {
                        notifyComponent.npcNotify(
                                ServicesAppIdEnum.lanling.getUnionId(),
                                familyInfoDTO.getOwnerUid(),
                                String.format("亲爱的%s家族，恭喜家族在第二阶段活动中获得第一名，奖励已下发至家族奖励中，快去查看吧！", familyInfoDTO.getName())
                        );
                    }
                }
            }
        }

        if ("ump:eternal_love:family_rank:3".equals(rankContext.getRankKey())) {
            if (!Boolean.TRUE.equals(redisManager.setnx("ump:eternal_love:send_prize_idempotent:family_rank:3", System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND))) {
                return Boolean.TRUE;
            }

            RankVO rank2 = this.getRank(RankContext.builder()
                    .rankKey(String.format(FAMILY_RANK, 2))
                    .activityCode(ACTIVITY_CODE)
                    .type(RankContext.RankType.family)
                    .build());
            RankVO rank3 = this.getRank(RankContext.builder()
                    .rankKey(String.format(FAMILY_RANK, 3))
                    .activityCode(ACTIVITY_CODE)
                    .type(RankContext.RankType.family)
                    .build());

            List<List<TeamVO>> teamListList = new ArrayList<>();
            List<Long> teamValue = new ArrayList<>();
            for (int i = 0; i < 2; i++) {
                teamListList.add(new ArrayList<>());
                teamValue.add(0L);
            }

            List<RankItem> rankList2 = rank2.getRankList();
            Map<Long, Long> collect = rank3.getRankList().stream().collect(Collectors.toMap(RankItem::getId, RankItem::getValue, (oldValue, newValue) -> oldValue));
            for (int i = 0; i < rankList2.size(); i++) {
                TeamVO teamVO = TeamVO.builder().id(rankList2.get(i).getId()).name(rankList2.get(i).getName()).icon(rankList2.get(i).getIcon()).value(collect.getOrDefault(rankList2.get(i).getId(), 0L)).build();
                if (i == 0 || i == 2 || i == 4 || i ==6) {
                    teamListList.get(0).add(teamVO);
                    teamValue.set(0, teamValue.get(0) + teamVO.getValue());
                } else if (i == 1 || i == 3 || i == 5 || i == 7) {
                    teamListList.get(1).add(teamVO);
                    teamValue.set(1, teamValue.get(0) + teamVO.getValue());
                }
            }

            Integer maxIndex = maxIndex(teamValue);
            log.info("maxIndex {}", maxIndex);
            if (maxIndex != null) {
                List<TeamVO> teamList = teamListList.get(maxIndex);
                teamList.sort((o1, o2) -> Math.toIntExact(o2.getValue() - o1.getValue()));
                for (int i = 0; i < teamList.size(); i++) {
                    Boolean updated = feignLanlingService.updateFamilyBoxOriginalCoin(teamList.get(i).getId(), FAMILY_RANK_3_COIN.get(i), null, null, null).successData();
                    log.info("id {} rank {} updated {}", teamList.get(i).getId(), FAMILY_RANK_3_COIN.get(i), updated);
                    eternalLoveTrackManager.allActivityReceiveAward("on_list", null, FAMILY_RANK_3_COIN.get(i), Math.toIntExact(FAMILY_RANK_3_COIN.get(i)), teamList.get(i).getId());
                    FamilyInfoDTO familyInfoDTO = familyFeignService.findFamilyInfoById(teamList.get(i).getId(), ServicesAppIdEnum.lanling.getAppId()).successData();
                    if (familyInfoDTO != null) {
                        notifyComponent.npcNotify(
                                ServicesAppIdEnum.lanling.getUnionId(),
                                familyInfoDTO.getOwnerUid(),
                                String.format("亲爱的%s家族，恭喜家族在第三阶段活动中获得第一名，奖励已下发至家族奖励中，快去查看吧！", familyInfoDTO.getName())
                        );
                    }
                }
            }
        }

        return Boolean.TRUE;
    }

    private Integer maxIndex(List<Long> nums) {
        Long max = 0L;
        Integer maxIndex = null;
        for (int i = 0; i < nums.size(); i++) {
            if (nums.get(i) > max) {
                max = nums.get(i);
                maxIndex = i;
            }
        }

        return maxIndex;
    }

}
