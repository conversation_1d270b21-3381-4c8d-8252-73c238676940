package cn.yizhoucp.ump.biz.project.biz.manager.activity.galaxyAdventure;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.enums.UserPackageBizType;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.product.client.UserPackageFeignService;
import cn.yizhoucp.product.dto.PackageQueryConditionDTO;
import cn.yizhoucp.product.dto.UsePackageDTO;
import cn.yizhoucp.product.dto.UserPackageDetailDTO;
import cn.yizhoucp.product.enums.PackageUseScene;
import cn.yizhoucp.ump.api.vo.activity.galaxyAdventure.PrizeItemStrategy;
import cn.yizhoucp.ump.api.vo.activity.galaxyAdventure.PrizeStrategy;
import cn.yizhoucp.ump.api.vo.activity.galaxyAdventure.RedeemItem;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.FloweringDreamConstant;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.GalaxyAdventureConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import lombok.RequiredArgsConstructor;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.GalaxyAdventureConstant.GIFT_COLLECTION_REWARD;

/**
 * 银河探险-银河熔炼
 */
@RequiredArgsConstructor
@Component
public class GalaxyAdventureSmeltGiftManager {


    private final UserPackageFeignService userPackageFeignService;

    protected final GalaxyAdventureLogManager logManager;

    private final SendPrizeManager sendPrizeManager;

    private final NotifyComponent notifyComponent;

    private final RedissonClient redissonClient;

    private final GalaxyAdventureTrackManager trackManager;

    /**
     * 兑换礼物
     * @Param index 兑换策略索引
     */
    @ActivityCheck(activityCode = GalaxyAdventureConstant.ACTIVITY_CODE, isThrowException = false)
    public void exchangeGift(Integer index) {
        String lockKey = String.format("ump:GalaxyAdventure:treasure:exchangeGift:%s", MDCUtil.getCurUserIdByMdc());
        RLock lock = redissonClient.getLock(lockKey);
        try {
            lock.lock(3, TimeUnit.SECONDS);
            Map<Integer, PrizeStrategy> starategyMap = GalaxyAdventureConstant.prizeExchangeStrategyList.stream()
                    .collect(Collectors.toMap(PrizeStrategy::getIndex, Function.identity()));
            PrizeStrategy prizeStrategy = starategyMap.get(index);
            if (Objects.isNull(prizeStrategy)) {
                throw new ServiceException(ErrorCode.INVALID_PARAM, "熔炼方案不存在");
            }
            List<String> giftList = prizeStrategy.getExchangeStrategy().stream().map(item -> item.getOriginPrize().getPrizeKey()).collect(Collectors.toList());

            // 判断用户背包礼物是否满足规则
            PackageQueryConditionDTO queryParam = PackageQueryConditionDTO.builder().uid(MDCUtil.getCurUserIdByMdc())
                    .bizIdList(giftList)
                    .bizType(UserPackageBizType.GIFT.getCode())
                    .build();
            Map<String, UserPackageDetailDTO> packageMap = userPackageFeignService.getPackageDetailMapByCondition(queryParam).successData();
            List<PrizeItemStrategy> exchangeStrategy = prizeStrategy.getExchangeStrategy();
            for (PrizeItemStrategy prizeItemStrategy : exchangeStrategy) {
                UserPackageDetailDTO userPackageDetailDTO = packageMap.get(prizeItemStrategy.getOriginPrize().getPrizeKey());
                if (Objects.isNull(userPackageDetailDTO) || userPackageDetailDTO.getAvailableNum() < prizeItemStrategy.getRequireNum()) {
                    throw new ServiceException(ErrorCode.MISS_PARAM, "背包礼物数量不足");
                }
            }

            // 扣除资源
            for (PrizeItemStrategy prizeItemStrategy : exchangeStrategy) {
                List<UsePackageDTO> usePackageList = userPackageFeignService.usePackageWithBizIdAndType(MDCUtil.getCurAppIdByMdc(), MDCUtil.getCurUnionIdByMdc(), MDCUtil.getCurUserIdByMdc(),
                        prizeItemStrategy.getOriginPrize().getPrizeKey(), UserPackageBizType.GIFT.getCode(), prizeItemStrategy.getRequireNum().longValue(), PackageUseScene.activity.getCode());
                if (CollectionUtils.isEmpty(usePackageList)) {
                    throw new ServiceException(ErrorCode.ACTIVITY_ERROR_180020, "礼物不足");
                }
            }
            // 下发奖励
            boolean sendSucess = sendPrizeManager.sendGift(MDCUtil.getCurAppIdByMdc(), prizeStrategy.getExchangePrize().getPrizeKey(), MDCUtil.getCurUserIdByMdc(), 1L, 30, GalaxyAdventureConstant.SMELT_GIFT, false, true);

            // 记录消耗及奖励日志
            if (sendSucess) {
                logManager.recordLog(prizeStrategy);

                // 发送小助手消息
                StringBuilder userPrizeDesc = new StringBuilder();
                for (PrizeItemStrategy prizeItemStrategy : exchangeStrategy) {
                    userPrizeDesc.append("「").append(prizeItemStrategy.getOriginPrize().getPrizeName()).append("」*")
                            .append(prizeItemStrategy.getRequireNum()).append("、");
                }
                userPrizeDesc.delete(userPrizeDesc.length() - 1, userPrizeDesc.length());
                notifyComponent.npcNotify(MDCUtil.getCurUserIdByMdc(), String.format(GalaxyAdventureConstant.SMELT_MSG, userPrizeDesc, prizeStrategy.getExchangePrize().getPrizeName()));
                // 上报埋点 熔炼 、完成任务
                trackManager.allActivityReceiveAward(MDCUtil.getCurUserIdByMdc(), prizeStrategy.getExchangePrize().getPrizeKey(), prizeStrategy.getExchangePrize().getValueGold().longValue(), 1);
                trackManager.allActivityTaskFinish(MDCUtil.getCurUserIdByMdc(), "fuel_smelting", index.longValue());
            }
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.forceUnlock();
            }
        }

    }


}
