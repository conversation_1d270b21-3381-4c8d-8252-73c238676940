package cn.yizhoucp.ump.biz.project.web.rest.controller.luckyBag;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ms.core.vo.centralservices.AdminPageResult;
import cn.yizhoucp.ump.biz.project.biz.manager.luckyBag.AstrologyValueManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.AstrologyValueRecordDO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
public class AstrologyValueController {

    @Resource
    private AstrologyValueManager astrologyValueManager;

    @GetMapping("/api/inner/astrology-value/get-by-uid")
    public Result<Long> getByUid(@RequestParam("uid") Long uid) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> astrologyValueManager.getBalanceByUid(uid));
    }

    @GetMapping("/api/inner/astrology-value/add-value")
    public Result<Boolean> addValue(@RequestParam("uid") Long uid, @RequestParam("value") Long value, @RequestParam("desc") String desc) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> astrologyValueManager.increase(uid, value, desc));
    }

    @GetMapping("/api/admin/ump/astrology-value/page-record")
    public Result<AdminPageResult<AstrologyValueRecordDO>> pageRecord(@RequestParam Integer page, @RequestParam Integer size, @RequestParam(required = false) Long userId) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> astrologyValueManager.pageRecord(page, size, userId));
    }
}
