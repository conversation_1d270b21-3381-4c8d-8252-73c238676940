package cn.yizhoucp.ump.biz.project.biz.enums.activity.familyKey;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通知文案
 *
 * @author: lianghu
 */
@Getter
@AllArgsConstructor
public enum NotifyMsgEnum {

    FAMILY_SPEAK("family_speak", "家族发言", "叮，在家族发言成功，家族车队因你向前 8km！助力家族PK成功，可瓜分 PK 奖金！！ "),
    LIMIT_CONTRIBUTION("limit_contribution", "达到 40 km", "赞，你已为家族车队贡献 40 km啦！家族送你一张助力券，可通往便利店购买超值商品哦～"),
    GIVE_GIFT_XDBD("give_gift_xdbd", "送礼心动爆灯", "叮，收到/送出心动爆灯，家族车队因你向前 %s km！助力家族夺得胜利，贡献越多瓜分 PK 胜利金越多\uD83D\uDCB0"),
    GIVE_GIFT_HYMM("give_gift_hymm", "送礼好运猫咪", "叮，收到/送出好运猫咪，家族车队因你向前 %s km！贡献越多瓜分 PK 胜利金越多\uD83D\uDCB0"),
    GIVE_GIFT_YSSH("give_gift_yssh", "送礼一生守护", "叮，收到/送出一生守护，家族车队因你向前 %s km！贡献越多瓜分 PK 胜利金越多\uD83D\uDCB0"),
            ;

    private String code;
    private String desc;
    private String text;
}
