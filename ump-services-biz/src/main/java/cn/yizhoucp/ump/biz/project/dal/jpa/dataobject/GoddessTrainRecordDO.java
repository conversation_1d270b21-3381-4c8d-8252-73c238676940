package cn.yizhoucp.ump.biz.project.dal.jpa.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2022/8/29 19:36
 * @Version 1.0
 */
@Table(name = "goddess_train_record")
@Entity
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GoddessTrainRecordDO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    /** 应用唯一标识 */
    @Column(name = "union_id")
    private String unionId;
    /** 应用 ID */
    @Column(name = "app_id")
    private Long appId;
    /** 用户id */
    private Long uid;
    /** 活动编号 */
    @Column(name = "activity_code")
    private String activityCode;
    /** 结业状态 */
    @Column(name = "is_graduation")
    private String isGraduation;
    /** 创建时间 */
    @Column(name = "create_time")
    private LocalDateTime createTime;
    /** 更新时间 */
    @Column(name = "update_time")
    private LocalDateTime updateTime;
    /** 扩展字段 */
    @Column(name = "ext_data")
    private String extData;


}
