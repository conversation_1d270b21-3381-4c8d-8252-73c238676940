package cn.yizhoucp.ump.biz.project.biz.enums.swordsman;


/**
 * <AUTHOR>
 * @description 剑客等级信息枚举
 * @createDate 2024/11/16
 */
public enum SwordsmanLevelInfoEnum {

    level0(0, 0, 0, 0,10L),
    level1(1, 99, 60, 72,20L),
    level2(2, 999, 120, 144,30L),
    level3(3, 2999, 140, 168,40L),
    level4(4, 5999, 180, 216,50L),
    level5(5, 8999, 200, 240,60L),
    level6(6, 13999, 220, 264,70L),
    level7(7, 19999, 240, 288,80L),
    level8(8, 25999, 260, 312,90L),
    level9(9, 33999, 280, 336,100L),
    level10(10, 39999, 300, 360,110L);


    /**
     * 等级
     */
    private Integer level;

    /**
     * 经验值
     */
    private Integer experience;

    /**
     * 掉落元宝速度
     */
    private Integer ingotSpeed;

    /**
     * 酒商掉落元宝速度
     */
    private Integer wineIngotSpeed;

    /**
     * 切换身份需要的金币
     */
    private Long changeIdentityNeedCoin;


    SwordsmanLevelInfoEnum(Integer level, Integer experience, Integer ingotSpeed, Integer wineIngotSpeed, Long changeIdentityNeedCoin) {
        this.level = level;
        this.experience = experience;
        this.ingotSpeed = ingotSpeed;
        this.wineIngotSpeed = wineIngotSpeed;
        this.changeIdentityNeedCoin = changeIdentityNeedCoin;
    }

    public static SwordsmanLevelInfoEnum getInfoByLevel(int level) {
        if (level <= 0) {
            return SwordsmanLevelInfoEnum.level0;
        }
        for (SwordsmanLevelInfoEnum e : SwordsmanLevelInfoEnum.values()) {
            if (e.getLevel() == level) {
                return e;
            }
        }
        return SwordsmanLevelInfoEnum.level0;
    }

    public static SwordsmanLevelInfoEnum getNextLevel(Integer level) {
        if (null == level) {
            return SwordsmanLevelInfoEnum.level1;
        }
        if (level <= 0) {
            return SwordsmanLevelInfoEnum.level1;
        }
        int nextLevel = level + 1;
        if (nextLevel >= 10) {
            return SwordsmanLevelInfoEnum.level10;
        }
        for (SwordsmanLevelInfoEnum e : SwordsmanLevelInfoEnum.values()) {
            if (e.getLevel() == nextLevel) {
                return e;
            }
        }
        return SwordsmanLevelInfoEnum.level1;
    }


    /**
     * 根据经验值获取等级
     * @param experience 用户当前的经验值
     * @return 对应的 LevelEnum 实例
     */
    public static SwordsmanLevelInfoEnum getLevelByExperience(Integer experience) {
        if (experience == null) {
            return SwordsmanLevelInfoEnum.level1;
        }
        // 默认等级
        SwordsmanLevelInfoEnum result = level1;
        for (SwordsmanLevelInfoEnum level : SwordsmanLevelInfoEnum.values()) {
            if (experience >= level.getExperience()) {
                result = level;
            } else {
                // 等级从低到高，匹配到后直接退出
                break;
            }
        }
        return result;
    }

    public Integer getLevel() {
        return level;
    }

    public Integer getExperience() {
        return experience;
    }

    public Integer getIngotSpeed() {
        return ingotSpeed;
    }

    public Integer getWineIngotSpeed() {
        return wineIngotSpeed;
    }

    public Long getChangeIdentityNeedCoin() { return changeIdentityNeedCoin;}


}
