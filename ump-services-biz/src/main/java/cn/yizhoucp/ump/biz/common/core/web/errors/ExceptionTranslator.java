package cn.yizhoucp.ump.biz.common.core.web.errors;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * Controller advice to translate the server side exceptions to client-friendly json structures.
 */
@Slf4j
@ControllerAdvice
public class ExceptionTranslator {

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    public Result processValidationError(MethodArgumentNotValidException ex) {
        log.error("捕获异常，ex={}", ex);
        ex.printStackTrace();
        ;
        BindingResult result = ex.getBindingResult();
        List<FieldError> fieldErrors = result.getFieldErrors();
        String error = CollectionUtils.isEmpty(fieldErrors) ? ErrorCode.MISS_PARAM.getDescription() : fieldErrors.get(0).getDefaultMessage();
        return new Result(ErrorCode.MISS_PARAM);
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseBody
    public Result processHttpMessageNotReadableException(Exception ex) {
        log.error("捕获异常，ex={}", ex);
        ex.printStackTrace();
        ;
        return new Result(ErrorCode.SERVER_EXCEPTION);
    }

    @ExceptionHandler(ServiceException.class)
    @ResponseBody
    public Result processBizCoreException(ServiceException ex) {
        log.warn("业务异常 msg:{}, ex:{}", ex.getErrorMsg(), ex);
        return new Result(ex.getErrorCode(), ex.getErrorMsg());
    }

    @ExceptionHandler(MissingServletRequestParameterException.class)
    @ResponseBody
    public Result processMissException(MissingServletRequestParameterException ex) {
        log.error("捕获异常，ex={}", ex);
        ex.printStackTrace();
        ;
        return new Result(ErrorCode.MISS_PARAM);
    }

    @ExceptionHandler(Exception.class)
    @ResponseBody
    public Result processRuntimeException(Exception ex) {
        log.error("捕获异常，ex={}", ex);
        ex.printStackTrace();
        ;
        return new Result(ErrorCode.SERVER_EXCEPTION);
    }
}
