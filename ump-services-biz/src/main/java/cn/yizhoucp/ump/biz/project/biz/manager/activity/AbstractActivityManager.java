package cn.yizhoucp.ump.biz.project.biz.manager.activity;

import cn.yizhoucp.ms.core.base.env.DynamicEnvManager;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityUrlUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ActivityDO;
import cn.yizhoucp.ump.biz.project.dal.service.ActivityService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;

import javax.annotation.Resource;

/**
 * 活动基类
 *
 * @author: lianghu
 */
public abstract class AbstractActivityManager {

    @Value("${spring.profiles.active}")
    private String env;
    @Resource
    private Environment environment;
    @Resource
    private ActivityService activityService;

    /**
     * 获取活动编号
     *
     * @param
     * @return
     */
    public abstract String getActivityCode();

    /**
     * 获取活动地址
     * <p>
     * 基础参数：uid、from
     *
     * @param param
     * @param from
     * @return
     */
    public String getActivityUrl(BaseParam param, String from) {
        ActivityDO activityDO = activityService.getByAppIdAndCode(param.getAppId(), getActivityCode());
        return String.format(ActivityUrlUtil.getH5BaseUrl(param.getUnionId(), env, Boolean.TRUE, environment.getProperty(DynamicEnvManager.GLOBAL_ENV_NAME)) + activityDO.getActivityUrl() + "?uid=%s&from=%s", param.getUid(), from);
    }

}
