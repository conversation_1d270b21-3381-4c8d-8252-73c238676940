package cn.yizhoucp.ump.biz.project.biz.manager.activity.wlzb;

import cn.hutool.core.util.URLUtil;
import cn.yizhoucp.family.api.client.FamilyFeignService;
import cn.yizhoucp.family.api.dto.family.FamilyInfoDTO;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.TopicConstant;
import cn.yizhoucp.ms.core.base.TopicTagEnum;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.enums.ServicesNameEnum;
import cn.yizhoucp.ms.core.base.enums.UserPackageBizType;
import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.ms.core.vo.landingservices.UserCharmGenerosityVO;
import cn.yizhoucp.ms.core.vo.roomservices.RoomHomePageVO;
import cn.yizhoucp.ms.core.vo.userservices.UserBaseVO;
import cn.yizhoucp.order.api.enums.OrderTypeEnum;
import cn.yizhoucp.order.api.param.ProductItem;
import cn.yizhoucp.order.api.param.addOrder.WulinHegemonyParam;
import cn.yizhoucp.order.api.vo.OrderVO;
import cn.yizhoucp.product.client.UserPackageFeignService;
import cn.yizhoucp.product.dto.*;
import cn.yizhoucp.product.enums.PackageUseScene;
import cn.yizhoucp.starter.cassandra.base.enums.MqDelayLevelEnum;
import cn.yizhoucp.starter.cassandra.base.kafka.YzKafkaProducerManager;
import cn.yizhoucp.starter.cassandra.base.rocketmq.RocketmqProducerManager;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.param.activity.wlzb.ExchangeParam;
import cn.yizhoucp.ump.api.vo.activity.wlzb.StakeVO;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.constant.AdSpaceConstant;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.WuLinHegemonyConstant;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw.inner.ExpiredTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.barrageList.DefaultBarrageManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.doDrawStrategy.common.ProbStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.commonActivity.ActivityStatusManager;
import cn.yizhoucp.ump.biz.project.biz.manager.pop.PopManager;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.FeignRoomService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.FeignSnsService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.coin.FeignCoinService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.order.AddActivityOrderRemoteService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.UserRemoteService;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityTimeUtil;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityUrlUtil;
import cn.yizhoucp.ump.biz.project.common.ActivityComponent;
import cn.yizhoucp.ump.biz.project.common.handler.HandlerContext;
import cn.yizhoucp.ump.biz.project.common.handler.component.bizHandler.PopBizHandler;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.LoginPopJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.WuLinHegemonyJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.WuLinHegemonyResultJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.WuLinStakeDetailsJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.LoginPopDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.WlzbGameDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.WlzbResultDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.WlzbStakeDetailsDO;
import cn.yizhoucp.ump.biz.project.dal.service.DrawPoolItemService;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Example;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static cn.yizhoucp.ms.core.base.TopicTagEnum.TOPIC_WLZB_COMPUTE_TASK;
import static cn.yizhoucp.ump.api.enums.ActivityCheckListEnum.WU_LIN_HEGEMONY;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.WuLinHegemonyConstant.*;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.WuLinHegemonyConstant.Status.created;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.WuLinHegemonyConstant.Status.started;
import static cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw.inner.PrizeTypeEnum.PRIZE_GIFT;
import static cn.yizhoucp.ump.biz.project.biz.util.ActivityTimeUtil.getNow;

/**
 * 武林争霸 业务处理
 *
 * <AUTHOR>
 * @Date 2023/3/8 15:42
 * @Version 1.0
 */
@Slf4j
@Service
public class WuLinHegemonyBizManager implements ActivityComponent, PopBizHandler {

    @Resource
    private ActivityStatusManager activityStatusManager;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private FeignCoinService feignCoinService;
    @Resource
    private FamilyFeignService familyFeignService;
    @Resource
    private FeignRoomService feignRoomService;
    @Resource
    private RedisManager redisManager;
    @Resource
    private ProbStrategy probStrategy;
    @Resource
    private DrawPoolItemService drawPoolItemService;
    @Resource
    private UserRemoteService userRemoteService;
    @Resource
    private AddActivityOrderRemoteService addActivityOrderRemoteService;
    @Resource
    private WuLinHegemonyJpaDAO wuLinHegemonyJpaDAO;
    @Resource
    private RocketmqProducerManager rocketmqProducerManager;
    @Resource
    private YzKafkaProducerManager yzKafkaProducerManager;
    @Value("${spring.profiles.active}")
    private String env;
    @Resource
    private WuLinHegemonyResultJpaDAO wuLinHegemonyResultJpaDAO;
    @Resource
    private WuLinStakeDetailsJpaDAO wuLinStakeDetailsJpaDAO;
    @Resource
    private UserPackageFeignService userPackageFeignService;
    @Resource
    private PopManager popManager;
    @Resource
    private LoginPopJpaDAO loginPopJpaDAO;
    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private DefaultBarrageManager defaultBarrageManager;
    @Resource
    private NotifyComponent notifyComponent;
    @Resource
    FeignSnsService feignSnsService;

    private static final Set<String> ACTIVITY_PRIZE_SET = Sets.newHashSet("LSF_GIFT", "TWFH_GIFT", "SDYY_GIFT", "LYYJ_GIFT", "FRP_GIFT", "YZ_GIFT");

    /**
     * 开始比赛
     *
     * @param param
     * @return
     */
    public Boolean startGame(BaseParam param) {
        // 查询待开启比赛记录
        List<WlzbGameDO> games = wuLinHegemonyJpaDAO.findAll(Example.of(WlzbGameDO.builder()
                .appId(param.getAppId())
                .unionId(param.getUnionId())
                .status(created.name()).build()));
        if (CollectionUtils.isEmpty(games)) {
            log.warn("无可开启比赛");
            return Boolean.FALSE;
        }
        WlzbGameDO game = games.get(0);
        LocalDateTime now = ActivityTimeUtil.getNow(getActivityCode());
        if (game.getStartTime().isAfter(now)) {
            log.warn("未到比赛开启时间 game:{}", JSON.toJSONString(game));
            return Boolean.FALSE;
        }
        game.setStartTime(now);
        game.setStatus(started.name());
        game.setValid(1);
        wuLinHegemonyJpaDAO.save(game);
        // 设置当前比赛缓存
        redisManager.set(WuLinHegemonyConstant.CURRENT_GAME_ID, game.getGameId(), RedisManager.ONE_DAY_SECONDS);

        // 延迟 50s 锁定比赛 MQ
        Map mqParam = new HashMap<>();
        mqParam.put("times", 0);
        mqParam.put("gameId", game.getGameId());
        rocketmqProducerManager.sendDelayMessage(TopicConstant.TOPIC_WLZB_EVENT.getTopicKey(), TopicTagEnum.TOPIC_WLZB_LOCK_GAME.getTagKey(), JSON.toJSONString(mqParam), System.currentTimeMillis() + "", MqDelayLevelEnum.THIRTY_SECOND);
        log.info("wlzb 开启比赛 gameId:{}", game.getGameId());
        return Boolean.TRUE;
    }

    /**
     * 锁定比赛 & 结算处理
     *
     * @param param
     * @return
     */
    public Boolean lockGame(BaseParam param, JSONObject bizParam) {
        WlzbGameDO game = wuLinHegemonyJpaDAO.findByGameId(bizParam.getLong("gameId"));

        // 幂等保证
        boolean lock = redisManager.setnx(String.format(LOCK_LOCK, game.getGameId()), System.currentTimeMillis(), RedisManager.ONE_DAY_SECONDS);
        if (!lock) {
            return Boolean.FALSE;
        }
        // 发送延迟 5s 通知比赛结果 MQ
        Map mqParam = new HashMap<>();
        mqParam.put("gameId", game.getGameId());
        rocketmqProducerManager.sendDelayMessage(TopicConstant.TOPIC_WLZB_EVENT.getTopicKey(), TopicTagEnum.TOPIC_WLZB_DRAW_GAME.getTagKey(), JSON.toJSONString(mqParam), System.currentTimeMillis() + "", MqDelayLevelEnum.FIVE_SECOND);

        // 锁定比赛，计算并持久化获胜门派
        log.info("wlzb 锁定结算比赛 gameId:{}", game.getGameId());
        game.setStatus(WuLinHegemonyConstant.Status.locked.name());
        game.setLockTime(LocalDateTime.now());
        Sect sect = getWinSect(game.getGameId());
//        Sect sect = Sect.diancang;
        game.setWinSect(sect.name());
        wuLinHegemonyJpaDAO.save(game);

        // 创建下一场比赛信息
        createNextGame(param, game);

        // 分片计算全服用户比赛结果
        String winSectUserInfoKey = String.format(STAKE_INFO, game.getGameId(), sect.name());
        Long winUserSize = Optional.ofNullable(redisManager.zSetGetSize(winSectUserInfoKey)).orElse(0L);
        if (winUserSize < 1L) {
            log.info("wlzb 本轮比赛无人获胜");
            return Boolean.FALSE;
        }
        int offset = 0;
        int count = 10;
        while (offset < winUserSize) {
            JSONObject taskInfo = new JSONObject();
            taskInfo.put("game", JSON.toJSONString(game));
            taskInfo.put("key", winSectUserInfoKey);
            taskInfo.put("offset", offset);
            taskInfo.put("count", count);
            rocketmqProducerManager.sendNormalMessage(TopicConstant.TOPIC_WLZB_EVENT.getTopicKey(), TOPIC_WLZB_COMPUTE_TASK.getTagKey(), JSON.toJSONString(taskInfo), System.currentTimeMillis() + "");
            offset += count;
        }
        return Boolean.TRUE;
    }

    /**
     * 创建比赛
     *
     * @param param
     * @return
     */
    public Boolean createNextGame(BaseParam param, WlzbGameDO lastGame) {
        WlzbGameDO game = wuLinHegemonyJpaDAO.save(WlzbGameDO.builder()
                .appId(param.getAppId())
                .unionId(param.getUnionId())
                .gameId(lastGame.getGameId() + 1)
                .status(WuLinHegemonyConstant.Status.created.name())
                .startTime(lastGame.getStartTime().plusSeconds(60))
                .lockTime(lastGame.getStartTime().plusSeconds(110))
                .drawnTime(lastGame.getStartTime().plusSeconds(115))
                .finishTime(lastGame.getStartTime().plusSeconds(120))
                .valid(0).build());
        // 超过截止时间时创建当晚 20:00:00 的比赛
        LocalTime localTime = game.getStartTime().toLocalTime();
        if (localTime.isAfter(LocalTime.parse("01:59:59")) && localTime.isBefore(LocalTime.parse("17:59:59"))) {
            game.setStartTime(game.getStartTime().plusHours(18));
            game.setLockTime(game.getLockTime().plusHours(18));
            game.setDrawnTime(game.getDrawnTime().plusHours(18));
            game.setFinishTime(game.getFinishTime().plusHours(18));
            wuLinHegemonyJpaDAO.save(game);
        }
        log.info("wlzb 创建新比赛 game:{}", JSON.toJSONString(game));
        return Boolean.TRUE;
    }

    /**
     * 比赛开奖
     *
     * @param param
     * @return
     */
    public Boolean drawGame(BaseParam param, JSONObject bizParam) {
        WlzbGameDO game = wuLinHegemonyJpaDAO.findByGameId(bizParam.getLong("gameId"));

        game.setStatus(WuLinHegemonyConstant.Status.drawn.name());
        game.setDrawnTime(LocalDateTime.now());

        // 通知结束比赛
        Map mqParam = new HashMap<>();
        mqParam.put("gameId", game.getGameId());
        rocketmqProducerManager.sendDelayMessage(TopicConstant.TOPIC_WLZB_EVENT.getTopicKey(), TopicTagEnum.TOPIC_WLZB_FINISH_GAME.getTagKey(), JSON.toJSONString(mqParam), System.currentTimeMillis() + "", MqDelayLevelEnum.FIVE_SECOND);

        // 更新比赛状态
        wuLinHegemonyJpaDAO.save(game);
        // 清空比赛缓存
        log.info("wlzb 比赛开奖 gameId:{}", game.getGameId());
        // 通知用户
        notifiedUser(param, game.getGameId(), game.getWinSect());
        return Boolean.TRUE;
    }

    /**
     * 结束比赛
     *
     * @param param
     * @return
     */
    public Boolean finishGame(BaseParam param, JSONObject bizParam) {
        WlzbGameDO game = wuLinHegemonyJpaDAO.findByGameId(bizParam.getLong("gameId"));

        // 更新比赛状态
        log.debug("debug-game:{}", JSON.toJSONString(game));
        game.setStatus(WuLinHegemonyConstant.Status.finished.name());
        game.setFinishTime(LocalDateTime.now());
        game.setValid(0);
        wuLinHegemonyJpaDAO.saveAndFlush(game);
        log.info("wlzb 结束比赛 gameId:{}", game.getGameId());
        return Boolean.TRUE;
    }

    /**
     * 比赛结算
     *
     * @param param
     * @param bizParam
     * @return
     */
    public Boolean settleDown(BaseParam param, JSONObject bizParam) {
        long start = System.currentTimeMillis();
        log.info("wlzb 比赛结算处理 bizParam:{}", JSON.toJSONString(bizParam));
        WlzbGameDO game = bizParam.getJSONObject("game").toJavaObject(WlzbGameDO.class);
        String winSectUserInfoKey = bizParam.getString("key");
        int offset = bizParam.getInteger("offset");
        int count = bizParam.getInteger("count");
        Set<ZSetOperations.TypedTuple<Object>> typedTuples = redisManager.reverseRangeByScoreWithScores(winSectUserInfoKey, 0, Double.MAX_VALUE, offset, count);
        log.debug("wlzb 结算用户清单 typedTuples:{}", JSON.toJSONString(typedTuples));
        if (CollectionUtils.isEmpty(typedTuples)) {
            return Boolean.FALSE;
        }
        List<WlzbResultDO> result = Lists.newArrayList();
        Map<Object, Object> notifyResult = new HashMap<>();
        for (ZSetOperations.TypedTuple<Object> typedTuple : typedTuples) {
            if (null == typedTuple.getValue() || null == typedTuple.getScore()) {
                continue;
            }
            // 获取押注用户 ID 与投入金币
            Long uid = Long.parseLong(typedTuple.getValue().toString());
            Long inCoin = typedTuple.getScore().longValue();
            // 设置幂等键（30s 过期）
            if (!redisManager.setnx(String.format(SETTLE_DOWN_IDEM_KEY, game.getGameId(), uid), System.currentTimeMillis(), 30)) {
                log.warn("wlzb 结算分片重复 gameId:{}, uid:{}", game.getGameId(), uid);
                continue;
            }
            // 查询用户信息
            UserBaseVO user = userRemoteService.getBasicAll(param.getAppId(), uid, Boolean.TRUE);
            // 计算赢得金币数量
            Long outCoin = inCoin * Sect.valueOf(game.getWinSect()).getMultiple();
            // 累计用户总产出
            redisManager.incrLong(TOTAL_OUTPUT, outCoin, RedisManager.ONE_DAY_SECONDS * 3);
            // 累计总榜
            redisManager.zIncrby(String.format(WU_LIN_RANK, getNowWuLinGameTime(getActivityCode())), uid.toString(), outCoin.doubleValue(), RedisManager.ONE_DAY_SECONDS * 3);
            log.info("wlzb 累计总榜 rankKey {} gameId:{}, uid:{}, inCoin:{}, outCoin:{}", String.format(WU_LIN_RANK, getNowWuLinGameTime(getActivityCode())), game.getGameId(), uid, inCoin, outCoin);
            // 计算奖品
            param.setUid(uid);
            List<SendPrizeDTO> gamePrize = getGamePrize(param, outCoin);
            // 下发礼物
            prizeHandle(param, gamePrize);
            // 添加弹幕
            defaultBarrageManager.putBarrage(getActivityCode(), String.format("恭喜 <strong style='color: #F3C04D;'>%s</strong> 获得了 %s", user.getName(), getBarrageMsg(gamePrize)));
            // 添加待持久化集合
            result.add(WlzbResultDO.builder().appId(param.getAppId()).unionId(param.getUnionId()).uid(uid).gameId(game.getGameId()).winSect(game.getWinSect()).stakeTime(Long.valueOf(inCoin / 20).intValue()).coin(outCoin).status(1).extData(JSON.toJSONString(gamePrize)).build());
            // 比赛结算埋点
            buriedGameResult(uid, game, gamePrize, -1L);
        }
        // 添加通知缓存集合
        redisManager.hmset(String.format(GAME_RESULT_NOTIFY_INFO, game.getGameId(), game.getWinSect()), notifyResult, RedisManager.ONE_DAY_SECONDS);
        // 持久化比赛结果
        log.debug("持久化比赛结果 gameId {}, offset:{}, count:{}, size:{}, ts:{}", game.getGameId(), offset, count, typedTuples.size(), System.currentTimeMillis() - start);
        wuLinHegemonyResultJpaDAO.saveAll(result);
        return Boolean.TRUE;
    }

    private String getBarrageMsg(List<SendPrizeDTO> prizes) {
        String barrageMsg = "";
        if (!CollectionUtils.isEmpty(prizes)) {
            for (SendPrizeDTO prize : prizes) {
                barrageMsg += prize.getPrizeName() + "*" + prize.getPrizeNum() + " ";
            }
        }
        return barrageMsg;
    }

    public Boolean sendGiftHandle(BaseParam param, List<CoinGiftGivedModel> modelList) {
        if (!activityStatusManager.activityIsEnable(param, getActivityCode())) {
            return Boolean.FALSE;
        }
        // 遍历处理荣誉值
        for (CoinGiftGivedModel gift : modelList) {
            log.debug("wlzb 名誉值礼物过滤 gift:{}", gift.getGiftKey());
            if (ACTIVITY_PRIZE_SET.contains(gift.getGiftKey())) {
                log.info("wlzb 名誉值处理 gift:{}", gift.getGiftKey());
                param.setUid(gift.getToUid());
                reputationValHandle(param, gift.getCoin());
            }
        }
        return Boolean.TRUE;
    }

    private Boolean reputationValHandle(BaseParam param, Long value) {
        log.info("wlzb 名誉值 uid:{}, value:{}", param.getUid(), value);
        RLock lock = redissonClient.getLock(String.format(REPUTATION_VALUE_HANDLE_LOCK, param.getUid()));
        lock.lock();
        try {
            // 累计积分
            Double after = redisManager.hincr(COIN_DURING_ACTIVITY, param.getUid().toString(), value, RedisManager.ONE_DAY_SECONDS * 30);
            // 获取已处理积分
            Double history = Optional.ofNullable((Integer) redisManager.hget(COIN_DONE_DURING_ACTIVITY, param.getUid().toString())).orElse(0).doubleValue();
            // 计算待处理积分
            Double diff = after - history;
            log.info("wlzb 名誉值 after:{}, history:{}, diff:{}", after, history, diff);
            // 基金处理
            if (diff >= 52) {
                // 累计基金值
                redisManager.hincr(REPUTATION_VALUE, param.getUid().toString(), Double.valueOf(diff.longValue() / 52) * 20, RedisManager.ONE_DAY_SECONDS * 30);
                // 累计已处理积分
                redisManager.hincr(COIN_DONE_DURING_ACTIVITY, param.getUid().toString(), diff, RedisManager.ONE_DAY_SECONDS * 30);
            }
        } finally {
            lock.unlock();
        }
        return Boolean.TRUE;
    }

    private Boolean prizeHandle(BaseParam param, List<SendPrizeDTO> gamePrize) {
        sendPrizeManager.sendPrize(param, gamePrize);
        return Boolean.TRUE;
    }

    private List<SendPrizeDTO> getGamePrize(BaseParam param, Long coin) {
        Map<String, SendPrizeDTO> result = Maps.newHashMap();
        while (coin >= 20L) {
            if (coin >= 50000L) {
                putPrize(result, SendPrizeDTO.builder().prizeName("洛神赋").prizeNum(1).prizeValue("LSF_GIFT").valueGold(50000L).build());
                coin -= 50000L;
            } else if (coin >= 30000L) {
                putPrize(result, SendPrizeDTO.builder().prizeName("天山飞狐").prizeNum(1).prizeValue("TWFH_GIFT").valueGold(30000L).build());
                coin -= 30000L;
            } else if (coin >= 10000L) {
                putPrize(result, SendPrizeDTO.builder().prizeName("神都夜宴").prizeNum(1).prizeValue("SDYY_GIFT").valueGold(10000L).build());
                coin -= 10000L;
            } else if (coin >= 1000L) {
                putPrize(result, SendPrizeDTO.builder().prizeName("良缘永结").prizeNum(1).prizeValue("LYYJ_GIFT").valueGold(1000L).build());
                coin -= 1000L;
            } else if (coin >= 100L) {
                putPrize(result, SendPrizeDTO.builder().prizeName("芙蓉佩").prizeNum(1).prizeValue("FRP_GIFT").valueGold(100L).build());
                coin -= 100L;
            } else if (coin >= 20L) {
                putPrize(result, SendPrizeDTO.builder().prizeName("玉镯").prizeNum(1).prizeValue("YZ_GIFT").valueGold(20L).build());
                coin -= 20L;
            }
        }
        return result.values().stream().map(p -> {
            // 补充默认下发礼物信息
            p.setToUid(param.getUid());
            p.setAppId(ServicesAppIdEnum.lanling.getAppId());
            p.setExpiredType(ExpiredTypeEnum.EFFECTIVE_DAYS.getCode());
            p.setPrizeEffectiveDay(30);
            p.setScene(getActivityCode());
            p.setPrizeType(PRIZE_GIFT.getCode());
            return p;
        }).collect(Collectors.toList());
    }

    /**
     * 获取当前进行比赛编号
     *
     * @param
     * @return
     */
    public Long getCurrentGameId(BaseParam param) {
        Long gameId = redisManager.getLong(CURRENT_GAME_ID);
        if (Objects.isNull(gameId)) {
            log.error("获取游戏异常 gameId:{}", gameId);
            throw new ServiceException(ErrorCode.MISS_PARAM, "gameId 异常");
        }
        return gameId;
    }

    public Long getNextGameId(BaseParam param, Long currGameId) {
        if (Objects.nonNull(currGameId)) {
            return currGameId + 1L;
        }
        return getCurrentGameId(param) + 1L;
    }

    /**
     * 获取当前进行比赛编号
     *
     * @param
     * @return
     */
    public Long getNextGameId(BaseParam param) {
        return getNextGameId(param, null);
    }


    /**
     * 押注
     *
     * @return
     */
    @ActivityCheck(activityCode = "wu-lin-hegemony")
    public StakeVO stake(BaseParam param, List<Integer> stakeList) {

        //if (LocalTime.now().isAfter(LocalTime.parse("02:00:00")) && LocalTime.now().isBefore(LocalTime.parse("20:00:00"))) {
        //    throw new ServiceException(ErrorCode.SERVER_EXCEPTION, "比赛还未开始哦");
        //}

        // 检查押注信息
        checkStakeList(stakeList);

        // 1、获取本轮比赛id
        Long gameId = getCurrentGameId(param);
        //  查询 db
        WlzbGameDO wlzbGameDO = wuLinHegemonyJpaDAO.findByGameId(gameId);
        if (Objects.isNull(wlzbGameDO)) {
            throw new ServiceException(ErrorCode.SERVER_EXCEPTION, "活动不存在");
        }
        if (!StringUtils.equalsIgnoreCase(wlzbGameDO.getStatus(), Status.started.name())) {
            throw new ServiceException(ErrorCode.SERVER_EXCEPTION, "现在不能选择门派哦");
        }
        WlzbStakeDetailsDO stakeDetailsDO;
        RLock lock = redissonClient.getLock(String.format(DO_STAKE_LOCK, param.getUid(), wlzbGameDO.getGameId()));
        if (lock.tryLock()) {
            try {
                // 查押注信息
                stakeDetailsDO = wuLinStakeDetailsJpaDAO.findByUidAndGameId(param.getUid(), gameId);
                if (Objects.isNull(stakeDetailsDO)) {
                    stakeDetailsDO = WlzbStakeDetailsDO.builder()
                            .appId(param.getAppId())
                            .unionId(param.getUnionId())
                            .uid(param.getUid())
                            .gameId(gameId)
                            .createTime(LocalDateTime.now())
                            .updateTime(LocalDateTime.now())
                            .build();
                }

                checkStakeDetailed(stakeList, stakeDetailsDO.getStakeResource());

                // 计算
                int resources = stakeList.stream().reduce(Integer::sum).orElse(0) - stakeDetailsDO.getStakeNums();
                if (resources > 0) {
                    // 扣除资源
                    List<UsePackageDTO> usePackageList = userPackageFeignService.usePackageWithBizIdAndType(param.getAppId(), param.getUnionId(), param.getUid(), ACTIVITY_GIFT_KEY, UserPackageBizType.GIFT.getCode(),
                            (long) resources, PackageUseScene.activity.getCode());
                    if (CollectionUtils.isEmpty(usePackageList)) {
                        throw new ServiceException(ErrorCode.ACTIVITY_ERROR_180020, "资源不足");
                    }
                    buriedUserGift(stakeDetailsDO.getStakeResource(), stakeList, wlzbGameDO);
                    // 添加资源缓存
                    for (int i = 0; i < stakeList.size(); i++) {
                        if (stakeList.get(i) > 0) {
                            log.debug("stake sectName {}", Sect.findByOrdinal(i));
                            redisManager.zSetAdd(String.format(STAKE_INFO, gameId, Sect.findByOrdinal(i).name()),
                                    param.getUid().toString(), TO_COIN_RATIO * stakeList.get(i).doubleValue(), DateUtil.ONE_DAY_SECONDS, TimeUnit.SECONDS);
                        }
                    }
                    // 标记 用户参与本轮比赛
                    redisManager.sSetExpire(String.format(JOIN_GAME_REMARK, gameId), DateUtil.ONE_MINUTE_SECONDS * 5, param.getUid());
                    // 累计 全服用户总投入
                    redisManager.incrLong(TOTAL_INPUT, resources * TO_COIN_RATIO, DateUtil.ONE_WEEK_SECONDS * 2);
                    // 累计 当前比赛各门派的总投入
                    accumulateCurGameSectTotal(stakeDetailsDO.getStakeResource(), stakeList);
                    log.info("stake details list oldStake {} newStake {}", JSON.toJSONString(stakeDetailsDO.getStakeResource()), JSON.toJSONString(stakeList));
                    // 更新db
                    stakeDetailsDO.setDetails(JSON.toJSONString(stakeList));
                    wuLinStakeDetailsJpaDAO.saveAndFlush(stakeDetailsDO);
                }
            } finally {
                lock.unlock();
            }
        } else {
            throw new ServiceException(ErrorCode.SERVER_EXCEPTION, "点的太快哦");
        }
        return StakeVO.builder().stakeList(stakeDetailsDO.getStakeResource()).resource(getResource(param)).build();
    }

    private Long getResource(BaseParam param) {
        List<UserPackageProductDetailDTO> userPackageProductDetailDTOS = userPackageFeignService.getPackageDetailWithProductByCondition(PackageQueryConditionDTO.builder().appId(ServicesAppIdEnum.lanling.getAppId())
                .uid(param.getUid()).unionId(param.getUnionId()).bizType(UserPackageBizType.PRODUCT.getCode()).bizId(ACTIVITY_GIFT_KEY)
                .build()).successData();
        if (CollectionUtils.isEmpty(userPackageProductDetailDTOS)) {
            return 0L;
        }
        log.debug("getResource userPackageProductDetailDTOS {}", JSON.toJSONString(userPackageProductDetailDTOS));
        return userPackageProductDetailDTOS.get(0).getAvailableNum();
    }

    private void buriedUserGift(List<Integer> oldStake, List<Integer> newStake, WlzbGameDO gameDO) {
        log.debug("buriedUserGift old {} new {}", JSON.toJSONString(oldStake), JSON.toJSON(newStake));
        for (int i = 0; i < oldStake.size(); i++) {
            if (newStake.get(i) - oldStake.get(i) > 0) {
                log.debug("buriedUserGift sect {}", Sect.findByOrdinal(i).name());
                buried(oldStake, newStake, gameDO, i);
            }
        }
    }

    private void buried(List<Integer> oldStake, List<Integer> newStake, WlzbGameDO gameDO, int idx) {
        Map<String, Object> param = Maps.newHashMap();
        param.put("all_times", newStake.get(idx) - oldStake.get(idx));
        param.put("chosen_sect", Sect.findByOrdinal(idx));
        param.put("contest_time", DateTimeFormatter.ofPattern("yyyyMMddHHmm").format(gameDO.getCreateTime()));
        param.put("relation_id", getFamilyId(MDCUtil.getCurAppIdByMdc(), MDCUtil.getCurUserIdByMdc()));
        log.info("buriedUserGift param {}", JSON.toJSONString(param));
        yzKafkaProducerManager.dataRangerTrack(gameDO.getAppId(), MDCUtil.getCurUserIdByMdc(), "wulin_hegemony_cheer_success", param, ServicesNameEnum.ump_services.getCode());
    }

    private void accumulateCurGameSectTotal(List<Integer> oldList, List<Integer> newList) {
        Long currentGameId = getCurrentGameId(BaseParam.ofMDC());
        for (int i = 0; i < oldList.size(); i++) {
            int diff = newList.get(i) - oldList.get(i);
            if (diff > 0) {
                redisManager.hincr(String.format(CUR_GAME_TOTAL_INPUT, currentGameId), Sect.findByOrdinal(i).name(), diff * TO_COIN_RATIO, DateUtil.ONE_DAY_SECONDS);
            }
        }
    }

    private static void checkStakeDetailed(List<Integer> newStake, List<Integer> oldStake) {
        if (CollectionUtils.isEmpty(oldStake)) {
            return;
        }
        for (int i = 0; i < newStake.size(); i++) {
            if (newStake.get(i) < oldStake.get(i)) {
                throw new ServiceException(ErrorCode.SERVER_EXCEPTION, "资源不能减哦");
            }
        }
    }

    private static void checkStakeList(List<Integer> stakeList) {
        log.info("checkStakeList {}", JSON.toJSONString(stakeList));

        // 防止数组中数据为null
        for (int i = 0; i < stakeList.size(); i++) {
            if (Objects.isNull(stakeList.get(i))) {
                stakeList.set(i, 0);
            }
        }

        // 检查押注门派数量
        long count = stakeList.stream().filter(item -> item != 0).count();
        if (count == 0) {
            throw new ServiceException(ErrorCode.SERVER_EXCEPTION, "请选择你喜欢的门派");
        }
        if (count > 2) {
            throw new ServiceException(ErrorCode.SERVER_EXCEPTION, "只能选择两个门派哦");
        }
    }

    /**
     * 通知用户比赛结果
     *
     * @param param
     */
    public void notifiedUser(BaseParam param, Long gameId, String sect) {
        Optional<LoginPopDO> optional = loginPopJpaDAO.findOne(Example.of(LoginPopDO.builder().appId(param.getAppId()).unionId(param.getUnionId()).bizKey("wlzb-result").build()));
        if (optional.isPresent()) {
            LoginPopDO popDO = optional.get();
            Map<Object, Object> items = redisManager.hmget(String.format(GAME_RESULT_NOTIFY_INFO, gameId, sect));
            if (CollectionUtils.isEmpty(items)) {
                return;
            }
            for (Map.Entry<Object, Object> entry : items.entrySet()) {
                popManager.popByLoginPopDO(param.getAppId(), param.getUnionId(), Long.valueOf(entry.getKey().toString()), popDO);
            }
        }
    }


    /**
     * 1. 只要押注 && 获胜 => 推送
     *
     * */


    /**
     * 获取活动 code
     *
     * @return
     */
    @Override
    public String getActivityCode() {
        return WU_LIN_HEGEMONY.getCode();
    }

    /**
     * 兑换订单
     *
     * @param param
     * @return
     */
    @ActivityCheck(activityCode = "wu-lin-hegemony")
    public Boolean exchangeOrder(ExchangeParam param) {
        if (Objects.isNull(param.getProductNum()) || param.getProductNum() < 1) {
            throw new ServiceException(ErrorCode.MISS_PARAM, "兑换数量不能为 0");
        }
        // todo：频率控制 @梁胡

        // 验证荣誉值
        Long needHonor = param.getProductNum() * 20L;
        Long honorValue = getHonorValue(BaseParam.builder().appId(param.getAppId()).unionId(param.getUnionId()).uid(param.getUid()).build());
        if (needHonor > honorValue) {
            throw new ServiceException(ErrorCode.MISS_PARAM, "荣誉值不足");
        }

        // 扣减荣誉值
        Double honorAfter = redisManager.hdecr(REPUTATION_VALUE, param.getUid().toString(), needHonor);
        if (honorAfter.longValue() < 0L) {
            redisManager.hincr(REPUTATION_VALUE, param.getUid().toString(), needHonor, RedisManager.ONE_DAY_SECONDS * 30);
            throw new ServiceException(ErrorCode.MISS_PARAM, "荣誉值不足");
        }

        // 下积分订单
        OrderVO orderVO = addActivityOrderRemoteService.addWulinHegemonyPointOrder(WulinHegemonyParam.builder()
                .appId(param.getAppId())
                .unionId(param.getUnionId())
                .uid(param.getUid())
                .orderType(OrderTypeEnum.WULIN_HEGEMONY_POINT_ORDER.getType())
                .memo("武林争霸兑换消耗")
                .productItemList(Lists.newArrayList(
                        ProductItem.builder().productCode("wldh_point").num(param.getProductNum()).build()
                ))
                .appScene("activity")
                .appFunction(getActivityCode()).build(), null);
        if (Objects.isNull(orderVO)) {
            redisManager.hincr(REPUTATION_VALUE, param.getUid().toString(), needHonor, RedisManager.ONE_DAY_SECONDS * 30);
            throw new ServiceException(ErrorCode.MISS_PARAM, "积分余额不足");
        }
        // 获取wlmj的埋点
        buriedExchangeResource(param.getAppId(), param.getUid(), param.getProductNum().longValue());
        return Boolean.TRUE;
    }

    public Long getHonorValue(BaseParam param) {
        return Optional.ofNullable((Integer) redisManager.hget(REPUTATION_VALUE, param.getUid().toString())).orElse(0).longValue();
    }

    public Boolean clearAdSpaceCache(Long uid) {
        redisManager.delete(String.format(AdSpaceConstant.AD_SPACE, uid));
        return Boolean.TRUE;
    }

    public String getNowWuLinGameTime(String activityCode) {
        LocalDateTime now = getNow(activityCode);
        LocalTime localTime = now.toLocalTime();
        // 00:00:00 -> 02:00:00
        if (localTime.isAfter(LocalTime.parse("00:00:00")) && localTime.isBefore(LocalTime.parse("02:00:00"))) {
            LocalDateTime end = LocalDateTime.of(now.toLocalDate(), LocalTime.MIN.withHour(2));
            LocalDateTime start = LocalDateTime.of(now.toLocalDate().plusDays(-1L), LocalTime.MIN.withHour(20));
            DateTimeFormatter format = DateTimeFormatter.ofPattern("yyyyMMddHH");
            return String.format("%s_%s", start.format(format), end.format(format));
        } else if ((localTime.isAfter(LocalTime.parse("20:00:00")) && localTime.isBefore(LocalTime.parse("23:59:59"))) || isTestMode()) {
            // 20:00:00 -> 23:59:59
            LocalDateTime end = LocalDateTime.of(now.toLocalDate().plusDays(1), LocalTime.MIN.withHour(2));
            LocalDateTime start = LocalDateTime.of(now.toLocalDate(), LocalTime.MIN.withHour(20));
            DateTimeFormatter format = DateTimeFormatter.ofPattern("yyyyMMddHH");
            return String.format("%s_%s", start.format(format), end.format(format));
        }
        throw new ServiceException(ErrorCode.SERVER_EXCEPTION);
    }

    private Boolean isTestMode() {
        return Objects.nonNull(redisManager.get(TEST_MODE));
    }

    /**
     * 02:00:00 -> 20:00:00 期间调用
     *
     * @param activityCode
     * @return
     */
    public String getLastWuLinGameTime(String activityCode) {
        LocalDateTime now = getNow(activityCode);
        LocalDateTime end = LocalDateTime.of(now.toLocalDate(), LocalTime.MIN.withHour(2));
        LocalDateTime start = LocalDateTime.of(now.toLocalDate().plusDays(-1L), LocalTime.MIN.withHour(20));
        DateTimeFormatter format = DateTimeFormatter.ofPattern("yyyyMMddHH");
        return String.format("%s_%s", start.format(format), end.format(format));
    }

    public String buildMsgByPrize(String prizeJson) {
        log.debug("buildMsgByPrize {}", prizeJson);
        if (org.springframework.util.StringUtils.isEmpty(prizeJson)) {
            return "";
        }
        List<SendPrizeDTO> sendPrizeDTOS = JSON.parseArray(prizeJson, SendPrizeDTO.class);
        StringBuilder sb = new StringBuilder();
        for (SendPrizeDTO sendPrizeDTO : sendPrizeDTOS) {
            sb.append(String.format(MSG_TEMPLATE, sendPrizeDTO.getPrizeName(), sendPrizeDTO.getValueGold(), sendPrizeDTO.getPrizeNum()));
        }
        log.debug("buildMsgByPrize result {}", sb.toString());
        return sb.toString();
    }


    private Sect getWinSect(Long gameId) {
        Sect sect;
        try {
            long size = redisManager.sGetSetSize(String.format(JOIN_GAME_REMARK, gameId));
            if (size < 1) {
                // 本轮无人参与 => 概率算法
                sect = getWinSectByProb(gameId);
            } else {
                // 有人参与 => 产出比算法
                sect = getWinSectByOutPut(gameId);
            }
        } catch (Exception e) {
            log.error("wlzb 计算获胜门派错误", e);
            sect = Sect.diancang;
        }
        // 更新获胜
        redisManager.listLpush(WIN_SECT_LIST, sect.name(), RedisManager.ONE_DAY_SECONDS * 3);
        redisManager.listTrim(WIN_SECT_LIST, 0, 4);
        return sect;
    }

    /**
     * 计算获胜门派
     * <p>
     * 获胜门派：取开奖后返奖率最接近预定值的门派
     * 返奖率 = 总产出 / 总投入
     * R = (M + X) / (C + D)
     * M - 全服用户总产出（不含本轮）
     * X - 本轮全服用户总产出 = 获胜门派收益倍数 * 投入金币数量
     * C - 全服用户总投入（不含本轮）
     * D - 本轮全服用户总投入 = SUM(每门派投入金币数量)
     *
     * @param
     * @return
     */
    private Sect getWinSectByOutPut(Long gameId) {
        // 获取预计返奖率
        double r = getRebateRate().doubleValue();
        // 获取全服用户总投入（含本轮）
        double cd = getUserHistoryInput().doubleValue();
        // 获取全服用户总产出
        double m = getUserHistoryOutput().doubleValue();
        // 计算各门派获胜后的返奖率差距
        Map<Sect, Double> sectRebateMap = new HashMap<>();
        getCurrGameUserInput().forEach((s, c) -> {
            // 当前门派返奖率
            double ir = (m + c * s.getMultiple()) / cd;
            double irdf = Math.abs(ir - r) * 10000000000d;
            sectRebateMap.put(s, irdf);
            log.info("wlzb 当前门派:{}, 返奖率:{}, 返奖率差距:{} (全服历史总产出 + 门派本轮总产出) / 全服总投入 - 预期返奖率 => ({} + {}) / {} - {}", s.name(), ir, irdf, m, c * s.getMultiple(), cd, r);
        });
        // 取返奖率差距最小门派（含连续重复验证）
        Sect result = sectRepeatHandle(sectRebateMap.entrySet().stream().sorted(Map.Entry.comparingByValue()).collect(Collectors.toList()));
        log.info("wlzb 产出比本轮获胜门派 gameId:{}, sect:{}", gameId, result);
        return result;
    }

    private Sect sectRepeatHandle(List<Map.Entry<Sect, Double>> sectList) {
        log.debug("sectList:{}", JSON.toJSONString(sectList));
        Sect result = sectList.get(0).getKey();
        // 验证是否连续出现 5 次
        List<String> recentSectList = (List) redisManager.lGet(WIN_SECT_LIST, 0, 4);
        if (CollectionUtils.isEmpty(recentSectList)) {
            return result;
        }
        int times = 0;
        for (String r : recentSectList) {
            if (r.equals(result.name())) {
                ++times;
            }
        }
        if (times >= 4) {
            log.info("wlzb 门派连续五次获胜自动切换 before:{}, after:{}", sectList.get(0).getKey(), sectList.get(1).getKey());
            result = sectList.get(1).getKey();
        }
        return result;
    }

    private Sect getWinSectByProb(Long gameId) {
        SendPrizeDTO prize = SendPrizeDTO.of(probStrategy.getDrawPoolItems(drawPoolItemService.getByPoolCode(WLZB_DRAW_POOL), 1, Boolean.TRUE).get(0));
        log.info("wlzb 概率本轮获胜门派 gameId:{}, sect:{}", gameId, prize.getPrizeValue());
        return Sect.valueOf(prize.getPrizeValue());
    }

    /**
     * 获取当前比赛用户在各门派投入总金币
     *
     * @param
     * @return
     */
    private Map<Sect, Long> getCurrGameUserInput() {
        Long currentGameId = getCurrentGameId(BaseParam.ofMDC());
        Map<Sect, Long> res = Maps.newHashMap();
        for (Sect sect : Sect.values()) {
            res.put(sect, 0L);
        }
        //Map<Sect, Long> res = Maps.asMap(Sets.newHashSet(Sect.values()), item -> 0L);
        Map<Object, Object> values = redisManager.hmget(String.format(CUR_GAME_TOTAL_INPUT, currentGameId));
        if (CollectionUtils.isEmpty(values)) {
            return res;
        }
        for (Map.Entry<Object, Object> entry : values.entrySet()) {
            log.debug("getCurrGameUserInput entry  key {} value {}", entry.getKey(), entry.getValue());
            res.put(Sect.valueOf(entry.getKey().toString()), Long.valueOf(entry.getValue().toString()));
        }
        return res;
    }

    /**
     * 获取全服用户历史总投入金币（含当前比赛投入）
     *
     * @param
     * @return
     */
    private Long getUserHistoryInput() {
        return Optional.ofNullable(redisManager.getLong(TOTAL_INPUT)).orElse(1L);
    }

    private Long getUserHistoryOutput() {
        return Optional.ofNullable(redisManager.getLong(TOTAL_OUTPUT)).orElse(0L);
    }

    private Double getRebateRate() {
        return Optional.ofNullable((Double) redisManager.get(REBATE_RATE_CACHE)).orElse(DEFAULT_REBATE_RATE);
    }

    /**
     * 检查豪气值
     *
     * @param uid
     * @return
     */
    private Boolean checkHaughtyValue(Long uid) {
        UserCharmGenerosityVO userCharmGenerosityVO = feignSnsService.getUserCharmAndGenerosity(MDCUtil.getCurAppIdByMdc(), uid).successData();
        if (Objects.isNull(userCharmGenerosityVO)) {
            log.info("checkHaughtyValue getUserCharmAndGenerosity not existed or call fail");
            return Boolean.FALSE;
        }
        log.debug("checkHaughtyValue userCharmGenerosityVO {}", JSON.toJSONString(userCharmGenerosityVO));
        return userCharmGenerosityVO.getGenerosityLevel() >= 3;
    }

    private void putPrize(Map<String, SendPrizeDTO> result, SendPrizeDTO newPrize) {
        SendPrizeDTO prize = result.get(newPrize.getPrizeValue());
        if (Objects.isNull(prize)) {
            result.put(newPrize.getPrizeValue(), newPrize);
        } else {
            prize.setPrizeNum(prize.getPrizeNum() + 1);
        }
    }

    /**
     * 比赛结果埋点处理
     *
     * @param uid
     * @param gameDO
     * @param sendPrizeDTOS
     * @param relationId
     */
    private void buriedGameResult(Long uid, WlzbGameDO gameDO, List<SendPrizeDTO> sendPrizeDTOS, Long relationId) {
        if (Objects.isNull(gameDO) || CollectionUtils.isEmpty(sendPrizeDTOS) || Objects.isNull(relationId)) {
            return;
        }
        log.debug("buriedGameResult gameId {} winSect {} prizes {} relationId {}", gameDO.getGameId(),
                gameDO.getWinSect(), JSON.toJSON(sendPrizeDTOS), relationId);
        for (SendPrizeDTO sendPrizeDTO : sendPrizeDTOS) {
            Map<String, Object> param = Maps.newHashMap();
            param.put("contest_time", DateTimeFormatter.ofPattern("yyyyMMddHHmm").format(gameDO.getDrawnTime()));
            param.put("winning_sect", gameDO.getWinSect());
            param.put("award_key", sendPrizeDTO.getPrizeValue());
            param.put("award_amount", sendPrizeDTO.getValueGold() * sendPrizeDTO.getPrizeNum());
            param.put("award_num", sendPrizeDTO.getPrizeNum());
            param.put("relation_id", getFamilyId(MDCUtil.getCurAppIdByMdc(), uid));
            yzKafkaProducerManager.dataRangerTrack(gameDO.getAppId(), uid, "wulin_hegemony_contest_over", param, ServicesNameEnum.ump_services.getCode());
        }
    }

    private void buriedExchangeResource(Long appId, Long uid, Long nums) {
        if (Objects.isNull(appId) || Objects.isNull(uid) || Objects.isNull(nums)) {
            return;
        }
        HashMap<String, Object> param = Maps.newHashMap();
        param.put("get_way", "convert");
        param.put("cheats_num", nums);
        param.put("consume_point", nums * 11.2);
        param.put("consume_reputation", nums * 20);
        param.put("relation_id", getFamilyId(appId, uid));
        log.debug("buriedExchangeResource uid {} param {}", uid, JSON.toJSONString(param));
        yzKafkaProducerManager.dataRangerTrack(appId, uid, "wulin_hegemony_get_cheats_success", param, ServicesNameEnum.ump_services.getCode());
    }

    private Long getFamilyId(Long appId, Long uid) {
        log.debug("getFamilyId appId {} uid {}", appId, uid);
        FamilyInfoDTO familyInfoDTO = familyFeignService.findFamilyInfoByUid(uid, appId).successData();
        return Objects.isNull(familyInfoDTO) ? -1 : familyInfoDTO.getId();
    }

    private String getActivityUrl() {
        return ActivityUrlUtil.getH5BaseUrl(ServicesAppIdEnum.lanling.getUnionId(), env) + "martial-battle";
    }

    @Override
    public Consumer<HandlerContext> getPopHandler() {
        return (context) -> {
            log.debug("wlzb getPopHandler context:{}", JSONObject.toJSONString(context));
            // 查询用户家族
            Long uid = context.getUid();
            Long appId = context.getAppId();
            LoginPopDO popDO = context.getPopDO();
            FamilyInfoDTO family = familyFeignService.findFamilyInfoByUid(uid, appId).successData();
            if (Objects.nonNull(family) && Objects.nonNull(family.getId())) {
                popDO.setActionUrl(String.format("https://router.nuan.chat/conversation-family?chat_id=%s&family_id=%s&url_with_jump=%s", family.getChatId(), family.getId(), URLUtil.encode(getActivityUrl())));
                return;
            }
            // 查询语音房
            int size = 5;
            List<RoomHomePageVO> roomList = feignRoomService.roomList(null, 0L, 5, null).successData().getList();
            if (CollectionUtils.isEmpty(roomList)) {
                popDO.setActionUrl(null);
                return;
            }
            if (roomList.size() < size) {
                size = roomList.size();
            }
            Long roomId = roomList.get(Long.valueOf(System.currentTimeMillis() % size).intValue()).getRoomId();
            log.debug("wlzb getPopHandler roomId:{}, roomList:{}", roomId, JSON.toJSONString(roomList));
            popDO.setActionUrl(String.format("https://router.nuan.chat/join_room?room_id=%s&from=%s&url_with_jump=%s", roomId, "login_pop", URLUtil.encode(getActivityUrl())));
        };
    }
}
