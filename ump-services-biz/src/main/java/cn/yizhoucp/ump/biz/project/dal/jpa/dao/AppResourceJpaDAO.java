package cn.yizhoucp.ump.biz.project.dal.jpa.dao;

import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.AppResourceDO;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

/**
 * APP 资源
 *
 * @author: lianghu
 */
@Repository
public interface AppResourceJpaDAO extends CrudRepository<AppResourceDO, Long> {

    /**
     * 根据活动 code 获取 APP 资源
     *
     * @param appId
     * @param activityCode
     * @return java.util.List<api.project.dal.jpa.dataobject.ActivityScenePrizeDO>
     */
    @Query(value = "select * from activity_app_resource where app_id = ?1 and activity_code = ?2 and status = 1", nativeQuery = true)
    AppResourceDO getByActivityCode(Long appId, String activityCode);

}
