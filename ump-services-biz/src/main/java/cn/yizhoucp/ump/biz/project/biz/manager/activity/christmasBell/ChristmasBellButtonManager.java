package cn.yizhoucp.ump.biz.project.biz.manager.activity.christmasBell;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.enums.sns.UserRelationType;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.api.param.jimu.ButtonFriendPaream;
import cn.yizhoucp.ump.api.param.jimu.ButtonRankListParam;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.AbstractButtonManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 圣诞铃铛按钮事件
 * @create 2024-12-13
 **/
@Service
@Slf4j
public class ChristmasBellButtonManager extends AbstractButtonManager {

    @Resource
    ChristmasBellSupport christmasBellSupport;

    @Override
    public Object event(ButtonEventParam eventParam) {
        log.info("ChristmasBellButtonManager event :{}, buttonEventParam:{}", eventParam.getEventCode(), eventParam);
        if (!eventParam.check() || !eventParam.getBaseParam().check()) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        Long uid = eventParam.getBaseParam().getUid();
        Long appId = eventParam.getBaseParam().getAppId();
        String extDate = eventParam.getExtDate();
        switch (eventParam.getEventCode()) {
            case ChristmasBellConstant.BELL_LOTTERY:
                return christmasBellSupport.bellLottery(uid, extDate);
            case ChristmasBellConstant.UPGRADE_BELL:
                return christmasBellSupport.upgradeBell(uid, extDate);
            case ChristmasBellConstant.OPEN_BELL:
                return christmasBellSupport.openBell(uid, appId, extDate);
            case ChristmasBellConstant.GIFT_MAGIC:
                return christmasBellSupport.giftMagic(uid, appId, extDate);
            case ChristmasBellConstant.RECEIVE_GIFT:
                return christmasBellSupport.receiveGift(uid, appId, extDate);
            case ChristmasBellConstant.CHOOSE_FRIEND:
                return christmasBellSupport.chooseFriend(uid, extDate);
            default:
                log.error("ChristmasBellButtonManager eventCode is not support:{}", eventParam.getEventCode());
                return null;
        }
    }

    /**
     * 好友列表
     * @return Object
     */
    @Override
    public Object friendList(ButtonFriendPaream friendPaream) {
        if (null == friendPaream || !friendPaream.getBaseParam().check()) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        BaseParam baseParam = friendPaream.getBaseParam();
        Long userId = baseParam.getUid();
        Long appId = baseParam.getAppId();
        String friendCode = friendPaream.getFriendCode();
        if (StringUtils.isBlank(friendCode)) {
            friendCode = UserRelationType.friend.getCode();
        }
        return christmasBellSupport.getFriendList(userId, appId, friendCode);
    }


    /**
     * 榜单列表
     */
    @Override
    public RankVO rankList(ButtonRankListParam rankListPaream) {
        if (null == rankListPaream || !rankListPaream.getBaseParam().check()) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        Long uid = rankListPaream.getBaseParam().getUid();
        return christmasBellSupport.getRank(uid);
    }



}

