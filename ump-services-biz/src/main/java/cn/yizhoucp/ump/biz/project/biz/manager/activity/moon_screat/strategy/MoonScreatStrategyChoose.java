package cn.yizhoucp.ump.biz.project.biz.manager.activity.moon_screat.strategy;

import cn.yizhoucp.ump.biz.project.biz.manager.activity.moon_screat.common.MoonScreatEnums;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.StrategyManager;
import org.springframework.stereotype.Component;


/**
 * 月夜下的秘密策略选择类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 14:42 2025/4/23
 */
@Component
public class MoonScreatStrategyChoose extends StrategyManager<MoonScreatEnums.ButtonEnum, ExecutableStrategy> {
}
