package cn.yizhoucp.ump.biz.project.biz.manager.activity.treasureInSecret;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw.inner.PrizeTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.log.LogComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.AbstractDrawTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawContext;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.doDrawStrategy.common.ProbStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.treasureInSecret.common.TlsEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.treasureInSecret.common.TlsRedisControls;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.manager.activity.treasureInSecret.common.TlsConstant.*;


@Service
@Slf4j
public class TreasureInSecretDrawManager extends AbstractDrawTemplate {

    @Resource
    private ProbStrategy probStrategy;
    @Resource
    private TreasureInSecretTrackManager trackManager;
    @Resource
    private LogComponent logComponent;
    @Resource
    private TlsRedisControls tlsRedisControls;
    @Resource
    private TreasureInSecretSupperManager treasureInSecretSupperManager;

    @Override
    protected void resourceCheck(DrawContext context) {
        DrawParam drawParam = context.getDrawParam();
        String poolCode = drawParam.getPoolCode();
        Integer poolTimes = drawParam.getTimes();
        Long uid = drawParam.getUid();
        // 校验奖池
        if (Objects.isNull(poolCode) || Objects.isNull(poolTimes)) {
            log.warn("Exception prize pool poolCode");
            throw new ServiceException(ErrorCode.INVALID_PARAM, "奖池错误!");
        }

        List<String> strings = Arrays.stream(TlsEnum.BoxEnum.values()).map(TlsEnum.BoxEnum::getBoxKey).collect(Collectors.toList());
        if (strings.contains(poolCode)) {
            // 是宝箱抽奖，pass
            return;
        }
        if (!HUNT_POOL.equals(poolCode) && !GEM_POOL.equals(poolCode)) {
            log.warn("Exception prize pool poolCode");
            throw new ServiceException(ErrorCode.INVALID_PARAM, "奖池错误!");
        }

        if (HUNT_POOL.equals(poolCode)) {
            // 校验卡片数量
            Long aLong = treasureInSecretSupperManager.backpackCard(uid);
            if (aLong < poolTimes) {
                throw new ServiceException(ErrorCode.INVALID_PARAM, "夺宝卡数量不足，去完成任务获取吧~");
            }
            // 这里再统计出用户改日的抽奖次数
            tlsRedisControls.addTimesPrizeToday(uid, Long.valueOf(poolTimes));
        } else {
            // 校验珍宝值
            Long userGemValue = tlsRedisControls.getUserGemValue(uid);
            if (userGemValue < poolTimes * 100) {
                throw new ServiceException(ErrorCode.INVALID_PARAM, "你的珍宝值不足～");
            }
        }

    }

    @Override
    protected void deductResource(DrawContext context) {
        DrawParam drawParam = context.getDrawParam();
        String poolCode = drawParam.getPoolCode();
        Integer poolTimes = drawParam.getTimes();
        Long uid = drawParam.getUid();

        List<String> strings = Arrays.stream(TlsEnum.BoxEnum.values()).map(TlsEnum.BoxEnum::getBoxKey).collect(Collectors.toList());
        if (strings.contains(poolCode)) {
            // 是宝箱抽奖，pass
            return;
        }

        if (HUNT_POOL.equals(poolCode)) {
            // 卡片数量
            treasureInSecretSupperManager.updateBackpackCard(uid, Long.valueOf(poolTimes));
        } else {
            // 珍宝值
            tlsRedisControls.deductUserGemValue(uid, Long.valueOf(poolTimes * 100));
        }

    }

    @Override
    protected void draw(DrawContext context) {
        List<DrawPoolItemDTO> drawPoolItemDTOList = probStrategy.getDrawPoolItems(context.getDrawPoolItemDOS(),
                context.getDrawParam().getTimes(),
                Boolean.TRUE);
        context.setPrizeItemList(drawPoolItemDTOList);
    }


    @Override
    protected void doCallback(DrawContext context) {
        // 记录日志
        DrawParam drawParam = context.getDrawParam();
        String poolCode = drawParam.getPoolCode();
        // 抽奖结果留存
        TlsEnum.BoxEnum boxEnum = TlsEnum.BoxEnum.getByBoxKey(poolCode);
        if (Objects.isNull(boxEnum)) {
            logComponent.putDrawLog(drawParam.getBaseParam(), drawParam.getActivityCode(), context.getPrizeItemList());
            // 埋点
            for (DrawPoolItemDTO drawPoolItemDTO : context.getPrizeItemList()) {
                DrawPoolItemDO drawPoolItemDO = drawPoolItemDTO.getDrawPoolItemDO();
                Long awardAmount = drawPoolItemDO.getItemValueGold() * drawPoolItemDTO.getTargetTimes();
                // 一次抽奖多个参数分别传递
                if (Objects.nonNull(drawPoolItemDO.getItemType()) && PrizeTypeEnum.PRIZE_DRESS.getCode().equals(drawPoolItemDO.getItemType())) {
                    // 头像框埋点价值处理
                    awardAmount = 0L;
                }
                String type = HUNT_POOL.equals(poolCode) ? "fsecret_realm" : "rare_treasure";
                trackManager.allActivityLottery(drawParam.getUid(), type, drawParam.getPoolCode(), drawPoolItemDO.getItemKey(), awardAmount, drawPoolItemDTO.getTargetTimes());
            }

        } else {
            List<DrawPoolItemDTO> prizeItemList = context.getPrizeItemList();
            prizeItemList.stream().forEach(item -> {
                DrawPoolItemDO drawPoolItemDO = item.getDrawPoolItemDO();
                drawPoolItemDO.setPoolCode(HUNT_POOL);
            });
            logComponent.putDrawLog(drawParam.getBaseParam(), drawParam.getActivityCode(), prizeItemList);
        }


    }
}
