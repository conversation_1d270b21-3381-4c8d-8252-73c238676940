package cn.yizhoucp.ump.biz.project.biz.remoteService.user.userAccount;


import cn.yizhoucp.ms.core.base.enums.AccountHandleType;
import cn.yizhoucp.ms.core.base.enums.AppFunctionEnum;
import cn.yizhoucp.ms.core.base.enums.AppScene;
import cn.yizhoucp.ms.core.base.enums.UserPackageBizType;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.vo.userservices.AccountBalanceChangeActionVO;
import cn.yizhoucp.ms.core.vo.userservices.AccountBalanceChangeResultVO;
import cn.yizhoucp.product.client.UserPackageFeignService;
import cn.yizhoucp.product.dto.*;
import cn.yizhoucp.product.enums.PackageUseConditionEnum;
import cn.yizhoucp.product.enums.PackageUseScene;
import cn.yizhoucp.ump.biz.project.biz.remoteService.AbstractRemoteService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.FeignUserService;
import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.vo.coinservices.UserAccountVO;
import cn.yizhoucp.ms.core.vo.userservices.userPackage.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 用户账户
 *
 * @author: lianghu
 */
@Slf4j
@Service
public class UserAccountRemoteService extends AbstractRemoteService {

    @Resource
    private UserAccountFeignService userAccountFeignService;
    @Resource
    private FeignUserService feignUserService;
    @Resource
    private UserPackageFeignService userPackageFeignService;

    /**
     * 根据用户 id 获取用户账户
     *
     * @param appId 应用id
     * @param uid   用户id
     * @return UserAccountVO
     */
    public UserAccountVO getUserAccountByUid(Long appId, Long uid) {
        Result<UserAccountVO> result = null;
        try {
            log.info("调用 UserAccountRemoteService.getUserAccountByUid 入参 appId:{}, uid:{}", appId, uid);
            result = userAccountFeignService.getUserAccountByUid(appId, uid);
            if (result.success()) {
                log.info("调用 UserAccountRemoteService.getUserAccountByUid 成功 result:{}", JSONObject.toJSONString(result));
                return result.successData();
            }
        } catch (Exception e) {
            log.error("调用 UserAccountRemoteService.getUserAccountByUid 异常", e);
        }
        log.error("调用 UserAccountRemoteService.getUserAccountByUid 失败 result:{}", JSONObject.toJSONString(result));
        return null;
    }

    /**
     * 更新用户账户信息
     *
     * @param unionId       应用唯一标识
     * @param appId         应用id
     * @param uid           用户id
     * @param userAccountId 账户id
     * @param feeAmount     付费部分
     * @param freeAmount    免费部分
     * @param type          账户操作类型 AccountHandleType
     * @param appScene      应用场景
     * @param appFunction   功能
     * @return Boolean
     */
    public Boolean refreshUserAccount(String unionId, Long appId, Long uid,
                                      Long userAccountId, Long feeAmount,
                                      Long freeAmount, AccountHandleType type, AppScene appScene, AppFunctionEnum appFunction) {
        Result<Boolean> result = null;
        try {
            log.info("调用 UserAccountRemoteService.refreshUserAccount 入参 appId:{}, uid:{}, userAccountId:{}, feeAmount:{}, freeAmount:{}, type:{} appScene {} appFunction {}",
                    appId, uid, userAccountId, feeAmount, freeAmount, type, appScene, appFunction);
            appScene = Objects.isNull(appScene) ? AppScene.platform : appScene;
            appFunction = Objects.isNull(appFunction) ? AppFunctionEnum.default_function : appFunction;
            AccountBalanceChangeActionVO param = AccountBalanceChangeActionVO.builder()
                    .unionId(unionId).appId(appId).uid(uid)
                    .feeAmount(feeAmount).freeAmount(freeAmount).type(type)
                    .scene(appScene.getCode()).appFunction(appFunction.getCode())
                    .build();
            AccountBalanceChangeResultVO accountResult = userAccountFeignService.accountBalanceChange(param).successData();
            log.info("调用 UserAccountRemoteService.refreshUserAccount 结果 result:{}", JSONObject.toJSONString(accountResult));
            return accountResult.getResult();
        } catch (Exception e) {
            log.error("调用 UserAccountRemoteService.refreshUserAccount 异常", e);
        }
        log.error("调用 UserAccountRemoteService.refreshUserAccount 失败 result:{}", JSONObject.toJSONString(result));
        return null;
    }

    /**
     * 获取背包使用的次数
     *
     * @param appId
     * @param uid
     * @param bizKey
     * @param startTime
     * @return
     */
    public Integer getPacketUsedTimeByUidAndType(Long appId, Long uid, String bizKey, String bizType, String startTime) {
        Result<Integer> result = null;
        try {
            log.info("调用 UserAccountRemoteService.getPacketUsedTimeByUidAndType 入参 appId:{}, uid:{}, bizKey:{}", appId, uid, bizKey);
            result = userPackageFeignService.getUserUsePackageCount(uid, UserPackageBizType.FAMILY_LUCKDRAW_TICKET.getCode(), UserPackageBizType.FAMILY_LUCKDRAW_TICKET.getCode());
            if (result.success()) {
                log.info("调用 UserAccountRemoteService.getPacketUsedTimeByUidAndType 成功 result:{}", JSONObject.toJSONString(result));
                return result.successData();
            }
        } catch (Exception e) {
            log.error("调用 UserAccountRemoteService.getFirstPackageByUidAndType 异常", e);
        }
        log.error("调用 UserAccountRemoteService.getFirstPackageByUidAndType 失败 result:{}", JSONObject.toJSONString(result));
        return null;
    }

    /**
     * 根据类型和业务id 获取用户背包数据
     *
     * @param appId   应用id
     * @param uid     用户id
     * @param bizKey  业务id
     * @param bizType 业务类型
     * @return List<UserPackageDetailDTO>
     */
    public List<UserPackageDetailDTO> getPackageByBizIdAndType(Long appId, Long uid, String bizKey, String bizType) {
        Result<List<UserPackageDetailDTO>> result = null;
        try {
            log.info("调用 UserAccountRemoteService.getPackageByBizIdAndType 入参 appId:{}, uid:{}, bizKey:{}", appId, uid, bizKey);
            PackageQueryConditionDTO queryParam = PackageQueryConditionDTO.builder().uid(uid).bizId(bizKey).bizType(bizType).build();
            result = userPackageFeignService.getPackageDetailByCondition(queryParam);
            if (result.success()) {
                log.info("调用 UserAccountRemoteService.getPackageByBizIdAndType 成功 result:{}", JSONObject.toJSONString(result));
                return result.successData();
            }
        } catch (Exception e) {
            log.error("调用 UserAccountRemoteService.getPackageByBizIdAndType 异常", e);
        }
        log.error("调用 UserAccountRemoteService.getPackageByBizIdAndType 失败 result:{}", JSONObject.toJSONString(result));
        return Lists.newArrayList();
    }

    /**
     * 根据类型和业务id 获取用户背包数据
     *
     * @param appId   应用id
     * @param uid     用户id
     * @param bizKey  业务id
     * @param bizType 业务类型
     * @return List<UserPackageDetailDTO>
     */
    public List<UserPackageDetailDTO> getPackageByBizIdListAndType(Long appId, Long uid, List<String> bizKey, String bizType) {
        Result<List<UserPackageDetailDTO>> result = null;
        try {
            log.info("调用 UserAccountRemoteService.getPackageByBizIdListAndType 入参 appId:{}, uid:{}, bizKey:{}", appId, uid, bizKey);
            PackageQueryConditionDTO queryParam = PackageQueryConditionDTO.builder().uid(uid).bizIdList(bizKey).bizType(bizType).build();
            result = userPackageFeignService.getPackageDetailByCondition(queryParam);
            if (result.success()) {
                log.info("调用 UserAccountRemoteService.getPackageByBizIdListAndType 成功 result:{}", JSONObject.toJSONString(result));
                return result.successData();
            }
        } catch (Exception e) {
            log.error("调用 UserAccountRemoteService.getPackageByBizIdListAndType 异常", e);
        }
        log.error("调用 UserAccountRemoteService.getPackageByBizIdListAndType 失败 result:{}", JSONObject.toJSONString(result));
        return Lists.newArrayList();
    }


    /**
     * 进行背包消费
     *
     * @param appId         应用id
     * @param uid           用户id
     * @param userPackageId 用户背包id
     * @param count         数量
     * @param toUids        目标用户id
     * @param toName        目标用户名
     * @return Boolean
     */
    public Boolean useUserPackageById(Long appId, Long uid, Long userPackageId, Long count, String toUids, String toName, PackageUseScene useScene) {
        try {
            log.info("调用 UserAccountRemoteService.useUserPackageById 入参 appId:{}, uid:{}, userPackageId:{}, count:{}, toUids:{}, toName:{}",
                    appId, uid, userPackageId, count, toUids, toName);
            List<UsePackageDTO> usePackageList = userPackageFeignService.usePackageWithId(appId, MDCUtil.getCurUnionIdByMdc(), uid, toUids, userPackageId, count, useScene.getCode());
            if (!CollectionUtils.isEmpty(usePackageList)) {
                log.info("调用 UserAccountRemoteService.useUserPackageById 成功 uid {} userPackageId {} count {} toUids {} useScene {}", uid, userPackageId, count, toUids, useScene);
                return Boolean.TRUE;
            }
        } catch (Exception e) {
            log.error("调用 UserAccountRemoteService.useUserPackageById 异常", e);
        }
        log.error("调用 UserAccountRemoteService.useUserPackageById 失败 uid {} userPackageId {} count {} toUids {} useScene {}", uid, userPackageId, count, toUids, useScene);
        return null;
    }

    /**
     * 使用背包奖券
     *
     * @param appId   应用id
     * @param uid     用户id
     * @param count   数量
     * @return CommonResultVO
     */
    public Boolean usePackageLuckyBagTicket(Long appId, Long uid, Long count) {
        try {
            log.info("调用 UserAccountRemoteService.usePackageLuckyBagTicket 入参 appId:{}, uid:{}, count:{}", appId, uid, count);
            List<UsePackageDTO> usePackageList = userPackageFeignService.usePackageWithLuckyBag(appId, MDCUtil.getCurUnionIdByMdc(), uid, count);
            if (!CollectionUtils.isEmpty(usePackageList)) {
                log.info("调用 UserAccountRemoteService.usePackageLuckyBagTicket 成功 appId {} uid {} count {}", appId, uid, count);
                return Boolean.TRUE;
            }
        } catch (Exception e) {
            log.error("调用 UserAccountRemoteService.usePackageLuckyBagTicket 异常", e);
        }
        log.error("调用 UserAccountRemoteService.usePackageLuckyBagTicket 失败 appId {} uid {} count {}", appId, uid, count);
        return null;
    }

}
