package cn.yizhoucp.ump.biz.project.biz.manager.activity.timeCarnival;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.yizhoucp.ms.core.base.enums.GiftWay;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.enums.sns.UserRelationType;
import cn.yizhoucp.ms.core.base.util.AppUtil;
import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.timeCarnival.rankStrategy.RankStrategyEnum;
import cn.yizhoucp.ump.biz.project.biz.remoteService.FeignSnsService;
import cn.yizhoucp.ump.biz.project.common.ActivityComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class TimeCarnivalBizManager implements ActivityComponent {

    @Resource
    private TimeCarnivalConstant timeCarnivalConstant;
    @Resource
    private RedisManager redisManager;
    @Resource
    private TimeCarnivalRankManager timeCarnivalRankManager;
    @Resource
    private TimeCarnivalTrackManager timeCarnivalTrackManager;
    @Resource
    private FeignSnsService feignSnsService;

    @Override
    public String getActivityCode() {
        return TimeCarnivalConstant.ACTIVITY_CODE;
    }

    @ActivityCheck(activityCode = TimeCarnivalConstant.ACTIVITY_CODE, isThrowException = false)
    public Boolean sendGiftHandle(BaseParam param, List<CoinGiftGivedModel> coinGiftGivedModelList) {
        log.info("TimeCarnivalBizManager#sendGiftHandler param {} coinGiftGivedModelList {}", param, coinGiftGivedModelList);
        for (CoinGiftGivedModel coinGiftGivedModel : coinGiftGivedModelList) {
            if (!checkGift(coinGiftGivedModel)) {
                continue;
            }
            if (TimeCarnivalConstant.FGSJH_GIFT_BOX.contains(coinGiftGivedModel.getGiftKey())
                    || TimeCarnivalConstant.MDHZB_GIFT_BOX.contains(coinGiftGivedModel.getGiftKey())
                    || TimeCarnivalConstant.QXSST_GIFT_BOX.contains(coinGiftGivedModel.getGiftKey())) {
                String bandId = timeCarnivalConstant.getBandId(param.getUid());
                String members = Optional.ofNullable(redisManager.hget(String.format(TimeCarnivalConstant.BAND_INFO_KEY, bandId), "members")).orElse("").toString();
                String[] memberList = members.split(",");
                Boolean isBandFull = true;
                if (memberList.length < 5) {
                    isBandFull = false;
                }
                addMusicSheetValue(param, coinGiftGivedModel, isBandFull);
                addRankValue(param, coinGiftGivedModel, isBandFull, bandId);
                continue;
            }
            taskFinish(param, coinGiftGivedModel);
            //时尚值
            if (checkIsFriend(param.getUid(), coinGiftGivedModel.getToUid())) {
                String bindKey = String.format(TimeCarnivalConstant.BIND_FRIEND_KEY, AppUtil.splicUserId(param.getUid(), coinGiftGivedModel.getToUid()));
                redisManager.setnx(bindKey, System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND);
                Long fashionValue = timeCarnivalConstant.incrementFashionValue(AppUtil.splicUserId(param.getUid(), coinGiftGivedModel.getToUid()), coinGiftGivedModel.getCoin());
                timeCarnivalRankManager.incrRankValue(AppUtil.splicUserId(param.getUid(), coinGiftGivedModel.getToUid()), coinGiftGivedModel.getCoin(), TimeCarnivalConstant.createRankKey(RankStrategyEnum.MODERN_FAMILY_RANK.getCode()));
                if (fashionValue >= TimeCarnivalConstant.MAX_FASHION_VALUE) {
                    timeCarnivalTrackManager.allActivityTaskFinish(param.getUid(), "modern_family_14999");
                    timeCarnivalTrackManager.allActivityTaskFinish(coinGiftGivedModel.getToUid(), "modern_family_14999");
                }
                if (fashionValue >= TimeCarnivalConstant.MIN_FASHION_VALUE) {
                    timeCarnivalTrackManager.allActivityTaskFinish(param.getUid(), "modern_family_399");
                    timeCarnivalTrackManager.allActivityTaskFinish(coinGiftGivedModel.getToUid(), "modern_family_399");
                }
            }
        }
        return true;
    }

    private Boolean checkIsFriend(Long uid, Long toUid) {
        String relation = feignSnsService.getUserRelation(uid, toUid, ServicesAppIdEnum.lanling.getAppId()).getData();
        return ObjectUtil.equals(relation, UserRelationType.friend.getCode());
    }

    private void taskFinish(BaseParam param, CoinGiftGivedModel coinGiftGivedModel) {
        if (TimeCarnivalConstant.TaskCodeEnum.TASK_1.getTaskGift().equals(coinGiftGivedModel.getGiftKey())) {
            Integer taskId = TimeCarnivalConstant.TaskCodeEnum.TASK_1.getTaskId();
            Boolean isLoop = TimeCarnivalConstant.TaskCodeEnum.TASK_1.getIsLooper();
            String sceneCode = TimeCarnivalConstant.TaskCodeEnum.TASK_1.getTaskId().toString();
            timeCarnivalConstant.incrementTaskProgress(taskId, param.getUid(), isLoop, sceneCode, coinGiftGivedModel.getProductCount());
        }
        if (TimeCarnivalConstant.TaskCodeEnum.TASK_2.getTaskGift().equals(coinGiftGivedModel.getGiftKey())) {
            Integer taskId = TimeCarnivalConstant.TaskCodeEnum.TASK_2.getTaskId();
            Boolean isLoop = TimeCarnivalConstant.TaskCodeEnum.TASK_2.getIsLooper();
            String sceneCode = TimeCarnivalConstant.TaskCodeEnum.TASK_2.getTaskId().toString();
            timeCarnivalConstant.incrementTaskProgress(taskId, param.getUid(), isLoop, sceneCode, coinGiftGivedModel.getProductCount());
        }
        if (TimeCarnivalConstant.TaskCodeEnum.TASK_3.getTaskGift().equals(coinGiftGivedModel.getGiftKey())) {
            Integer taskId = TimeCarnivalConstant.TaskCodeEnum.TASK_3.getTaskId();
            Boolean isLoop = TimeCarnivalConstant.TaskCodeEnum.TASK_3.getIsLooper();
            String sceneCode = TimeCarnivalConstant.TaskCodeEnum.TASK_3.getTaskId().toString();
            timeCarnivalConstant.incrementTaskProgress(taskId, param.getUid(), isLoop, sceneCode, coinGiftGivedModel.getProductCount());
        }
    }

    private void addRankValue(BaseParam param, CoinGiftGivedModel coinGiftGivedModel, Boolean isBandFull, String bandId) {
        Long userFaction = timeCarnivalConstant.getUserFaction(param.getUid());
        Long toUserFaction = timeCarnivalConstant.getUserFaction(coinGiftGivedModel.getToUid());
        TimeCarnivalConstant.Faction faction = TimeCarnivalConstant.Faction.getByFactionId(userFaction.intValue());
        TimeCarnivalConstant.Faction toFaction = TimeCarnivalConstant.Faction.getByFactionId(toUserFaction.intValue());
        if (faction != null) {
            //阵营排行榜
            String rankKey = TimeCarnivalConstant.createRankKey(faction.getCode().toString());
            timeCarnivalRankManager.incrRankValue(param.getUid(), coinGiftGivedModel.getCoin(), rankKey);
            timeCarnivalConstant.incrementFactionTotalScore(faction.getCode(), coinGiftGivedModel.getCoin());
        }
        if (toFaction != null) {
            String rankKey = TimeCarnivalConstant.createRankKey(toFaction.getCode().toString());
            timeCarnivalRankManager.incrRankValue(coinGiftGivedModel.getToUid(), coinGiftGivedModel.getCoin(), rankKey);
            timeCarnivalConstant.incrementFactionTotalScore(toFaction.getCode(), coinGiftGivedModel.getCoin());
        }
        //乐队排行榜
        if (StrUtil.isBlank(bandId)) {
            return;
        }
        if (!isBandFull) {
            return;
        }
        timeCarnivalRankManager.incrRankValue(bandId, coinGiftGivedModel.getCoin(), TimeCarnivalConstant.createRankKey(RankStrategyEnum.USER_BAND_RANK.getCode()));
    }

    private void addMusicSheetValue(BaseParam param, CoinGiftGivedModel coinGiftGivedModel, Boolean isBandFull) {
        if (!isBandFull) {
            return;
        }
        timeCarnivalConstant.incrementScore(param.getUid(), coinGiftGivedModel.getCoin());
    }

    private Boolean checkGift(CoinGiftGivedModel coinGiftGivedModel) {
        // 面板礼物
        if (StringUtils.isEmpty(coinGiftGivedModel.getGiftWay()) || !StringUtils.equalsIgnoreCase(coinGiftGivedModel.getGiftWay(), GiftWay.NORMAL.getCode())) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    @ActivityCheck(activityCode = TimeCarnivalConstant.ACTIVITY_CODE, isThrowException = false)
    public void initiateFormOfficialCombine(String type, Long fromUid, Long toUid) {
        if ("form".equals(type)) {
            Integer taskId = TimeCarnivalConstant.TaskCodeEnum.TASK_4.getTaskId();
            Boolean isLoop = TimeCarnivalConstant.TaskCodeEnum.TASK_4.getIsLooper();
            String sceneCode = TimeCarnivalConstant.TaskCodeEnum.TASK_4.getTaskId().toString();
            timeCarnivalConstant.incrementTaskProgress(taskId, fromUid, isLoop, sceneCode, 1L);
            timeCarnivalConstant.incrementTaskProgress(taskId, toUid, isLoop, sceneCode, 1L);
        }
    }

    @ActivityCheck(activityCode = TimeCarnivalConstant.ACTIVITY_CODE, isThrowException = false)
    public void finishBuyPromiseRing(Long fromUid, Long toUid) {
        Integer taskId = TimeCarnivalConstant.TaskCodeEnum.TASK_5.getTaskId();
        Boolean isLoop = TimeCarnivalConstant.TaskCodeEnum.TASK_5.getIsLooper();
        String sceneCode = TimeCarnivalConstant.TaskCodeEnum.TASK_5.getTaskId().toString();
        timeCarnivalConstant.incrementTaskProgress(taskId, fromUid, isLoop, sceneCode, 1L);
        timeCarnivalConstant.incrementTaskProgress(taskId, toUid, isLoop, sceneCode, 1L);
    }


}
