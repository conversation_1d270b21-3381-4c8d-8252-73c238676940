package cn.yizhoucp.ump.biz.project.biz.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Getter
@AllArgsConstructor
public enum MissionCycleTypeEnum {

    DAY("day", "每天"),
    WEEK("week", "每周"),
    MONTH("month", "每月"),
    YEAR("year", "每年"),
    ACTIVITY("activity", "活动期间"),
    ;
    private String code;
    private String desc;

    private static final Map<String, MissionCycleTypeEnum> instanceMap = new ConcurrentHashMap<>(8);

    static {
        for (MissionCycleTypeEnum item : values()) {
            instanceMap.put(item.getCode(), item);
        }
    }

    public static MissionCycleTypeEnum getInstance(String code) {
        return instanceMap.get(code);
    }

}
