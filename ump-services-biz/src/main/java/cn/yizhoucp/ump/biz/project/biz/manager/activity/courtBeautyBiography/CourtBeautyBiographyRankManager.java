package cn.yizhoucp.ump.biz.project.biz.manager.activity.courtBeautyBiography;

import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.vo.jimu.rank.CpRankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.courtBeautyBiography.common.CbbCacheManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.AbstractRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.manager.activity.courtBeautyBiography.common.CbbConstant.ACTIVITY_CODE;
import static cn.yizhoucp.ump.biz.project.biz.manager.activity.courtBeautyBiography.common.CbbConstant.BETTE_RANK_KEY;

@Service
@Slf4j
public class CourtBeautyBiographyRankManager extends AbstractRankManager {

    @Resource
    private SendPrizeManager sendPrizeManager;

    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;

    @Resource
    private NotifyComponent notifyComponent;

    @Resource
    private CourtBeautyBiographyTrackManager trackManager;
    
    @Resource
    private CbbCacheManager cbbCacheManager;


    /**
     * 后置处理
     */
    @Override
    protected void postProcess(RankContext rankContext) {
        try {
            if (rankContext.getType() != RankContext.RankType.cp) {
                return;
            }
            RankVO rankVO = rankContext.getRankVO();
            List<CpRankItem> cpRankItemList = rankVO.getCpRankItemList();
            CpRankItem myCpRankItem = rankVO.getMyCpRankItem();
            log.info("LoveLetterLYRankManager#postProcess cpRankItemList:{}", JSON.toJSONString(cpRankItemList));
            List<Long> uidList = new ArrayList<>();
            if (myCpRankItem != null) {
                uidList.add(myCpRankItem.getMaleUid());
                uidList.add(myCpRankItem.getFemaleUid());
            }
            for (CpRankItem cpRankItem : cpRankItemList) {
                if (myCpRankItem != null && (uidList.contains(cpRankItem.getMaleUid()) && uidList.contains(cpRankItem.getFemaleUid()))) {
                    continue;
                }
                cpRankItem.setMaleAvatar("https://res-cdn.nuan.chat/nuanliao-fronted/activity/guanxuan-jun/default-avatar.png");
                cpRankItem.setFemaleAvatar("https://res-cdn.nuan.chat/nuanliao-fronted/activity/guanxuan-jun/default-avatar.png");
                cpRankItem.setMaleUserName("神秘嘉宾");
                cpRankItem.setFemaleUserName("神秘嘉宾");
            }
        } catch (Exception e) {
            log.warn("CourtBeautyBiography#postProcess error {}", e.getMessage());
        }
    }

    @Override
    protected void doPreProcess(RankContext rankContext) {
        rankContext.setSupportDiff(Boolean.TRUE);
    }


    /**
     * 发放情书榜单
     */
    public void sendCourtBeautyBiography() {
        //总榜
        List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(1L, ACTIVITY_CODE, "rank");
        log.info("sendCourtBeautyBiography scenePrizeDOList {}", JSON.toJSONString(scenePrizeDOList));
        if (CollectionUtils.isEmpty(scenePrizeDOList)) {
            return;
        }

        RankVO rankVO = this.getRank(RankContext.builder()
                .activityCode(ACTIVITY_CODE)
                .rankKey(cbbCacheManager.buildActivityTimeKey(BETTE_RANK_KEY))
                .rankLen(5L)
                .type(RankContext.RankType.cp)
                .build());
        log.info("sendCourtBeautyBiography rankVO {}", rankVO);
        if (rankVO == null) {
            return;
        }
        for (CpRankItem cpRankItem : rankVO.getCpRankItemList()) {
            if (cpRankItem.getValue() < 131400L) {
                log.info("relationId {} rankItem {} 没有奖励", cpRankItem.getRelationId(), cpRankItem);
                continue;
            }
            List<ScenePrizeDO> scenePrizeDOs = scenePrizeDOList.stream().filter(scenePrizeDO ->
                    (Objects.equals(cpRankItem.getRank(), scenePrizeDO.getPrizeBelongToRank()))).collect(Collectors.toList());
            log.info("sendCourtBeautyBiography#sendPrize cpRankItem {} scenePrize {}", cpRankItem, scenePrizeDOs);
            if (CollectionUtils.isEmpty(scenePrizeDOs)) {
                continue;
            }

            // 发放奖励
            sendPrizeManager.sendPrize(
                    BaseParam.builder()
                            .appId(ServicesAppIdEnum.lanling.getAppId())
                            .unionId(ServicesAppIdEnum.lanling.getUnionId())
                            .uid(cpRankItem.getMaleUid())
                            .build(),
                    scenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, cpRankItem.getMaleUid())).collect(Collectors.toList())
            );
            sendPrizeManager.sendPrize(
                    BaseParam.builder()
                            .appId(ServicesAppIdEnum.lanling.getAppId())
                            .unionId(ServicesAppIdEnum.lanling.getUnionId())
                            .uid(cpRankItem.getFemaleUid())
                            .build(),
                    scenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, cpRankItem.getFemaleUid())).collect(Collectors.toList())
            );
            // 小助手
            ScenePrizeDO scenePrizeItem = scenePrizeDOs.get(0);
            notifyComponent.npcNotify(
                    ServicesAppIdEnum.lanling.getUnionId(),
                    cpRankItem.getMaleUid(),
                    String.format("恭喜在「宫廷美人传」活动「执子之手」榜单中排第%s名，奖励已经下发至背包，快去查看吧～", cpRankItem.getRank())
            );
            notifyComponent.npcNotify(
                    ServicesAppIdEnum.lanling.getUnionId(),
                    cpRankItem.getFemaleUid(),
                    String.format("恭喜在「宫廷美人传」活动「执子之手」榜单中排第%s名，奖励已经下发至背包，快去查看吧～", cpRankItem.getRank())
            );

            // 埋点
            for (ScenePrizeDO scenePrizeDO : scenePrizeDOs) {
                trackManager.allActivityReceiveAward(cpRankItem.getMaleUid(), "love_rank",
                        scenePrizeDO.getPrizeValue(), scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeNum());
                trackManager.allActivityReceiveAward(cpRankItem.getFemaleUid(), "love_rank",
                        scenePrizeDO.getPrizeValue(), scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeNum());
            }
        }
    }
}