package cn.yizhoucp.ump.biz.project.biz.manager.activity.seasonalFestival.strategy;

import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.seasonalFestival.common.SeasonalFestivalRedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class SeasonalFestivalTaskTaken implements ExecutableStrategy {

    @Resource
    private SeasonalFestivalRedisManager seasonalFestivalRedisManager;

    @Override
    public Object execute(ButtonEventParam buttonEventParam) {
        String bizKey = buttonEventParam.getBizKey();
        Long uid = buttonEventParam.getBaseParam().getUid();
        //是否领取过
        ScenePrizeDO scenePrizeDO = seasonalFestivalRedisManager.getTaskGift(bizKey);
        if (scenePrizeDO == null) {
            return Boolean.FALSE;
        }
        String giftKey = scenePrizeDO.getPrizeValue();
        if (seasonalFestivalRedisManager.getTaskClaimed(uid, giftKey)) {
            return Boolean.FALSE;
        }
        //领取任务
        seasonalFestivalRedisManager.setTaskClaimed(uid, giftKey);
        return Boolean.TRUE;
    }
}
