package cn.yizhoucp.ump.biz.project.biz.manager.activity.seasonalFestival.common;

import cn.yizhoucp.ump.biz.project.biz.manager.activity.seasonalFestival.strategy.SeasonalFestivalDemoStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.seasonalFestival.strategy.SeasonalFestivalRedeem;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.seasonalFestival.strategy.SeasonalFestivalTaskTaken;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.StrategyEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 节气活动枚举类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 12:11 2025/2/10
 */
public class SeasonalFestivalEnums {

    @AllArgsConstructor
    @Getter
    public enum ButtonEnum implements StrategyEnum {
        DEMO_BUTTON("demo_button", "seasonalFestivalDemoStrategy", SeasonalFestivalDemoStrategy.class),
        REDEEM_BUTTON("redeem", "seasonalFestivalRedeem",SeasonalFestivalRedeem.class),
        TASK_BUTTON("task_taken","seasonalFestivalTaskTaken", SeasonalFestivalTaskTaken.class);
        private final String strategyName;
        private final String beanName;
        private final Class<? extends ExecutableStrategy> strategyClass;
    }
}
