package cn.yizhoucp.ump.biz.project.biz.manager.activity.loveLetterLY;

import cn.yizhoucp.depth.api.client.DepthFeignService;
import cn.yizhoucp.depth.api.vo.CardiacRelationVO;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.PageVO;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.annotation.NoRepeatSubmit;
import cn.yizhoucp.ms.core.base.auth.SecurityUser;
import cn.yizhoucp.ms.core.base.auth.SecurityUtils;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.util.AppUtil;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.CommonUserVO;
import cn.yizhoucp.ms.core.vo.snsservices.relation.WrapUserVO;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.DrawLogNewVO;
import cn.yizhoucp.ump.api.vo.jimu.rank.CpRankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.AbstractRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.lanling.FeignLanlingService;
import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.FeignUserService;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.manager.activity.loveLetterLY.LoveLetterLYConstant.*;

@Service
@Slf4j
public class LoveLetterLYRankManager extends AbstractRankManager {

    @Resource
    private LoveLetterLYConstant constant;

    @Resource
    private SendPrizeManager sendPrizeManager;

    @Lazy
    @Resource
    private FeignLanlingService feignLanlingService;

    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;

    @Resource
    private NotifyComponent notifyComponent;

    @Resource
    private LoveLetterLYTrackManager trackManager;

    @Resource
    protected FeignUserService feignUserService;

    @Resource
    private DepthFeignService depthFeignService;

    /**
     * 后置处理
     */
    @Override
    protected void postProcess(RankContext rankContext) {
        try {
            if (rankContext.getType() != RankContext.RankType.cp) {
                return;
            }
            RankVO rankVO = rankContext.getRankVO();
            List<CpRankItem> cpRankItemList = rankVO.getCpRankItemList();
            CpRankItem myCpRankItem = rankVO.getMyCpRankItem();
            log.info("LoveLetterLYRankManager#postProcess cpRankItemList:{}", JSON.toJSONString(cpRankItemList));
            List<Long> uidList = new ArrayList<>();
            if (myCpRankItem != null) {
                uidList.add(myCpRankItem.getMaleUid());
                uidList.add(myCpRankItem.getFemaleUid());
            }
            for (CpRankItem cpRankItem : cpRankItemList) {
                if (myCpRankItem != null && (uidList.contains(cpRankItem.getMaleUid()) && uidList.contains(cpRankItem.getFemaleUid()))) {
                    continue;
                }
                cpRankItem.setMaleAvatar("https://res-cdn.nuan.chat/nuanliao-fronted/activity/guanxuan-jun/default-avatar.png");
                cpRankItem.setFemaleAvatar("https://res-cdn.nuan.chat/nuanliao-fronted/activity/guanxuan-jun/default-avatar.png");
                cpRankItem.setMaleUserName("神秘嘉宾");
                cpRankItem.setFemaleUserName("神秘嘉宾");
            }
        } catch (Exception e) {
            log.warn("LoveLetterLY#postProcess error {}", e.getMessage());
        }
    }

    @Override
    protected void doPreProcess(RankContext rankContext) {
        rankContext.setSupportDiff(Boolean.TRUE);
    }


    /**
     * 获取好友列表
     */
    public List<CommonUserVO> friendList() {
        SecurityUser user = SecurityUtils.getCurrentUser();
        if (Objects.isNull(user) || Objects.isNull(user.getUserId())) {
            log.warn("SecurityUtils getCurrentUser is null");
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        Long uid = user.getUserId();
        UserVO userVO = feignUserService.getBasic(uid, ServicesAppIdEnum.lanling.getAppId()).successData();
        if (Objects.isNull(userVO) || Objects.isNull(userVO.getSex())) {
            return null;
        }
        String userSex = userVO.getSex().getCode();
        Long start = 0L;
        Integer num = 100;
        PageVO<WrapUserVO> friendList = feignLanlingService.getFriendList(start, num).getData();
        PageVO<WrapUserVO> subFriendList = feignLanlingService.getSubscribeList(start, num).getData();
        PageVO<WrapUserVO> fansList = feignLanlingService.getFansList(start, num).getData();
        List<WrapUserVO> closeFriends = getCloseFriends(uid);

        if (Objects.isNull(friendList) && Objects.isNull(subFriendList) && Objects.isNull(fansList)) {
            return Collections.emptyList();
        }

        List<WrapUserVO> list = new ArrayList<>();
        if (friendList != null) {
            list.addAll(friendList.getList());
        }
        if (subFriendList != null) {
            list.addAll(subFriendList.getList());
        }
        if (fansList != null) {
            list.addAll(fansList.getList());
        }
        if (closeFriends != null) {
            list.addAll(closeFriends);
        }

        // 去重操作
        List<WrapUserVO> uniqueList = list.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                wrapUserVO -> wrapUserVO.getUser().getId(),
                                wrapUserVO -> wrapUserVO,
                                (existing, replacement) -> existing
                        ),
                        map -> new ArrayList<>(map.values())
                ));
        log.info("获取好友列表：{}", uniqueList);

        return uniqueList.stream().map(WrapUserVO::getUser)
                .limit(50)
                .filter(Objects::nonNull)
                .filter(toUserVO -> !toUserVO.getSex().equals(userSex))
                .collect(Collectors.toList());
    }

    public List<WrapUserVO> getCloseFriends(Long uid) {
        Map<Long, CardiacRelationVO> allUserDepth = depthFeignService.getAllUserDepth(MDCUtil.getCurUserIdByMdc()).successData();
        if (allUserDepth == null || allUserDepth.isEmpty()) {
            return null;
        }
        List<WrapUserVO> list = new ArrayList<>();
        allUserDepth.forEach((k, v) -> {
            UserVO userVO = feignUserService.getBasic(k, MDCUtil.getCurAppIdByMdc()).successData();
            if (userVO != null && userVO.getSex() != null) {
                CommonUserVO commonUserVO = new CommonUserVO();
                commonUserVO.setId(userVO.getId());
                commonUserVO.setName(userVO.getName());
                commonUserVO.setAvatar(userVO.getAvatar());
                commonUserVO.setSex(userVO.getSex().getCode());
                WrapUserVO wrapUserVO = new WrapUserVO();
                wrapUserVO.setUser(commonUserVO);
                list.add(wrapUserVO);
            }
        });

        return list;
    }

    /**
     * 结伴
     */
    public Boolean accompany(Long toUid) {
        SecurityUser user = SecurityUtils.getCurrentUser();
        if (Objects.isNull(user) || Objects.isNull(user.getUserId())) {
            log.warn("SecurityUtils getCurrentUser is null");
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        Long uid = user.getUserId();
        if (!noSameSex(uid, toUid)) {
            log.warn("exchange noSameSex uid:{}, toUid:{}", uid, toUid);
            return Boolean.FALSE;
        }
        String userKey = String.format(USER_RELATION_KEY, uid);
        String toUserKey = String.format(USER_RELATION_KEY, toUid);
        Double score = redisManager.score(userKey, toUid);
        if (Objects.isNull(score)) {
            this.incrRankValue(toUid, 0L, userKey);
            this.incrRankValue(uid, 0L, toUserKey);
        }
        return Boolean.TRUE;
    }

    /**
     * 获取cp榜单
     */
    public RankVO obtainedCpList(BaseParam param, Long toUid, Long len) {
        if (Objects.isNull(toUid) || !noSameSex(param.getUid(), toUid)) {
            // 获取当前用户cp情书值最高
            return getRank(RankContext.builder()
                    .param(param)
                    .otherUid(topOneToUid(param.getUid()))
                    .activityCode(ACTIVITY_CODE)
                    .rankKey(RELATION_RANK_KEY)
                    .rankLen(len)
                    .type(RankContext.RankType.cp)
                    .build());
        }

        return getRank(RankContext.builder()
                .param(param)
                .otherUid(toUid)
                .activityCode(ACTIVITY_CODE)
                .rankKey(RELATION_RANK_KEY)
                .rankLen(len)
                .type(RankContext.RankType.cp)
                .build());
    }

    /**
     * 同性不能参加活动
     * 是异性
     */
    public Boolean noSameSex(Long uid, Long toUid) {
        if (Objects.isNull(uid) || Objects.isNull(toUid)) {
            return false;
        }
        UserVO userVO = feignUserService.getBasic(uid, ServicesAppIdEnum.lanling.getAppId()).getData();
        UserVO toUserVO = feignUserService.getBasic(toUid, ServicesAppIdEnum.lanling.getAppId()).getData();
        if (Objects.isNull(userVO) || Objects.isNull(toUserVO)) {
            log.warn("userVO or toUserVO is null uid:{}, toUid:{}", uid, toUid);
            return false;
        }
        return !userVO.getSex().equals(toUserVO.getSex());
    }

    /**
     * 获取情书值最高的toUserId
     */
    public Long topOneToUid(Long uid) {
        if (Objects.isNull(uid)) {
            return null;
        }
        String userRankKey = String.format(USER_RELATION_KEY, uid);
        RankVO userRank = getRank(RankContext.builder()
                .rankKey(userRankKey)
                .rankLen(1L)
                .type(RankContext.RankType.user)
                .build());
        if (Objects.isNull(userRank)) {
            return null;
        }

        if (CollectionUtils.isEmpty(userRank.getRankList())) {
            return null;
        }
        return userRank.getRankList().get(0).getId();
    }


    /**
     * 情书商店兑换
     */
    public Boolean exchange(String productKey, Long toUid) {
        SecurityUser user = SecurityUtils.getCurrentUser();
        if (Objects.isNull(user) || Objects.isNull(user.getUserId())) {
            log.warn("SecurityUtils getCurrentUser is null");
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        Long uid = user.getUserId();
        toUid = Objects.isNull(toUid) ? topOneToUid(uid) : toUid;
        if (Objects.isNull(toUid)) {
            log.warn("topOneToUid is null");
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        String splicUserId = AppUtil.splicUserId(uid, toUid);
        String lock = "ump:love_letter_ly:exchange_lock:%s";
        if (!redisManager.setnx(String.format(lock, splicUserId), 1, DateUtil.ONE_MINUTE_SECONDS / 60)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "兑换太快了～");
        }
        // 校验情书数量 处理接口越权
        Long acquired = constant.acquireLetterCount(uid, toUid);
        ProductEnum productEnum = ProductEnum.getByProductKey(productKey);
        if (Objects.isNull(productEnum)) {
            log.warn("productEnum is null productKey {}", productKey);
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        if (acquired < productEnum.getQuantityRequired()) {
            log.warn("acquired is less than product QuantityRequired,:{}", productKey);
            throw new ServiceException(ErrorCode.INVALID_PARAM, "情书不足，快去获取情书吧～");
        }

        // 下发动作
        List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(1L, ACTIVITY_CODE, productKey);
        log.info("exchange scenePrizeDOList {}", JSON.toJSONString(scenePrizeDOList));
        if (CollectionUtils.isEmpty(scenePrizeDOList)) {
            log.warn("scenePrizeDOList is empty");
            throw new ServiceException(ErrorCode.INVALID_PARAM, "礼物不存在");
        }
        constant.reduceLetterCount(uid, toUid, productEnum.getQuantityRequired());
        sendPrizeManager.sendPrize(
                BaseParam.builder()
                        .appId(ServicesAppIdEnum.lanling.getAppId())
                        .unionId(ServicesAppIdEnum.lanling.getUnionId())
                        .uid(uid)
                        .build(),
                scenePrizeDOList.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, uid)).collect(Collectors.toList())
        );
        String msg = "恭喜在「写给你的情书」中成功兑换%s礼物一个，礼物已经下发至背包，快去查看吧～";
        notifyComponent.npcNotify(
                ServicesAppIdEnum.lanling.getUnionId(),
                uid,
                String.format(msg, scenePrizeDOList.get(0).getPrizeDesc())
        );
        return Boolean.TRUE;
    }


    /**
     * 巧克力制作
     */
    @NoRepeatSubmit(time = 3)
    public Boolean manufacture(String materialsKeys, String chocolateKey) {
        SecurityUser user = SecurityUtils.getCurrentUser();
        if (Objects.isNull(user) || Objects.isNull(user.getUserId())) {
            log.warn("SecurityUtils getCurrentUser is null");
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        Long uid = user.getUserId();
        List<String> list = Arrays.asList(materialsKeys.split(","));
        if (CollectionUtils.isEmpty(list) || list.size() != 3) {
            log.warn("materialsKeys is error:{}, uid:{}", chocolateKey, uid);
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        ChocolateEnum chocolateEnum = ChocolateEnum.getByChocolateKey(chocolateKey);
        if (Objects.isNull(chocolateEnum)) {
            log.warn("chocolateKey is error:{}, uid:{}", chocolateKey, uid);
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        // 先扣减材料再说
        list.forEach(item -> {
            constant.reduceMaterialsCount(uid, item, 1L);
        });
        // 三种材料的名称
        String a = MaterialsEnum.getByMaterialsKey(list.get(0));
        String b = MaterialsEnum.getByMaterialsKey(list.get(1));
        String c = MaterialsEnum.getByMaterialsKey(list.get(2));

        if (!isMaterialsCorrect(list) || !areListsEqualIgnoringOrder(chocolateEnum.getAssociatedMaterials(), list)) {
            // 材料不对
            constant.addLog(uid, DateUtil.format(LocalDateTime.now(), DateUtil.YMD_WITH_LINE), DateUtil.format(LocalDateTime.now(), DateUtil.HMS), String.format(FAIL_LOG_KEY, a, b, c));
            log.info("materials is mistake:{},{},{}, uid:{}", a, b, c, uid);
            throw new ServiceException(ErrorCode.INVALID_PARAM, "材料选择错误哦～");
        }

        // 制作成功
        redisManager.set(String.format(CHOCOLATE_KEY, uid, chocolateEnum.getChocolateKey()), 1L, DateUtil.ONE_MONTH_SECOND);
        // 保存日志
        constant.addLog(uid, DateUtil.format(LocalDateTime.now(), DateUtil.YMD_WITH_LINE), DateUtil.format(LocalDateTime.now(), DateUtil.HMS), String.format(SUCCESS_LOG_KEY, a, b, c));
        // 记录埋点
        trackManager.allActivityTaskFinish(uid, chocolateEnum.getPointType());

        return Boolean.TRUE;
    }

    /**
     * 制作记录
     */
    public DrawLogNewVO manufactureLog() {
        SecurityUser user = SecurityUtils.getCurrentUser();
        if (Objects.isNull(user) || Objects.isNull(user.getUserId())) {
            log.warn("SecurityUtils getCurrentUser is null");
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        Long uid = user.getUserId();
        String key = String.format(CHOCOLATE_LOG_KEY, uid);
        return constant.predictLog(key);
    }

    /**
     * 暖心礼物领取
     */
    public Boolean warm() {
        SecurityUser user = SecurityUtils.getCurrentUser();
        if (Objects.isNull(user) || Objects.isNull(user.getUserId())) {
            log.warn("SecurityUtils getCurrentUser is null");
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        Long uid = user.getUserId();
        if (!StatusEnum.REWARDED_AVAILABLE.getStatusValue().equals(constant.warmGiftStatus(uid))) {
            log.warn("warmGiftStatus is not completed:{}", uid);
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(1L, ACTIVITY_CODE, "warm");
        log.info("warm scenePrizeDOList {}", JSON.toJSONString(scenePrizeDOList));
        if (CollectionUtils.isEmpty(scenePrizeDOList)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        redisManager.set(String.format(WARM_GIFT_STATUS_KEY, uid), 1L, DateUtil.ONE_MONTH_SECOND);
        sendPrizeManager.sendPrize(
                BaseParam.builder()
                        .appId(ServicesAppIdEnum.lanling.getAppId())
                        .unionId(ServicesAppIdEnum.lanling.getUnionId())
                        .uid(uid)
                        .build(),
                scenePrizeDOList.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, uid)).collect(Collectors.toList())
        );
        String msg = "恭喜在「写给你的情书」活动中成功获得%s礼物一个，礼物已经下发至背包，快去领取吧～";
        notifyComponent.npcNotify(
                ServicesAppIdEnum.lanling.getUnionId(),
                uid,
                String.format(msg, scenePrizeDOList.get(0).getPrizeDesc())
        );
        trackManager.allActivityReceiveAward(uid, "sweet_love",
                scenePrizeDOList.get(0).getPrizeValue(), scenePrizeDOList.get(0).getPrizeValueGold(), scenePrizeDOList.get(0).getPrizeNum());
        return Boolean.TRUE;
    }

    /**
     * 特性：我们这里的巧克力制作材料都是不一样的，直接判断重复
     */
    private boolean isMaterialsCorrect(List<String> materials) {
        ArrayList<String> list = new ArrayList<>(materials);
        long distinctCount = list.stream().distinct().count();
        return distinctCount == (long) list.size();
    }

    /**
     * 比较材料是否正确
     */
    private boolean areListsEqualIgnoringOrder(List<String> list1, List<String> list2) {
        Set<String> set1 = new HashSet<>(list1);
        Set<String> set2 = new HashSet<>(list2);
        return set1.equals(set2);
    }


/******************  定时任务  ******************/


    /**
     * 发放情书榜单
     */
    public void sendingLoveLetters() {
        //总榜
        List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(1L, ACTIVITY_CODE, "rank");
        log.info("sendingLoveLetters scenePrizeDOList {}", JSON.toJSONString(scenePrizeDOList));
        if (CollectionUtils.isEmpty(scenePrizeDOList)) {
            return;
        }

        RankVO rankVO = this.getRank(RankContext.builder()
                .activityCode(ACTIVITY_CODE)
                .rankKey(RELATION_RANK_KEY)
                .rankLen(5L)
                .type(RankContext.RankType.cp)
                .build());
        log.info("sendingLoveLetters rankVO {}", rankVO);
        if (rankVO == null) {
            return;
        }
        for (CpRankItem cpRankItem : rankVO.getCpRankItemList()) {
            if (cpRankItem.getValue() < MIN_SUM_RANK_VALUE) {
                log.info("relationId {} rankItem {} 没有奖励", cpRankItem.getRelationId(), cpRankItem);
                continue;
            }
            List<ScenePrizeDO> scenePrizeDOs = scenePrizeDOList.stream().filter(scenePrizeDO ->
                    (Objects.equals(cpRankItem.getRank(), scenePrizeDO.getPrizeBelongToRank()))).collect(Collectors.toList());
            log.info("TreasureHall#sendPrize cpRankItem {} scenePrize {}", cpRankItem, scenePrizeDOs);
            if (CollectionUtils.isEmpty(scenePrizeDOs)) {
                continue;
            }

            // 发放奖励
            sendPrizeManager.sendPrize(
                    BaseParam.builder()
                            .appId(ServicesAppIdEnum.lanling.getAppId())
                            .unionId(ServicesAppIdEnum.lanling.getUnionId())
                            .uid(cpRankItem.getMaleUid())
                            .build(),
                    scenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, cpRankItem.getMaleUid())).collect(Collectors.toList())
            );
            sendPrizeManager.sendPrize(
                    BaseParam.builder()
                            .appId(ServicesAppIdEnum.lanling.getAppId())
                            .unionId(ServicesAppIdEnum.lanling.getUnionId())
                            .uid(cpRankItem.getFemaleUid())
                            .build(),
                    scenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, cpRankItem.getFemaleUid())).collect(Collectors.toList())
            );
            // 小助手
            ScenePrizeDO scenePrizeItem = scenePrizeDOs.get(0);
            notifyComponent.npcNotify(
                    ServicesAppIdEnum.lanling.getUnionId(),
                    cpRankItem.getMaleUid(),
                    String.format("恭喜在「写给你的情书」中，排名%s，礼物已经下发至背包了哦～快去查看吧～", cpRankItem.getRank())
            );
            notifyComponent.npcNotify(
                    ServicesAppIdEnum.lanling.getUnionId(),
                    cpRankItem.getFemaleUid(),
                    String.format("恭喜在「写给你的情书」中，排名%s，礼物已经下发至背包了哦～快去查看吧～", cpRankItem.getRank())
            );

            // 埋点
            for (ScenePrizeDO scenePrizeDO : scenePrizeDOs) {
                trackManager.allActivityReceiveAward(cpRankItem.getMaleUid(), "love_rank",
                        scenePrizeDO.getPrizeValue(), scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeNum());
                trackManager.allActivityReceiveAward(cpRankItem.getFemaleUid(), "love_rank",
                        scenePrizeDO.getPrizeValue(), scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeNum());
            }
        }
    }


}