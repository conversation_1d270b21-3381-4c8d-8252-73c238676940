package cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.pre.common;

public class PreChineseNewYear2025Constant {

    /**
     * 活动code
     */
    public static final String ACTIVITY_CODE = "2025_Chinese_new_year";

    /**
     * 用户是否是第一次进入活动
     */
    public static final String IS_FIRST_TIME = "ump:%s:is_first_time:%s";
    /**
     * 用户是否领取过登录奖励
     */
    public static final String HAS_RECEIVED_LOGIN_REWARD = "ump:%s:has_received_login_reward:%s";
    /**
     * 用户登录天数
     */
    public static final String LOGIN_DAY = "ump:%s:login_day:%s";
    /**
     * 登录奖励
     */
    public static final String LOGIN_REWARD_KEY = "login_reward_%s";

    /**
     * 登陆奖励任务code
     */
    public static final String LOGIN_REWARD_TASK_CODE = "login_reward";


    public static final String TASK_PROGRESS = "ump:%s:task_progress:%s:%s";
    public static final String TASK_DESC = "赠送「%s」礼物%s个";
    public static final String TASK_REWARD_DESC = "获取「%s」%s个";
    public static final String TASK_CLAIMED = "ump:%s:task_claimed:%s:%s";
    public static final String TASK_REWARD = "ump:%s:task_reward:%s:%s";
    public static final String TASK_GIFT_MAP = "ump:%s:task_gift_map";
    public static final String CURRENT_SCENE = "ump:%s:current_scene:%s";
    public static final String DECORATION_NODE = "ump:%s:decoration_node:%s:%s";
    public static final String DECORATION_COMPLETION_REWARD = "decoration_completion_reward";
    public static final String DECORATION_COMPLETION_REWARD_CLAIMED = "ump:%s:decoration_completion_reward_claimed:%s";
    public static final Long MAX_LOGIN_DAY = 7L;
    public static final String MESSAGE_TEMPLATE_CACHE_KEY = "ump:%s:message_template_cache:%s";
    public static final String LOGIN_REWARD_MESSAGE = "login_reward_message";
    public static final String DECORATION_COMPLETION_MESSAGE = "decoration_completion_message";
    public static final String LOGIN_REWARD_END_TIME = "login_reward_expiry";
    public static final String DECORATION_GIFT_MAP = "ump:%s:decoration_gift_map";
}
