package cn.yizhoucp.ump.biz.project.web.rest.controller.activity;


import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.umpServices.activity.spring.LuckyFishVO;
import cn.yizhoucp.ms.core.vo.userservices.UserBaseVO;
import cn.yizhoucp.ump.api.vo.activity.sakaWeekStar.IndexVO;
import cn.yizhoucp.ump.api.vo.activity.sakaWeekStar.PageInfo;
import cn.yizhoucp.ump.api.vo.activity.sakaWeekStar.TopItem;
import cn.yizhoucp.ump.api.vo.jimu.draw.PrizeItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.sakaWeekStar.WeekStarBizManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.sakaWeekStar.WeekStarPageManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.WlzbResultDO;
import com.google.common.collect.Lists;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * saka 礼物周星活动
 *
 * @author: lianghu
 */
@RestController
@RequestMapping("/api/inner/activity/week-star")
public class SakaWeekStarController {

    @Resource
    private WeekStarPageManager weekStarPageManager;
    @Resource
    private WeekStarBizManager weekStarBizManager;

    @RequestMapping("/get-index")
    public Result<IndexVO> getIndex(BaseParam param) {
        return Result.successResult(weekStarPageManager.getIndex(param));
    }

    @RequestMapping("/get-index-url")
    public Result<String> getIndexUrl(BaseParam param) {
        return Result.successResult(weekStarPageManager.getIndexUrl(param));
    }

    @RequestMapping("/get-week-prize")
    public Result<Map<String, PrizeItem>> getPrizeItem(BaseParam param) {
        List<PrizeItem> weekPrizeList = weekStarBizManager.getWeekPrizeList(param);
        if (CollectionUtils.isEmpty(weekPrizeList)) {
            return Result.successResult(new HashMap<>());
        }
        return Result.successResult(weekPrizeList.stream().collect(Collectors.toMap(PrizeItem::getPrizeKey, v -> v, (o1, o2) -> o1)));
    }

    @RequestMapping("/get-charm-rank-top")
    public Result<Map<String, UserBaseVO>> getCharmRankTop(BaseParam param) {
        return Result.successResult(weekStarBizManager.getCharmTopUserMap(param));
    }

}
