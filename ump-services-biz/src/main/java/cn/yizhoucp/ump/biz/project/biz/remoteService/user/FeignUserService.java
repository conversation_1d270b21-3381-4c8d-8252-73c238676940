package cn.yizhoucp.ump.biz.project.biz.remoteService.user;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.vo.BatchIdRequestVO;
import cn.yizhoucp.ms.core.vo.coinservices.CoinAccountVO;
import cn.yizhoucp.ms.core.vo.coinservices.UserAccountVO;
import cn.yizhoucp.ms.core.vo.userservices.UserBaseVO;
import cn.yizhoucp.ms.core.vo.userservices.UserExtendVO;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import com.alibaba.fastjson.JSONObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 用户服务接口
 *
 * <AUTHOR>
 */
@FeignClient("user-services")
public interface FeignUserService {

    /**
     * 获取用户注册天数
     *
     * @param uid   用户id
     * @param appId 应用id
     * @return Long
     */
    @RequestMapping("/api/inner/get-user-registered-day")
    Result<Long> getUserRegisteredDay(@RequestParam("uid") Long uid, @RequestParam("appId") Long appId);

    /**
     * 获取用户额外信息列表
     *
     * @param appId 应用 ID
     * @param uidList 用户 ID 列表
     * @return 列表
     */
    @RequestMapping("/api/inner/user/user-account/get-user-extend-list")
    Result<List<UserExtendVO>> getUserExtendByUidList(@RequestParam("appId") Long appId, @RequestParam("uidList") List<Long> uidList);

    /**
     * 获取用户基础信息
     *
     * @param userId 用户 ID
     * @param appId  应用 ID
     * @return UserVO
     */
    @RequestMapping("/api/inner/get-basic-info")
    Result<UserVO> getBasic(@RequestParam("userId") Long userId, @RequestParam("appId") Long appId);

    /**
     * 内部获取用户的基础属性 包含 id 昵称 头像 性别 年龄 (有缓存)
     *
     * @param appId  应用id
     * @param userId 用户id
     * @return UserBaseVO
     */
    @RequestMapping("/api/inner/user/get-user-base-info")
    Result<UserBaseVO> getUserBaseVO(@RequestParam("appId") Long appId, @RequestParam("userId") Long userId);

    @RequestMapping("/api/inner/get-basic-info-without-status")
    Result<UserBaseVO> getUserWithoutStatusBaseVO(@RequestParam("appId") Long appId, @RequestParam("userId") Long userId);

    /**
     * 获取用户账户
     *
     * @param appId 应用id
     * @param uid   用户id
     * @return UserAccountVO
     */
    @RequestMapping("/api/inner/user-account/get-user-account-by-uid")
    Result<UserAccountVO> getUserAccountByUid(@RequestParam("appId")Long appId,@RequestParam("uid") Long uid);

    /**
     * 获取用户设置的语言信息
     *
     * @param appId  应用id
     * @param userId 用户id
     * @return JSONObject
     */
    @RequestMapping("/api/user/get-user-language")
    Result<JSONObject> getUserLanguage(@RequestParam("appId") Long appId, @RequestParam("userId") Long userId);

    /**
     * 获取用户应用标识
     *
     * @param uid 用户id JSON
     * @return Map<Long, String>
     */
    @RequestMapping("/api/inner/get-user-union-id")
    Result<String> getUserUnionId(@RequestParam("uid") Long uid);

    /**
     * 获取用户应用标识
     *
     * @param uidStrs 用户id JSON
     * @return Map<Long, String>
     */
    @RequestMapping("/api/inner/get-user-union-id-map")
    Result<Map<Long, String>> getUserUnionIdMap(@RequestParam("uidStrs") String uidStrs);

    /**
     * 批量获取用户信息
     *
     * @param request 请求 id 集合
     * @return List<UserVO>
     */
    @PostMapping("/api/inner/acquire-users-bulk-list-post")
    Result<List<UserVO>> acquireUsersBulkListPost(@RequestBody BatchIdRequestVO request);

    /**
     * 批量获取用户信息
     *
     * @param uidList 用户id
     * @return List<UserVO>
     */
    default Result<List<UserVO>> acquireUsersBulkListPost(List<Long> uidList) {
        return acquireUsersBulkListPost(BatchIdRequestVO.builder().idList(uidList).build());
    }

    /**
     * 通过用户id集合获取 Map<Long,UserVO>
     *
     * @param request 请求 id 集合
     * @return Map<Long, UserVO>
     */
    @PostMapping("/api/inner/acquire-users-map-post2")
    Result<Map<Long, UserVO>> acquireUsersInBulkPost2(@RequestBody BatchIdRequestVO request);

    /**
     * 通过用户id集合获取 Map<Long,UserVO>
     *
     * @param uidList 用户id
     * @return Map<Long, UserVO>
     */
    default Result<Map<Long, UserVO>> acquireUsersInBulkPost2(List<Long> uidList) {
        return acquireUsersInBulkPost2(BatchIdRequestVO.builder().idList(uidList).build());
    }

    /**
     * 查看当前登录账户的用户金币账户的数据
     *
     * @return CoinAccountVO
     */
    @GetMapping("/api/inner/user-account/my-coin")
    Result<CoinAccountVO> getMyCoin();

}
