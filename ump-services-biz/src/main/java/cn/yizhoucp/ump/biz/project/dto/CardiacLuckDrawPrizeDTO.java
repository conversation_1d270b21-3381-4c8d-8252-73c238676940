package cn.yizhoucp.ump.biz.project.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 心动抽奖奖励通用类
 * <AUTHOR> 2022-01-19
 */
@Data
public class CardiacLuckDrawPrizeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 抽中的奖励的 Key（Key的结构为 key + "," + number）
     */
    private String key;

    /**
     * 抽中的奖励的等级
     * @See cn.yizhoucp.lanling.api.project.biz.enums.depth.CardiacLuckDrawPrizeLevelEnum
     */
    private String level;

    /**
     * 抽中的奖励的类型
     * @See cn.yizhoucp.lanling.api.project.biz.enums.depth.CardiacLuckDrawPrizeTypeEnum
     */
    private String type;

    /**
     * 抽中的奖励的权重
     */
    private Integer weight;

}