package cn.yizhoucp.ump.biz.project.biz.manager.activity.journeyToTheWest.claimRewardStrategy.strategy;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.journeyToTheWest.JourneyToTheWestConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.journeyToTheWest.JourneyToTheWestTrackManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.journeyToTheWest.claimRewardStrategy.JourneyRewardStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class TowerViewReward implements JourneyRewardStrategy {
    @Resource
    private JourneyToTheWestConstant journeyToTheWestConstant;
    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private JourneyToTheWestTrackManager journeyToTheWestTrackManager;

    @Override
    public Object claimReward(Long uid, Long toUid, String rewardId, String extData) {
        JourneyToTheWestConstant.TaskCodeEnum taskCodeEnum = JourneyToTheWestConstant.TaskCodeEnum.getTaskByTaskId(Long.parseLong(rewardId));
        Boolean allClaimed = Boolean.TRUE;
        if (taskCodeEnum == null) {
            Boolean isComplete = Boolean.TRUE;
            for (JourneyToTheWestConstant.TaskCodeEnum task : JourneyToTheWestConstant.TaskCodeEnum.values()) {
                isComplete = journeyToTheWestConstant.getToViewTaskIsComplete(uid, task) && isComplete;
            }
            if (!isComplete) {
                throw new ServiceException(ErrorCode.INVALID_PARAM, "任务未完成");
            }
            Boolean isClaimed = journeyToTheWestConstant.isFinalRewardClaimed(uid);
            if (isClaimed) {
                throw new ServiceException(ErrorCode.INVALID_PARAM, "最终礼物已领取");
            }
            List<ScenePrizeDO> rewardList = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), JourneyToTheWestConstant.ACTIVITY_CODE, "tower_view_reward");
            sendPrizeManager.sendPrize(
                    BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(uid).build(),
                    rewardList.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, uid)).collect(Collectors.toList())
            );
            journeyToTheWestConstant.setFinalRewardClaimed(uid);
            for (ScenePrizeDO scenePrizeDO : rewardList) {
                journeyToTheWestTrackManager.allActivityReceiveAward(JourneyToTheWestConstant.AwardTypeEnum.VIEW_TOWER.getCode(), scenePrizeDO.getPrizeValue(), scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeNum(), uid);
            }
            for (JourneyToTheWestConstant.TaskCodeEnum task : JourneyToTheWestConstant.TaskCodeEnum.values()) {
                allClaimed = journeyToTheWestConstant.getToViewTaskIsClaimed(uid, task) && allClaimed;
            }
            if (allClaimed) {
                journeyToTheWestConstant.incrementLoopCount(JourneyToTheWestConstant.SceneEnum.SCENE_3.getSceneCode(), uid.toString());
            }
            return Boolean.TRUE;
        }
        Boolean isComplete = journeyToTheWestConstant.getToViewTaskIsComplete(uid, taskCodeEnum);
        if (!isComplete) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "任务未完成");
        }
        Boolean isClaimed = journeyToTheWestConstant.getToViewTaskIsClaimed(uid, taskCodeEnum);
        if (isClaimed) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "已领取过");
        }
        journeyToTheWestConstant.incrementPurpleGoldBellNum(uid, taskCodeEnum.getRewardNum());
        journeyToTheWestConstant.setToViewTaskIsClaimed(uid, taskCodeEnum);
        //埋点
        journeyToTheWestTrackManager.allActivityReceiveAward(JourneyToTheWestConstant.AwardTypeEnum.VIEW_TOWER.getCode(), "ZJL_GIFT", 0L, taskCodeEnum.getRewardNum().intValue(), uid);
        for (JourneyToTheWestConstant.TaskCodeEnum task : JourneyToTheWestConstant.TaskCodeEnum.values()) {
            allClaimed = journeyToTheWestConstant.getToViewTaskIsClaimed(uid, task) && allClaimed;
        }
        Boolean finalRewardClaimed = journeyToTheWestConstant.isFinalRewardClaimed(uid);
        if (allClaimed && finalRewardClaimed) {
            journeyToTheWestConstant.incrementLoopCount(JourneyToTheWestConstant.SceneEnum.SCENE_3.getSceneCode(), uid.toString());
        }
        return Boolean.TRUE;
    }
}
