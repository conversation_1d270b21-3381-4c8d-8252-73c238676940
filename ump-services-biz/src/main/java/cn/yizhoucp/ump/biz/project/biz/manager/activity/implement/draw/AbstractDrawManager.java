package cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw;

import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.annotation.NoRepeatSubmit;
import cn.yizhoucp.ms.core.base.auth.SecurityUtils;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.international.InternationalManager;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.product.client.UserPackageFeignService;
import cn.yizhoucp.product.dto.PackageQueryConditionDTO;
import cn.yizhoucp.product.dto.UsePackageDTO;
import cn.yizhoucp.product.dto.UserPackageDetailDTO;
import cn.yizhoucp.product.enums.PackageUseScene;
import cn.yizhoucp.starter.cassandra.base.kafka.YzKafkaProducerManager;
import cn.yizhoucp.starter.cassandra.base.rocketmq.RocketmqProducerManager;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.jimu.draw.DrawReturn;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw.inner.PrizeTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ActivityReportManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.SceneEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.log.LogComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.sendPrize.SendPrizeComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.barrageList.BarrageManagerFactory;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawConfig;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawContext;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.doDrawStrategy.DoDrawFactory;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.DrawManager;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.biz.manager.userCoinAccount.UserCoinAccountManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.FeignSnsService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.coin.FeignCoinService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.FeignUserService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.UserRemoteService;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityInfoUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.DrawLogJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.dal.service.DrawPoolItemService;
import cn.yizhoucp.ump.biz.project.dal.service.DrawPoolService;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.yizhoucp.ms.core.base.ErrorCode.ACTIVITY_DRAW_DEDUCT_ERROR;

/**
 * 抽奖模板
 *
 * @author: lianghu
 */
@Deprecated
public abstract class AbstractDrawManager implements DrawManager {

    @Resource
    private LogComponent logComponent;
    @Resource
    protected DoDrawFactory doDrawFactory;
    @Resource
    protected SendPrizeComponent sendPrizeComponent;
    @Resource
    protected RedisManager redisManager;
    @Resource
    protected BarrageManagerFactory barrageManagerFactory;
    @Resource
    protected UserRemoteService userRemoteService;
    @Resource
    protected NotifyComponent notifyComponent;
    @Resource
    protected FeignCoinService feignCoinService;
    @Resource
    protected FeignSnsService feignSnsService;
    @Resource
    protected FeignUserService feignUserService;
    @Resource
    protected InternationalManager internationalManager;
    @Resource
    protected UserCoinAccountManager userCoinAccountManager;
    @Resource
    protected RocketmqProducerManager rocketmqProducerManager;
    @Resource
    protected YzKafkaProducerManager yzKafkaProducerManager;
    @Resource
    protected UserPackageFeignService userPackageFeignService;
    @Resource
    protected SendPrizeManager sendPrizeManager;
    @Resource
    protected ActivityReportManager activityReportManager;
    @Resource
    protected DrawLogJpaDAO drawLogJpaDAO;
    @Resource
    protected DrawPoolItemService drawPoolItemService;
    @Resource
    protected DrawPoolService drawPoolService;

    /**
     * 抽奖
     *
     * @return
     */
    @NoRepeatSubmit
    @Override
    public DrawReturn draw(DrawParam param) {
        DrawContext context = DrawContext.builder().drawParam(param).build();

        // 资源检查
        resourceCheck(context);

        // 获取抽奖奖品
        List<DrawPoolItemDO> prizeDOList = (List<DrawPoolItemDO>) doDrawFactory.getDoDrawStrategy().draw(param);
        context.setPrizeDOList(prizeDOList);
        context.setDrawPoolItemDOS(prizeDOList);
        context.setPrizeItemList(prizeDOList.stream().map(x -> DrawPoolItemDTO.builder()
                .drawPoolItemDO(x)
                .targetTimes(x.getTargetTimes()).build()
        ).collect(Collectors.toList()));
        // 扣除资源
        deductResource(context);

        // 下发奖励
        sendPrize(ActivityInfoUtil.getActivityCode(), SecurityUtils.getCurrentUserIdLong(), prizeDOList, context);

        // 记录日志
        writeLog(context);

        // 后置处理
        callback(context);

        // 允许额外抽奖逻辑
        prizeDOList = context.getDrawPoolItemDOS();

        return DrawReturn.builder()
                .hasRealPrize(hasRealPrize(prizeDOList))
                .prizeItemList(Lists.transform(prizeDOList, DrawPoolItemDO::convert2PrizeItem)).build();
    }

    protected abstract void resourceCheck(DrawContext context);

    protected abstract void deductResource(DrawContext context);

    protected void callback(DrawContext context) {
        DrawParam param = context.getDrawParam();
        DrawConfig drawConfig = context.getDrawConfig();

        // 验证使用模版上报流程
        if (Objects.nonNull(drawConfig) && Boolean.TRUE.equals(drawConfig.getUseTemplateTrace())) {
            List<DrawPoolItemDTO> prizeItemList = context.getPrizeItemList();
            if (!CollectionUtils.isEmpty(prizeItemList)) {
                for (DrawPoolItemDTO item : prizeItemList) {
                    for (int i = 0; i < item.getTargetTimes(); i++) {
                        activityReportManager.drawReport(
                                BaseParam.builder().appId(param.getAppId()).unionId(param.getUnionId()).uid(param.getUid()).build(),
                                drawConfig.getActivityCode(),
                                drawConfig.getPoolCode(),
                                item.getDrawPoolItemDO().getItemKey(),
                                item.getDrawPoolItemDO().getItemValueGold(),
                                null
                        );
                    }
                }
            }
        }
    }

    protected void writeLog(DrawContext context) {
        DrawParam param = context.getDrawParam();
        List<DrawPoolItemDO> prizeDOList = context.getPrizeDOList();
        logComponent.putDrawLog(param.getUnionId(), param.getAppId(), param.getActivityCode(), param.getUid(), prizeDOList);
    }

    protected void sendPrize(String activityCode, Long uid, List<DrawPoolItemDO> prizeList, DrawContext drawContext) {
        if (Boolean.TRUE.equals(drawContext.getDrawParam().getNoSendPrize())) {
            return;
        }

        sendPrizeComponent.sendPrize(null, activityCode, SceneEnum.DRAW.getCode(), uid, Lists.transform(prizeList, DrawPoolItemDO::convert2ScenePrizeDO));
        drawContext.setPrizeDOList(prizeList);
    }

    protected Boolean hasRealPrize(List<DrawPoolItemDO> prizeDOList) {
        Boolean hasRealPrize = false;
        for (DrawPoolItemDO item : prizeDOList) {
            if (PrizeTypeEnum.PRIZE_REAL.getCode().equals(item.getItemType())) {
                hasRealPrize = true;
            }
        }
        return hasRealPrize;
    }

    protected String getPrizeDescListStr(List<DrawPoolItemDO> prizes, Integer limit) {
        if (CollectionUtils.isEmpty(prizes)) {
            return null;
        }
        StringBuilder prizeBuilder = new StringBuilder("");
        for (DrawPoolItemDO prize : prizes) {
            if (Objects.nonNull(prize.getItemValueGold()) && prize.getItemValueGold() > limit) {
                prizeBuilder.append(prize.getItemName()).append("、");
            }
        }
        return StringUtils.removeEnd(prizeBuilder.toString(), "、");
    }

    protected Integer getPackageNum(Long uid, String giftKey) {
        if (StringUtils.isBlank(giftKey)) {
            return 0;
        }
        PackageQueryConditionDTO queryParam = PackageQueryConditionDTO.builder().uid(uid).packageScene("spring_activity").build();
        List<UserPackageDetailDTO> packageList = userPackageFeignService.getPackageDetailByCondition(queryParam).successData();
        if (CollectionUtils.isEmpty(packageList)) {
            return 0;
        }
        Long result = packageList.stream().filter(obj -> giftKey.equals(obj.getBizId())).mapToLong(UserPackageDetailDTO::getAvailableNum).sum();
        return result.intValue();
    }

    protected void deductPackageNum(Long uid, String giftKey, Integer num) {
        if (StringUtils.isBlank(giftKey) || Objects.isNull(num)) {
            return;
        }
        List<UsePackageDTO> usePackageList = userPackageFeignService.usePackageWithBizId(ServicesAppIdEnum.lanling.getAppId(), ServicesAppIdEnum.lanling.getUnionId(), uid,
                giftKey, num.longValue(), PackageUseScene.draw.getCode());
        if (CollectionUtils.isEmpty(usePackageList)) {
            throw new ServiceException(ACTIVITY_DRAW_DEDUCT_ERROR);
        }
    }
}
