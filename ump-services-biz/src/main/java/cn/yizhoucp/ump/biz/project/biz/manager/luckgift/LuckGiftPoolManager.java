package cn.yizhoucp.ump.biz.project.biz.manager.luckgift;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.vo.landingservices.AdminPageVO;
import cn.yizhoucp.ump.api.vo.luckBag.admin.DrawPoolAdminItemVO;
import cn.yizhoucp.ump.api.vo.luckBag.admin.DrawPoolAdminVO;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw.DrawPooExtractType;
import cn.yizhoucp.ump.biz.project.biz.enums.navigation.BoatEnum;
import cn.yizhoucp.ump.biz.project.biz.enums.navigation.MileEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.drawStrategy.LotteryManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.KeyWeight;
import cn.yizhoucp.ump.biz.project.biz.manager.navigation.NavigationConstant;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.DrawPoolJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.dal.service.DrawPoolItemService;
import cn.yizhoucp.ump.biz.project.dal.service.DrawPoolService;
import cn.yizhoucp.ump.biz.project.dto.DrawPoolDTO;
import com.aliyun.openservices.shade.com.google.common.collect.ArrayListMultimap;
import com.aliyun.openservices.shade.com.google.common.collect.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class LuckGiftPoolManager {
    @Resource
    private DrawPoolService drawPoolService;

    @Resource
    private DrawPoolItemService drawPoolItemService;

    @Resource
    private LotteryManager lotteryManager;

    @Resource
    private DrawPoolJpaDAO drawPoolJpaDAO;

    public AdminPageVO<DrawPoolAdminVO> listDrawPool() {
        AdminPageVO<DrawPoolAdminVO> result = new AdminPageVO<>();
        List<DrawPoolDO> poolList = drawPoolJpaDAO.findByActivityCode("voice_room_luck_gift");
        if (CollectionUtils.isEmpty(poolList)){
            return result;
        }
        List<String> poolCodes = poolList.stream().map(DrawPoolDO::getPoolCode).collect(Collectors.toList());
        ArrayListMultimap<String, DrawPoolItemDO> byPoolCodes = drawPoolItemService.getByPoolCodes(poolCodes);
        ArrayList<DrawPoolAdminVO> data = Lists.newArrayList();
        //设置返奖率什么的
        for (DrawPoolDO drawPoolDO : poolList) {
            data.add(converDrawPoolAdminVO(drawPoolDO, byPoolCodes.get(drawPoolDO.getPoolCode())));
        }
        result.setPageIndex(1);
        result.setTotalCount((long) data.size());
        result.setItems(data);
        return result;
    }

    private DrawPoolAdminVO converDrawPoolAdminVO(DrawPoolDO drawPoolDO, List<DrawPoolItemDO> drawPoolItemDOS) {
        DrawPoolAdminVO drawPoolAdminVO = new DrawPoolAdminVO();
        BeanUtils.copyProperties(drawPoolDO, drawPoolAdminVO);
        drawPoolAdminVO.setCreateTime(drawPoolDO.getCreateTime().format(DateUtil.LOCAL_DATE_FORMAT_YMDHMS));
        drawPoolAdminVO.setUpdateTime(drawPoolDO.getUpdateTime().format(DateUtil.LOCAL_DATE_FORMAT_YMDHMS));
        drawPoolAdminVO.setPoolType(drawPoolDO.getPoolType().getCode());

        if (CollectionUtils.isEmpty(drawPoolItemDOS)){
            return drawPoolAdminVO;
        }
        List<DrawPoolAdminItemVO> drawItems = Lists.newArrayList();
        //进行返奖率计算
        for (DrawPoolItemDO drawPoolItemDO : drawPoolItemDOS) {
            Integer weight = drawPoolItemDO.getWeight();
            String itemKey = drawPoolItemDO.getItemKey();
            DrawPoolAdminItemVO drawPoolAdminItemVO = new DrawPoolAdminItemVO();
            drawPoolAdminItemVO.setItemKey(itemKey);
            drawPoolAdminItemVO.setWeight(weight);
            drawItems.add(drawPoolAdminItemVO);
        }
        drawPoolAdminVO.setDrawItems(drawItems);
        return drawPoolAdminVO;
    }

    /**
     * 新增/修改奖池
     * @param param
     * @return
     */
    public Boolean saveOrUpdatePrizePool(DrawPoolAdminVO param){
        if (!param.getPoolCode().startsWith("luckgift-")) {
            param.setPoolCode("luckgift-" + param.getPoolCode());
        }
        DrawPoolDO poolDO = drawPoolService.findByPoolCode(param.getPoolCode());
        if (poolDO != null) {
            param.setId(poolDO.getId());
            param.setPoolCode(poolDO.getPoolCode());
            param.setStatus(poolDO.getStatus());
            BeanUtils.copyProperties(param,poolDO);
        } else {
            poolDO = new DrawPoolDO();
            BeanUtils.copyProperties(param,poolDO);
            poolDO.setCreateTime(LocalDateTime.now());
            poolDO.setId(null);
            //默认不生效的
            poolDO.setStatus(1);
        }
        poolDO.setPoolType(DrawPooExtractType.valueOf(param.getPoolType()));
        poolDO.setUpdateTime(LocalDateTime.now());
        //进行数据库保存
        drawPoolService.save(poolDO);

        //处理奖池了
        LocalDateTime now = LocalDateTime.now();
        List<DrawPoolItemDO> byPoolCode = drawPoolItemService.getByPoolCodeAndUnionId(param.getPoolCode(),param.getUnionId());
        Map<String, DrawPoolItemDO> itemDOMap = byPoolCode.stream().collect(Collectors.toMap(DrawPoolItemDO::getItemKey, one -> one, (o, p) -> o));
        List<DrawPoolAdminItemVO> drawItems = param.getDrawItems();
        for (DrawPoolAdminItemVO drawItem : drawItems) {
            String itemKey = drawItem.getItemKey();
            Integer weight = drawItem.getWeight();
            DrawPoolItemDO drawPoolItemDO;
            if (null != itemDOMap.get(itemKey)) {
                drawPoolItemDO = itemDOMap.get(itemKey);
                byPoolCode.remove(drawPoolItemDO);
            } else {
                drawPoolItemDO = DrawPoolItemDO.builder().itemName("自定义").itemType("custom").itemNum(1).itemKey(itemKey).itemEffectiveDay(-1).itemValueGold(0L).status(1).createTime(now).updateTime(now).poolCode(poolDO.getPoolCode()).itemIcon("").unionId(poolDO.getUnionId()).build();
            }
            drawPoolItemDO.setWeight(weight);
            drawPoolItemService.save(drawPoolItemDO);
        }
        for (DrawPoolItemDO drawPoolItemDO : byPoolCode) {
            drawPoolItemService.deleteById(drawPoolItemDO.getId());
        }
        //删除奖品缓存
        drawPoolItemService.deleteCacheByPoolCode(poolDO.getPoolCode());
        lotteryManager.clearPool(poolDO.getPoolType(), poolDO.getPoolCode());
        return true;
    }
}
