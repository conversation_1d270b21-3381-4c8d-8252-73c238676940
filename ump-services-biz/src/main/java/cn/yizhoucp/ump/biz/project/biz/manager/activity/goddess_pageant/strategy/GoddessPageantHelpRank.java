package cn.yizhoucp.ump.biz.project.biz.manager.activity.goddess_pageant.strategy;

import cn.hutool.core.text.CharSequenceUtil;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.convert.SexType;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.goddess_pageant.GoddessPageantRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.goddess_pageant.common.GoddessPageantConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.goddess_pageant.common.GoddessPageantRedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import cn.yizhoucp.user.manager.UserFeignManager;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class GoddessPageantHelpRank implements ExecutableStrategy {

    @Resource
    private GoddessPageantRankManager goddessPageantRankManager;

    @Resource
    private GoddessPageantRedisManager goddessPageantRedisManager;
    @Resource
    private NotifyComponent notifyComponent;

    @Resource
    private UserFeignManager userFeignManager;

    @Override
    public Object execute(ButtonEventParam buttonEventParam) {
        Long uid = buttonEventParam.getBaseParam().getUid();
        String bizKey = buttonEventParam.getBizKey();
        if (CharSequenceUtil.isBlank(bizKey)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "助力用户不能为空");
        }
        Long helpUid = Long.valueOf(bizKey);
        Long helpCount = goddessPageantRedisManager.getHelpCount(uid, helpUid);
        if (helpCount > 10) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "今天为该女神助力已经10次了哦～换个女神助力吧～");
        }
        Integer supportHornCount = goddessPageantRedisManager.getSupportHornCount(uid);
        if (supportHornCount <= 0) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "助力呐喊不足哦～快去获取助力呐喊吧～");
        }
        goddessPageantRedisManager.decrementSupportHornCount(uid, GoddessPageantConstant.HELP_COUNT);
        Long helpValue = GoddessPageantConstant.HELP_COUNT * GoddessPageantConstant.HELP_VALUE;

        // 助力女生排行榜统计
        incrRankForUser(helpUid, helpValue, GoddessPageantConstant.WOMAN_DAILY_RANK_CODE, GoddessPageantConstant.WOMAN_TOTAL_RANK_CODE);

        // 助力男生排行榜统计
        UserVO userVO = userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), uid);
        if (SexType.MAN.getCode().equals(userVO.getSex().getCode())) {
            incrRankForUser(uid, helpValue, GoddessPageantConstant.MAN_DAILY_RANK_CODE, GoddessPageantConstant.MAN_TOTAL_RANK_CODE);
        }


        return Boolean.TRUE;
    }

    private void incrRankForUser(Long uid, Long helpValue, String dailyRankCode, String totalRankCode) {
        goddessPageantRankManager.incrRankValue(
                uid,
                helpValue,
                goddessPageantRedisManager.getDailyRankKey(dailyRankCode));
        goddessPageantRankManager.incrRankValue(
                uid,
                helpValue,
                goddessPageantRedisManager.getTotalRankKey(totalRankCode));
    }


}
