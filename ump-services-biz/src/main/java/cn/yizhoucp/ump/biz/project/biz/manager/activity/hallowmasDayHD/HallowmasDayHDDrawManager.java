package cn.yizhoucp.ump.biz.project.biz.manager.activity.hallowmasDayHD;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.jimu.draw.inner.DrawLogItem;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw.inner.PrizeTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.log.LogComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.AbstractDrawTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawContext;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawLogParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.doDrawStrategy.common.ProbStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.biz.remoteService.lanling.FeignLanlingService;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawLogDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.manager.activity.hallowmasDayHD.HallowmasDayHDConstant.*;

/**
 * 2024万圣节抽奖管理
 */
@Service
@Slf4j
public class HallowmasDayHDDrawManager extends AbstractDrawTemplate {

    @Resource
    private HallowmasDayHDConstant hallowmasDayHDConstant;

    @Resource
    private RedisManager redisManager;

    @Resource
    private ProbStrategy probStrategy;

    @Resource
    private HallowmasDayHDTrackManager trackManager;

    @Resource
    private LogComponent logComponent;

    @Override
    protected void resourceCheck(DrawContext context) {
        DrawParam drawParam = context.getDrawParam();
        String poolCode = drawParam.getPoolCode();
        Long uid = drawParam.getUid();
        if (GIFT_BOX.contains(poolCode)) {
            drawParam.setNoSendPrize(Boolean.TRUE);
            return;
        }

        // 校验奖池
        if (Objects.isNull(poolCode) || !HD_POLL.equals(poolCode) || Objects.isNull(uid)) {
            log.warn("Exception prize pool poolCode");
            throw new ServiceException(ErrorCode.INVALID_PARAM, "奖池错误!");
        }

        // 检查是否足够
        Long candyCount = hallowmasDayHDConstant.getCandyCount(uid);
        if (candyCount < 10) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "当前糖果不足哦～");
        }
    }

    @Override
    protected void deductResource(DrawContext context) {
        DrawParam drawParam = context.getDrawParam();
        String poolCode = drawParam.getPoolCode();
        if (GIFT_BOX.contains(poolCode)) {
            return;
        }
        redisManager.decrLong(String.format(USER_CANDY_COUNT_KEY, drawParam.getUid()), 10L, DateUtil.ONE_MONTH_SECOND);
    }


    @Override
    protected void draw(DrawContext context) {
        List<DrawPoolItemDTO> drawPoolItemDTOList = probStrategy.getDrawPoolItems(context.getDrawPoolItemDOS(),
                context.getDrawParam().getTimes(),
                Boolean.TRUE);
        context.setPrizeItemList(drawPoolItemDTOList);
    }


    @Override
    protected void doCallback(DrawContext context) {
        DrawParam drawParam = context.getDrawParam();
        if (GIFT_BOX.contains(drawParam.getPoolCode())) {
            return;
        }
        // 抽奖结果留存
        logComponent.putDrawLog(drawParam.getBaseParam(), drawParam.getActivityCode(), context.getPrizeItemList());

        // 用户在活动中抽奖开奖时 埋点
        for (DrawPoolItemDTO drawPoolItemDTO : context.getPrizeItemList()) {
            DrawPoolItemDO drawPoolItemDO = drawPoolItemDTO.getDrawPoolItemDO();
            Long awardAmount = drawPoolItemDO.getItemValueGold() * drawPoolItemDTO.getTargetTimes();
            // 一次抽奖多个参数分别传递
            if (Objects.nonNull(drawPoolItemDO.getItemType()) && PrizeTypeEnum.PRIZE_DRESS.getCode().equals(drawPoolItemDO.getItemType())) {
                // 头像框埋点价值处理
                awardAmount = 0L;
            }
            trackManager.allActivityLottery(drawParam.getUid(), drawPoolItemDO.getItemKey(), awardAmount, drawPoolItemDTO.getTargetTimes());
        }
    }

    @Override
    protected List<DrawLogItem> drawLogWrapper(DrawLogParam param, List<DrawLogDO> drawLogDOList) {
        List<DrawLogItem> drawLogItemList = drawLogDOList.stream().map(drawLogDO -> {
            DrawPoolItemDTO drawPoolItemDTO = JSON.parseObject(drawLogDO.getLogJson(), DrawPoolItemDTO.class);
            DrawPoolItemDO drawPoolItemDO = drawPoolItemDTO.getDrawPoolItemDO();
            return DrawLogItem.builder()
                    .icon(drawPoolItemDO.getItemIcon())
                    .valueGold(drawPoolItemDO.getItemValueGold())
                    .text(drawPoolItemDO.getItemName())
                    .itemNum(drawPoolItemDO.getItemNum() * drawPoolItemDTO.getTargetTimes())
                    .time(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm").format(drawLogDO.getCreateTime())).build();
        }).collect(Collectors.toList());
        log.debug("drawLogItemList {}", JSON.toJSONString(drawLogItemList));
        Collections.reverse(drawLogItemList);

        return drawLogItemList;
    }

}
