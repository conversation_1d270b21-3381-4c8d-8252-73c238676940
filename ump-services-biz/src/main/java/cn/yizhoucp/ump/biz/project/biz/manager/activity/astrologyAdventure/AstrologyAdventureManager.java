package cn.yizhoucp.ump.biz.project.biz.manager.activity.astrologyAdventure;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.config.ThreadPoolConfig;
import cn.yizhoucp.ms.core.base.enums.ServicesNameEnum;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.starter.cassandra.base.kafka.YzKafkaProducerManager;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.activity.astrologyAdventure.IndexVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.DrawReturn;
import cn.yizhoucp.ump.api.vo.jimu.draw.PrizeItem;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.event.astrologydraw.AstrologyDrawEvent;
import cn.yizhoucp.ump.biz.project.biz.event.astrologydraw.AstrologyDrawMessage;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.AbstractActivityManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.doDrawStrategy.common.ProbStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.IndexManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ActivityDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.dal.service.ActivityService;
import cn.yizhoucp.ump.biz.project.dal.service.DrawPoolItemService;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AstrologyAdventureManager extends AbstractActivityManager implements IndexManager {

    /** yyyyMMdd */
    public static final String STARGOD_BOARD = "ump:astrology-adventure:stargod-board:%s";
    /** yyyyMMdd:userId */
    public static final String ASTROLOGY_TIMES = "ump:astrology-adventure:astrology-time:%s:%s";
    public static final String STARGOD_POOL_CODE = "ASTROLOGY_STARGOD";
    public static final String STARGOD1_POOL_CODE = "ASTROLOGY_STARGOD1";
    public static final String STARLIGHT_POOL_CODE = "ASTROLOGY_STARLIGHT";
    public static final String STARSECRET_POOL_CODE = "ASTROLOGY_STARSECRET";
    /** poolCode:yyyyMMdd:userId */
    public static final String XBX_GIFT_OPEN = "ump:astrology-adventure:open:%s:%s:%s";
    @Resource
    private RedisManager redisManager;
    @Resource
    private AstrologyAdventureRankManager astrologyAdventureRankManager;
    @Resource
    private ProbStrategy probStrategy;
    @Resource
    private DrawPoolItemService drawPoolItemService;
    @Resource
    private ActivityService activityService;
    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private NotifyComponent notifyComponent;
    @Resource
    private YzKafkaProducerManager yzKafkaProducerManager;

    @Override
    public String getActivityCode() {
        return "astrology-adventure";
    }

    @Override
    public IndexVO getIndex(BaseParam param, Long toUid, String extData) {
        String date = getDate();
        return IndexVO.builder()
                .astrologyTimes(Optional.ofNullable(redisManager.getLong(String.format(ASTROLOGY_TIMES, date, param.getUid()))).orElse(0L))
                .xmbxGiftOpen(Boolean.TRUE.equals(redisManager.hasKey(String.format(XBX_GIFT_OPEN, "XMBX_GIFT", date, param.getUid()))))
                .xybxGiftOpen(Boolean.TRUE.equals(redisManager.hasKey(String.format(XBX_GIFT_OPEN, "XYBX_GIFT", date, param.getUid()))))
                .xsbx1GiftOpen(Boolean.TRUE.equals(redisManager.hasKey(String.format(XBX_GIFT_OPEN, "XSBX1_GIFT", date, param.getUid()))))
                .xsbxGiftOpen(Boolean.TRUE.equals(redisManager.hasKey(String.format(XBX_GIFT_OPEN, "XSBX_GIFT", date, param.getUid()))))
                .stargodBoard(astrologyAdventureRankManager.getRank(RankContext.builder().rankKey(String.format(STARGOD_BOARD, date)).type(RankContext.RankType.user).param(param).build()))
                .build();
    }

    @Override
    public String getTemplateType() {
        return null;
    }

//    @EventListener
    @Async(ThreadPoolConfig.ASYNC_EXECUTOR_NAME)
    @ActivityCheck(activityCode = "astrology-adventure", isThrowException = false)
    public void drawHandler(AstrologyDrawEvent event) {
        AstrologyDrawMessage source = event.getSource();
        log.info("source {}", JSON.toJSONString(source));
        if (!Objects.equals(source.getDrawTimes(), 1) && !Objects.equals(source.getDrawTimes(), 10) && !Objects.equals(source.getDrawTimes(), 100)) {
            log.debug("抽奖次数不对");
            return;
        }

        String date = getDate();

        Integer times = source.getDrawTimes();
        // 占星次数增加
        Long newTimes = redisManager.incrLong(String.format(ASTROLOGY_TIMES, date, source.getUid()), source.getConsumeTimes(), DateUtil.ONE_MONTH_SECOND);
        if (newTimes >= 5000 && newTimes - times < 5000) {
            Map<String, Object> params = new HashMap<>();
            params.put("activity_type", "astrology_journey_activity");
            params.put("activity_attribute", "astrology_activity");
            params.put("task_type", "star_god_stage_2_task");
            yzKafkaProducerManager.dataRangerTrack(1L, source.getUid(), "all_activity_task_finish", params, ServicesNameEnum.ump_services.getCode());
        }
        if (newTimes >= 2000 && newTimes - times < 2000) {
            Map<String, Object> params = new HashMap<>();
            params.put("activity_type", "astrology_journey_activity");
            params.put("activity_attribute", "astrology_activity");
            params.put("task_type", "star_god_stage_1_task");
            yzKafkaProducerManager.dataRangerTrack(1L, source.getUid(), "all_activity_task_finish", params, ServicesNameEnum.ump_services.getCode());
        }
        if (newTimes >= 100 && newTimes - times < 100) {
            Map<String, Object> params = new HashMap<>();
            params.put("activity_type", "astrology_journey_activity");
            params.put("activity_attribute", "astrology_activity");
            params.put("task_type", "starburst_pool");
            yzKafkaProducerManager.dataRangerTrack(1L, source.getUid(), "all_activity_task_finish", params, ServicesNameEnum.ump_services.getCode());
        }
        if (newTimes >= 30 && newTimes - times < 30) {
            Map<String, Object> params = new HashMap<>();
            params.put("activity_type", "astrology_journey_activity");
            params.put("activity_attribute", "astrology_activity");
            params.put("task_type", "star_secret_pool");
            yzKafkaProducerManager.dataRangerTrack(1L, source.getUid(), "all_activity_task_finish", params, ServicesNameEnum.ump_services.getCode());
        }
        // 星神榜单增加
        if (newTimes >= 2000 && redisManager.hasKey(String.format(AstrologyAdventureManager.XBX_GIFT_OPEN, "XSBX1_GIFT", date, source.getUid()))) {
            astrologyAdventureRankManager.incrRankValue(source.getUid(), Long.valueOf(source.getConsumeTimes()), String.format(STARGOD_BOARD, date));
        }
/*
        if (STARGOD_POOL_CODE.equals(this.getDrawPool(source.getUid()))) {
            astrologyAdventureRankManager.incrRankValue(source.getUid(), Long.valueOf(source.getConsumeTimes()), String.format(STARGOD_BOARD, date));
        }
*/
    }

    public DrawReturn openBox(Long userId, String poolCode) {
        if (userId == null || (!"XMBX_GIFT".equals(poolCode) && !"XYBX_GIFT".equals(poolCode) && !"XSBX1_GIFT".equals(poolCode) && !"XSBX_GIFT".equals(poolCode))) {
            throw new ServiceException(ErrorCode.MISS_PARAM, "当前占星次数不够哦~");
        }

        // 校验占星次数
        String date = getDate();
        long astrologyTimes = Optional.ofNullable(redisManager.getLong(String.format(ASTROLOGY_TIMES, date, userId))).orElse(0L);
        if (("XMBX_GIFT".equals(poolCode) && astrologyTimes < 30)
                || ("XYBX_GIFT".equals(poolCode) && astrologyTimes < 100)
                || ("XSBX1_GIFT".equals(poolCode) && astrologyTimes < 2000)
                || ("XSBX_GIFT".equals(poolCode) && astrologyTimes < 5000)) {
            throw new ServiceException(ErrorCode.MISS_PARAM, "当前占星次数不够哦~");
        }
        // 校验是否开启
        if (!Boolean.TRUE.equals(redisManager.setnx(String.format(XBX_GIFT_OPEN, poolCode, date, userId), System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND))) {
            throw new ServiceException(ErrorCode.MISS_PARAM, "宝箱开启成功~");
        }
        // 概率抽奖
        List<DrawPoolItemDO> drawPoolItemDOList = drawPoolItemService.getByPoolCode(poolCode);
        List<DrawPoolItemDTO> drawPoolItems = probStrategy.getDrawPoolItems(drawPoolItemDOList, 1, Boolean.TRUE);
        List<PrizeItem> prizeItemList = drawPoolItems.stream().map(DrawPoolItemDTO::convert2PrizeItem).collect(Collectors.toList());

        BaseParam param = BaseParam.ofMDC();
        String boxName = "星秘宝箱";
        if ("XYBX_GIFT".equals(poolCode)) {
            boxName = "星曜宝箱";
        } else if ("XSBX_GIFT".equals(poolCode) || "XSBX1_GIFT".equals(poolCode)) {
            boxName = "星神宝箱";
        }
        sendPrizeManager.sendPrize(param, prizeItemList.stream().map(p -> SendPrizeDTO.of(p, this.getActivityCode())).collect(Collectors.toList()));
        notifyComponent.npcNotify(param.getUnionId(), param.getUid(), String.format("恭喜您在“占星奇旅”活动中成功开启一次【%s】并获得%s金币礼物“%s”，礼物已发放至您的背包，请注意查收哦~", boxName, prizeItemList.get(0).getValueGold(), prizeItemList.get(0).getPrizeName()));

        Map<String, Object> params = new HashMap<>();
        params.put("activity_type", "astrology_journey_activity");
        params.put("activity_attribute", "astrology_activity");
        params.put("award_key", prizeItemList.get(0).getPrizeKey());
        params.put("award_amount", prizeItemList.get(0).getValueGold());
        params.put("pool_code", poolCode);
        yzKafkaProducerManager.dataRangerTrack(param.getAppId(), param.getUid(), "all_activity_receive_award", params, ServicesNameEnum.ump_services.getCode());
        return DrawReturn.builder()
                .hasRealPrize(Boolean.FALSE)
                .prizeItemList(prizeItemList).build();
    }

    public String getDrawPool(Long userId) {
        ActivityDO activityDO = activityService.getByCode(MDCUtil.getCurAppIdByMdc(), MDCUtil.getCurUnionIdByMdc(), this.getActivityCode());
        if (activityDO == null || activityDO.getStatus() == 0) {
            return null;
        }

        // 查询占星次数
        String date = getDate();
        long astrologyTimes = Optional.ofNullable(redisManager.getLong(String.format(ASTROLOGY_TIMES, date, userId))).orElse(0L);
        // 查询对应奖池
        if (astrologyTimes >= 100 && Boolean.TRUE.equals(redisManager.hasKey(String.format(XBX_GIFT_OPEN, "XYBX_GIFT", date, userId)))) {
            return STARGOD_POOL_CODE;
        }
        if (astrologyTimes >= 30 && Boolean.TRUE.equals(redisManager.hasKey(String.format(XBX_GIFT_OPEN, "XMBX_GIFT", date, userId)))) {
            return STARLIGHT_POOL_CODE;
        }

        return STARSECRET_POOL_CODE;
    }

    public String getDrawPool1(Long userId) {
        ActivityDO activityDO = activityService.getByCode(MDCUtil.getCurAppIdByMdc(), MDCUtil.getCurUnionIdByMdc(), this.getActivityCode());
        if (activityDO.getStatus() == 0) {
            return null;
        }

        // 查询占星次数
        String date = getDate();
        long astrologyTimes = Optional.ofNullable(redisManager.getLong(String.format(ASTROLOGY_TIMES, date, userId))).orElse(0L);
        // 查询对应奖池
        if (astrologyTimes >= 2000 && Boolean.TRUE.equals(redisManager.hasKey(String.format(XBX_GIFT_OPEN, "XSBX1_GIFT", date, userId)))) {
            return STARGOD_POOL_CODE;
        }
        if (astrologyTimes >= 100 && Boolean.TRUE.equals(redisManager.hasKey(String.format(XBX_GIFT_OPEN, "XYBX_GIFT", date, userId)))) {
            return STARGOD1_POOL_CODE;
        }
        if (astrologyTimes >= 30 && Boolean.TRUE.equals(redisManager.hasKey(String.format(XBX_GIFT_OPEN, "XMBX_GIFT", date, userId)))) {
            return STARLIGHT_POOL_CODE;
        }

        return STARSECRET_POOL_CODE;
    }

    public Boolean canOpenBox(Long userId) {
        ActivityDO activityDO = activityService.getByCode(MDCUtil.getCurAppIdByMdc(), MDCUtil.getCurUnionIdByMdc(), this.getActivityCode());
        if (activityDO.getStatus() == 0) {
            return false;
        }

        // 查询占星次数
        String date = getDate();
        long astrologyTimes = Optional.ofNullable(redisManager.getLong(String.format(ASTROLOGY_TIMES, date, userId))).orElse(0L);
        if (astrologyTimes < 30) {
            return false;
        }
        if (Boolean.FALSE.equals(redisManager.hasKey(String.format(XBX_GIFT_OPEN, "XMBX_GIFT", date, userId)))) {
            return true;
        }
        if (astrologyTimes < 100) {
            return false;
        }
        if (Boolean.FALSE.equals(redisManager.hasKey(String.format(XBX_GIFT_OPEN, "XYBX_GIFT", date, userId)))) {
            return true;
        }
        if (astrologyTimes < 2000) {
            return false;
        }
        if (Boolean.FALSE.equals(redisManager.hasKey(String.format(XBX_GIFT_OPEN, "XSBX1_GIFT", date, userId)))) {
            return true;
        }
        if (astrologyTimes < 5000) {
            return false;
        }
        if (Boolean.FALSE.equals(redisManager.hasKey(String.format(XBX_GIFT_OPEN, "XSBX_GIFT", date, userId)))) {
            return true;
        }

        return false;
    }

    public String getDate() {
        LocalDateTime localDateTime = LocalDateTime.now();
        int hour = localDateTime.getHour();
        String date = null;
        if (hour < 2) {
            date = DateUtil.format(DateUtil.getBeforeDay(new Date()), DateUtil.YMD_WITHOUT_LINE);
        } else {
            date = DateUtil.format(new Date(), DateUtil.YMD_WITHOUT_LINE);
        }

        return date;
    }

}
