package cn.yizhoucp.ump.biz.project.biz.manager.activity.loveInBloomFestival;

import cn.hutool.core.collection.CollUtil;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.vo.jimu.rank.CpRankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.loveInBloomFestival.common.LoveInBloomFestivalConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.loveInBloomFestival.common.LoveInBloomFestivalRedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.moonAboveTheSea.common.MoonAboveTheSeaConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.AbstractRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dal.mybatis.domain.ActivityMessageTemplates;
import cn.yizhoucp.ump.biz.project.dal.mybatis.service.ActivityMessageTemplatesService;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 2025情人节排行榜类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 11:58 2025/2/6
 */
@Slf4j
@Service
public class LoveInBloomFestivalRankManager extends AbstractRankManager {

    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    private LoveInBloomFestivalRedisManager loveInBloomFestivalRedisManager;
    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private NotifyComponent notifyComponent;
    @Resource
    private ActivityMessageTemplatesService activityMessageTemplatesService;
    @Resource
    private LoveInBloomFestivalTrackManager loveInBloomFestivalTrackManager;

    @Override
    protected void postProcess(RankContext rankContext) {

    }

    @Override
    protected void doPreProcess(RankContext rankContext) {

    }

    public Boolean sendRankPrize() {

        List<ScenePrizeDO> manPrizeDOList = scenePrizeJpaDAO.getListBySceneCode(1L, LoveInBloomFestivalConstant.ACTIVITY_CODE, "man_rank");
        List<ScenePrizeDO> womanPrizeDOList = scenePrizeJpaDAO.getListBySceneCode(1L, LoveInBloomFestivalConstant.ACTIVITY_CODE, "woman_rank");
        log.info("scenePrizeDOList man: {} woman: {}", JSON.toJSONString(manPrizeDOList), JSON.toJSONString(womanPrizeDOList));
        if (CollectionUtils.isEmpty(manPrizeDOList) || CollectionUtils.isEmpty(womanPrizeDOList)) {
            return Boolean.FALSE;
        }

        RankVO rankVO = this.getRank(RankContext.builder()
                .activityCode(LoveInBloomFestivalConstant.ACTIVITY_CODE)
                .rankKey(loveInBloomFestivalRedisManager.getRankKey())
                .rankLen(10L)
                .type(RankContext.RankType.cp)
                .build());
        log.info("rankVO {}", rankVO);
        if (rankVO == null) {
            return Boolean.FALSE;
        }

        List<CpRankItem> rankList = rankVO.getCpRankItemList();
        if (CollectionUtils.isEmpty(rankList)) {
            return Boolean.FALSE;
        }

        for (CpRankItem cpRankItem : rankList) {
            Long rank = cpRankItem.getRank();
            Long value = cpRankItem.getValue();
            if (value < MoonAboveTheSeaConstant.MIN_RANK_PRIZE_VALUE) {
                log.info("cpRank:{} 小于最小奖励值 {}", cpRankItem, MoonAboveTheSeaConstant.MIN_RANK_PRIZE_VALUE);
                continue;
            }
            List<ScenePrizeDO> manScenePrizeDOs = manPrizeDOList.stream().filter(scenePrizeDO -> (Objects.equals(rank, scenePrizeDO.getPrizeBelongToRank()))).collect(Collectors.toList());
            List<ScenePrizeDO> womanScenePrizeDOs = womanPrizeDOList.stream().filter(scenePrizeDO -> (Objects.equals(rank, scenePrizeDO.getPrizeBelongToRank()))).collect(Collectors.toList());
            log.info("MainChineseNewYear2025RankManager#sendPrize cpRankItem {}", cpRankItem);
            if (CollUtil.isEmpty(manScenePrizeDOs) || CollUtil.isEmpty(womanScenePrizeDOs)) {
                log.info("maleUid {} femaleUid {} rank {} 没有奖励", cpRankItem.getMaleUid(), cpRankItem.getFemaleUid(), rank);
                continue;
            }
            sendPrizeManager.sendPrize(
                    BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(cpRankItem.getMaleUid()).build(),
                    manScenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, cpRankItem.getMaleUid())).collect(Collectors.toList())
            );
            sendPrizeManager.sendPrize(
                    BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(cpRankItem.getFemaleUid()).build(),
                    womanScenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, cpRankItem.getFemaleUid())).collect(Collectors.toList())
            );
            ActivityMessageTemplates activityMessageTemplates = activityMessageTemplatesService.findByActivityCodeAndSceneCode(LoveInBloomFestivalConstant.ACTIVITY_CODE, LoveInBloomFestivalConstant.MESSAGE_TEMPLATE_SCENE_CODE);
            if (activityMessageTemplates != null) {
                String msg = activityMessageTemplates.getMessageTemplate();
                notifyComponent.npcNotify(
                        ServicesAppIdEnum.lanling.getUnionId(),
                        cpRankItem.getMaleUid(),
                        String.format(msg, cpRankItem.getRank())
                );
                notifyComponent.npcNotify(
                        ServicesAppIdEnum.lanling.getUnionId(),
                        cpRankItem.getFemaleUid(),
                        String.format(msg, cpRankItem.getRank())
                );
            }
            for (ScenePrizeDO scenePrizeDO : manScenePrizeDOs) {
                loveInBloomFestivalTrackManager.allActivityReceiveAward("rank_of_heart", scenePrizeDO.getPrizeValue(), scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeNum(), cpRankItem.getMaleUid());
            }
            for (ScenePrizeDO scenePrizeDO : womanPrizeDOList) {
                loveInBloomFestivalTrackManager.allActivityReceiveAward("rank_of_heart", scenePrizeDO.getPrizeValue(), scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeNum(), cpRankItem.getFemaleUid());
            }


        }

        return Boolean.TRUE;
    }


}
