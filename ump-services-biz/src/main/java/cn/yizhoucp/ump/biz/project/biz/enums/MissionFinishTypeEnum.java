package cn.yizhoucp.ump.biz.project.biz.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Getter
@AllArgsConstructor
public enum MissionFinishTypeEnum {

    SEND("send", "送"),
    RECEIVE("receive", "收"),
    SEND_OR_RECEIVE("send_or_receive", "送或收"),
    DRAW("draw", "抽"),
    GIFT_UNIT_PRICE("gift_unit_price", "礼物单价"),
    GIFT_TOTAL_PRICE("gift_total_price", "礼物总价"),
    ;
    private String code;
    private String desc;

    private static final Map<String, MissionFinishTypeEnum> instanceMap = new ConcurrentHashMap<>(8);

    static {
        for (MissionFinishTypeEnum item : values()) {
            instanceMap.put(item.getCode(), item);
        }
    }

    public static MissionFinishTypeEnum getInstance(String code) {
        return instanceMap.get(code);
    }

}
