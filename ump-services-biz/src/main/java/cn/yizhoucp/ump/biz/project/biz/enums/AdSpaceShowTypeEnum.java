package cn.yizhoucp.ump.biz.project.biz.enums;

import cn.yizhoucp.ms.core.base.jpa.IBaseJpaEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 广告位展示类型
 * <AUTHOR>
 */
@Deprecated
public enum AdSpaceShowTypeEnum implements IBaseJpaEnum {

  /** 按配置时间段展示 */
  normal(0,"normal", "普通，按配置时间段展示"),
  /** 按小时周期展示 */
  cycle_hour(1,"cycle_hour", "按小时周期展示"),
  ;

  private Integer index;
  private String code;

  private String desc;

  AdSpaceShowTypeEnum(String code, String desc) {
    this.code = code;
    this.desc = desc;
  }

  AdSpaceShowTypeEnum(Integer index, String code, String desc) {
    this.index = index;
    this.code = code;
    this.desc = desc;
  }

  /**
   * 根据 code 获取枚举
   * @param code  code
   * @return  AdSpaceTypeEnum
   */
  public static AdSpaceShowTypeEnum findByCode(String code) {
    if (StringUtils.isBlank(code)) {
      return null;
    }
    for (AdSpaceShowTypeEnum type : values()) {
      if (StringUtils.equals(type.getCode(), code)) {
        return type;
      }
    }
    return null;
  }

  /**
   * 获取所有枚举键值对
   * @return  List<Map<String, String>>
   */
  public static List<Map<String, String>> getList() {
    List<Map<String, String>> result = new ArrayList<>();
    Map<String, String> map = new HashMap<>();
    for (AdSpaceShowTypeEnum type : values()) {
      map.put("code", type.getCode());
      map.put("desc", type.getDesc());
    }
    result.add(map);
    return result;
  }

  public String getCode() {
    return code;
  }

  public String getDesc() {
    return desc;
  }

  public Integer getIndex() {
    return index;
  }

  @Override
  public Integer getIdx() {
    return this.index;
  }

}
