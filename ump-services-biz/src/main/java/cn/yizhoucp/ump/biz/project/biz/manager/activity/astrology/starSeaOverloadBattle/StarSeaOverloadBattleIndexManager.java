package cn.yizhoucp.ump.biz.project.biz.manager.activity.astrology.starSeaOverloadBattle;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.activity.astrology.starSeaOverloadBattle.IndexVO;
import cn.yizhoucp.ump.api.vo.activity.astrology.starSeaOverloadBattle.OverloadVO;
import cn.yizhoucp.ump.api.vo.jimu.task.TaskItem;
import cn.yizhoucp.ump.api.vo.jimu.task.TaskVO;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaOverloadBattleConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.doDrawStrategy.common.ProbStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.IndexManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityInfoUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ActivityDO;
import cn.yizhoucp.ump.biz.project.dal.service.DrawPoolItemService;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Optional;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaOverloadBattleConstant.ACTIVITY_CODE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaOverloadBattleConstant.ASTROLOGY_TIMES;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaOverloadBattleConstant.BOX_OPEN;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaOverloadBattleConstant.COUNTRY_TASK_CUR_FINISH_TIMES;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaOverloadBattleConstant.OVERLOAD;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaOverloadBattleConstant.OVERLOAD_RANK;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaOverloadBattleConstant.STAR_SEA_RANK;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaOverloadBattleConstant.STAR_SEA_RANK_OVERALL1;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaOverloadBattleConstant.STAR_SEA_RANK_OVERALL2;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaOverloadBattleConstant.TASK_CUR_FINISH_TIMES;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaOverloadBattleConstant.TASK_TAKE_PRIZE;

@Service
@Slf4j
public class StarSeaOverloadBattleIndexManager implements IndexManager {

    @Resource
    private RedisManager redisManager;
    @Resource
    private ProbStrategy probStrategy;
    @Resource
    private DrawPoolItemService drawPoolItemService;
    @Resource
    private StarSeaOverloadBattleBizManager starSeaOverloadBattleBizManager;
    @Resource
    private StarSeaOverloadBattleRankManager starSeaOverloadBattleRankManager;

    @Override
    public IndexVO getIndex(BaseParam param, Long toUid, String extData) {
        ActivityDO activityDO = ActivityInfoUtil.get();
        if (activityDO == null || activityDO.getStatus() == null || activityDO.getStatus() <= 0) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "活动已结束哦～");
        }

        IndexVO indexVO = new IndexVO();

        indexVO.setOpenStatus(Lists.newArrayListWithCapacity(StarSeaOverloadBattleConstant.Box.values().length));
        for (StarSeaOverloadBattleConstant.Box box : StarSeaOverloadBattleConstant.Box.values()) {
            indexVO.getOpenStatus().add(Boolean.TRUE.equals(redisManager.hasKey(String.format(BOX_OPEN, param.getUid(), starSeaOverloadBattleBizManager.getDate(), box.name()))));
        }

        indexVO.setAstrologyTimesToday(Optional.ofNullable(redisManager.getInteger(String.format(ASTROLOGY_TIMES, param.getUid(), starSeaOverloadBattleBizManager.getDate()))).orElse(0));

        indexVO.setTask(TaskVO.builder().taskItemList(Lists.newArrayListWithCapacity(6)).build());
        for (StarSeaOverloadBattleConstant.Task task : StarSeaOverloadBattleConstant.Task.values()) {
            TaskItem taskItem = new TaskItem();
            taskItem.setTaskCode(task.name());
            taskItem.setCurFinishTimes(Math.min(task.getItemNum(), Optional.ofNullable(redisManager.getInteger(String.format(TASK_CUR_FINISH_TIMES, param.getUid(), task.name()))).orElse(0)));
            int buttonStatus;
            if (Boolean.TRUE.equals(redisManager.hasKey(String.format(TASK_TAKE_PRIZE, param.getUid(), taskItem.getTaskCode())))) {
                buttonStatus = -1;
            } else if (taskItem.getCurFinishTimes() < task.getItemNum()) {
                buttonStatus = 0;
            } else {
                buttonStatus = 1;
            }
            taskItem.setButtonStatus(buttonStatus);
            indexVO.getTask().getTaskItemList().add(taskItem);
        }

        LocalDateTime startTime = Optional.ofNullable(activityDO.getStartTime()).orElse(LocalDateTime.now()).truncatedTo(ChronoUnit.DAYS);
        LocalDateTime now = LocalDateTime.now();
        long between = ChronoUnit.DAYS.between(startTime, now);

        indexVO.setPhaseOneTask(TaskVO.builder().taskItemList(new ArrayList<>(7)).build());
        indexVO.setPhaseTwoTask(TaskVO.builder().taskItemList(new ArrayList<>(7)).build());
        for (int i = 0; i < StarSeaOverloadBattleConstant.CountryTask.values().length - 1; i++) {
            StarSeaOverloadBattleConstant.CountryTask countryTask = StarSeaOverloadBattleConstant.CountryTask.values()[i];
            TaskItem taskItem = new TaskItem();
            taskItem.setCurFinishTimes(Math.min(countryTask.getTargetValue(), Optional.ofNullable(redisManager.getInteger(String.format(COUNTRY_TASK_CUR_FINISH_TIMES, countryTask.name()))).orElse(0)));
            if (i < between) {
                taskItem.setButtonStatus(taskItem.getCurFinishTimes() >= countryTask.getTargetValue() ? 1 : -2);
            } else if (i == between) {
                taskItem.setButtonStatus(taskItem.getCurFinishTimes() >= countryTask.getTargetValue() ? 1 : 0);
            } else {
                taskItem.setButtonStatus(-1);
            }

            if (i < 7) {
                indexVO.getPhaseOneTask().getTaskItemList().add(taskItem);
            } else {
                indexVO.getPhaseTwoTask().getTaskItemList().add(taskItem);
            }
        }

        indexVO.setCurPhase(between < 7 ? 0 : 1);

        String date = starSeaOverloadBattleBizManager.getDate();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd HH:mm:ss");
        LocalDateTime parse = LocalDateTime.parse(date + " 00:00:00", formatter);
        between = ChronoUnit.DAYS.between(activityDO.getStartTime().truncatedTo(ChronoUnit.DAYS), parse);
        log.debug("startTime {} parse {}", startTime, parse);
        if (between < 4) {
            indexVO.setCurRank(0);
            indexVO.setStarSeaRank(starSeaOverloadBattleRankManager.getRank(
                    RankContext.builder()
                            .activityCode(ACTIVITY_CODE)
                            .rankKey(String.format(STAR_SEA_RANK, date))
                            .type(RankContext.RankType.user)
                            .param(param)
                            .build()));
        } else if (between < 7) {
            indexVO.setCurRank(1);
            indexVO.setStarSeaRank(starSeaOverloadBattleRankManager.getRank(
                    RankContext.builder()
                            .activityCode(ACTIVITY_CODE)
                            .rankKey(STAR_SEA_RANK_OVERALL2)
                            .type(RankContext.RankType.user)
                            .param(param)
                            .build()));
        } else if (between < 11) {
            indexVO.setCurRank(2);
            indexVO.setStarSeaRank(starSeaOverloadBattleRankManager.getRank(
                    RankContext.builder()
                            .activityCode(ACTIVITY_CODE)
                            .rankKey(String.format(STAR_SEA_RANK, date))
                            .type(RankContext.RankType.user)
                            .param(param)
                            .build()));
        } else {
            indexVO.setCurRank(3);
            indexVO.setStarSeaRank(starSeaOverloadBattleRankManager.getRank(
                    RankContext.builder()
                            .activityCode(ACTIVITY_CODE)
                            .rankKey(STAR_SEA_RANK_OVERALL1)
                            .type(RankContext.RankType.user)
                            .param(param)
                            .build()));
        }

        indexVO.setOverloadRank(starSeaOverloadBattleRankManager.getRank(
                RankContext.builder()
                        .activityCode(ACTIVITY_CODE)
                        .rankKey(OVERLOAD_RANK)
                        .type(RankContext.RankType.user)
                        .param(param)
                        .build()));
        Object overload = redisManager.get(OVERLOAD);
        if (overload != null) {
            OverloadVO overloadVO = JSON.parseObject(String.valueOf(overload), OverloadVO.class);
            indexVO.setOverload(overloadVO);
        }

        return indexVO;
    }

    @Override
    public String getActivityCode() {
        return ACTIVITY_CODE;
    }

    @Override
    public String getTemplateType() {
        return null;
    }

}
