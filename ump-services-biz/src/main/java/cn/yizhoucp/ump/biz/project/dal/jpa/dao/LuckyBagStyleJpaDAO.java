package cn.yizhoucp.ump.biz.project.dal.jpa.dao;

import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.LuckyBagStyleDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * 福袋样式
 *
 * @author: lianghu
 */
@Repository
public interface LuckyBagStyleJpaDAO extends JpaRepository<LuckyBagStyleDO, Long> {
    List<LuckyBagStyleDO> findByIdIn(List<Long> ids);
}
