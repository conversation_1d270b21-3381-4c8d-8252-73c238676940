package cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.strategy;

import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.common.EmotionalBondsEnums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.common.EmotionalBondsRedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.vo.PendingBondsVO;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import cn.yizhoucp.ump.biz.project.dal.mybatis.domain.EmotionalBondSummaryDO;
import cn.yizhoucp.ump.biz.project.dal.mybatis.service.EmotionalBondRelationshipService;
import cn.yizhoucp.ump.biz.project.dal.mybatis.service.EmotionalBondSummaryService;
import cn.yizhoucp.user.manager.UserFeignManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class EmotionalBondMyBondList implements ExecutableStrategy {

    @Resource
    private EmotionalBondRelationshipService emotionalBondRelationshipService;

    @Resource
    private EmotionalBondSummaryService emotionalBondSummaryService;

    @Resource
    private UserFeignManager userFeignManager;

    @Resource
    private EmotionalBondsRedisManager emotionalBondsRedisManager;

    @Override
    public Object execute(ButtonEventParam buttonEventParam) {
        Long userId = buttonEventParam.getBaseParam().getUid();
        log.info("Getting bonded relationships for user: {}", userId);

        Date currentDate = new Date();

        // 查询合并表中的有效关系
        List<EmotionalBondSummaryDO> summaryRelationships = emotionalBondSummaryService.getActiveRelationships(userId);

        Long currentBond= emotionalBondsRedisManager.getEquippedRelationship(userId);

        if (summaryRelationships == null || summaryRelationships.isEmpty()) {
            log.info("No active non-expired bonded relationships found for user: {}", userId);
            return PendingBondsVO.builder().pendingBonds(new ArrayList<>()).build();
        }

        // 收集所有需要获取的用户ID
        List<Long> userIds = summaryRelationships.stream()
                .flatMap(relationship -> Stream.of(relationship.getUserId(), relationship.getOppositeId()))
                .distinct()
                .collect(Collectors.toList());

        // 获取用户信息
        Map<Long, UserVO> userMap = userIds.stream()
            .map(id -> userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), id))
            .collect(Collectors.toMap(
                UserVO::getId,
                user -> user,
                (existing, replacement) -> existing // 保留第一个值，忽略重复键
            ));

        // 构建响应
        List<PendingBondsVO.BondsVO> bondsVOList = summaryRelationships.stream()
                .map(summary -> {
                    EmotionalBondsEnums.RolePairEnum rolePairEnum = EmotionalBondsEnums.RolePairEnum.getRolePairEnumByName(summary.getEffectKey());

                    // 确定当前用户是主动方还是被动方
                    boolean isInitiator = summary.getUserId().equals(userId);

                    PendingBondsVO.BondUserVO userA = new PendingBondsVO.BondUserVO();
                    userA.setUid(summary.getUserId());
                    UserVO userAInfo = userMap.get(summary.getUserId());
                    if (userAInfo != null) {
                        userA.setName(userAInfo.getName());
                        userA.setAvatar(userAInfo.getAvatar());
                    }
                    userA.setRole(summary.getUserSelectedRole());

                    PendingBondsVO.BondUserVO userB = new PendingBondsVO.BondUserVO();
                    userB.setUid(summary.getOppositeId());
                    UserVO userBInfo = userMap.get(summary.getOppositeId());
                    if (userBInfo != null) {
                        userB.setName(userBInfo.getName());
                        userB.setAvatar(userBInfo.getAvatar());
                    }
                    userB.setRole(summary.getOppositeSelectedRole());

                    return PendingBondsVO.BondsVO.builder()
                            .bondId(summary.getId().toString())
                            .userA(userA)
                            .userB(userB)
                            .build();
                })
                .collect(Collectors.toList());

        log.info("Found {} active non-expired bonded relationships for user: {}", bondsVOList.size(), userId);
        return PendingBondsVO.builder()
                .pendingBonds(bondsVOList)
                .currentBondId(currentBond)
                .build();
    }
}
