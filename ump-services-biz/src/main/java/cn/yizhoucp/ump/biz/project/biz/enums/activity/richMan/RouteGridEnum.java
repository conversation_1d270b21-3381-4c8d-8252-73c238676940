package cn.yizhoucp.ump.biz.project.biz.enums.activity.richMan;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> pepper
 * @Classname DiceTypeEnum
 * @Description 路径的格子类型枚举
 * @Date 2022/5/12 16:37
 */
@Getter
@AllArgsConstructor
public enum RouteGridEnum {
    ZERO(0,"normal","起点",0),
    I(1,"chat","发消息",1),
    II(2,"getMissionList","抽奖",2),
    III(3,"coin","金币",3),
    IV(4,"move","前进2位",6),
    V(5,"move","回到起点",0),
    VI(6,"normal","休息区",6),
    VII(7,"drawCardiac","抽奖",7),
    VIII(8,"chat","发消息",8),
    IX(9,"move","退3位",6),
    X(10,"normal","官宣戒指",10),//也就是啥也没有
    XI(11,"move","回到起点",0),
    XII(12,"normal","奔赴成功",12),//终点
    ;
    //当前位置
    private final Integer index;

    //类型：任务（发消息）、抽奖、抽金币、移动、普通（无动作）
    private final String type;

    //描述
    private final String desc;

    //终点位置
    private final Integer finish;

    public static RouteGridEnum getInstanceByIndex(Integer index){
        for (RouteGridEnum item: RouteGridEnum.values()){
            if (index == item.index){
                return item;
            }
        }
        return null;
    }

    public static String getDefaultType(){
        return ZERO.type;
    }

}
