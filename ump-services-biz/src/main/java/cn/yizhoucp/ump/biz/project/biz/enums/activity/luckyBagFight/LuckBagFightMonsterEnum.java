package cn.yizhoucp.ump.biz.project.biz.enums.activity.luckyBagFight;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @Description 怪兽枚举
 * @date 2022-12-09 16:51
 */
public enum LuckBagFightMonsterEnum {

    /**
     * 怪兽枚举
     *
     */
    DAILY_LEVEL_1(1,"DAILY_LEVEL_1","lv.1巡山小怪（蓝）","daily",1000,"/monster/icon/monsterImage_1.png","/monster/monster_1.png","/monster/monster_lock_1.png","{\"familyCoin\":30,\"crystal\":3000}"),
    DAILY_LEVEL_2(2,"DAILY_LEVEL_2","lv.2巡山小怪（绿）","daily",3000,"/monster/icon/monsterImage_2.png","/monster/monster_2.png","/monster/monster_lock_2.png","{\"familyCoin\":90,\"crystal\":9000}"),
    DAILY_LEVEL_3(3,"DAILY_LEVEL_3","lv.3巡山小怪（紫）","daily",6000,"/monster/icon/monsterImage_3.png","/monster/monster_3.png","/monster/monster_lock_3.png","{\"familyCoin\":180,\"crystal\":18000}"),
    DAILY_LEVEL_4(4,"DAILY_LEVEL_4","lv.4巡山小怪（黄）","daily",15000,"/monster/icon/monsterImage_4.png","/monster/monster_4.png","/monster/monster_lock_4.png","{\"familyCoin\":450,\"crystal\":45000}"),
    DAILY_LEVEL_5(5,"DAILY_LEVEL_5","lv.5巡山小怪（红）","daily",30000,"/monster/icon/monsterImage_5.png","/monster/monster_5.png","/monster/monster_lock_5.png","{\"familyCoin\":900,\"crystal\":90000}"),
    DAILY_LEVEL_6(6,"DAILY_LEVEL_6","lv.6黑山小妖（蓝）","daily",45000,"/monster/icon/monsterImage_6.png","/monster/monster_6.png","/monster/monster_lock_6.png","{\"familyCoin\":1350,\"crystal\":135000}"),
    DAILY_LEVEL_7(7,"DAILY_LEVEL_7","lv.7黑山小妖（绿）","daily",65000,"/monster/icon/monsterImage_7.png","/monster/monster_7.png","/monster/monster_lock_7.png","{\"familyCoin\":1950,\"crystal\":195000}"),
    DAILY_LEVEL_8(8,"DAILY_LEVEL_8","lv.8黑山小妖（紫）","daily",100000,"/monster/icon/monsterImage_8.png","/monster/monster_8.png","/monster/monster_lock_8.png","{\"familyCoin\":3000,\"crystal\":300000}"),
    DAILY_LEVEL_9(9,"DAILY_LEVEL_9","lv.9黑山小妖（黄）","daily",150000,"/monster/icon/monsterImage_9.png","/monster/monster_9.png","/monster/monster_lock_9.png","{\"familyCoin\":4500,\"crystal\":450000}"),
    DAILY_LEVEL_10(10,"DAILY_LEVEL_10","lv.10黑山小妖（红）","daily",225000,"/monster/icon/monsterImage_10.png","/monster/monster_10.png","/monster/monster_lock_10.png","{\"familyCoin\":6750,\"crystal\":675000}"),
    public_LEVEL_1(51,"public_LEVEL_1","一阶.黑福王","public",4500000,"/monster/icon/monster_51_icon_new.png","/monster/monster_51_new.png","/monster/monster_lock_51_new.png","{\"familyCoin\":0,\"crystal\":13500000}"),

    ;

    /** 怪物级别 */
    private Integer level;

    public String code;

    /** 怪物名称 */
    private String name;

    /** 怪物类型 LuckBagFightType 用于区分日常怪和全服怪 */
    private String fightType;

    /** 生命值 */
    private Integer HPValue;

    /** 怪物图片 */
    private String icon;
    private String monsterImage;
    private String monsterUnlockImage;

    /** 击杀奖励 json格式方便扩展 目前包含家族宝箱和水晶奖励 */
    private String killReward;

    LuckBagFightMonsterEnum(Integer level, String code, String name, String fightType, Integer HPValue, String icon, String monsterImage, String monsterUnlockImage, String killReward) {
        this.level = level;
        this.code = code;
        this.name = name;
        this.fightType = fightType;
        this.HPValue = HPValue;
        this.icon = icon;
        this.monsterImage = monsterImage;
        this.monsterUnlockImage = monsterUnlockImage;
        this.killReward = killReward;
    }

    public static LuckBagFightMonsterEnum getByCode(String code){
        for (LuckBagFightMonsterEnum item: LuckBagFightMonsterEnum.values()){
            if (item.code.equals(code)){
                return item;
            }
        }
        return null;
    }

    public Integer getLevel() {
        return level;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }

    public String getMonsterUnlockImage() {
        return "https://res-cdn.nuan.chat/res/activity/luckyBag/style/fight" + monsterUnlockImage;
    }

    public String getFightType() {
        return fightType;
    }

    public Integer getHPValue() {
        return HPValue;
    }

    public String getMonsterImage() {
        return "https://res-cdn.nuan.chat/res/activity/luckyBag/style/fight" + monsterImage;
    }

    public static void main(String[] args) {
        String monsterImage1 = LuckBagFightMonsterEnum.public_LEVEL_1.getMonsterImage();
        System.out.println(monsterImage1);
    }

    public String getKillReward() {
        return killReward;
    }

    public String getIcon() {
        return "https://res-cdn.nuan.chat/res/activity/luckyBag/style/fight" + icon;
    }

    public Integer getFamilyRewardCoin(){
        JSONObject jsonObject = JSON.parseObject(killReward);
        Integer familyCoin = jsonObject.getInteger("familyCoin");
        return familyCoin;
    }

    public Integer getCrystalRewardNum(){
        JSONObject jsonObject = JSON.parseObject(killReward);
        Integer crystal = jsonObject.getInteger("crystal");
        return crystal;
    }
}
