package cn.yizhoucp.ump.biz.project.biz.mq.kafka.sync;

import cn.yizhoucp.ump.biz.project.biz.manager.RedPacketSyncManager;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 红包数据迁移
 *
 * @author: lianghu
 */
@Slf4j
@Service
public class RedPacketSyncConsumer {

    @Resource
    @Lazy
    private RedPacketSyncManager redPacketSyncManager;

    @KafkaListener(topics = "octopus_ump_red_packet_cdc", containerFactory = "batchFactory")
    public void consume(List<String> records) {
        try {
            log.info("红包迁移数据消费 records:{}", JSON.toJSONString(records));
            redPacketSyncManager.sync(records);
        } catch (Exception e) {
            log.error("红包迁移数据消费失败 records:{}", JSON.toJSONString(records), e);
        }
    }
}
