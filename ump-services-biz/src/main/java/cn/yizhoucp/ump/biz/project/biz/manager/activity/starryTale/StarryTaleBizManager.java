package cn.yizhoucp.ump.biz.project.biz.manager.activity.starryTale;

import cn.yizhoucp.ms.core.base.enums.GiftFrom;
import cn.yizhoucp.ms.core.base.enums.GiftWay;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.remoteService.FeignRoomService;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.common.ActivityComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class StarryTaleBizManager implements ActivityComponent {

    @Resource
    private StarryTaleRedisServices starryTaleRedisServices;
    @Resource
    private StarryTaleRankManager starryTaleRankManager;
    @Resource
    private FeignRoomService feignRoomService;


    @Override
    public String getActivityCode() {
        return "";
    }


    @ActivityCheck(activityCode = StarryTaleConstant.ACTIVITY_CODE, isThrowException = false)
    public Boolean sendGiftHandle(BaseParam param, List<CoinGiftGivedModel> coinGiftGivedModelList) {
        for (CoinGiftGivedModel coinGiftGivedModel : coinGiftGivedModelList) {
            //检查渠道
            if (!checkGift(param, coinGiftGivedModel)) {
                continue;
            }
            //非礼盒礼物不统计
            for (StarryTaleEnums.BoxEnums boxEnums : StarryTaleEnums.BoxEnums.values()) {
                if (!boxEnums.getGiftKeys().contains(coinGiftGivedModel.getGiftKey())) {
                    continue;
                }
                //点亮礼物墙
                litGiftGrid(param, coinGiftGivedModel);
                //排行榜
                incrementRankValue(param, coinGiftGivedModel);
            }
        }
        return Boolean.TRUE;
    }

    private void incrementRankValue(BaseParam param, CoinGiftGivedModel coinGiftGivedModel) {
        //日榜
        starryTaleRankManager.incrRankValue(param.getUid(), coinGiftGivedModel.getCoin() * 10, String.format(StarryTaleConstant.DAILY_RANK_KEY, StarryTaleConstant.ACTIVITY_CODE, DateUtil.getNowYyyyMMdd()));
    }

    private void litGiftGrid(BaseParam param, CoinGiftGivedModel coinGiftGivedModel) {
        starryTaleRedisServices.litGiftGrid(param.getUid(), coinGiftGivedModel.getGiftKey());
    }

    private Boolean checkGift(BaseParam param, CoinGiftGivedModel coinGiftGivedModel) {
        // 面板礼物
        if (StringUtils.isEmpty(coinGiftGivedModel.getGiftWay()) || !StringUtils.equalsIgnoreCase(coinGiftGivedModel.getGiftWay(), GiftWay.NORMAL.getCode())) {
            return Boolean.FALSE;
        }

        // 聊天室
        if (StringUtils.isEmpty(coinGiftGivedModel.getFrom()) ||
                !StringUtils.equalsIgnoreCase(coinGiftGivedModel.getFrom(), GiftFrom.room.getCode()) && !StringUtils.equalsIgnoreCase(coinGiftGivedModel.getFrom(), GiftFrom.live_room.getCode())) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

}
