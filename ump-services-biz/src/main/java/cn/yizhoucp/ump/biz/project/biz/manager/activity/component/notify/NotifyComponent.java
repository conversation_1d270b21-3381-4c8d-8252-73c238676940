package cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify;


import cn.hutool.core.lang.Pair;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.SystemNPC;
import cn.yizhoucp.ms.core.base.enums.ClientPushKey;
import cn.yizhoucp.ms.core.base.enums.ImMessageType;
import cn.yizhoucp.ms.core.base.enums.RoomAgoraType;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.enums.serverPush.SingleImgEnum;
import cn.yizhoucp.ms.core.base.env.DynamicEnvManager;
import cn.yizhoucp.ms.core.base.util.Base64Util;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.dto.serverPush.SingleImgActionDTO;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.avservices.AgoraRestfulMessageData;
import cn.yizhoucp.ms.core.vo.imservices.*;
import cn.yizhoucp.ms.core.vo.landingservices.family.FamilyVO;
import cn.yizhoucp.ms.core.vo.roomservices.RoomVO;
import cn.yizhoucp.starter.configuration.manager.ServiceAppIdConfig;
import cn.yizhoucp.starter.configuration.manager.vo.ServicesAppIdVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.AbstractComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.inner.NotifyTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.inner.WindowParam;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityUrlUtil;
import cn.yizhoucp.ump.biz.project.biz.util.LanlingActivityUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.LoginPopDO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

import static cn.yizhoucp.ms.core.base.SystemNPC.LANLING_LITTLE;

/**
 * 通知管理
 *
 * @author: lianghu
 */
@Slf4j
@Component
public class NotifyComponent extends AbstractComponent {

    private static final Long DEFAULT_APPID = 1L;

    @Resource
    private ServiceAppIdConfig serviceAppIdConfig;
    @Value("${spring.profiles.active}")
    private String env;
    @Resource
    private Environment environment;

    // todo: 接入 NotifyTypeEnum 其他通知类型
    public boolean notify(String activityCode, String sceneCode, Long uid) {
        List<NotifyTypeEnum> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            for (NotifyTypeEnum item : list) {
                switch (item) {
                    case NPC_TALK:
                        // todo:
                        npcNotify(uid, null);
                        break;
                    case FAMILY_SYS_MSG:
                        // todo:

                        break;
                    case ROOM_SYS_MSG:
                        // todo:
                        roomNotify(null, null);
                        break;
                    case GIFT_BARRAGE:
                        // todo:
                        sendGiftBarrage(null, null, null, null, null);
                        break;
                    case AIRDROP:
                        // todo:
                        break;
                    case CLIENT_WINDOW:
                        // todo:
                        serverPush(null);
                        break;
                }
            }
        }
        return true;
    }

    /**
     * 刷新用户会话
     *
     * @param param
     * @param toUid
     * @return
     */
    public Boolean refreshUserChat(BaseParam param, Long toUid) {
        ImChatInfoVO imChatInfoVO = feignImService.getChat(param.getAppId(), param.getUid(), toUid).successData();
        SystemMessageModel smm = new SystemMessageModel();
        smm.setType(ImMessageType.server_push.getCode());
        SystemMessageAttrsConversationUpdate attr = new SystemMessageAttrsConversationUpdate();
        attr.setKey(ClientPushKey.conversation_list_update.getCode());
        Long[] chatIds = new Long[1];

        chatIds[0] = imChatInfoVO.getId();
        attr.setChatIdList(chatIds);
        smm.setAttrs(attr);


        Boolean res1 = feignImService.systemPushMessage(SystemNPC.findByAppId(param.getAppId()).getUserId(), param.getUid(), param.getAppId(), JSON.toJSONString(smm)).successData();
        Boolean res2 = feignImService.systemPushMessage(SystemNPC.findByAppId(param.getAppId()).getUserId(), toUid, param.getAppId(), JSON.toJSONString(smm)).successData();
        log.info("end userId1 : {} userId2 : {} res1 : {} res2 : {}", param.getUid(), toUid, res1, res2);
        return Boolean.TRUE;
    }

    /**
     * 语音房系统通知
     *
     * @param roomId
     * @param msg
     * @return boolean
     */
    public boolean roomNotify(Long roomId, String msg) {
        RoomVO roomVO = feignRoomService.getRoomInfoByRoomId(roomId, ServicesAppIdEnum.lanling.getAppId()).successData();
        Long fromUid = SystemNPC.findByAppId(ServicesAppIdEnum.lanling.getAppId()).getUserId();
        try {
            AgoraRestfulMessageData message = new AgoraRestfulMessageData();
            message.setType(AgoraRestfulMessageData.TYPE_SYSTEM);
            message.setSubtype(RoomAgoraType.HTML.getSubtype());
            message.setSenderUid(fromUid);
            message.setText(msg);
            feignAvService.sendChannelMessages(fromUid, roomVO.getChannelId(), JSONObject.toJSONString(message));
        } catch (Exception e) {
            log.error("语音房通知失败，fromUid:{}，channelId:{}，msg:{}", fromUid, roomVO.getChannelId(), msg, e);
            return false;
        }
        return true;
    }

    /**
     * 家族聊天页系统通知
     *
     * @param familyId
     * @param msg
     * @return java.lang.Boolean
     */
    public Boolean familyNotify(Long familyId, String msg) {
        FamilyVO familyInfo = feignLanlingService.familyInfo(familyId).successData();
        Long familyChatId = feignImService.getTeamChatByImTid(familyInfo.getImTid()).successData();
        SystemMessageModel smm = new SystemMessageModel();
        smm.setType(ImMessageType.system_msg.getCode());
        smm.setText(msg);
        feignImService.sendTeamCustomSystemMsgBase64(familyChatId, familyInfo.getOwner().getId(), ServicesAppIdEnum.lanling.getAppId(), Base64Util.encodeToString(JSONObject.toJSONString(smm)));
        return true;
    }

    /**
     * 交友大厅系统通知
     *
     * @param chatroomId
     * @param msg
     * @return java.lang.Boolean
     */
    public Boolean chatroomNotify(Long chatroomId, String msg) {
        SystemMessageModel smm = new SystemMessageModel();
        smm.setType(ImMessageType.system_msg.getCode());
        smm.setText(msg);
        feignImService.sendChatroomMessageByChatroomIdBase64(chatroomId, ServicesAppIdEnum.lanling.getAppId(), LANLING_LITTLE.getUserId(), null, Base64Util.encodeToString(JSONObject.toJSONString(smm)));
        return true;
    }

    /**
     * 私聊系统通知 双向 两个人都看到的
     *
     * @param fromUid
     * @param toUid
     * @param msg
     * @return java.lang.Boolean
     */
    public Boolean chatNotify(Long fromUid, Long toUid, String msg) {
        SystemMessageModel smm = new SystemMessageModel();
        smm.setType(ImMessageType.system_msg.getCode());
        smm.setText(msg);
        feignImService.sendPrivateChatMsgBase64(fromUid, toUid, ServicesAppIdEnum.lanling.getAppId(), null, Base64Util.encodeToString(JSONObject.toJSONString(smm)));
        feignImService.sendPrivateChatMsgBase64(toUid, fromUid, ServicesAppIdEnum.lanling.getAppId(), null, Base64Util.encodeToString(JSONObject.toJSONString(smm)));
        return true;
    }

    /**
     * 单向消息通知
     * @param fromUid
     * @param toUid
     * @param msg
     * @return
     */
    public Boolean chatNotifyOneWay(Long fromUid, Long toUid, String msg) {
        SystemMessageModel smm = new SystemMessageModel();
        smm.setType(ImMessageType.system_msg.getCode());
        smm.setText(msg);
        feignImService.sendPrivateChatMsgBase64(fromUid, toUid, ServicesAppIdEnum.lanling.getAppId(), null, Base64Util.encodeToString(JSONObject.toJSONString(smm)));
        return true;
    }

    /**
     * 飘屏通知（自定义 Icon）
     *
     * @param coin 等价金币数（决定展示时长）
     * @param msg  自定义消息
     * @return boolean
     */
    public boolean sendGiftBarrage(Long coin, String msg, String mainBg, String giftIcon, String clickIcon) {
        Map map = new HashMap<>();
        map.put("msg", msg);
        map.put("mainBg", mainBg);
        map.put("giftIcon", giftIcon);
        map.put("clickIcon", clickIcon);
        Long appId = MDCUtil.getCurAppIdByMdc();
        try {
            feignLanlingService.sendGiftBarrage(coin, SystemNPC.findByAppId(appId).getUserId(), SystemNPC.findByAppId(appId).getUserId().toString(), MDCUtil.getCurAppIdByMdc(), -1L, -1L, -1L, 0, "", JSONObject.toJSONString(map)).successData();
        } catch (Exception e) {
            log.error("触发飘窗失败", e);
            return false;
        }
        return true;
    }

    public boolean sendGiftBarrage(Long coin, String msg) {
        Map map = new HashMap<>();
        map.put("msg", msg);
        Long appId = MDCUtil.getCurAppIdByMdc();
        return sendGiftBarrage(coin, SystemNPC.findByAppId(appId).getUserId(), SystemNPC.findByAppId(appId).getUserId() + "", MDCUtil.getCurAppIdByMdc(), -1L, -1L, -1L, 0, "", JSONObject.toJSONString(map));
    }

    public boolean sendGiftBarrage(Long coin, Long fromUid, String toUids, Long appId, Long productId, Long productCount, Long relationId, Integer siteType, String giftKey, String extData) {
        try {
            feignLanlingService.sendGiftBarrage(coin, fromUid, toUids, appId, productId, productCount, relationId, siteType, giftKey, extData).successData();
        } catch (Exception e) {
            log.error("触发飘窗失败", e);
            return false;
        }
        return true;
    }

    public boolean npcNotify(String unionId, Long uid, String msg) {
        if (StringUtils.isBlank(msg)) {
            return false;
        }
        ServicesAppIdVO servicesAppIdVO = null;
        try {
            servicesAppIdVO = serviceAppIdConfig.convertFromUnionId(unionId);
            if (Objects.isNull(servicesAppIdVO)) {
                log.warn("unionId is invalid param, unionId : {}", unionId);
                servicesAppIdVO = serviceAppIdConfig.convertFormAppId(DEFAULT_APPID);
            }
            feignImService.npcTalkBase64(SystemNPC.findByAppId(servicesAppIdVO.getAppId()).getCode(), Base64Util.encodeToString(msg), "ump-services", uid);
        } catch (Exception e) {
            log.error("暖聊小助手通知失败，npcKey:{}，uid:{}，msg:{}", Objects.isNull(servicesAppIdVO) ? SystemNPC.findByAppId(serviceAppIdConfig.convertFormAppId(DEFAULT_APPID).getAppId()).getCode() :
                    SystemNPC.findByAppId(servicesAppIdVO.getAppId()).getCode(), uid, msg, e);
            return false;
        }
        return true;
    }

    /**
     * 暖聊小助手通知
     *
     * @param uid 用户 ID
     * @param msg 消息
     * @return boolean
     */
    public boolean npcNotify(Long uid, String msg) {
        Long appId = MDCUtil.getCurAppIdByMdc();
        try {
            log.debug("小助手消息 uid:{}, msg:{}", uid, msg);
            feignImService.npcTalkBase64(SystemNPC.findByAppId(appId).getCode(), Base64Util.encodeToString(msg), "ump-services", uid);
        } catch (Exception e) {
            log.error("暖聊小助手通知失败，npcKey:{}，uid:{}，msg:{}", SystemNPC.findByAppId(appId).getCode(), uid, msg, e);
            return false;
        }
        return true;
    }

    /**
     * 客户端弹窗通知
     *
     * @param param
     */
    @Deprecated
    public void serverPush(WindowParam param) {
        SystemMessageAttrsCommonContent content = new SystemMessageAttrsCommonContent();
        content.setBackgroundImage(param.getBackgroundImage());
        content.setActionImage(param.getActionImage());
        if (StringUtils.isNotBlank(param.getTitle())) {
            content.setTitle(param.getTitle());
        }
        if (StringUtils.isNotBlank(param.getContentMsg())) {
            content.setContent("<font color=\"#FDEFB5\" text-align=\"center\">            " + param.getContentMsg() + "</font>");
        }
        if (StringUtils.isNotBlank(param.getActionUrl())) {
            String url = LanlingActivityUtil.getH5BaseUrl(env) + param.getActionUrl();
            content.setActionUrl(url);
        }
        content.setNeedCloseIcon(param.getNeedCloseIcon());
        content.setRenderType(param.getRenderType());
        content.setPriority(param.getPriority());
        serverPushManager.sendSystemCommonContent(LANLING_LITTLE, param.getToUid(), content);
    }

    /**
     * 客户端弹窗通知
     *
     * @param param
     */
    public Boolean commonClientPopPush(WindowParam param) {
        SystemMessageAttrsCommonContent content = new SystemMessageAttrsCommonContent();
        content.setFrom(param.getFrom());
        content.setType(param.getType());
        content.setBackgroundImage(param.getBackgroundImage());
        content.setActionImage(param.getActionImage());
        if (StringUtils.isNotBlank(param.getTitle())) {
            content.setTitle(param.getTitle());
        }
        if (StringUtils.isNotBlank(param.getContentMsg())) {
            content.setContent("<font color=\"#FDEFB5\" text-align=\"center\">            " + param.getContentMsg() + "</font>");
        }
        if (StringUtils.isNotBlank(param.getActionUrl())) {
            content.setActionUrl(param.getActionUrl());
        }
        content.setNeedCloseIcon(param.getNeedCloseIcon());
        content.setRenderType(param.getRenderType());
        content.setPriority(param.getPriority());
        content.setListPayloads(param.getListPayloads());
        content.setBottomButton(param.getBottomButton());
        serverPushManager.sendSystemCommonContent(LANLING_LITTLE, param.getToUid(), content);
        return Boolean.TRUE;
    }

    /**
     * 客户端弹窗通知 (单图) 背景图 + 按钮图 + 一张奖励 icon + icon 下一句文案（颜色可配）
     *
     * @param uid           用户id
     * @param singleImgEnum 单图场景
     * @param prizeIcon     奖励 icon
     * @param text          文案
     * @param params        参数
     */
    public void clientSinglePrizePopPush(Long uid, SingleImgEnum singleImgEnum, String prizeIcon, String text, Long coin, Map<String, Object> params) {
        SystemMessageModel smm = new SystemMessageModel();
        smm.setType(ImMessageType.server_push.getCode());
        smm.setText("<text></text>");
        SystemMessageAttrsBase baseAttr = new SystemMessageAttrsBase();
        baseAttr.setKey(singleImgEnum.getCode());
        baseAttr.setType("singleImg");
        JSONObject payload = new JSONObject();
        payload.put("backgroundImg", singleImgEnum.getBackgroundImg());
        payload.put("prizeIcon", prizeIcon);
        payload.put("text", text);
        payload.put("textBackground", "https://res-cdn.nuan.chat/activity/astrology/pt/text-bg.png");
        payload.put("coinShow", coin+"金币");
        payload.put("topCenterImg", "https://res-cdn.nuan.chat/activity/astrology/tag/sm.png");
        payload.put("textColor", singleImgEnum.getTextColor());
        payload.put("closeImg", singleImgEnum.getCloseImg());
        payload.put("buttonImg", singleImgEnum.getButtonImg());
        SingleImgActionDTO singleImgAction = SingleImgActionDTO.builder()
                .actionType(singleImgEnum.getActionType().getType())
                .actionUrl(singleImgEnum.getActionUrl())
                .params(params)
                .build();
        payload.put("buttonAction", singleImgAction);
        baseAttr.setPayload(payload);
        smm.setAttrs(baseAttr);
        log.info("smm {}", JSON.toJSONString(smm));
        feignImService.serverPushMessageBase64(SystemNPC.LANLING_SYSTEM_BROTHER.getUserId(), uid,
                SystemNPC.LANLING_SYSTEM_BROTHER.getAppId(), Base64Util.encodeToString(JSON.toJSONString(smm))).successData();
    }

    public Boolean h5ClientPopPush(Long uid, LoginPopDO loginPopDO) {
        serverPushManager.sendAppStartUpServerPush(LANLING_LITTLE, uid, buildByLoginPopDO(uid, loginPopDO));
        return Boolean.TRUE;
    }

    private SystemMessageAttrsUserStartUpApp buildByLoginPopDO(Long uid, LoginPopDO loginPopDO) {
        log.debug("buildByLoginPopDO uid {} loginPopDO {}", uid, JSON.toJSONString(loginPopDO));
        if (Objects.isNull(loginPopDO)) {
            return null;
        }
        if (StringUtils.isBlank(loginPopDO.getUrl())) {
            log.error("loginPopDo.url is {}", JSONObject.toJSONString(loginPopDO));
            throw new ServiceException(ErrorCode.ACTIVITY_CONFIG_ERROR);
        }
        SystemMessageAttrsUserStartUpApp userRecall = new SystemMessageAttrsUserStartUpApp();
        Map<String, String> payload = Maps.newHashMap();
        payload.put("url", appendUrlParam(ActivityUrlUtil.getH5BaseUrl(loginPopDO.getUnionId(), env, Boolean.TRUE, environment.getProperty(DynamicEnvManager.GLOBAL_ENV_NAME)) + loginPopDO.getUrl(), null));
        userRecall.setKey(loginPopDO.getPushType());
        userRecall.setPayload(payload);
        return userRecall;
    }

    private String appendUrlParam(String url, Pair... params) {
        if (null == params || params.length < 1) {
            return url;
        }
        for (Pair p : params) {
            if (StringUtils.contains(url, "?")) {
                url += "&";
            } else {
                url += "?";
            }
            url += p.getKey() + "=" + p.getValue();
        }
        return url;
    }

}
