package cn.yizhoucp.ump.biz.common.base.config;

import cn.yizhoucp.ump.biz.common.core.web.filter.CommonRequestDataFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.CommonsRequestLoggingFilter;

/**
 *
 * <AUTHOR>
 * @version 0.1 : FilterConfiguration v0.1 2017/10/17 下午9:15 yanlv Exp $
 */

@Configuration
public class CustomerFilterConfiguration {

    @Bean
    public FilterRegistrationBean CommonsRequestLoggingFilter() {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        //注入过滤器
        registration.setFilter(new CommonsRequestLoggingFilter());
        //拦截规则
        registration.addUrlPatterns("/*");
        //过滤器名称
        registration.setName("commonsRequestLoggingFilter");
        //是否自动注册 false 取消Filter的自动注册
        registration.setEnabled(true);
        //过滤器顺序
        registration.setOrder(1);
        return registration;
    }


    @Bean
    public FilterRegistrationBean CommonRequestDataFilter() {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        //注入过滤器
        registration.setFilter(new CommonRequestDataFilter());
        //拦截规则
        registration.addUrlPatterns("/*");
        //过滤器名称
        registration.setName("commonRequestDataFilter");
        //是否自动注册 false 取消Filter的自动注册
        registration.setEnabled(true);
        //过滤器顺序
        registration.setOrder(2);
        return registration;
    }



}
