package cn.yizhoucp.ump.biz.project.web.rest.controller.userActivity;

import cn.yizhoucp.ump.biz.project.biz.manager.userActivity.UserActivityManager;
import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.umpServices.activity.common.jimu.ActivityVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 用户活动
 *
 * @author: lianghu
 */
@RequestMapping("/api/inner/user-activity")
@RestController
public class UserActivityController {

    @Resource
    private UserActivityManager userActivityManager;

    /**
     * 根据 belongActivityCode 获取用户当前参与活动信息
     *
     * @param baseParam
     * @param belongActivityCode
     * @return
     */
    @GetMapping("/get")
    public Result<ActivityVO> getUserActivity(BaseParam baseParam, String belongActivityCode) {
        return Result.successResult(userActivityManager.getUserActivity(baseParam, belongActivityCode));
    }

    /**
     * 根据 belongActivityCode 获取用户当前参与活动信息
     *
     * @param baseParam
     * @param belongActivityCode
     * @return
     */
    @GetMapping("/get-all")
    public Result<List<ActivityVO>> getUserActivityAll(BaseParam baseParam, String belongActivityCode) {
        return Result.successResult(userActivityManager.getUserActivityAll(baseParam, belongActivityCode));
    }

    /**
     * 根据 belongActivityCode 获取用户当前参与活动信息
     *
     * @param baseParam
     * @param belongActivityCode
     * @return
     */
    @GetMapping("/get-online-test")
    public Result<List<ActivityVO>> getOnlineTest(BaseParam baseParam, String belongActivityCode) {
        return Result.successResult(userActivityManager.joinUserActivity(baseParam.getAppId(), baseParam.getUnionId(), baseParam.getUid(), belongActivityCode));
    }


}
