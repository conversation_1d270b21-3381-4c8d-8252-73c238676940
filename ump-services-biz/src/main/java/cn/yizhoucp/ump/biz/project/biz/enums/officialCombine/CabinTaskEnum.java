package cn.yizhoucp.ump.biz.project.biz.enums.officialCombine;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 小屋任务
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum CabinTaskEnum {

    polish(0, "polish", "https://res-cdn.nuan.chat/officialCombine/cabin/cabin_task_polish_icon.png", 25L, 10L, "打扫小屋 10 次", "成功打扫小屋10次", "可获得 25 浪漫值"),
    decorate(1, "decorate", "https://res-cdn.nuan.chat/officialCombine/cabin/cabin_task_decorate_icon.png", 375L, 10L, "装修小屋 10 次", "成功装修小屋10次", "可获得 375 浪漫值"),
    draw(2, "draw", "https://res-cdn.nuan.chat/officialCombine/cabin/cabin_task_draw_icon.png", 50L, 1L, "开启甜蜜宝箱 1 次", "开启甜蜜宝箱1次", "可获得 50 浪漫值"),
    coin_cost3344(3, "coin_cost3344", "https://res-cdn.nuan.chat/officialCombine/cabin/cabin_task_coin_icon.png", 386L, 3344L, "小屋消耗 3344 金币", "小屋中消耗3344金币", "可获得 386 浪漫值"),
    coin_cost5200(4, "coin_cost5200", "https://res-cdn.nuan.chat/officialCombine/cabin/cabin_task_coin_icon.png", 914L, 5200L, "小屋消耗 5200 金币", "小屋中消耗5200金币", "可获得 914 浪漫值"),
    ;

    private static final Map<String, CabinTaskEnum> INIT_MAP = new HashMap<>();
    private static final Map<Integer, CabinTaskEnum> INDEX_MAP = new HashMap<>();

    static {
        for (CabinTaskEnum item : CabinTaskEnum.values()) {
            INIT_MAP.put(item.getCode(), item);
            INDEX_MAP.put(item.getIndex(), item);
        }
    }

    private Integer index;
    private String code;
    private String icon;
    private Long prize;
    private Long limit;
    private String name;
    private String barName;
    private String desc;

    public static CabinTaskEnum getByCode(String code) {
        return INIT_MAP.get(code);
    }

    public static CabinTaskEnum getByIndex(Integer index) {
        return INDEX_MAP.get(index);
    }

    /**
     * 获取领取的奖励
     *
     * @return Long
     */
    public Long getReceivePrize() {
        return this.prize * 10;
    }

}
