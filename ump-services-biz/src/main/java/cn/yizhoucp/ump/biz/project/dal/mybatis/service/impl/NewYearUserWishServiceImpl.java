package cn.yizhoucp.ump.biz.project.dal.mybatis.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.yizhoucp.ump.biz.project.dal.mybatis.domain.NewYearUserWish;
import cn.yizhoucp.ump.biz.project.dal.mybatis.service.NewYearUserWishService;
import cn.yizhoucp.ump.biz.project.dal.mybatis.mapper.NewYearUserWishMapper;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【new_year_user_wish(元旦用户心愿表)】的数据库操作Service实现
* @createDate 2024-12-23 16:21:58
*/
@Service
@Slf4j
public class NewYearUserWishServiceImpl extends ServiceImpl<NewYearUserWishMapper, NewYearUserWish>
    implements NewYearUserWishService{

    @Autowired
    private RedissonClient redissonClient;

    private static final String CANCEL_LOCK = "ump:new_year_user_wish:cancel_like_lock:id:%s";

    @Override
    public Boolean cancelLike(Long wishId) {
        if (Objects.isNull(wishId)) {
            return Boolean.FALSE;
        }
        String key = String.format(CANCEL_LOCK, wishId);
        RLock lock = redissonClient.getLock(key);
        try {
            lock.lock();
            LambdaQueryWrapper<NewYearUserWish> queryWrapper = new LambdaQueryWrapper<NewYearUserWish>()
                    .eq(NewYearUserWish::getId, wishId);
            NewYearUserWish newYearUserWish = this.getOne(queryWrapper);

            if (Objects.isNull(newYearUserWish) || newYearUserWish.getNumber() < 1) {
                log.warn("deductStamina Error wishId {} number:{}", wishId, newYearUserWish == null ? 0 : newYearUserWish.getNumber());
                return false;
            }

            // 扣减动作
            LambdaUpdateWrapper<NewYearUserWish> updateWrapper = new LambdaUpdateWrapper<NewYearUserWish>()
                    .eq(NewYearUserWish::getId, wishId)
                    .set(NewYearUserWish::getNumber, newYearUserWish.getNumber() - 1);
            boolean updated = this.update(null, updateWrapper);

            if (!updated) {
                log.warn("cancelLike Failed to update wishId {}  number:{}", wishId, newYearUserWish.getNumber());
                return false;
            }
            return true;
        } catch (Exception e) {
            log.info("cancelLike Error wishId {} e : {} ", wishId, JSON.toJSONString(e));
            return false;
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

    }
}




