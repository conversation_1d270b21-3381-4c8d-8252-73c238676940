package cn.yizhoucp.ump.biz.project.biz.manager.adminBiz;

import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.CommonResultVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.pop.PopManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static cn.yizhoucp.enums.CommonPopEnum.WARN_POP_H5;

/**
 * 管理后台业务接口
 *
 * @author: lianghu
 */
@Slf4j
@Service
public class AdminBizManager {

    @Resource
    private NotifyComponent notifyComponent;
    @Resource
    private PopManager popManager;

    public CommonResultVO sendWarnPop(BaseParam param, Long toUid) {

        // 仅暖聊可用
        if (!ServicesAppIdEnum.lanling.getUnionId().equals(param.getUnionId())) {
            return CommonResultVO.fail("仅对暖聊应用开放");
        }

        // 发送警告弹窗
        if (!popManager.popByCode(param.getAppId(), param.getUnionId(), toUid, WARN_POP_H5.getCode())) {
            log.error("弹窗失败");
        }

        // 发送小助手消息
        if (!notifyComponent.npcNotify(toUid, "经举报并人工审核后，发现您使用低俗、淫秽色情、性暗示等用语，违反《暖聊用户协议》第五大条：“内容规范” 相关规定；以及《暖聊文明公约》第三大条：“具体要求措施” 相关规定，现给予严重警告。如若再犯，将进行封号处理。")) {
            log.error("小助手通知失败");
        }

        return CommonResultVO.success();
    }

}
