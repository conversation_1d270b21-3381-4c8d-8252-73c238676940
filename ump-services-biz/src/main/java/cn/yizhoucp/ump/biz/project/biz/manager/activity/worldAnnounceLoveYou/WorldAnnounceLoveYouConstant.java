package cn.yizhoucp.ump.biz.project.biz.manager.activity.worldAnnounceLoveYou;

import cn.hutool.core.collection.CollUtil;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class WorldAnnounceLoveYouConstant {

    @Resource
    private RedisManager redisManager;
    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;

    public static final String ACTIVITY_CODE = "world_announce_love_you";

    /**
     * 礼包配置key
     */
    public static final String BAGS_KEY = "OFFICIAL_COMBINE_BAGS";

    /* 官宣礼物listKey */
    public static final String GIFT_LIST_OFFICIAL_DECLARATION = "ump:world_announce_love_you_v3:official_declaration";

    /* 浪漫轨迹礼物 */
    public static final String GIFT_KEYS_ROMANTIC_TRACK = "ump:world_announce_love_you_v3:romantic_track_keys";

    /* 浪漫轨迹点亮key */
    public static final String GIFT_KEYS_ROMANTIC_TRACK_LIGHTEN = "ump:world_announce_love_you_v3:romantic_track:user:%s:lighten:%s";

    /* 全部用户点亮 全部浪漫轨迹留存 */
    public static final String GIFT_KEYS_ALL_USER_LIGHTEN = "ump:world_announce_love_you_v3:all_user_lighten";

    /* 用户领取过浪漫轨迹留存 */
    public static final String GIFT_KEYS_RECEIVED_LIGHTEN = "ump:world_announce_love_you_v3:received_lighten:user:%s";

    /* 礼包官宣追加礼物 */
    public static final String GIFT_KEYS_OFFICIAL_DECLARATION_ADDITION = "ump:world_announce_love_you_v3:official_declaration_addition:user:%s:type:%s";

    /* 追加礼物发送过的标记 */
    public static final String GIFT_KEYS_ADDITION_SENT = "ump:world_announce_love_you_v3:gift_addition_sent:user:%s";

    /* 追加礼物赠送完的标记 */
    public static final String GIFT_KEYS_ADDITION_GIFTED = "ump:world_announce_love_you_v3:gift_addition_gifted:user:%s:type:%s";

    /* 拥有亲密度 > 520 */
    public static final String GIFT_KEYS_INTIMACY_GT_520 = "ump:world_announce_love_you_v3:intimacy_gt_520:user:%s";

    /* 亲密度到达520小助手消息留存 */
    public static final String GIFT_KEYS_INTIMACY_520_ASSISTANT = "ump:world_announce_love_you_v3:intimacy_520_assistant:user:%s";

    public static final String GIFT_SCENE="gift_scene";
    public static final String REWARD_SCENE_MAN="reward_scene_man";
    public static final String REWARD_SCENE_WOMAN="reward_scene_woman";


    /**
     * 礼包类型 甜蜜/真爱/永恒
     */
    public static final String BAG_TYPE_SWEET = "sweet";
    public static final String BAG_TYPE_LOVE = "love";
    public static final String BAG_TYPE_FOREVER = "forever";
    /**
     * 新增礼包类型 挚爱/梦幻
     */
    public static final String BAG_TYPE_DEVOTION = "devotion";
    public static final String BAG_TYPE_DREAM = "dream";


    /**
     * 获取用户是否点亮浪漫轨迹的key
     */
    public String getRomanticLightenKey(Long uid, String giftKey) {
        return String.format(GIFT_KEYS_ROMANTIC_TRACK_LIGHTEN, uid, giftKey);
    }

    /**
     * 校验用户是否点亮浪漫轨迹
     */
    public Boolean checkLighten(Long uid, String giftKey) {
        return redisManager.hasKey(getRomanticLightenKey(uid, giftKey));
    }

    /**
     * 用户点亮浪漫轨迹
     */
    public void lighten(Long uid, String giftKey) {
        redisManager.set(getRomanticLightenKey(uid, giftKey), 1, DateUtil.ONE_MONTH_SECOND);
    }

    /**
     * 获取浪漫轨迹keys
     */
    public List<String> acquireLoveTrackKeys() {
        List<ScenePrizeDO> scenePrizeDOS= scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), ACTIVITY_CODE, GIFT_SCENE);
        if(CollUtil.isEmpty(scenePrizeDOS)){
            return CollUtil.newArrayList();
        }
        return scenePrizeDOS.stream()
                // 先根据 extData 中的 index 排序
                .sorted(Comparator.comparingInt(s -> {
                    try {
                        JSONObject obj = JSONObject.parseObject(s.getExtData());
                        return obj.getIntValue("index");
                    } catch (Exception e) {
                        // 解析失败时放到最后
                        return Integer.MAX_VALUE;
                    }
                }))
                // 再取 prizeValue
                .map(ScenePrizeDO::getPrizeValue)
                .collect(Collectors.toList());
    }

    /**
     * 根据官宣type获取追加礼物keys
     * 左：追加奖励 ， 右：需要追加的keys
     */
    public Pair<List<String>, List<String>> acquireReadditionGiftKeys(String type) {
        switch (type) {
            case BAG_TYPE_SWEET:
                return Pair.of(Arrays.asList("HDXN_GIFT", "1999"), Arrays.asList("QYJX_GIFT", "MZZD_GIFT", "JZQS_GIFT"));
            case BAG_TYPE_LOVE:
                return Pair.of(Arrays.asList("ZSTS_GIFT", "3344"), Arrays.asList("AZJX_GIFT", "LCJX_GIFT"));
            case BAG_TYPE_FOREVER:
                return Pair.of(Arrays.asList("YHLAJ_GIFT", "5200"), Arrays.asList("QBTQL_GIFT_OFF", "ASZF_GIFT_OFF"));
            case BAG_TYPE_DEVOTION:
                return Pair.of(Arrays.asList("DWJQ_GIFT", "9999"), Arrays.asList("HHBM_GIFT", "BMWZ_GIFT"));
            case BAG_TYPE_DREAM:
                return Pair.of(Arrays.asList("HYLL_GIFT", "13140"), Arrays.asList("QBTQL_GIFT_OFF", "XDRL1_GIFT"));
            default:
                return null;
        }
    }

    public String getPrizeScene() {
        return null;
    }

    /**
     * 追送礼物各个属性枚举
     */
    @AllArgsConstructor
    @Getter
    public enum GiftTypeEnum {
        QBZJ_GIFT("BSZH_GIFT", 1888L, "冰霜之华"),
        QYJX_GIFT("QBZJ_GIFT", 3344L, "情不自禁"),
        AZJX_GIFT("XDRL_GIFT", 5200L, "心动热恋"),
        LCJX_GIFT("NRCF_GIFT", 9999L, "你若成风"),
        MZZD_GIFT("CSFR_GIFT", 13140L, "出水芙蓉");

        private final String giftKey;
        private final Long giftValue;
        private final String giftName;

        public static GiftTypeEnum getByCode(String item) {
            for (GiftTypeEnum giftTypeEnum : GiftTypeEnum.values()) {
                if (giftTypeEnum.getGiftKey().equals(item)) {
                    return giftTypeEnum;
                }
            }
            return null;
        }
    }


    @AllArgsConstructor
    @Getter
    public enum StatusEnum {
        /**
         * 未开启
         */
        NOT_FINISHED("NOT_FINISHED", 0),
        /**
         * 可领取
         */
        REWARDED_AVAILABLE("REWARDED_AVAILABLE", 1),
        /**
         * 已领取
         */
        COMPLETED("COMPLETED", 2);
        private final String status;
        private final Integer statusValue;

    }
}
