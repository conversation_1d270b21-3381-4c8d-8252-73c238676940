package cn.yizhoucp.ump.biz.project.biz.enums.navigation;

import lombok.Getter;

@Getter
public enum BoatEnum {
    A("木船", "B", new int[]{1,1,1,1,1,1,1}, new int[]{1,1,1,2,4,10,21}),
    B("渔船",  "C", new int[]{1,1,1,1,1,1,1}, new int[]{1,1,2,4,10,20,52}),
    C("货船", "D", new int[]{1,1,3,3,3,3,3}, new int[]{1,1,4,12,42,99,128}),
    D("游艇", "E", new int[]{1,1,3,3,3,3,3}, new int[]{1,1,4,15,45,156,365}),
    E("超级游艇", "A", new int[]{1,2,6,12,12,12,12}, new int[]{1,2,10,35,115,235,450});

    private String boatName;

    /** 没有实际作用，只是如果抽到后退了返回给用户用的 */
    private String nextName;

    private int[] needPetrol;

    private int[] goldNeedPetrol;

    BoatEnum(String boatName, String nextName, int[] needPetrol, int[] goldNeedPetrol) {
        this.boatName = boatName;
        this.nextName = nextName;
        this.needPetrol = needPetrol;
        this.goldNeedPetrol = goldNeedPetrol;
    }

}
