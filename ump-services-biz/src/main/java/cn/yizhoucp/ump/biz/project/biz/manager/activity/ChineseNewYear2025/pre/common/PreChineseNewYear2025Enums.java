package cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.pre.common;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

public class PreChineseNewYear2025Enums {

    @AllArgsConstructor
    @Getter
    public enum TaskStatusEnum {
        /**
         * 未领取任务
         */
        NOT_TAKEN(1, "未领取"),
        /**
         * 已领取任务
         */
        TAKEN(2, "已领取"),
        /**
         * 已完成任务
         */
        FINISHED(3, "已完成"),
        /**
         * 已领取奖励
         */
        REWARD_TAKEN(4, "已领取奖励");

        private final Integer code;
        private final String desc;
    }

    /**
     * 每日任务
     */
    @AllArgsConstructor
    @Getter
    public enum DailyTaskEnum {

        TASK_1("task_1", "reward_1", 3,"decoartion_task_1"),
        TASK_2("task_2", "reward_2", 2,"decoartion_task_2"),
        TASK_3("task_3", "reward_3", 1,"decoartion_task_3"),
        TASK_4("task_4", "reward_4", 1,"decoartion_task_4"),
        TASK_5("task_5", "reward_5", 1,"decoartion_task_5"),
        TASK_6("task_6", "reward_6", 1,"decoartion_task_6"),
        TASK_7("task_7", "reward_7", 1,"decoartion_task_7"),
        TASK_8("task_8", "reward_8", 1,"decoartion_task_8"),
        ;

        private final String taskCode;
        private final String rewardCode;
        private final Integer totalProgress;
        private final String trackCode;

        public static DailyTaskEnum getEnumByCode(String taskCode) {
            for (DailyTaskEnum value : DailyTaskEnum.values()) {
                if (value.getTaskCode().equals(taskCode)) {
                    return value;
                }
            }
            return null;
        }

        public static boolean containTask(String bizType) {
            for (DailyTaskEnum value : DailyTaskEnum.values()) {
                if (value.getTaskCode().equals(bizType)) {
                    return true;
                }
            }
            return false;
        }
    }


    @AllArgsConstructor
    @Getter
    public enum DecorationCompetionEnum {
        FAMILY_REUNION("FamilyReunion","successfully_decorate_street1"),
        SPRING_FESTIVAL("JoyfulSpringFestival","successfully_decorate_street2");
        private final String taskCode;
        private final String trackCode;

        public static DecorationCompetionEnum getBySceneCode(String bizType) {
            for(DecorationCompetionEnum value : DecorationCompetionEnum.values()){
                if(value.getTaskCode().equals(bizType)){
                    return value;
                }
            }
            return null;
        }
    }

    @AllArgsConstructor
    @Getter
    public enum ButtonEnum {
        PRE_REWARD_TAKEN("pre_reward_taken", "preRewardTakenStrategy"),
        PRE_TASK_TAKEN("pre_task_taken", "preTaskTakenStrategy"),
        PRE_SAVE_SCENE("pre_save_scene", "preSaveSceneStrategy"),
        PRE_SWITCH_SCENE("pre_switch_scene", "preSwitchSceneStrategy");
        private final String buttonCode;

        private final String beanName;

        public static String getBeanNameByName(String name) {
            if (StrUtil.isBlank(name)) {
                return CharSequenceUtil.EMPTY;
            }
            for (ButtonEnum value : ButtonEnum.values()) {
                if (name.equals(value.getButtonCode())) {
                    return value.beanName;
                }
            }
            return CharSequenceUtil.EMPTY;
        }


    }

}
