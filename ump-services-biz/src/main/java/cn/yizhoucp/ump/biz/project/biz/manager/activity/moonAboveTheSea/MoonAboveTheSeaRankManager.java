package cn.yizhoucp.ump.biz.project.biz.manager.activity.moonAboveTheSea;

import cn.hutool.core.collection.CollUtil;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.vo.jimu.rank.CpRankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.moonAboveTheSea.common.MoonAboveTheSeaConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.AbstractRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityRedisKeyGenerator;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dal.mybatis.domain.ActivityMessageTemplates;
import cn.yizhoucp.ump.biz.project.dal.mybatis.service.ActivityMessageTemplatesService;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 月照星海排行榜
 *
 * <AUTHOR>
 * @version V1.0
 * @since 16:13 2025/1/13
 */
@Service
@Slf4j
public class MoonAboveTheSeaRankManager extends AbstractRankManager {

    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    private MoonAboveTheSeaTrackManager moonAboveTheSeaTrackManager;
    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private NotifyComponent notifyComponent;
    @Resource
    private ActivityRedisKeyGenerator activityRedisKeyGenerator;
    @Resource
    private ActivityMessageTemplatesService activityMessageTemplatesService;


    @Override
    protected void postProcess(RankContext rankContext) {

    }

    @Override
    protected void doPreProcess(RankContext rankContext) {
        rankContext.setSupportDiff(Boolean.TRUE);
    }

    public Boolean sendRankPrize() {
        if (!Boolean.TRUE.equals(redisManager.setnx(String.format(MoonAboveTheSeaConstant.RANK_LOCK_KEY, MoonAboveTheSeaConstant.ACTIVITY_CODE), System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND))) {
            return Boolean.TRUE;
        }

        List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(1L, MoonAboveTheSeaConstant.ACTIVITY_CODE, MoonAboveTheSeaConstant.RANK_SCENE_CODE);
        log.info("scenePrizeDOList {}", JSON.toJSONString(scenePrizeDOList));
        if (CollectionUtils.isEmpty(scenePrizeDOList)) {
            return Boolean.FALSE;
        }

        RankVO rankVO = this.getRank(RankContext.builder()
                .activityCode(MoonAboveTheSeaConstant.ACTIVITY_CODE)
                .rankKey(activityRedisKeyGenerator.generateKeyWithActivityStartTime(MoonAboveTheSeaConstant.RANK_KEY, MoonAboveTheSeaConstant.ACTIVITY_CODE))
                .rankLen(10L)
                .type(RankContext.RankType.cp)
                .build());
        log.info("rankVO {}", rankVO);
        if (rankVO == null) {
            return Boolean.FALSE;
        }

        List<CpRankItem> rankList = rankVO.getCpRankItemList();
        if (CollectionUtils.isEmpty(rankList)) {
            return Boolean.FALSE;
        }

        for (CpRankItem cpRankItem : rankList) {
            Long rank = cpRankItem.getRank();
            Long value=cpRankItem.getValue();
            if(value<MoonAboveTheSeaConstant.MIN_RANK_PRIZE_VALUE){
                log.info("cpRank:{} 小于最小奖励值 {}",cpRankItem,MoonAboveTheSeaConstant.MIN_RANK_PRIZE_VALUE);
                continue;
            }
            List<ScenePrizeDO> scenePrizeDOs = scenePrizeDOList.stream().filter(scenePrizeDO -> (Objects.equals(rank, scenePrizeDO.getPrizeBelongToRank()))).collect(Collectors.toList());
            log.info("MoonAboveTheSeaRankManager#sendPrize cpRankItem {}", cpRankItem);
            if (CollUtil.isEmpty(scenePrizeDOs)) {
                log.info("maleUid {} femaleUid {} rank {} 没有奖励", cpRankItem.getMaleUid(), cpRankItem.getFemaleUid(), rank);
                continue;
            }
            sendPrizeManager.sendPrize(
                    BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(cpRankItem.getMaleUid()).build(),
                    scenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, cpRankItem.getMaleUid())).collect(Collectors.toList())
            );
            sendPrizeManager.sendPrize(
                    BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(cpRankItem.getFemaleUid()).build(),
                    scenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, cpRankItem.getFemaleUid())).collect(Collectors.toList())
            );
            ActivityMessageTemplates activityMessageTemplates = activityMessageTemplatesService.findByActivityCodeAndSceneCode(MoonAboveTheSeaConstant.ACTIVITY_CODE, MoonAboveTheSeaConstant.MESSAGE_TEMPLATE_SCENE_CODE);
            if (activityMessageTemplates != null) {
                String msg = activityMessageTemplates.getMessageTemplate();
                notifyComponent.npcNotify(
                        ServicesAppIdEnum.lanling.getUnionId(),
                        cpRankItem.getMaleUid(),
                        String.format(msg, cpRankItem.getRank(), scenePrizeDOs.get(0).getPrizeDesc())
                );
                notifyComponent.npcNotify(
                        ServicesAppIdEnum.lanling.getUnionId(),
                        cpRankItem.getFemaleUid(),
                        String.format(msg, cpRankItem.getRank(), scenePrizeDOs.get(0).getPrizeDesc())
                );
            }
            //埋点
            for (ScenePrizeDO gift : scenePrizeDOs) {
                moonAboveTheSeaTrackManager.allActivityReceiveAward(gift.getPrizeValue(), gift.getPrizeValueGold().longValue(), gift.getPrizeNum(), MoonAboveTheSeaConstant.RANK_SCENE_CODE, cpRankItem.getMaleUid());
                moonAboveTheSeaTrackManager.allActivityReceiveAward(gift.getPrizeValue(), gift.getPrizeValueGold().longValue(), gift.getPrizeNum(), MoonAboveTheSeaConstant.RANK_SCENE_CODE, cpRankItem.getFemaleUid());
            }
        }

        return Boolean.TRUE;
    }
}
