package cn.yizhoucp.ump.biz.project.biz.manager.activity.dragon_Boat_Festival_2025;

import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.AbstractDrawTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 2025端午节抽奖类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 17:21 2025/5/29
 */
@Slf4j
@Service
public class DragonBoatFestival2025DrawManager extends AbstractDrawTemplate {

    @Override
    protected void resourceCheck(DrawContext context) {

    }

    @Override
    protected void deductResource(DrawContext context) {

    }

    @Override
    protected void doCallback(DrawContext context) {

    }
}
