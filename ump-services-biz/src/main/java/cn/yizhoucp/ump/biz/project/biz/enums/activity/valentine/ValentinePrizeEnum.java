package cn.yizhoucp.ump.biz.project.biz.enums.activity.valentine;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @ClassName: ValentinePrizeEnum
 * @Description: 奖品礼物
 * @author: pepper
 * @date: 2022/1/17 11:52
 */
@AllArgsConstructor
@Getter
public enum ValentinePrizeEnum {
    BBK_GIFT("BBK_GIFT", "表白卡", 1, PrizeTypeEnum.VIRTUAL_GIFT, "https://design-platform-cdn.yizhoucp.cn/gift-image/2021-10/1634804165966494.png"),
    FSAX_GIFT("FSAX_GIFT", "发射爱心", 10, PrizeTypeEnum.VIRTUAL_GIFT, "https://design-platform-cdn.yizhoucp.cn/gift-image/2021-11/1637055614856995.png"),
    AXPPJ_GIFT("AXPPJ1_GIFT_16444_1644499857", "爱心泡泡机", 52, PrizeTypeEnum.VIRTUAL_GIFT, "https://design-platform-cdn.yizhoucp.cn/gift-image/2022-01/1643093127739387.png"),
    ADMXP_GIFT("ADMXP_GIFT", "爱的明信片", 520, PrizeTypeEnum.VIRTUAL_GIFT, "https://design-platform-cdn.yizhoucp.cn/gift-image/2021-09/1632294660984754.png"),
    ASJL_GIFT("ASJL_GIFT", "爱神降临", 3344, PrizeTypeEnum.VIRTUAL_GIFT, "https://design-platform-cdn.yizhoucp.cn/gift-image/2022-02/1644398058859588.png"),
    CPZJ_GIFT("gaobaichaopao_mount", "告白超跑", 5200, PrizeTypeEnum.SEAT_GIFT, "https://res-cdn.nuan.chat/res/test/mount/image/nl_im_gaobai_chaopao20220210.png"),
    WZXHN_GIFT("wozhixihuanni_entry_special_effect", "我只喜欢你", 520, PrizeTypeEnum.PATTERN_GIFT, "https://res-cdn.nuan.chat/res/test/entry_special_effect/image/nl_im_welcome_wzxhn0124_icon.png"),
    LYYD_GIFT("longyuyunduan_head_frame", "龙于云端", 1314, PrizeTypeEnum.FRAME_GIFT, "https://res-cdn.nuan.chat/res/test/head-frame/image/nl_im_head_lyyd_icon.png"),

    MGWY_GIFT("MGWY_GIFT", "玫瑰物语", 10, PrizeTypeEnum.VIRTUAL_GIFT, "https://res-cdn.nuan.chat/res/test/gift/image/bmg.png"),
    MKL_GIFT("MKL_GIFT", "马卡龙", 199, PrizeTypeEnum.VIRTUAL_GIFT, "https://res-cdn.nuan.chat/res/test/gift/image/image-197be7970b9236184bb878a86092b038-200-200.png"),
    LMXH_GIFT("langmanxinghe_entry_special_effect", "浪漫星河", 666, PrizeTypeEnum.PATTERN_GIFT, "https://res-cdn.nuan.chat/res/qa/entry_special_effect/image/icon211124.png"),
    FYXJ_GIFT("fengyuxinjian_head_frame", "凤于心尖", 1314, PrizeTypeEnum.FRAME_GIFT, "https://res-cdn.nuan.chat/res/test/head-frame/image/nl_im_head_fyxj_icon.png"),
    QRJKH_GIFT("QRJKH_GIFT", "Dior 999 口红", 5781, PrizeTypeEnum.ACTUAL_GIFT, "https://nuanchat.oss-cn-hangzhou.aliyuncs.com/valentine/KH.png"),
    QRJXS_GIFT("QRJXS_GIFT", "CHANEL 香水", 6563, PrizeTypeEnum.ACTUAL_GIFT, "https://nuanchat.oss-cn-hangzhou.aliyuncs.com/valentine/XS.png"),
    QRJHF_GIFT("QRJHF_GIFT", "SK II 体验套装", 10781, PrizeTypeEnum.ACTUAL_GIFT, "https://nuanchat.oss-cn-hangzhou.aliyuncs.com/valentine/HFP.png"),
    QRJZJ_GIFT("QRJZJ_GIFT", "I Do 钻戒", 82797, PrizeTypeEnum.ACTUAL_GIFT, "https://nuanchat.oss-cn-hangzhou.aliyuncs.com/valentine/ZJ.png"),

    DEFAULT_GIFT("DEFAULT_GIFT", "普通礼品", 0, PrizeTypeEnum.VIRTUAL_GIFT, "");
    private String code;

    /**
     * 礼物描述
     */
    private String desc;

    /**
     * 礼物价值
     * >= 1314 是全服弹幕飘屏礼物、>=520 是 公屏弹幕、其他都是普通礼物
     */
    private Integer value;

    /**
     * 奖品类型
     */
    private PrizeTypeEnum type;

    private String icon;


}
