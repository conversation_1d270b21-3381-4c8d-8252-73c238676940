package cn.yizhoucp.ump.biz.project.biz.manager;

import cn.hutool.core.collection.CollUtil;
import cn.yizhoucp.enums.RoomCoinBoxType;
import cn.yizhoucp.ump.biz.project.dal.mp.dao.RoomCoinBoxConfigDAO;
import cn.yizhoucp.ump.biz.project.dal.mp.dataobject.RoomCoinBoxConfigDO;
import cn.yizhoucp.ump.biz.project.dto.RoomCoinBoxWeightDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RoomCoinBoxConfigManager {
    @Resource
    private RoomCoinBoxConfigDAO roomCoinBoxConfigDAO;

    public int[] getBoxProb(Integer feeCoin) {
        Map<String, List<RoomCoinBoxWeightDTO>> map = roomCoinBoxConfigDAO.listByConfigType(RoomCoinBoxConfigDO.NORMAL)
                .stream()
                .collect(Collectors.toMap(RoomCoinBoxConfigDO::getBoxType, RoomCoinBoxConfigDO::getWeightList));
        return new int[]{getWeight(map.get(RoomCoinBoxType.NONE.name()), feeCoin)
                , getWeight(map.get(RoomCoinBoxType.COPPER.name()), feeCoin)
                , getWeight(map.get(RoomCoinBoxType.SILVER.name()), feeCoin)
                , getWeight(map.get(RoomCoinBoxType.GOLD.name()), feeCoin)};
    }

    private int getWeight(List<RoomCoinBoxWeightDTO> roomCoinBoxWeightList, Integer feeCoin) {
        if (CollectionUtils.isEmpty(roomCoinBoxWeightList)) {
            return 0;
        }
        List<RoomCoinBoxWeightDTO> configList = roomCoinBoxWeightList
                .stream()
                .sorted(Comparator.comparingInt(RoomCoinBoxWeightDTO::getMinCoin).reversed())
                .collect(Collectors.toList());
        for (RoomCoinBoxWeightDTO roomCoinBoxWeightDTO : configList) {
            if (feeCoin > roomCoinBoxWeightDTO.getMinCoin()) {
                return roomCoinBoxWeightDTO.getWeight();
            }
        }
        return 0;
    }

    public RoomCoinBoxConfigDO getBoxInfo(String boxName) {
        return roomCoinBoxConfigDAO.getByConfigTypeAndBoxName(RoomCoinBoxConfigDO.NORMAL, boxName);
    }

    public Map<String, List<RoomCoinBoxConfigDO>> getGuaranteeBoxInfo() {
        List<RoomCoinBoxConfigDO> roomCoinBoxConfigDOList = roomCoinBoxConfigDAO.listByConfigType(RoomCoinBoxConfigDO.GUARANTEE);
        return roomCoinBoxConfigDOList.stream().collect(Collectors.groupingBy(RoomCoinBoxConfigDO::getGuaranteeType));
    }
}
