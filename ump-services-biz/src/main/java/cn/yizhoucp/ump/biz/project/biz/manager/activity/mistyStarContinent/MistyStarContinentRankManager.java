package cn.yizhoucp.ump.biz.project.biz.manager.activity.mistyStarContinent;

import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.enums.ServicesNameEnum;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.starter.cassandra.base.kafka.YzKafkaProducerManager;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.AbstractRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.MistyStarContinentConstant.ACTIVITY_CODE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.MistyStarContinentConstant.BOARD;
import static cn.yizhoucp.ump.biz.project.biz.manager.activity.mistyStarContinent.MistyStarContinentIndexManager.FREE_SENIOR_REFRESH_TIME;

@Service
@Slf4j
public class MistyStarContinentRankManager extends AbstractRankManager {

    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private NotifyComponent notifyComponent;
    @Resource
    private YzKafkaProducerManager yzKafkaProducerManager;

    @Override
    protected void doPreProcess(RankContext rankContext) {
        rankContext.setRankLen(5L);
        log.debug("rankContext {}", JSON.toJSONString(rankContext));
    }

    @Override
    protected void postProcess(RankContext rankContext) {
    }

    public Boolean sendPrize() {
        if (!Boolean.TRUE.equals(redisManager.setnx(
                String.format("ump:misty_star_continent:send_prize_idempotent:%s", DateUtil.format(new Date(), DateUtil.YMD_WITHOUT_LINE)),
                System.currentTimeMillis(),
                DateUtil.ONE_MONTH_SECOND
        ))) {
            return false;
        }

        List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(1L, ACTIVITY_CODE, ACTIVITY_CODE);
        log.info("scenePrizeDOList {}", scenePrizeDOList);
        if (CollectionUtils.isEmpty(scenePrizeDOList)) {
            return false;
        }

        RankVO rankVO = this.getRank(RankContext.builder()
                .rankLen(3L)
                .activityCode(ACTIVITY_CODE)
                .rankKey(String.format(BOARD, DateUtil.format(new Date(), DateUtil.YMD_WITHOUT_LINE)))
                .build());
        log.info("rankVO {}", rankVO);
        if (rankVO == null) {
            return false;
        }

        List<RankItem> rankList = rankVO.getRankList();
        if (CollectionUtils.isEmpty(rankList)) {
            return false;
        }

        for (RankItem rankItem : rankList) {
            Long rank = rankItem.getRank();
            List<ScenePrizeDO> scenePrizeDOS = scenePrizeDOList.stream().filter(scenePrizeDO -> Objects.equals(rank, scenePrizeDO.getPrizeBelongToRank())).collect(Collectors.toList());
            log.info("scenePrizeDOS {}", scenePrizeDOS);
            if (CollectionUtils.isEmpty(scenePrizeDOS)) {
                continue;
            }

            sendPrizeManager.sendPrize(
                    BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(rankItem.getId()).build(),
                    scenePrizeDOS.stream().map(item -> SendPrizeDTO.of(item, rankItem.getId())).collect(Collectors.toList())
            );
            Long freeSeniorRefreshTime = null;
            if (rank == 1L) {
                freeSeniorRefreshTime = 3L;
            } else if (rank == 2L) {
                freeSeniorRefreshTime = 2L;
            } else if (rank == 3L) {
                freeSeniorRefreshTime = 1L;
            }
            if (freeSeniorRefreshTime != null) {
                redisManager.incrLong(String.format(FREE_SENIOR_REFRESH_TIME, rankItem.getId()), freeSeniorRefreshTime, DateUtil.ONE_MONTH_SECOND);
                log.info("add refresh time uid {} refreshTime {}", rankItem.getId(), freeSeniorRefreshTime);
            }

            for (ScenePrizeDO scenePrizeDO : scenePrizeDOS) {
                notifyComponent.npcNotify(
                        ServicesAppIdEnum.lanling.getUnionId(),
                        rankItem.getId(),
                        String.format("恭喜您在“迷雾星洲”活动中获得“星洲排行榜”第%s名并获得%s金币礼物“%s”和“繁星图册”%s次高级刷新次数，奖励已发放至您的账号，请注意查收哦~", rank, scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeDesc(), freeSeniorRefreshTime)
                );

                Map<String, Object> params = new HashMap<>();
                params.put("activity_type", ACTIVITY_CODE);
                params.put("award_module", "star_continent_list");
                params.put("award_amount", scenePrizeDO.getPrizeValueGold());
                params.put("award_key", scenePrizeDO.getPrizeValue());
                yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), rankItem.getId(), "all_activity_receive_award", params, ServicesNameEnum.ump_services.getCode());
            }

            Map<String, Object> params = new HashMap<>();
            params.put("activity_type", ACTIVITY_CODE);
            params.put("award_module", "star_continent_list");
            params.put("award_amount", freeSeniorRefreshTime);
            params.put("award_key", "refresh_times");
            yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), rankItem.getId(), "all_activity_receive_award", params, ServicesNameEnum.ump_services.getCode());
        }

        return true;
    }

}
