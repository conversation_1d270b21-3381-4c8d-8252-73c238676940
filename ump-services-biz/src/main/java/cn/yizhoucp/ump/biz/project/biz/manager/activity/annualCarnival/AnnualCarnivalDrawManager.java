package cn.yizhoucp.ump.biz.project.biz.manager.activity.annualCarnival;

import cn.yizhoucp.ump.biz.project.biz.manager.activity.annualCarnival.internal.AnnualCarnivalEnums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.annualCarnival.internal.AnnualCarnivalRedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.AbstractDrawTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawContext;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.doDrawStrategy.common.ProbStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.biz.manager.clientPush.ServerPushManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class AnnualCarnivalDrawManager extends AbstractDrawTemplate {
    @Resource
    private ProbStrategy probStrategy;
    @Resource
    private AnnualCarnivalRedisManager annualCarnivalRedisManager;
    @Resource
    private AnnualCarnivalRankManager annualCarnivalRankManager;
    @Value("${spring.profiles.active}")
    private String env;
    @Resource
    private Environment environment;
    @Resource
    private ServerPushManager serverPushManager;
    @Resource
    private AnnualCarnivalBizManager annualCarnivalBizManager;


    @Override
    protected void resourceCheck(DrawContext context) {
        String poolCode = context.getDrawParam().getPoolCode();
        //锦鲤抽奖不下发礼物
        for (AnnualCarnivalEnums.TargetRewardEnum target : AnnualCarnivalEnums.TargetRewardEnum.values()) {
            if (target.getTaskCode().equals(poolCode)) {
                context.getDrawParam().setNoSendPrize(Boolean.TRUE);
            }
        }
    }

    @Override
    protected void draw(DrawContext context) {
        List<DrawPoolItemDTO> drawPoolItemDTOList = probStrategy.getDrawPoolItems(context.getDrawPoolItemDOS(),
                context.getDrawParam().getTimes(),
                Boolean.TRUE);
        context.setPrizeItemList(drawPoolItemDTOList);
    }

    @Override
    protected void deductResource(DrawContext context) {

    }

    @Override
    protected void doCallback(DrawContext context) {
        Long uid = context.getDrawParam().getUid();
        String from = context.getDrawParam().getFrom();
    }
}
