package cn.yizhoucp.ump.biz.project.biz.manager.activity.palpitatingHeartYou;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.roomservices.RoomVO;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.activity.palpitatingHeartYou.CardStatistics;
import cn.yizhoucp.ump.api.vo.activity.palpitatingHeartYou.PHYIndexVO;
import cn.yizhoucp.ump.api.vo.activity.palpitatingHeartYou.PHYLeaderboardVO;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.PalpitatingHeartYouConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.palpitatingHeartYou.internal.PalpitaingHeartYouEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.IndexManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.remoteService.FeignRoomService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.FeignUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.PalpitatingHeartYouConstant.*;

@Service
@Slf4j
public class PalpitatingHeartYouIndexManager implements IndexManager {

    @Resource
    private RedisManager redisManager;

    @Resource
    private PalpitatingHeartYouRankManager PHYRankManager;

    @Resource
    private FeignRoomService feignRoomService;

    @Resource
    private FeignUserService feignUserService;
    @Resource
    private PalpitatingHeartYouBizManager palpitatingHeartYouBizManager;

    @Override
    @ActivityCheck(activityCode = PalpitatingHeartYouConstant.ACTIVITY_CODE)
    public PHYIndexVO getIndex(BaseParam param, Long toUid, String extData) {
        if (param.getUid() == null || param.getAppId() == null || param.getUnionId() == null) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        PHYIndexVO phyIndexVO = new PHYIndexVO();
        Long uid = param.getUid();
        log.info("user index PalpitatingHeartYouIndexManager:{}", uid);
        // 卡片统计
        phyIndexVO.setCardStatistics(cardStatistics(uid));

        // 心动卡片
        phyIndexVO.setHeartCard(numberOfHeartCards(uid));

        // 陪伴榜 + 用户榜单数据
        phyIndexVO = this.indexAccompanyLeaderboard(phyIndexVO, param);

        // 心动榜 + 用户榜单数据
        phyIndexVO = this.indexHeartbeatLeaderboard(phyIndexVO, param);

        return phyIndexVO;
    }

    /**
     * 卡片统计
     *
     * @return
     */
    public CardStatistics cardStatistics(Long uid) {
        String key = String.format(palpitatingHeartYouBizManager.buildActivityTimeKey(FOUR_FRAGMENT_USER_KEY), uid);
        log.info("cardStatistics user key:{}-{}", uid, key);
        CardStatistics cardStatistics = CardStatistics.builder()
                .redRoses(Optional.ofNullable((Integer) redisManager.hget(key, PalpitaingHeartYouEnum.FourfragmentEnum.FIVE_GIFY_1.getFragmentKey())).orElse(0).longValue())
                .lovePoetry(Optional.ofNullable((Integer) redisManager.hget(key, PalpitaingHeartYouEnum.FourfragmentEnum.FIVE_GIFY_2.getFragmentKey())).orElse(0).longValue())
                .pledgeRing(Optional.ofNullable((Integer) redisManager.hget(key, PalpitaingHeartYouEnum.FourfragmentEnum.FIVE_GIFY_3.getFragmentKey())).orElse(0).longValue())
                .pearlString(Optional.ofNullable((Integer) redisManager.hget(key, PalpitaingHeartYouEnum.FourfragmentEnum.FIVE_GIFY_4.getFragmentKey())).orElse(0).longValue())
                .build();
        log.info("user cardStatistics map uid:{}, key:{}, result:{}", uid, key, cardStatistics);
        return cardStatistics;
    }


    /**
     * 心动卡片数量
     */
    public Long numberOfHeartCards(Long uid) {
        return Optional.ofNullable(redisManager.getLong(String.format(palpitatingHeartYouBizManager.buildActivityTimeKey(HEART_BEAT_KEY), uid))).orElse(0L);
    }

    /**
     * 陪伴榜
     *
     * @return
     */
    public PHYIndexVO indexAccompanyLeaderboard(PHYIndexVO indexVO, BaseParam param) {
        // 保存榜单信息
        RankVO topTenRanks = null;
        try {
            topTenRanks = PHYRankManager.getRank(RankContext.builder()
                    .activityCode(ACTIVITY_CODE)
                    .rankKey(palpitatingHeartYouBizManager.buildActivityTimeKey(ACCOMPANY_RANK_KEY))
                    .type(RankContext.RankType.user)
                    .param(param)
                    .build());
        } catch (Exception e) {
            log.warn("获取陪伴榜信息失败", e);
        }
        List<PHYLeaderboardVO> accompanyLeaderboard = new ArrayList<>();
        if (topTenRanks != null && topTenRanks.getRankList() != null) {
            accompanyLeaderboard = topTenRanks.getRankList().stream()
                    .map(rankItem -> PHYLeaderboardVO.builder()
                            .userName(rankItem.getName())
                            .avatar(rankItem.getIcon())
                            .rank(rankItem.getRank())
                            .score(rankItem.getValue())
                            .build())
                    .collect(Collectors.toList());
        }
        indexVO.setAccompanyLeaderboard(accompanyLeaderboard);

        // 保存自己信息
        PHYLeaderboardVO userAccompanyLeaderboard = new PHYLeaderboardVO();
        RankItem myselfRank = null;
        if (topTenRanks != null && topTenRanks.getMyselfRank() != null) {
            myselfRank = topTenRanks.getMyselfRank();
            userAccompanyLeaderboard = PHYLeaderboardVO.builder()
                    .userName(myselfRank.getName())
                    .avatar(myselfRank.getIcon())
                    .rank(myselfRank.getRank())
                    .score(myselfRank.getValue())
                    .build();
        } else {
            Long uid = MDCUtil.getCurUserIdByMdc();
            log.info("userEnvoyLeaderboard is null uid:{}", uid);

            Result<UserVO> result = feignUserService.getBasic(uid, ServicesAppIdEnum.lanling.getAppId());
            if (null == result || !StringUtils.equalsIgnoreCase(ErrorCode.SUCCESS.getCode(), result.getCode())) {
                log.warn("feignUserService getBasic error:id:{}, appID:{}", uid, ServicesAppIdEnum.lanling.getAppId());
                throw new ServiceException(ErrorCode.LANLING_GET_COIN_BALANCE_ERROR);

            } else {
                UserVO data = result.getData();
                userAccompanyLeaderboard.setAvatar(data.getAvatar());
                userAccompanyLeaderboard.setUserName(data.getName());
                userAccompanyLeaderboard.setScore(0L);
                userAccompanyLeaderboard.setRank(-1L);
            }
        }

        indexVO.setUserAccompanyLeaderboard(userAccompanyLeaderboard);
        return indexVO;
    }

    /**
     * 心动榜
     *
     * @return
     */
    public PHYIndexVO indexHeartbeatLeaderboard(PHYIndexVO indexVO, BaseParam param) {
        RankVO topTenRanks = null;
        try {
            topTenRanks = PHYRankManager.getRank(RankContext.builder()
                    .activityCode(ACTIVITY_CODE)
                    .rankKey(palpitatingHeartYouBizManager.buildActivityTimeKey(HEARTBEAT_RANK_KEY))
                    .type(RankContext.RankType.room)
                    .build());
        } catch (Exception e) {
            log.warn("获取心动榜单信息失败", e);
        }

        log.info("Get the Heart list:{}", topTenRanks);
        List<PHYLeaderboardVO> heartbeatLeaderboard = new ArrayList<>();
        if (topTenRanks != null && topTenRanks.getRankList() != null) {
            for (RankItem rankItem : topTenRanks.getRankList()) {
                Long value = rankItem.getValue();
                PHYLeaderboardVO fdPlusLeaderboardVO = PHYLeaderboardVO.builder()
                        .avatar(rankItem.getIcon())
                        .rank(rankItem.getRank())
                        .score(value)
                        .userName(rankItem.getName())
                        .build();
                heartbeatLeaderboard.add(fdPlusLeaderboardVO);
            }
        }
        log.info("indexHeartbeatLeaderboard:{}", heartbeatLeaderboard);
        indexVO.setHeartbeatLeaderboard(heartbeatLeaderboard);

        Long uid = MDCUtil.getCurUserIdByMdc();
        // 判断用户是否存在自己的房间 uid -- 房主 id
        Result<RoomVO> roomVOResult = feignRoomService.roomMessage(null, "ROOM_SOURCE_PERSONAL");
        if (!StringUtils.equalsIgnoreCase(ErrorCode.SUCCESS.getCode(), roomVOResult.getCode()) || Objects.isNull(roomVOResult.getData())) {
            log.info("[feignRoomService - roomMessage] to null uid {}", uid);
            return indexVO;
        }
        RoomVO roomVO = roomVOResult.getData();
        if (Objects.isNull(roomVO.getRoomId())) {
            return indexVO;
        }
        // 保存自己榜单信息
        Long rank = Optional.ofNullable(redisManager.reverseRank(palpitatingHeartYouBizManager.buildActivityTimeKey(HEARTBEAT_RANK_KEY), roomVO.getRoomId().toString())).orElse(-1L);
        Double score = Optional.ofNullable(redisManager.score(palpitatingHeartYouBizManager.buildActivityTimeKey(HEARTBEAT_RANK_KEY), roomVO.getRoomId().toString())).orElse(0.0);
        if (rank < 0 || score < 1) {
            log.info("user room rank to null to null uid {} --> roomId:{}", uid, roomVO.getRoomId());
            PHYLeaderboardVO userHeartbeatLeaderboard = PHYLeaderboardVO.builder()
                    .userName(roomVO.getRoomName())
                    .avatar(roomVO.getRoomIcon())
                    .rank(-1L)
                    .score(0L)
                    .build();
            indexVO.setUserHeartbeatLeaderboard(userHeartbeatLeaderboard);
            return indexVO;
        }

        PHYLeaderboardVO userHeartbeatLeaderboard = PHYLeaderboardVO.builder()
                .userName(roomVO.getRoomName())
                .avatar(roomVO.getRoomIcon())

                // 这里获得的排名从 0 开始，需要对rank++
                .rank(++rank)

                .score(score.longValue())
                .build();

        indexVO.setUserHeartbeatLeaderboard(userHeartbeatLeaderboard);
        return indexVO;
    }

    @Override
    public String getTemplateType() {
        return "";
    }

    @Override
    public String getActivityCode() {
        return PalpitatingHeartYouConstant.ACTIVITY_CODE;
    }
}
