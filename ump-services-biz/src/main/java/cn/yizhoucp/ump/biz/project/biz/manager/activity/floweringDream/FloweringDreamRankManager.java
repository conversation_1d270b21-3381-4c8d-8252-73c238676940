package cn.yizhoucp.ump.biz.project.biz.manager.activity.floweringDream;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.enums.dressup.DressUpType;
import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.roomservices.RoomVO;
import cn.yizhoucp.ump.api.vo.activity.floweringDream.DateTheaterLeaderboardVO;
import cn.yizhoucp.ump.api.vo.activity.floweringDream.FDPlusLeaderboardVO;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.FloweringDreamConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.AbstractRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.FeignRoomService;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dal.service.ScenePrizeService;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 梦境剧场榜单 & 自定义接口实现方法
 */
@Service
@Slf4j
public class FloweringDreamRankManager extends AbstractRankManager {

    @Resource
    private SendPrizeManager sendPrizeManager;

    @Resource
    private ScenePrizeService scenePrizeService;

    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;

    @Resource
    private NotifyComponent notifyComponent;

    @Resource
    private FeignRoomService feignRoomService;

    @Resource
    private FloweringDreamTrackManager trackManager;

    /**
     * 后置处理
     */
    @Override
    protected void postProcess(RankContext rankContext) {

    }

    @Override
    protected void doPreProcess(RankContext rankContext) {
    }

    /**
     * 领取抽奖次数统计奖励
     */
    public Boolean receiveDrawCountReward(String activityCode, String rewardsKey) {
        // 校验活动
        if (!activityCode.equals(FloweringDreamConstant.ACTIVITY_CODE)) {
            log.warn("receiveDrawCountReward error activityCode:{}", activityCode);
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }

        Long uid = MDCUtil.getCurUserIdByMdc();
        if (Objects.isNull(uid)) {
            log.warn("MDCUtil getCurUserIdByMdc is null");
            return Boolean.FALSE;
        }
        FloweringDreamConstant.DrawCountEnum drawCountEnum = FloweringDreamConstant.DrawCountEnum.getByRewardsKey(rewardsKey);
        // 校验用户是否真的达到抽奖次数
        if (!redisManager.hasKey(String.format(FloweringDreamConstant.USER_DREAM_COUNT, uid)) || Objects.isNull(drawCountEnum)) {
            log.warn("user dream count is null:{}", uid);
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        Long aLong = redisManager.getLong(String.format(FloweringDreamConstant.USER_DREAM_COUNT, uid));
        log.info("user dream count uid:{} --> count:{} --> drawCountEnum getCount:{}", uid, aLong, drawCountEnum.getCount());
        if (aLong < drawCountEnum.getCount()) {
            log.warn("user dream count is less than:{}", uid);
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }

        // 发放奖励
        String rankKey = FloweringDreamConstant.createUserDreamCountAwardKey(uid, drawCountEnum.getAwardKey());
        List<ScenePrizeDO> scenePrizeDOList = scenePrizeService.getListBySceneCode(FloweringDreamConstant.ACTIVITY_CODE, drawCountEnum.getRewardsKey());
        log.debug("scenePrizeDOList to uid:{} --> scenePrizeDOList:{}", uid, scenePrizeDOList);
        sendPrizeManager.sendPrize(
                BaseParam.builder()
                        .appId(ServicesAppIdEnum.lanling.getAppId())
                        .unionId(ServicesAppIdEnum.lanling.getUnionId())
                        .uid(uid)
                        .build()
                , scenePrizeDOList.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, uid)).collect(Collectors.toList())
        );

        // 埋点
        ScenePrizeDO scenePrizeDO = scenePrizeDOList.get(0);
        trackManager.allActivityReceiveAward(uid, scenePrizeDO.getPrizeValue(), scenePrizeDO.getPrizeValueGold(), 1);

        // 更改对应奖励状态
        redisManager.set(rankKey, FloweringDreamConstant.AwardReceiveEnum.RECEIVED.getStatus(), DateUtil.ONE_MONTH_SECOND);
        return Boolean.TRUE;
    }

    /**
     * 获取指定日期梦境剧场榜
     */
    public JSONObject getTheaterLeaderboardByDate(String activityCode, String date) {
        // 校验活动
        if (!activityCode.equals(FloweringDreamConstant.ACTIVITY_CODE)) {
            log.warn("getTheaterLeaderboardByDate error activityCode:{}", activityCode);
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }

        DateTheaterLeaderboardVO dateTheaterLeaderboardVO = new DateTheaterLeaderboardVO();

        // 保存榜单信息
        String theaterLeaderboardKey = FloweringDreamConstant.createDreamTheaterRankKey(date);
        RankVO topTenRanks = null;
        try {
            topTenRanks = this.getRank(RankContext.builder()
                    .activityCode(FloweringDreamConstant.ACTIVITY_CODE)
                    .rankKey(theaterLeaderboardKey)
                    .type(RankContext.RankType.room)
                    .rankLen(10L)
                    .build());
        } catch (Exception e) {
            log.warn("获取梦境剧场榜单信息失败", e);
        }

        List<FDPlusLeaderboardVO> theaterLeaderboard = new ArrayList<>();
        if (topTenRanks != null && topTenRanks.getRankList() != null) {
            for (RankItem rankItem : topTenRanks.getRankList()) {
                Long value = rankItem.getValue();
                long result = Math.floorDiv(value, FloweringDreamConstant.THRESHOLD_VALUE);
                FDPlusLeaderboardVO fdPlusLeaderboardVO = FDPlusLeaderboardVO.builder()
                        .avatar(rankItem.getIcon())
                        .rank(rankItem.getRank())
                        .score(value)
                        .userName(rankItem.getName())
                        .theaterAward(result > FloweringDreamConstant.MAX_COUNT ? FloweringDreamConstant.MAX_COUNT : result)
                        .build();
                theaterLeaderboard.add(fdPlusLeaderboardVO);
            }
        }
        dateTheaterLeaderboardVO.setTheaterLeaderboard(theaterLeaderboard);

        Long uid = MDCUtil.getCurUserIdByMdc();
        // 判断用户是否存在自己的房间 uid -- 房主 id
        Result<RoomVO> roomVOResult = feignRoomService.roomMessage(null, "ROOM_SOURCE_PERSONAL");
        if (!StringUtils.equalsIgnoreCase(ErrorCode.SUCCESS.getCode(), roomVOResult.getCode()) || Objects.isNull(roomVOResult.getData())) {
            log.info("[feignRoomService - roomMessage] to null uid {}", uid);
            return JSONObject.parseObject(JSON.toJSONString(dateTheaterLeaderboardVO));
        }

        RoomVO roomVO = roomVOResult.getData();

        if (Objects.isNull(roomVO.getRoomId())) {
            return JSONObject.parseObject(JSON.toJSONString(dateTheaterLeaderboardVO));
        }
        // 保存自己榜单信息
        Long rank = Optional.ofNullable(redisManager.reverseRank(theaterLeaderboardKey, roomVO.getRoomId().toString())).orElse(-1L);
        Double score = Optional.ofNullable(redisManager.score(theaterLeaderboardKey, roomVO.getRoomId().toString())).orElse(0.0);
        if (rank < 0 || score < 1) {
            log.info("user room rank to null uid to null uid {} --> roomId:{}", uid, roomVO.getRoomId());
            FDPlusLeaderboardVO userTheaterLeaderboard = FDPlusLeaderboardVO.builder()
                    .userName(roomVO.getRoomName())
                    .avatar(roomVO.getRoomIcon())
                    .rank(0L)
                    .score(0L)
                    .theaterAward(0L)
                    .build();
            dateTheaterLeaderboardVO.setUserTheaterLeaderboard(userTheaterLeaderboard);
            return JSONObject.parseObject(JSON.toJSONString(dateTheaterLeaderboardVO));
        }


        // 林中微光 奖励个数
        long theaterAward = Math.floorDiv(score.longValue(), FloweringDreamConstant.THRESHOLD_VALUE);
        FDPlusLeaderboardVO userTheaterLeaderboard = FDPlusLeaderboardVO.builder()
                .userName(roomVO.getRoomName())
                .avatar(roomVO.getRoomIcon())

                // 这里获得的排名从 0 开始，需要对rank++
                .rank(++rank)

                .score(score.longValue())
                .theaterAward(theaterAward > FloweringDreamConstant.MAX_COUNT ? FloweringDreamConstant.MAX_COUNT : theaterAward)
                .build();
        dateTheaterLeaderboardVO.setUserTheaterLeaderboard(userTheaterLeaderboard);
        return JSONObject.parseObject(JSON.toJSONString(dateTheaterLeaderboardVO));
    }

    /******************  定时任务  ******************/

    /**
     * 发放繁花使者榜单奖励
     */
    public Boolean sendEnvoyLeaderboard() {
        // 获取该榜单对应奖品
        // 前三的礼物奖励
        List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), FloweringDreamConstant.ACTIVITY_CODE, "rank_envoy");
        log.info("flowering dream scenePrizeDOList:{}", scenePrizeDOList);
        // 前五的头像框奖励
//        List<ScenePrizeDO> topFiveList = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), FloweringDreamConstant.ACTIVITY_CODE, "top_five");
        // 获取榜单，根据 rank 发放礼物
        RankVO rankVO = null;
        try {
            rankVO = this.getRank(RankContext.builder()
                    .activityCode(FloweringDreamConstant.ACTIVITY_CODE)
                    .rankKey(FloweringDreamConstant.ENVOY_DREAM_RANK_KEY)
                    .type(RankContext.RankType.user)
                    .rankLen(10L)
                    .build());
        } catch (Exception e) {
            log.warn("获取繁华使者榜单信息失败", e);
        }
        if (rankVO == null) {
            return Boolean.FALSE;
        }

        List<RankItem> rankList = rankVO.getRankList();
        if (CollectionUtils.isEmpty(rankList)) {
            log.warn("king rankList is null");
            return Boolean.FALSE;
        }

        for (RankItem rankItem : rankList) {
            Long rank = rankItem.getRank();
            List<ScenePrizeDO> scenePrizeDOs = scenePrizeDOList.stream().filter(scenePrizeDO -> Objects.equals(rank, scenePrizeDO.getPrizeBelongToRank())).collect(Collectors.toList());

            if (rankItem.getValue() < 200000L) {
                log.info("uid {} rank {} 没有奖励", rankItem.getId(), rank);
                continue;
            }

            if (rank < 4) {
                // 发放礼物奖励 + 头像框奖励
                if (CollectionUtils.isEmpty(scenePrizeDOs)) {
                    log.info("uid {} rank {} 没有奖励", rankItem.getId(), rank);
                    continue;
                }
                log.info("user king scene PrizeDO List:{} ++  --> uid:{}", scenePrizeDOs, rankItem.getId());
                sendPrizeManager.sendPrize(
                        BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(rankItem.getId()).build(),
                        scenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, rankItem.getId())).collect(Collectors.toList())
                );

                ScenePrizeDO prizeDO = scenePrizeDOs.get(0);
                String prizeValue = prizeDO.getPrizeValue();
                Long prizeValueGold = prizeDO.getPrizeValueGold();
                trackManager.allActivityReceiveAward(rankItem.getId(), prizeValue, prizeValueGold, 1);

                sendPrizeManager.sendDressUp(ServicesAppIdEnum.lanling.getAppId(), rankItem.getId(), DressUpType.head_frame, "longyuyunduan_head_frame", 15, 1);
                log.info("send dressUp - longyuyunduan_head_frame uid:{}", rankItem.getId());
                // 头像框为固定
                trackManager.allActivityReceiveAward(rankItem.getId(), "longyuyunduan_head_frame", 500L, 1);

                // 小助手消息
                String msg = "恭喜你在”奇迹恋歌“活动中，荣获恋歌使者榜单第" + rank + "！奖励已下发至您的背包，请注意查收哦~";
                notifyComponent.npcNotify(ServicesAppIdEnum.lanling.getUnionId(), rankItem.getId(), msg);

            } else if (rank < 6) {
                sendPrizeManager.sendDressUp(ServicesAppIdEnum.lanling.getAppId(), rankItem.getId(), DressUpType.head_frame, "longyuyunduan_head_frame", 15, 1);
                log.info("send dressUp - longyuyunduan_head_frame uid:{} to rank:{}", rankItem.getId(), rank);

                trackManager.allActivityReceiveAward(rankItem.getId(), "longyuyunduan_head_frame", 500L, 1);

                // 小助手消息
                String msg = "恭喜你在”奇迹恋歌“活动中，荣获恋歌使者榜单第" + rank + "！奖励已下发至您的背包，请注意查收哦~";
                notifyComponent.npcNotify(ServicesAppIdEnum.lanling.getUnionId(), rankItem.getId(), msg);
            }
        }

        return Boolean.TRUE;
    }

    /**
     * 发放梦境剧场榜单奖励
     * 每天0:00， 最后一天为 23:00
     */
    public Boolean sendTheaterLeaderboard(String date) {
        String redisKey = "ump:fairy_tale_dream:send_fairy_tale_dream_a:rank:%s";

        if (!Boolean.TRUE.equals(redisManager.setnx(String.format(redisKey, DateUtil.getNowYyyyMMdd()), System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND))) {
            return Boolean.TRUE;
        }

        // 获取该榜单对应奖品 -- 林中微光 需要计算个数 --> uid
        List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), FloweringDreamConstant.ACTIVITY_CODE, "the_forest");
        log.info("flowering dream Theater scenePrizeDOList:{}", scenePrizeDOList);
        // 获取榜单，根据 rank 发放礼物
        String theaterLeaderboardKey = FloweringDreamConstant.createDreamTheaterRankKey(date == null ? dayBeforeToStr() : date);

        log.info("theaterLeaderboardKey:{}", theaterLeaderboardKey);

        List<RankItem> rankItems = getRankByMin(theaterLeaderboardKey);
        if (CollectionUtils.isEmpty(rankItems)) {
            log.warn("list is empty check");
            return Boolean.TRUE;
        }
        for (RankItem rankItem : rankItems) {
            // 查询房主 uid
            Result<RoomVO> roomResult = feignRoomService.getRoomInfoByRoomId(rankItem.getId(), ServicesAppIdEnum.lanling.getAppId());
            if (!roomResult.success() || Objects.isNull(roomResult.successData())) {
                log.warn("获取房间信息失败，roomId:{}", rankItem.getId());
                continue;
            }

            Long roomOwnerId = roomResult.successData().getRoomOwnerId();
            log.info("user king scene roomOwnerId:{}", roomOwnerId);

            Long value = rankItem.getValue();
            Long rank = rankItem.getRank();
            long theaterAward = Math.floorDiv(value, FloweringDreamConstant.THRESHOLD_VALUE);
            // 根据分数发放奖励
            theaterAward = theaterAward > FloweringDreamConstant.MAX_COUNT ? FloweringDreamConstant.MAX_COUNT : theaterAward;
            log.info("user king scene rank:{} value:{} theaterAward:{} --> roomId:{} --> uid{}", rank, value, theaterAward, rankItem.getId(), roomOwnerId);
            if (theaterAward <= 0) {
                continue;
            }
            if (scenePrizeDOList != null && !scenePrizeDOList.isEmpty()) {
                try {
                    for (int i = 0; i < theaterAward; i++) {
                        log.debug("user king scene scenePrizeDOList:{} --> uid:{}", scenePrizeDOList, rankItem.getId());
                        sendPrizeManager.sendPrize(
                                BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(roomOwnerId).build(),
                                scenePrizeDOList.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, rankItem.getId())).collect(Collectors.toList())
                        );
                    }
                } catch (Exception e) {
                    log.error("sendPrizeManager sendPrize for error, roomOwnerId:{}", roomOwnerId, e);
                    throw new ServiceException(ErrorCode.SERVER_EXCEPTION, "for 发放奖励异常");
                }
                // 获得奖励埋点
                ScenePrizeDO scenePrizeDO = scenePrizeDOList.get(0);
                String prizeValue = scenePrizeDO.getPrizeValue();
                trackManager.allActivityReceiveAward(roomOwnerId, prizeValue, scenePrizeDO.getPrizeValueGold(), (int) theaterAward);
                // 小助手消息
                String msg = "恭喜您的房间在“奇迹恋歌”活动中，荣获恋歌剧场榜单第" + rank + "！奖励已下发至您的背包，请注意查收哦~";
                notifyComponent.npcNotify(ServicesAppIdEnum.lanling.getUnionId(), roomOwnerId, msg);
            }
        }

        return Boolean.TRUE;
    }

    private List<RankItem> getRankByMin(String key) {
        List<RankItem> rank = new ArrayList<>();
        Long start = 0L;
        Long batchSize = 1000L;
        Long end = batchSize - 1;
        Long index = 0L;
        while (true) {
            Set<ZSetOperations.TypedTuple<Object>> typedTuples = redisManager.reverseRangeByScoreWithScores(key,
                    500000, Double.MAX_VALUE, start, end);
            log.info("fairyTaleDreamRankManager#sendRankPrize typedTuples size {}",typedTuples.size());
            if (org.springframework.util.CollectionUtils.isEmpty(typedTuples)) {
                break;
            }
            for (ZSetOperations.TypedTuple<Object> typedTuple : typedTuples) {
                ++index;
                if (null == typedTuple.getValue() || null == typedTuple.getScore()) {
                    continue;
                }
                Long uid = Long.parseLong(typedTuple.getValue().toString());
                Integer value = typedTuple.getScore().intValue();
                rank.add(RankItem.builder()
                        .rank(index)
                        .id(uid)
                        .value(value.longValue())
                        .build());
            }
            start += batchSize;
            end = end + batchSize - 1;
        }

        return rank;
    }

    /**
     * 前一天
     *
     * @return
     */
    public String dayBeforeToStr() {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        return yesterday.format(formatter);
    }

    public String dayNowToStr() {
        LocalDate yesterday = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        return yesterday.format(formatter);
    }
}
