package cn.yizhoucp.ump.biz.project.biz.manager.activity.perfect_lover.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class PerfectLoveForwardResponse {
    private List<PerfectLoverIndexVO.Route> routes;
    private List<PerfectLoverIndexVO.Reward> rewardList;
    private String message;
}
