package cn.yizhoucp.ump.biz.project.biz.manager.activity.legendaryNeverland.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

public class LnEnum {

    @AllArgsConstructor
    @Getter
    public enum StatusEnum {
        /**
         * 未开启
         */
        NOT_FINISHED("NOT_FINISHED", 0),
        /**
         * 可领取
         */
        REWARDED_AVAILABLE("REWARDED_AVAILABLE", 1),
        /**
         * 已领取
         */
        COMPLETED("COMPLETED", 2);
//        /**
//         * 未领取（已过期）
//         */
//        EXPIRED("EXPIRED", 3);

        private final String status;
        private final Integer statusValue;


        public static StatusEnum getByCode(String item) {
            for (StatusEnum statusEnum : StatusEnum.values()) {
                if (statusEnum.getStatus().equals(item)) {
                    return statusEnum;
                }
            }
            return null;
        }
    }


    /**
     * 任务枚举
     */
    @AllArgsConstructor
    @Getter
    public enum TaskEnum {

        task_1("task_1", 52L, 9L),
        task_2("task_2", 99L, 13L),
        task_3("task_3", 299L, 35L),
        task_4("task_4", 520L, 40L),
        task_5("task_5", 999L, 45L);

        private final String taskKey;
        // 需要礼物价值
        private final Long needGiftValue;
        // 奖励精华
        private final Long sendValue;

        public static TaskEnum getByGiftValue(Long item) {
            for (TaskEnum taskEnum : TaskEnum.values()) {
                if (taskEnum.getNeedGiftValue().equals(item)) {
                    return taskEnum;
                }
            }
            return null;
        }

        public static TaskEnum getByTaskKey(String item) {
            for (TaskEnum taskEnum : TaskEnum.values()) {
                if (taskEnum.getTaskKey().equals(item)) {
                    return taskEnum;
                }
            }
            return null;
        }
    }
}
