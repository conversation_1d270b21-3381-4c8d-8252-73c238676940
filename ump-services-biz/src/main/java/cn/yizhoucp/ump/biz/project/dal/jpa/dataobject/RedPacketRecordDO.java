package cn.yizhoucp.ump.biz.project.dal.jpa.dataobject;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "red_packet_record")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RedPacketRecordDO {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "red_packet_record_generator")
    @SequenceGenerator(name = "red_packet_record_generator",sequenceName = "red_packet_record_seq", allocationSize = 10)
    private Long id;
    /** 应用ID */
    private Long appId;
    /** 红包ID */
    private Long redPacketId;
    /** 发红包用户ID */
    private Long userId;
    /** 家族ID或用户ID */
    private Long fromId;
    /** 红包类型 */
    private Integer type;
    /** 抢到红包金额 */
    private Integer amount;
    /** 创建时间 */
    private Date createTime;
    /** 修改时间 */
    private Date updateTime;

}
