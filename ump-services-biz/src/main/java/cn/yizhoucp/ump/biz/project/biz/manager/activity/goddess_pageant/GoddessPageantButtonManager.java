package cn.yizhoucp.ump.biz.project.biz.manager.activity.goddess_pageant;

import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.api.param.jimu.ButtonRankListParam;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.goddess_pageant.common.GoddessPageantConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.goddess_pageant.common.GoddessPageantRedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.goddess_pageant.strategy.GoddessPageantStrategyChoose;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.AbstractButtonManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class GoddessPageantButtonManager extends AbstractButtonManager {

  @Resource private GoddessPageantStrategyChoose strategyChoose;
  @Resource private GoddessPageantRedisManager goddessPageantRedisManager;

  @Override
  public RankVO rankList(ButtonRankListParam buttonRankListParam) {
    String listCode = buttonRankListParam.getListCode();
    String rankKey;
    long len = 10L;
    if (GoddessPageantConstant.WOMAN_DAILY_RANK_CODE.equals(listCode)) {
      len = 30L;
    }
    if (listCode.contains(GoddessPageantConstant.DAILY_RANK_CODE)) {
      rankKey = goddessPageantRedisManager.getDailyRankKey(listCode);
    } else {
      rankKey = goddessPageantRedisManager.getTotalRankKey(listCode);
    }
    ButtonRankListParam buttonFriendParam = new ButtonRankListParam();
    buttonFriendParam.setBaseParam(buttonRankListParam.getBaseParam());
    buttonFriendParam.setRankKey(rankKey);
    buttonFriendParam.setSupportDiff(Boolean.TRUE);
    buttonFriendParam.setRankLen(len);
    buttonFriendParam.setActivityCode(buttonFriendParam.getActivityCode());
    buttonFriendParam.setListType(RankContext.RankType.user.getType());
    return super.rankList(buttonFriendParam);
  }

  @Override
  public Object event(ButtonEventParam buttonEventParam) {
    return strategyChoose.executeStrategy(buttonEventParam);
  }
}
