package cn.yizhoucp.ump.biz.project.biz.manager.activity.milesOfLove;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw.inner.PrizeTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.AbstractDrawTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawContext;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.doDrawStrategy.common.ProbStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.milesOfLove.common.MolRedisControls;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

import static cn.yizhoucp.ump.biz.project.biz.manager.activity.milesOfLove.common.MolConstant.POOL_CODE_LIST;
import static cn.yizhoucp.ump.biz.project.biz.manager.activity.milesOfLove.common.MolConstant.USER_SIGN_COUNT;

/**
 * 抽奖管理
 */
@Service
@Slf4j
public class MilesOfLoveDrawManager extends AbstractDrawTemplate {

    @Resource
    private ProbStrategy probStrategy;
    @Resource
    private MolRedisControls molRedisControls;
    @Resource
    private MilesOfLoveTrackManager trackManager;


    @Override
    protected void resourceCheck(DrawContext context) {
        DrawParam drawParam = context.getDrawParam();
        String poolCode = drawParam.getPoolCode();
        Long uid = drawParam.getUid();
        Integer times = drawParam.getTimes();


        // 校验奖池
        if (Objects.isNull(poolCode) || !POOL_CODE_LIST.contains(poolCode) || Objects.isNull(uid)) {
            log.warn("Exception prize pool poolCode");
            throw new ServiceException(ErrorCode.INVALID_PARAM, "奖池错误!");
        }

        // 检查是否足够
        Long candyCount = molRedisControls.getSignValue(uid, poolCode);
        if (candyCount < times) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "当前签不足哦～");
        }
    }

    @Override
    protected void deductResource(DrawContext context) {
        DrawParam drawParam = context.getDrawParam();
        String poolCode = drawParam.getPoolCode();
        Long uid = drawParam.getUid();
        Integer times = drawParam.getTimes();

        molRedisControls.deductSignValue(uid, poolCode, Long.valueOf(times));
    }


    @Override
    protected void draw(DrawContext context) {
        List<DrawPoolItemDTO> drawPoolItemDTOList = probStrategy.getDrawPoolItems(context.getDrawPoolItemDOS(),
                context.getDrawParam().getTimes(),
                Boolean.TRUE);
        context.setPrizeItemList(drawPoolItemDTOList);
    }


    @Override
    protected void doCallback(DrawContext context) {
        DrawParam drawParam = context.getDrawParam();

        // 埋点
        for (DrawPoolItemDTO drawPoolItemDTO : context.getPrizeItemList()) {
            DrawPoolItemDO drawPoolItemDO = drawPoolItemDTO.getDrawPoolItemDO();
            Long awardAmount = drawPoolItemDO.getItemValueGold() * drawPoolItemDTO.getTargetTimes();
            // 一次抽奖多个参数分别传递
            if (Objects.nonNull(drawPoolItemDO.getItemType()) && PrizeTypeEnum.PRIZE_DRESS.getCode().equals(drawPoolItemDO.getItemType())) {
                // 头像框埋点价值处理
                awardAmount = 0L;
            }
            trackManager.allActivityLottery(drawParam.getUid(), drawParam.getPoolCode(), drawPoolItemDO.getItemKey(), awardAmount, drawPoolItemDTO.getTargetTimes());
        }
    }

}
