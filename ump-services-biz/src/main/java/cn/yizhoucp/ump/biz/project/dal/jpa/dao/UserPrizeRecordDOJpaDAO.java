package cn.yizhoucp.ump.biz.project.dal.jpa.dao;

import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.UserPrizeRecordDO;
import cn.yizhoucp.ms.core.base.enums.UserPackageBizType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import javax.transaction.Transactional;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 福袋
 *
 * @author: lianghu
 */
public interface UserPrizeRecordDOJpaDAO extends CrudRepository<UserPrizeRecordDO, Long> {

    @Transactional
    @Query(value = "SELECT * FROM lucky_bag_user_record WHERE prize_coin >= 500 and prize_biz_type = 'GIFT' order by id desc limit 60",nativeQuery = true)
    List<UserPrizeRecordDO> findPrizeShowPrize();


    /**
     * 分页查询抽奖记录
     * @param userId
     * @param activity
     * @param prizeBizType
     * @param pageable
     * @return
     */
    Page<UserPrizeRecordDO> findByUserIdAndActivityIdAndPrizeBizTypeOrderByIdDesc(Long userId, Long activity, UserPackageBizType prizeBizType, Pageable pageable);

    Page<UserPrizeRecordDO> findByUserIdOrderByIdDesc(Long userId, Pageable pageable);

    /**
     * 记录用户奖池抽奖的总金币数量
     * @param uid
     * @param appId
     * @param prizeBizType
     * @return
     */
    @Transactional
    @Query(value = "select sum(prize_coin*prize_num) from lucky_bag_user_record where user_id = ?1 and app_id = ?2 and prize_biz_type = ?3", nativeQuery = true)
    Integer findSumCoinByUserIdAndAppIdAndType(Long uid, Long appId,UserPackageBizType prizeBizType);

    @Query(value = "select user_id as userId,sum(prize_coin*prize_num) as num from lucky_bag_user_record where user_id in ?1 and create_time between ?2 and ?3 group by userId", nativeQuery = true)
    List<Map<String,Object>> getUserPriceTotalCoin(List<Long> userIds, String start, String end);
}
