package cn.yizhoucp.ump.biz.project.biz.enums.activity.familyKey;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 家族隐藏任务
 *
 * @author: lianghu
 */
@Getter
@AllArgsConstructor
public enum FamilyTaskEnum {

    TASK_1("JYS_GIFT", "金钥匙", 20, 50),
    TASK_2("JYS_GIFT", "金钥匙", 40, 100),
    TASK_3("JYS_GIFT", "金钥匙", 60, 200),
    TASK_4("JYS_GIFT", "金钥匙", 80, 300),
    TASK_5("JYS_GIFT", "金钥匙", 100, 400),
    ;

    private String prizeKey;
    private String prizeName;
    private Integer prizeNum;
    private Integer personNum;

    public static FamilyTaskEnum getBelongInstance(Integer personNum) {
        FamilyTaskEnum result = null;
        for (FamilyTaskEnum item : values()) {
            if (personNum >= item.getPersonNum()) {
                result = item;
            }
        }
        return result;
    }

}
