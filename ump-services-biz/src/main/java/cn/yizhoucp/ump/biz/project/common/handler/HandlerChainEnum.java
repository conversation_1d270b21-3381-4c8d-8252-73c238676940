package cn.yizhoucp.ump.biz.project.common.handler;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum HandlerChainEnum {

    QUERY_AD_RESOURCE("queryAdResourceChain", "获取资源位处理链", "queryAdResourceChain"),
    EXIT_ACTIVITY("exitActivityChain", "退出活动处理链", "exitActivityChain"),
    JOIN_ACTIVITY("joinActivityChain", "加入活动处理链", "joinActivityChain"),
    PUSH_POP("pushPopChain", "推送弹窗处理链", "pushPopChain"),
    QUERY_RANK("queryRank", "查榜单后置处理链", "queryRank"),
    ;
    private String code;
    private String desc;
    private String configKey;

}
