package cn.yizhoucp.ump.biz.project.biz.manager.activity.togetherIn2024;

import cn.hutool.core.net.URLEncoder;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.util.AppUtil;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.order.api.client.NormalOrderFeignService;
import cn.yizhoucp.order.api.vo.OrderVO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.appAdSpace.AppAdSpaceVO;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.enums.pop.IdempotentTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.appAdSpace.AppAdSpaceManager;
import cn.yizhoucp.ump.biz.project.biz.manager.officialCombine.OfficialCombineCommonManager;
import cn.yizhoucp.ump.biz.project.biz.manager.pop.PopManager;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.FeignUserService;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.LoginPopDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dal.service.ScenePrizeService;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.TogetherIn2024Constant.ACTIVITY_CODE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.TogetherIn2024Constant.COUNT_DOWN;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.TogetherIn2024Constant.COUNT_DOWN_LEFT_TIMES;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.TogetherIn2024Constant.RANK;

@Service
@Slf4j
public class TogetherIn2024BizManager {

    @Resource
    private ScenePrizeService scenePrizeService;
    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private NotifyComponent notifyComponent;
    @Resource
    private AppAdSpaceManager appAdSpaceManager;
    @Resource
    private RedisManager redisManager;
    @Resource
    private NormalOrderFeignService normalOrderFeignService;
    @Resource
    private TogetherIn2024RankManager togetherIn2024RankManager;
    @Resource
    private PopManager popManager;
    @Resource
    private FeignUserService feignUserService;

    @ActivityCheck(activityCode = ACTIVITY_CODE, isThrowException = false)
    public void sendPrizeByOrderPayPrice(Long uid, Long toUid, Long orderId) {
        log.info("uid {} orderPrice {}", uid, orderId);
        if (uid == null || orderId == null) {
            return;
        }

        OrderVO orderVO = normalOrderFeignService.getOrder(orderId).successData();
        log.info("orderVO {}", JSON.toJSONString(orderVO));
        if (orderVO == null) {
            return;
        }
        Long orderPrice = orderVO.getOrderPrice();

        List<ScenePrizeDO> scenePrizeDOList = scenePrizeService.getListBySceneCode(ACTIVITY_CODE, "gradient_prize");
        log.info("scenePrizeDOList {}", JSON.toJSONString(scenePrizeDOList));
        if (CollectionUtils.isEmpty(scenePrizeDOList)) {
            return;
        }

        for (ScenePrizeDO scenePrizeDO : scenePrizeDOList) {
            if (orderPrice >= scenePrizeDO.getNeedCount()) {
                sendPrizeManager.sendPrize(
                        BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(uid).build(),
                        Collections.singletonList(SendPrizeDTO.of(scenePrizeDO, uid))
                );

                String url = null;
                String msg;
                if (orderPrice >= 131400) {
                    url = this.getPopUrl(uid, toUid, "sweet");
                    msg = "亲爱的有情人，恭喜您参与“2024在一起”活动并完成官宣，您可获得一个%s金币的礼物%s和一张专属的甜蜜爱情签作为奖励，奖励已下发，请注意查收~";
                } else if (orderPrice >= 52000) {
                    url = this.getPopUrl(uid, toUid, "romantic");
                    msg = "亲爱的有情人，恭喜您参与“2024在一起”活动并完成官宣，您可获得一个%s金币的礼物%s和一张专属的浪漫爱情签作为奖励，奖励已下发，请注意查收~";
                } else {
                    msg = "亲爱的有情人，恭喜您参与“2024在一起”活动并完成官宣，您可获得一个%s金币的礼物%s作为奖励，奖励已下发，请注意查收~";
                }
                if (url != null) {
                    popManager.popByLoginPopDO(ServicesAppIdEnum.lanling.getAppId(), MDCUtil.getCurUnionIdByMdc(), uid, LoginPopDO.builder()
                            .unionId(MDCUtil.getCurUnionIdByMdc())
                            .idempotentType(IdempotentTypeEnum.NONE.name())
                            .pushType("h5dialog")
                            .url(url)
                            .build());
                }
                notifyComponent.npcNotify(
                        ServicesAppIdEnum.lanling.getUnionId(),
                        uid,
                        String.format(msg, scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeDesc())
                );

                break;
            }
        }
    }

    @ActivityCheck(activityCode = ACTIVITY_CODE, isThrowException = false)
    public void addCountDownTimes(Long uid, Long toUid) {
        redisManager.incrLong(String.format(COUNT_DOWN_LEFT_TIMES, AppUtil.splicUserId(uid, toUid)), 1L, DateUtil.ONE_MONTH_SECOND);
    }

    @ActivityCheck(activityCode = ACTIVITY_CODE, isThrowException = false)
    public Boolean sendGiftHandle(BaseParam param, List<CoinGiftGivedModel> coinGiftGivedModels) {
        for (CoinGiftGivedModel coinGiftGivedModel : coinGiftGivedModels) {
            // 活动期间官宣
            Integer countDownLeftTimes = redisManager.getInteger(String.format(COUNT_DOWN_LEFT_TIMES, AppUtil.splicUserId(coinGiftGivedModel.getFromUid(), coinGiftGivedModel.getToUid())));
            if (countDownLeftTimes == null) {
                continue;
            }

            // 官宣礼物
            List<OrderVO> orderVOList = normalOrderFeignService.batchQueryOrder(JSONObject.toJSONString(coinGiftGivedModel.getUserPrizeRecordIdList())).successData();
            if (CollectionUtils.isNotEmpty(orderVOList)) {
                if (countDownLeftTimes > 0) {
                    redisManager.decrLong(String.format(COUNT_DOWN_LEFT_TIMES, AppUtil.splicUserId(coinGiftGivedModel.getFromUid(), coinGiftGivedModel.getToUid())), 1L, DateUtil.ONE_MONTH_SECOND);

                    long countDownTime = LocalDateTime.now().plusMinutes(15).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    redisManager.setnx(String.format(COUNT_DOWN, coinGiftGivedModel.getFromUid()), countDownTime, 15 * DateUtil.ONE_MINUTE_SECONDS);
                    redisManager.setnx(String.format(COUNT_DOWN, coinGiftGivedModel.getToUid()), countDownTime, 15 * DateUtil.ONE_MINUTE_SECONDS);

                    appAdSpaceManager.refreshUserAdSpace(param);
                    appAdSpaceManager.refreshUserAdSpace(BaseParam.builder().appId(param.getAppId()).uid(coinGiftGivedModel.getToUid()).build());

                    popManager.popByLoginPopDO(ServicesAppIdEnum.lanling.getAppId(), param.getUnionId(), param.getUid(), LoginPopDO.builder()
                            .unionId(param.getUnionId())
                            .idempotentType(IdempotentTypeEnum.NONE.name())
                            .pushType("h5dialog")
                            .url(String.format("guanxuan-countdown?uid=%s&from=h5dialog", param.getUid()))
                            .build());
                }
            }

            if (Boolean.TRUE.equals(redisManager.hasKey(String.format(COUNT_DOWN, coinGiftGivedModel.getFromUid())))) {
                togetherIn2024RankManager.incrRankValue(AppUtil.splicUserId(coinGiftGivedModel.getFromUid(), coinGiftGivedModel.getToUid()), 5 * coinGiftGivedModel.getCoin() * coinGiftGivedModel.getProductCount(), RANK);
            } else {
                togetherIn2024RankManager.incrRankValue(AppUtil.splicUserId(coinGiftGivedModel.getFromUid(), coinGiftGivedModel.getToUid()), coinGiftGivedModel.getCoin() * coinGiftGivedModel.getProductCount(), RANK);
            }
        }

        return Boolean.TRUE;
    }

    private String getPopUrl(Long uid, Long toUid, String type) {
        UserVO user = feignUserService.getBasic(uid, 1L).successData();
        UserVO toUser = feignUserService.getBasic(toUid, 1L).successData();
        String avatar = Optional.of(user).map(UserVO::getAvatar).orElse("");
        String toAvatar = Optional.of(toUser).map(UserVO::getAvatar).orElse("");

        return String.format("guanxuan-card?uid=%s&from=h5dialog&male=%s&female=%s&type=%s", uid, URLEncoder.ALL.encode(avatar, StandardCharsets.UTF_8), URLEncoder.ALL.encode(toAvatar, StandardCharsets.UTF_8), type);
    }

}
