package cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.drawStrategy;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.KeyWeight;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 大小奖池抽奖
 * 1. 大奖池采用hash结构
 *     poolCode 作为hash Key
 *     subId 作为item key
 *     公共奖池默认subId为 0
 * 2. 小奖池采用set结构
 *      采用普通抽奖策略
 */
@Slf4j
@Service
public class BigSmallPoolLotteryStrategy extends BaseLotteryWithPool{

    public static final String BIG_POOL_LOTTERY_KEY = "BIG_POOL_LOTTERY_KEY_%s";

    public static final String LOCK_POOL_CODE_KEY = "LOCK_POOL_CODE_KEY_%s";

    public static final Long DEFAULT_SUB_ID = 0L;

    @Resource
    private RedisManager redisManager;

    @Resource
    private RedissonClient redissonClient;

    public List<String> drawBySmallPool(String poolCode, Integer initSize, Integer drawTimes, List<KeyWeight> originPool) {
        return drawBySmallPool(poolCode, initSize, drawTimes, DEFAULT_SUB_ID, originPool);
    }

    public List<String> drawBySmallPool(String poolCode, Integer initSize, Integer drawTimes, Long subId, List<KeyWeight> originPool) {
        if (drawTimes <= 0) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "抽奖次数不能小于1");
        }
        String smallPoolCode = generateSmallPoolCode(poolCode, subId);
        List<String> result = super.drawTimes(drawTimes, smallPoolCode, originPool);
        log.debug("小奖池抽奖结果 -> {}", result);
        // 如果小奖池抽出来的数量不足, 初始化小奖池
        if (result.size() < drawTimes) {
            this.initSmallPool(poolCode, initSize, subId, originPool);
            // 再抽 drawTimes - result.size() 次
            result.addAll(drawBySmallPool(poolCode, initSize, drawTimes - result.size(), subId, originPool));
        }
        return result;
    }

    public List<String> drawByBigPool(String poolCode, Integer drawTimes, Long subId, List<KeyWeight> originPool) {
        RLock lock = redissonClient.getLock(String.format(LOCK_POOL_CODE_KEY, poolCode + subId));
        try {
            lock.lock();
            List<String> reduceKeyList = this.getAndReduceByBigPool(poolCode, drawTimes, subId, originPool);
            if (CollectionUtils.isEmpty(reduceKeyList)) {
                throw new RuntimeException("奖池为空或不存在，抽奖失败");
            }
            if (reduceKeyList.size() < drawTimes) {
                reduceKeyList.addAll(drawByBigPool(poolCode, drawTimes - reduceKeyList.size(), subId, originPool));
            }
            return reduceKeyList;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 初始化小奖池
     *
     * @param poolCode
     * @param initSize
     * @param subId
     * @param originPool
     */
    public List<String> initSmallPool(String poolCode, Integer initSize, Long subId, List<KeyWeight> originPool) {
        if (initSize <= 0) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "奖池数量不足，奖池初始化大小必须大于0");
        }
        log.debug("init Small Pool, poolCode: {}, initSize: {}, subId: {}", poolCode, initSize, subId);
        RLock lock = redissonClient.getLock(String.format(LOCK_POOL_CODE_KEY, poolCode + subId));
        try {
            lock.lock();
            String smallPoolKey = getSmallPoolCacheKey(generateSmallPoolCode(poolCode, subId));
            if (redisManager.hasKey(smallPoolKey) || redisManager.sGetSetSize(smallPoolKey) > 0) {
                return new ArrayList<>();
            }
            List<String> reduceKeyList = this.getAndReduceByBigPool(poolCode, initSize, subId, originPool);
            log.debug("从大奖池中扣减, reduceKeyList: {}", reduceKeyList);
            return super.putToPool(generateSmallPoolCode(poolCode, subId), reduceKeyList);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 从大奖池中抽取并扣减initSize个元素
     *
     * @param poolCode
     * @param initSize
     * @param subId
     * @param originPool
     * @return
     */
    public List<String> getAndReduceByBigPool(String poolCode, Integer initSize, Long subId, List<KeyWeight> originPool) {
        List<KeyWeight> poolItemList;
        String cacheKey = getBigPoolCacheKey(poolCode);
        // 从缓存中获取奖池配置
        Object poolJson = redisManager.hget(cacheKey, String.valueOf(subId));
        if (StringUtils.isEmpty(poolJson)) {
            poolItemList = originPool;
        } else {
            poolItemList = JSONObject.parseArray(poolJson.toString(), KeyWeight.class);
        }

        int lastCount = poolItemList.stream().mapToInt(KeyWeight::getWeight).sum();
        if (lastCount <= 0) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "奖池数量不足");
        }
        List<String> reduceKeyList = new ArrayList<>();
        if (lastCount <= initSize) {
            for (KeyWeight keyWeight : poolItemList) {
                String itemKey = keyWeight.getKey();
                for (int i = 0; i < keyWeight.getWeight(); i++) {
                    reduceKeyList.add(itemKey);
                }
            }
            redisManager.hdel(cacheKey, String.valueOf(subId));
        } else {
            long startTime = System.currentTimeMillis();
            log.info("开始不放回抽奖, stratTime -> {}", startTime);
            List<String> keys = super.randomGetCountKeysBaseOnWeight(poolItemList, initSize, true);
            log.info("不放回抽奖结束，开始 -> {},  耗时 -> {} ms", startTime, System.currentTimeMillis() - startTime);
            Map<String, KeyWeight> keyKeyWeight = poolItemList.stream().collect(Collectors.toMap(KeyWeight::getKey, Function.identity()));
            for (String key : keys) {
                reduceKeyList.add(key);
                KeyWeight keyWeight = keyKeyWeight.get(key);
                keyWeight.setWeight(keyWeight.getWeight() - 1);
                if (keyWeight.getWeight() <= 0) {
                    keyKeyWeight.remove(key);
                }
            }
            List<KeyWeight> poolItemListNew = new ArrayList<>(keyKeyWeight.values());
            redisManager.hset(cacheKey, String.valueOf(subId), JSONObject.toJSONString(poolItemListNew), 7 * DateUtil.ONE_DAY_SECOND);
        }
        return reduceKeyList;
    }

    private String generateSmallPoolCode(String poolCode, Long subId) {
        return poolCode + subId;
    }

    private String getBigPoolCacheKey(String poolCode) {
        return String.format(BIG_POOL_LOTTERY_KEY, poolCode);
    }

    @Override
    public void clearPool(String poolCode) {
        // 删除大奖池
        redisManager.delete(getBigPoolCacheKey(poolCode));

        // 删除小奖池
        super.clearPool(poolCode + DEFAULT_SUB_ID);
    }
}
