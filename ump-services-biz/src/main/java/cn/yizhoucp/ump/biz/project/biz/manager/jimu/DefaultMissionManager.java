package cn.yizhoucp.ump.biz.project.biz.manager.jimu;

import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.ump.api.param.jimu.QueryMissionParam;
import cn.yizhoucp.ump.api.vo.jimu.task.TakePrizeParam;
import cn.yizhoucp.ump.api.vo.jimu.task.TakePrizeReturn;
import cn.yizhoucp.ump.api.vo.jimu.task.TaskVO;
import cn.yizhoucp.ump.biz.project.biz.enums.MissionCycleTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.enums.MissionDistributeTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.enums.MissionFinishTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.MissionContext;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.param.MissionParam;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.biz.manager.userActivity.ActivityManager;
import cn.yizhoucp.ump.biz.project.common.checker.CheckerChainEnum;
import cn.yizhoucp.ump.biz.project.common.checker.CheckerChainFactory;
import cn.yizhoucp.ump.biz.project.common.checker.CheckerContext;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ActivityMissionRecordJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.PrizeItemJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ActivityDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ActivityMissionDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ActivityMissionRecordDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.PrizeItemDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
public class DefaultMissionManager implements MissionManager {

    @Resource
    private CheckerChainFactory checkerChainFactory;
    @Resource
    private PrizeItemJpaDAO prizeItemJpaDAO;
    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private ActivityMissionRecordJpaDAO activityMissionRecordJpaDAO;
    @Resource
    private ActivityManager activityManager;

    @Override
    public TaskVO getMissionList(QueryMissionParam param) {
        return null;
    }

    @Override
    public TakePrizeReturn takePrize(TakePrizeParam param) {
        return null;
    }

    @Override
    public Boolean process(MissionParam param) {
        log.debug("process uid:{}, taskCode:{}", param.getUid(), param.getTaskCode());

        MissionContext context = MissionContext.builder().missionParam(param).build();

        // 任务初始化检查
        init(context);

        // 完成验证
        if (!check(context)) {
            return Boolean.FALSE;
        }

        // 完成处理
        completedProcess(context);
        return Boolean.TRUE;
    }

    protected Boolean init(MissionContext context) {
        return true;
    }

    private Boolean check(MissionContext context) {
        MissionParam missionParam = context.getMissionParam();
        BaseParam baseParam = missionParam.getBaseParam();
        JSONObject bizParam = Optional.ofNullable(missionParam.getBizParam()).orElse(new JSONObject());

        // 走子类业务验证
        if (!doCheck(context)) {
            return Boolean.FALSE;
        }

        // 验证链
        if (Objects.nonNull(missionParam.getType()) && missionParam.getType().getHasChecker()) {
            return checkerChainFactory.getChainByType(CheckerChainEnum.PROCESS_MISSION).check(CheckerContext.builder()
                            .chainEnum(CheckerChainEnum.PROCESS_MISSION)
                            .missionExt(JSON.parseObject(context.getMissionParam().getBizParam()
                                    .getObject("missionConfig", ActivityMissionDO.class).getExtData())).build(),
                    context.getMissionParam().getBizParam());
        }
        return Boolean.TRUE;
    }

    protected Boolean doCheck(MissionContext context) {
        log.debug("context: {}", context);

        MissionParam missionParam = context.getMissionParam();
        if (missionParam == null) {
            return false;
        }
        JSONObject bizParam = missionParam.getBizParam();
        if (bizParam == null) {
            return false;
        }
        ActivityMissionDO activityMissionDO = (ActivityMissionDO) bizParam.get("missionConfig");
        if (activityMissionDO == null) {
            return false;
        }
        JSONArray coinGiftGivedModels = bizParam.getJSONArray("coinGiftGivedModels");
        if (CollectionUtils.isEmpty(coinGiftGivedModels)) {
            return false;
        }

        ActivityMissionRecordDO activityMissionRecordDO = activityMissionRecordJpaDAO.getByUserIdAndMissionCodeAndDeadlineLimit1(missionParam.getUid(), activityMissionDO.getCode(), getMissionDeadline(activityMissionDO));
        // 查询活动任务记录
        if (activityMissionRecordDO == null) {
            activityMissionRecordDO = initActivityMissionRecord(missionParam.getBaseParam(), activityMissionDO);
            if (activityMissionRecordDO.getDeadline() == null) {
                return false;
            }
        }

        List<CoinGiftGivedModel> coinGiftGivedModelList = coinGiftGivedModels.toJavaList(CoinGiftGivedModel.class);
        long productCount = 0L;
        switch (MissionFinishTypeEnum.getInstance(activityMissionDO.getFinishType())) {
            case SEND:
                for (CoinGiftGivedModel coinGiftGivedModel : coinGiftGivedModelList) {
                    if (!activityMissionDO.getGiftKey().equals(coinGiftGivedModel.getGiftKey())) {
                        continue;
                    }
                    // 礼物数量未达到要求
                    if (activityMissionDO.getGiftCount() > coinGiftGivedModel.getProductCount()) {
                        return false;
                    }
                    // 完成次数超过上限
                    if (activityMissionRecordDO.getCompleteTimes() >= activityMissionDO.getLimitTimes()) {
                        return false;
                    }
                    // 完成次数累加并保存
                    activityMissionRecordDO.setCompleteTimes(activityMissionRecordDO.getCompleteTimes() + 1);
                    activityMissionRecordJpaDAO.save(activityMissionRecordDO);
                    return true;
                }
                break;
            case RECEIVE:
                for (CoinGiftGivedModel coinGiftGivedModel : coinGiftGivedModelList) {
                    if (!activityMissionDO.getGiftKey().equals(coinGiftGivedModel.getGiftKey())) {
                        continue;
                    }
                    // 礼物数量未达到要求
                    if (activityMissionDO.getGiftCount() > coinGiftGivedModel.getProductCount()) {
                        return false;
                    }
                    // 完成次数超过上限
                    if (activityMissionRecordDO.getPrizeSent() == 1 && activityMissionRecordDO.getCompleteTimes() >= activityMissionDO.getLimitTimes()) {
                        return false;
                    }
                    // 完成次数累加并保存
                    activityMissionRecordDO.setCompleteTimes(activityMissionRecordDO.getCompleteTimes() + 1);
                    activityMissionRecordJpaDAO.save(activityMissionRecordDO);
                    return true;
                }
                break;
            case SEND_OR_RECEIVE:
                for (CoinGiftGivedModel coinGiftGivedModel : coinGiftGivedModelList) {
                    if (!activityMissionDO.getGiftKey().equals(coinGiftGivedModel.getGiftKey())) {
                        continue;
                    }
                    // 完成次数超过上限
                    if (activityMissionRecordDO.getCompleteTimes() >= activityMissionDO.getLimitTimes()) {
                        return false;
                    }
                    activityMissionRecordDO.setCurrentTimes(activityMissionRecordDO.getCurrentTimes() + coinGiftGivedModel.getProductCount());
                    if (activityMissionRecordDO.getCurrentTimes() >= activityMissionDO.getGiftCount()) {
                        activityMissionRecordDO.setCompleteTimes(activityMissionRecordDO.getCompleteTimes() + 1);
                        activityMissionRecordJpaDAO.save(activityMissionRecordDO);
                        return true;
                    } else {
                        activityMissionRecordJpaDAO.save(activityMissionRecordDO);
                        return false;
                    }
                }
                break;
            case DRAW:
                for (CoinGiftGivedModel coinGiftGivedModel : coinGiftGivedModelList) {
                    if (!activityMissionDO.getGiftKey().equals(coinGiftGivedModel.getLotteryGiftKey())) {
                        continue;
                    }
                    productCount += coinGiftGivedModel.getProductCount();
                }
                // 完成次数超过上限
                if (activityMissionRecordDO.getCompleteTimes() >= activityMissionDO.getLimitTimes()) {
                    return false;
                }
                activityMissionRecordDO.setCurrentTimes(activityMissionRecordDO.getCurrentTimes() + productCount);
                if (activityMissionRecordDO.getCurrentTimes() >= activityMissionDO.getGiftCount()) {
                    activityMissionRecordDO.setCompleteTimes(activityMissionRecordDO.getCompleteTimes() + 1);
                    activityMissionRecordJpaDAO.save(activityMissionRecordDO);
                    return true;
                } else {
                    activityMissionRecordJpaDAO.save(activityMissionRecordDO);
                    return false;
                }
            case GIFT_UNIT_PRICE:
                for (CoinGiftGivedModel coinGiftGivedModel : coinGiftGivedModelList) {
                    if (activityMissionDO.getGiftCount() > coinGiftGivedModel.getCoin()) {
                        continue;
                    }
                    productCount += coinGiftGivedModel.getProductCount();
                }
                if (productCount == 0L) {
                    return false;
                }
                // 完成次数超过上限
                if (activityMissionRecordDO.getPrizeSent() == 1 && activityMissionRecordDO.getCompleteTimes() >= activityMissionDO.getLimitTimes()) {
                    return false;
                }
                // 完成次数累加并保存
                activityMissionRecordDO.setCompleteTimes(activityMissionRecordDO.getCompleteTimes() + 1);
                activityMissionRecordJpaDAO.save(activityMissionRecordDO);
                return true;
            case GIFT_TOTAL_PRICE:
                long totalPrice = 0L;
                for (CoinGiftGivedModel coinGiftGivedModel : coinGiftGivedModelList) {
                    totalPrice += coinGiftGivedModel.getCoin();
                }
                if (totalPrice < activityMissionDO.getGiftCount()) {
                    return false;
                }
                // 完成次数超过上限
                if (activityMissionRecordDO.getPrizeSent() == 1 && activityMissionRecordDO.getCompleteTimes() >= activityMissionDO.getLimitTimes()) {
                    return false;
                }
                // 完成次数累加并保存
                activityMissionRecordDO.setCompleteTimes(activityMissionRecordDO.getCompleteTimes() + 1);
                activityMissionRecordJpaDAO.save(activityMissionRecordDO);
                log.debug("activityMissionRecordDO: {}", activityMissionRecordDO);
                return true;
            default:
                break;
        }

        return false;
    }

    protected Boolean completedProcess(MissionContext context) {
        return sendPrize(context);
    }

    protected Boolean sendPrize(MissionContext context) {
        log.debug("context: {}", context);

        MissionParam missionParam = context.getMissionParam();
        if (missionParam == null) {
            return false;
        }
        JSONObject bizParam = missionParam.getBizParam();
        if (bizParam == null) {
            return false;
        }
        ActivityMissionDO activityMissionDO = (ActivityMissionDO) bizParam.get("missionConfig");
        if (activityMissionDO == null) {
            return false;
        }
        if (MissionDistributeTypeEnum.MANUAL.getCode().equals(activityMissionDO.getDistributeType())) {
            return false;
        }

        List<PrizeItemDO> prizeItemDOList = prizeItemJpaDAO.getByMissionId(activityMissionDO.getId());
        if (CollectionUtils.isEmpty(prizeItemDOList)) {
            return false;
        }
        context.setPrizeItemDOList(prizeItemDOList);
        ActivityMissionRecordDO activityMissionRecordDO = activityMissionRecordJpaDAO.getByUserIdAndMissionCodeAndDeadlineLimit1(missionParam.getUid(), activityMissionDO.getCode(), getMissionDeadline(activityMissionDO));
        if (activityMissionRecordDO == null) {
            return false;
        }
        if (activityMissionRecordDO.getPrizeSent() == 1) {
            return true;
        }

        activityMissionRecordDO.setPrizeSent(1);
        activityMissionRecordJpaDAO.save(activityMissionRecordDO);
        log.info("下发奖励 context: {}", context);

        return sendPrizeManager.sendPrize(missionParam.getBaseParam(), prizeItemDOList.stream().map(prizeItemDO -> SendPrizeDTO.of(prizeItemDO, getActivityCode())).collect(Collectors.toList()));
    }

    protected String getMissionDeadline(ActivityMissionDO activityMissionDO) {
        String deadline = null;
        if (activityMissionDO == null) {
            return deadline;
        }
        switch (MissionCycleTypeEnum.getInstance(activityMissionDO.getCycleType())) {
            case DAY:
                deadline = DateUtil.format(new Date(), DateUtil.YMD_WITHOUT_LINE);
                break;
            case WEEK:
                deadline = DateUtil.getStartDateOfWeek(new Date(), DateUtil.YMD_WITHOUT_LINE);
                break;
            case MONTH:
                deadline = DateUtil.format(new Date(), DateUtil.YM_WITHOUT_LINE);
                break;
            case YEAR:
                deadline = DateUtil.format(new Date(), DateUtil.YEAR_WITH_LINE);
                break;
            case ACTIVITY:
                deadline = MissionCycleTypeEnum.ACTIVITY.getCode();
                break;
            default:
                break;
        }
        return deadline;
    }

    private ActivityMissionRecordDO initActivityMissionRecord(BaseParam baseParam, ActivityMissionDO activityMissionDO) {
        ActivityMissionRecordDO activityMissionRecordDO = new ActivityMissionRecordDO();
        activityMissionRecordDO.setAppId(baseParam.getAppId());
        activityMissionRecordDO.setUnionId(baseParam.getUnionId());
        activityMissionRecordDO.setUserId(baseParam.getUid());
        activityMissionRecordDO.setCurrentTimes(0L);
        activityMissionRecordDO.setCompleteTimes(0L);
        activityMissionRecordDO.setCycleType(activityMissionDO.getCycleType());

        ActivityDO activityDO = activityManager.getOnlineActivityInfo(baseParam, activityMissionDO.getBelongActivityCode());
        if (activityDO != null) {
            switch (MissionCycleTypeEnum.getInstance(activityMissionRecordDO.getCycleType())) {
                case DAY:
                    activityMissionRecordDO.setDeadline(DateUtil.format(new Date(), DateUtil.YMD_WITHOUT_LINE));
                    break;
                case WEEK:
                    activityMissionRecordDO.setDeadline(DateUtil.getStartDateOfWeek(new Date(), DateUtil.YMD_WITHOUT_LINE));
                    break;
                case MONTH:
                    activityMissionRecordDO.setDeadline(DateUtil.format(new Date(), DateUtil.YM_WITHOUT_LINE));
                    break;
                case YEAR:
                    activityMissionRecordDO.setDeadline(DateUtil.format(new Date(), DateUtil.YEAR_WITH_LINE));
                    break;
                case ACTIVITY:
                    activityMissionRecordDO.setDeadline(MissionCycleTypeEnum.ACTIVITY.getCode());
                    break;
                default:
                    break;
            }
        }

        activityMissionRecordDO.setPrizeSent(0);
        activityMissionRecordDO.setMissionId(activityMissionDO.getId());
        activityMissionRecordDO.setMissionCode(activityMissionDO.getCode());
        return activityMissionRecordDO;
    }

}
