package cn.yizhoucp.ump.biz.project.biz.manager.activity.familyCompetition;

import cn.yizhoucp.ump.api.vo.jimu.draw.inner.DrawLogItem;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.AbstractDrawTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawContext;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawLogParam;
import org.springframework.stereotype.Service;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawLogDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class FamilyCompetitionDrawManager extends AbstractDrawTemplate {
    @Override
    protected void resourceCheck(DrawContext context) {

    }

    @Override
    protected void draw(DrawContext context) {

    }

    @Override
    protected void deductResource(DrawContext context) {

    }

    @Override
    protected void doCallback(DrawContext context) {

    }

    @Override
    protected List<DrawLogItem> drawLogWrapper(DrawLogParam param, List<DrawLogDO> drawLogDOList) {
        List<DrawLogItem> drawLogItemList = drawLogDOList.stream().map(drawLogDO -> {
            DrawPoolItemDTO drawPoolItemDTO = JSON.parseObject(drawLogDO.getLogJson(), DrawPoolItemDTO.class);
            DrawPoolItemDO drawPoolItemDO = drawPoolItemDTO.getDrawPoolItemDO();
            return DrawLogItem.builder()
                    .icon(drawPoolItemDO.getItemIcon())
                    .valueGold(drawPoolItemDO.getItemValueGold())
                    .text(drawPoolItemDO.getItemName())
                    .itemNum(drawPoolItemDO.getItemNum() * drawPoolItemDTO.getTargetTimes())
                    .time(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm").format(drawLogDO.getCreateTime())).build();
        }).collect(Collectors.toList());
        log.debug("drawLogItemList {}", JSON.toJSONString(drawLogItemList));
        Collections.reverse(drawLogItemList);
        return drawLogItemList;
    }
}
