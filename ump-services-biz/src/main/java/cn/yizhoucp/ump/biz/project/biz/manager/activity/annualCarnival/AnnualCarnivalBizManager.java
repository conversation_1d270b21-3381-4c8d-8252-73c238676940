package cn.yizhoucp.ump.biz.project.biz.manager.activity.annualCarnival;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.yizhoucp.ms.core.base.enums.GiftFrom;
import cn.yizhoucp.ms.core.base.enums.GiftWay;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.env.DynamicEnvManager;
import cn.yizhoucp.ms.core.base.util.AppUtil;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.annualCarnival.internal.AnnualCarnivalConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.annualCarnival.internal.AnnualCarnivalEnums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.annualCarnival.internal.AnnualCarnivalRedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.clientPush.ServerPushManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.manager.pop.PopManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.FeignRoomService;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityUrlUtil;
import cn.yizhoucp.ump.biz.project.common.ActivityComponent;
import cn.yizhoucp.user.manager.UserFeignManager;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class AnnualCarnivalBizManager implements ActivityComponent {

    @Resource
    private AnnualCarnivalRankManager annualCarnivalRankManager;
    @Resource
    private FeignRoomService feignRoomService;
    @Resource
    private AnnualCarnivalIndexManager annualCarnivalIndexManager;
    @Resource
    private UserFeignManager userFeignManager;
    @Resource
    private AnnualCarnivalRedisManager annualCarnivalRedisManager;
    @Resource
    private ServerPushManager serverPushManager;
    @Value("${spring.profiles.active}")
    private String env;
    @Resource
    private Environment environment;
    @Resource
    private AnnualCarnivalTrackManager annualCarnivalTrackManager;
    @Resource
    private PopManager popManager;
    @Resource
    private AnnualCarnivalDrawManager annualCarnivalDrawManager;
    @Resource
    private NotifyComponent notifyComponent;


    @Override
    public String getActivityCode() {
        return "";
    }


    @ActivityCheck(activityCode = AnnualCarnivalConstant.ACTIVITY_CODE, isThrowException = false)
    public Boolean sendGiftHandle(BaseParam param, List<CoinGiftGivedModel> coinGiftGivedModelList) {
        for (CoinGiftGivedModel coinGiftGivedModel : coinGiftGivedModelList) {
            // 面板礼物
            if (StringUtils.isEmpty(coinGiftGivedModel.getGiftWay()) || !StringUtils.equalsIgnoreCase(coinGiftGivedModel.getGiftWay(), GiftWay.NORMAL.getCode())) {
                continue;
            }
            Long currentStage = annualCarnivalIndexManager.getCurrentStage(param);
            if (currentStage >= AnnualCarnivalEnums.StageEnum.STAGE_2.getStage()) {
                //锦鲤排行榜
                koiRank(coinGiftGivedModel);
                //记录锦鲤道具收集次数
                recordCollectTimes(coinGiftGivedModel, param.getUid());

            }
            if (currentStage >= AnnualCarnivalEnums.StageEnum.STAGE_3.getStage()) {
                //争霸称王神谕之战排行榜
                warOfKingsRank(param, coinGiftGivedModel);
                //诸神黄昏
                twilightOfTheGodsRank(param, coinGiftGivedModel);
            }
        }
        return Boolean.TRUE;
    }

    @ActivityCheck(activityCode = AnnualCarnivalConstant.ACTIVITY_CODE, isThrowException = false)
    public void handleRechargeEvent(Long appId, String unionId, Long userId, JSONObject param) {
        String productType = param.getString("productType");
        Long uid = param.getLong("userId");
        if (StrUtil.isBlank(productType)) {
            return;
        }
        if (!AnnualCarnivalConstant.PRODUCT_TYPE.equals(productType)) {
            return;
        }
        Long price = param.getLong("price");
        if (price == null) {
            return;
        }
        Long amount = price / 100;
        //下发幸运号码牌
        sendLuckNumberPlate(uid, amount);
        //增加排行榜
        String rankKey = String.format(AnnualCarnivalConstant.RANK_KEY, AnnualCarnivalConstant.ACTIVITY_CODE, AnnualCarnivalEnums.RankTypeEnum.HeroicList.name());
        annualCarnivalRankManager.incrRankValue(uid, amount, rankKey);
    }

    private void sendLuckNumberPlate(Long userId, Long amount) {
        AnnualCarnivalEnums.RechargeAmountRewardEnum rechargeAmountRewardEnum = AnnualCarnivalEnums.RechargeAmountRewardEnum.getByAmount(amount);
        if (rechargeAmountRewardEnum == null) {
            return;
        }
        //增加幸运号码牌
        annualCarnivalRedisManager.incrementLuckNumberPlate(userId, rechargeAmountRewardEnum.getRewardNum());
        //发送弹窗
        //获得收集道具h5弹窗
        String url = ActivityUrlUtil.getH5BaseUrl(MDCUtil.getCurUnionIdByMdc()
                , env
                , Boolean.TRUE
                , environment.getProperty(DynamicEnvManager.GLOBAL_ENV_NAME)) + "celebration-carnival-popup" + String.format("?Num=%s", rechargeAmountRewardEnum.getRewardNum());

        serverPushManager.sendH5ServerPush(userId, url);
        annualCarnivalTrackManager.allActivityTaskFinish(userId, rechargeAmountRewardEnum.getTaskType());
    }

    private void twilightOfTheGodsRank(BaseParam param, CoinGiftGivedModel coinGiftGivedModel) {
        if (!checkChatRoomPrize(coinGiftGivedModel)) {
            return;
        }
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startTime = annualCarnivalIndexManager.getActivityStartTime(param);
        Long dayBetween = ChronoUnit.DAYS.between(startTime, now);
        String rankKey = "";
        Integer stage = annualCarnivalIndexManager.getCurrentRankStage(param).intValue();
        Long relationId = coinGiftGivedModel.getRelationId();
        if (relationId == null) {
            return;
        }
        rankKey = String.format(AnnualCarnivalConstant.RANK_KEY, AnnualCarnivalConstant.ACTIVITY_CODE, AnnualCarnivalEnums.RankTypeEnum.RagnarokList.name() + "_" + stage);
        if (stage > 0) {
            List<Long> topPerformer = getTopPerFormer(param, stage);
            if (!topPerformer.contains(coinGiftGivedModel.getRelationId())) {
                return;
            }
        }
/*
        RoomVO roomVO = feignRoomService.getRoomInfoByRoomId(relationId, ServicesAppIdEnum.lanling.getAppId()).successData();
        if (Objects.isNull(roomVO)) {
            log.warn("failed to obtain user room information procedure: to uid:{}, relationId:{}", param.getUid(), relationId);
            return;
        }
*/
        annualCarnivalRankManager.incrRankValue(relationId, coinGiftGivedModel.getCoin() * AnnualCarnivalConstant.BASE_RANK_NUM, rankKey);
    }

    private List<Long> getTopPerFormer(BaseParam param, Integer stage) {
        List<Long> topPerformer = Lists.newArrayList();
        stage = stage - 1;
        RankVO rank = annualCarnivalRankManager.getRank(RankContext.builder()
                .param(param)
                .activityCode(AnnualCarnivalConstant.ACTIVITY_CODE)
                .rankKey(String.format(AnnualCarnivalConstant.RANK_KEY, AnnualCarnivalConstant.ACTIVITY_CODE, AnnualCarnivalEnums.RankTypeEnum.RagnarokList.name() + "_" + stage))
                .rankLen(getTopCount(stage))
                .supportDiff(Boolean.TRUE)
                .type(RankContext.RankType.room)
                .build());
        for (RankItem rankItem : rank.getRankList()) {
            topPerformer.add(rankItem.getId());
        }
        return topPerformer;
    }

    private Long getTopCount(Integer stage) {
        switch (stage) {
            case 0:
                return 10L;
            case 1:
                return 6L;
            case 2:
                return 4L;
            default:
                return 0L;
        }
    }


    public Boolean checkChatRoomPrize(CoinGiftGivedModel coinGiftGivedModel) {
        if (StringUtils.isEmpty(coinGiftGivedModel.getFrom()) ||
                !StringUtils.equalsIgnoreCase(coinGiftGivedModel.getFrom(), GiftFrom.room.getCode()) && !StringUtils.equalsIgnoreCase(coinGiftGivedModel.getFrom(), GiftFrom.live_room.getCode())) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private void warOfKingsRank(BaseParam param, CoinGiftGivedModel coinGiftGivedModel) {
        if (!checkPrize(coinGiftGivedModel)) {
            return;
        }
        UserVO userVO = userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), param.getUid());
        UserVO toUserVO = userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), coinGiftGivedModel.getToUid());
        if (Objects.isNull(userVO) || Objects.isNull(toUserVO)) {
            return;
        }
        if (ObjectUtil.equals(userVO.getSex().getCode(), toUserVO.getSex().getCode())) {
            return;
        }
        String rankKey = String.format(AnnualCarnivalConstant.RANK_KEY, AnnualCarnivalConstant.ACTIVITY_CODE, AnnualCarnivalEnums.RankTypeEnum.OracleWarList.name());
        annualCarnivalRankManager.incrRankValue(AppUtil.splicUserId(param.getUid(), coinGiftGivedModel.getToUid()), coinGiftGivedModel.getCoin() * AnnualCarnivalConstant.BASE_RANK_NUM, rankKey);
        recordGiftToOppositeSex(param, coinGiftGivedModel);
    }

    private void recordGiftToOppositeSex(BaseParam param, CoinGiftGivedModel coinGiftGivedModel) {
        String rankKey = String.format(AnnualCarnivalConstant.RANK_KEY, AnnualCarnivalConstant.ACTIVITY_CODE, String.format(AnnualCarnivalConstant.RANK_KEY_GIFT_TO_OPPOSITE_SEX, param.getUid()));
        String rankKeyTo = String.format(AnnualCarnivalConstant.RANK_KEY, AnnualCarnivalConstant.ACTIVITY_CODE, String.format(AnnualCarnivalConstant.RANK_KEY_GIFT_TO_OPPOSITE_SEX, coinGiftGivedModel.getToUid()));
        annualCarnivalRankManager.incrRankValue(coinGiftGivedModel.getToUid(), coinGiftGivedModel.getCoin() * AnnualCarnivalConstant.BASE_RANK_NUM, rankKey);
        annualCarnivalRankManager.incrRankValue(param.getUid(), coinGiftGivedModel.getCoin() * AnnualCarnivalConstant.BASE_RANK_NUM, rankKeyTo);
    }

    private Boolean checkPrize(CoinGiftGivedModel coinGiftGivedModel) {
        if (StringUtils.isEmpty(coinGiftGivedModel.getFrom()) ||
                !StringUtils.equalsIgnoreCase(coinGiftGivedModel.getFrom(), GiftFrom.chat.getCode())) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private void koiRank(CoinGiftGivedModel coinGiftGivedModel) {
        if (!checkChatRoomPrize(coinGiftGivedModel)) {
            return;
        }
        AnnualCarnivalEnums.TargetRewardEnum targetRewardEnum = AnnualCarnivalEnums.TargetRewardEnum.getByTaskCode(coinGiftGivedModel.getLotteryGiftKey());
        if (targetRewardEnum == null) {
            return;
        }
        String rankKey = String.format(AnnualCarnivalConstant.RANK_KEY, AnnualCarnivalConstant.ACTIVITY_CODE, AnnualCarnivalEnums.RankTypeEnum.KoiList.name());
        annualCarnivalRankManager.incrRankValue(coinGiftGivedModel.getToUid(), coinGiftGivedModel.getProductCount(), rankKey);
    }


    private Boolean checkRoomPrize(String from) {
        if (StringUtils.isEmpty(from) ||
                !StringUtils.equalsIgnoreCase(from, GiftFrom.room.getCode()) && !StringUtils.equalsIgnoreCase(from, GiftFrom.live_room.getCode())) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private void recordCollectTimes(CoinGiftGivedModel coinGiftGivedModel, Long uid) {
        if (!checkRoomPrize(coinGiftGivedModel.getFrom())) {
            return;
        }
        String poolCode = coinGiftGivedModel.getLotteryGiftKey();
        Integer prizeNum = coinGiftGivedModel.getProductCount().intValue();
        AnnualCarnivalEnums.TargetRewardEnum target = AnnualCarnivalEnums.TargetRewardEnum.getByTaskCode(poolCode);
        if (target == null) {
            return;
        }
        //收集道具集齐次数
        Integer collectTimes = annualCarnivalRedisManager.recordCollectTimes(uid, coinGiftGivedModel.getGiftKey(), target.getTaskCode(), prizeNum);
        if (collectTimes >= AnnualCarnivalConstant.COLLECT_TIMES_LIMIT) {
            //集齐道具发送小助手消息
            sendCollectGiftMsg(uid, target);
        }
    }

    private void sendCollectGiftMsg(Long uid, AnnualCarnivalEnums.TargetRewardEnum taskEnums) {
        if (!annualCarnivalRedisManager.sendCollectGiftMsgLock(uid, taskEnums.getTaskCode())) {
            return;
        }
        String msg = String.format("亲爱的用户，您在「庆典嘉年华」活动中，收集了%s的全部礼物,快去查看吧～", taskEnums.getTaskName());
        notifyComponent.npcNotify(
                ServicesAppIdEnum.lanling.getUnionId(),
                uid,
                msg
        );
    }


}
