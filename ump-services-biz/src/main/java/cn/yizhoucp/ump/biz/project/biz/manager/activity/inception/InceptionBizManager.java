package cn.yizhoucp.ump.biz.project.biz.manager.activity.inception;

import cn.yizhoucp.ms.core.base.enums.GiftFrom;
import cn.yizhoucp.ms.core.base.enums.GiftWay;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.env.DynamicEnvManager;
import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.ms.core.vo.roomservices.RoomVO;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.manager.clientPush.ServerPushManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.FeignRoomService;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityUrlUtil;
import cn.yizhoucp.ump.biz.project.common.ActivityComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class InceptionBizManager implements ActivityComponent {

    @Resource
    private InceptionRankManager inceptionRankManager;
    @Resource
    private FeignRoomService feignRoomService;
    @Resource
    private InceptionConstant inceptionConstant;
    @Value("${spring.profiles.active}")
    private String env;
    @Resource
    private Environment environment;
    @Resource
    private ServerPushManager serverPushManager;
    @Autowired
    private InceptionTrackManager inceptionTrackManager;


    @Override
    public String getActivityCode() {
        return "";
    }

    @ActivityCheck(activityCode = InceptionConstant.ACTIVITY_CODE, isThrowException = false)
    public Boolean sendGiftHandle(BaseParam param, List<CoinGiftGivedModel> coinGiftGivedModelList) {
        for (CoinGiftGivedModel coinGiftGivedModel : coinGiftGivedModelList) {
            if (!checkPanelGift(coinGiftGivedModel)) {
                continue;
            }
            //完成任务
            finishTask(param, coinGiftGivedModel);
            //排行榜
            incrTotalRankValue(param.getUid(), coinGiftGivedModel);
            //积分
            inceptionConstant.incrementScore(param.getUid(), coinGiftGivedModel.getCoin());
        }
        return Boolean.TRUE;
    }

    private void finishTask(BaseParam param, CoinGiftGivedModel coinGiftGivedModel) {
        InceptionConstant.TaskEnum taskEnum = InceptionConstant.TaskEnum.getByGiftCode(coinGiftGivedModel.getGiftKey());
        if (taskEnum == null) {
            return;
        }
        inceptionConstant.incrementCardQuantity(param.getUid(), taskEnum.getRewardNum().longValue() * coinGiftGivedModel.getProductCount());
        String url = ActivityUrlUtil.getH5BaseUrl(MDCUtil.getCurUnionIdByMdc()
                , env
                , Boolean.TRUE
                , environment.getProperty(DynamicEnvManager.GLOBAL_ENV_NAME)) + "inception-rewards" + String.format("?num=%s", taskEnum.getRewardNum()*coinGiftGivedModel.getProductCount());
        log.info("from task sendH5ServerPush uid:{} -> url:{}, task:{}", param.getUid(), url, taskEnum.getTaskId());
        serverPushManager.sendH5ServerPush(param.getUid(), url);
        inceptionTrackManager.allActivityTaskFinish(param.getUid(),taskEnum.getTaskTrack(),taskEnum.getRewardNum());
    }

    

    private Boolean checkPanelGift(CoinGiftGivedModel coinGiftGivedModel) {
        //
        if (StringUtils.isEmpty(coinGiftGivedModel.getGiftWay()) || !StringUtils.equalsIgnoreCase(coinGiftGivedModel.getGiftWay(), GiftWay.NORMAL.getCode())) {
            return Boolean.FALSE;
        }

        // 聊天室
        if (StringUtils.isEmpty(coinGiftGivedModel.getFrom()) ||
                !StringUtils.equalsIgnoreCase(coinGiftGivedModel.getFrom(), GiftFrom.room.getCode()) && !StringUtils.equalsIgnoreCase(coinGiftGivedModel.getFrom(), GiftFrom.live_room.getCode())) {
            return Boolean.FALSE;
        }
        InceptionConstant.TaskEnum taskEnum = InceptionConstant.TaskEnum.getByGiftCode(coinGiftGivedModel.getGiftKey());
        if(taskEnum == null){
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private void incrTotalRankValue(Long uid, CoinGiftGivedModel coinGiftGivedModel) {
        Long relationId = coinGiftGivedModel.getRelationId();
        if (relationId == null) {
            return;
        }
        RoomVO roomVO = feignRoomService.getRoomInfoByRoomId(relationId, ServicesAppIdEnum.lanling.getAppId()).successData();
        if (Objects.isNull(roomVO)) {
            log.warn("failed to obtain user room information procedure: to uid:{}, relationId:{}", uid, relationId);
            return;
        }
        //总榜
        inceptionRankManager.incrRankValue(roomVO.getRoomId(), coinGiftGivedModel.getCoin(), InceptionConstant.TOTAL_RANK_KEY);
        //日榜
        inceptionRankManager.incrRankValue(roomVO.getRoomId(), coinGiftGivedModel.getCoin(), String.format(InceptionConstant.DAILY_RANK_KEY, DateUtil.getNowYyyyMMdd()));
    }


}
