package cn.yizhoucp.ump.biz.project.biz.manager.activity.astrology.carnival;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.activity.CommonProcess;
import cn.yizhoucp.ump.api.vo.activity.carnival.IndexVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.PrizeItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.doDrawStrategy.common.ProbStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dal.service.DrawPoolItemService;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.manager.activity.astrology.carnival.CarnivalConstManager.*;

@Service
@Slf4j
public class CarnivalUserManager {

    @Resource
    private CarnivalDataManager carnivalDataManager;

    @Resource
    private SendPrizeManager sendPrizeManager;

    @Resource
    private DrawPoolItemService drawPoolItemService;

    @Resource
    private ProbStrategy probStrategy;

    @Resource
    private NotifyComponent notifyComponent;

    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;

    @Resource
    private CarnivalRankManager carnivalRankManager;

    @Resource
    private CarnivalTrackManager carnivalTrackManager;

    @ActivityCheck(activityCode = "astrology_carnival_month")
    public IndexVO index() {
        BaseParam param = BaseParam.ofMDC();
        Long uid = param.getUid();
        Long userTodayDrawTimes = carnivalDataManager.getUserTodayDrawTimes(uid);
        List<Integer> luckBagOpen = new ArrayList<>(6);
        for (int i = 0; i < 6; i++) {
            luckBagOpen.add(Boolean.TRUE.equals(carnivalDataManager.isOpenLuckBag(uid, i)) ? 1 : 0);
        }
        List<CommonProcess> taskProcess = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            CommonProcess process = new CommonProcess();
            int taskNeedTimes = TASK_NEED_TIMES[i];
            process.setAllTimes(((long) taskNeedTimes));
            Long userTaskTimes = carnivalDataManager.getUserTaskTimes(uid, i);
            if (userTaskTimes < taskNeedTimes) {
                process.setCurTimes(userTaskTimes);
                process.setStatus(CommonProcess.CommonStatus.NO_SUCCESS.getCode());
            } else {
                process.setCurTimes(process.getAllTimes());
                if (carnivalDataManager.isOpenTask(uid, i)) {
                    process.setStatus(CommonProcess.CommonStatus.OPENED.getCode());
                } else {
                    process.setStatus(CommonProcess.CommonStatus.WAIT_OPEN.getCode());
                }
            }
            taskProcess.add(process);
        }

        Pair<Integer, Integer> todayIndexAndStep = getTodayIndexAndStep();
        Integer curStep = todayIndexAndStep.getFirst();
        Integer todayIndex = todayIndexAndStep.getSecond();
        List<CommonProcess> changeStepOne = getStepProcess(curStep, todayIndex, 0);
        List<CommonProcess> changeStepTwo = getStepProcess(curStep, todayIndex, 1);

        RankVO carnivalRank;
        RankVO sprintRank = null;
        Integer todayIndexInActivity = getTodayIndexInActivity();
        if (todayIndexInActivity > 10) {
            carnivalRank = carnivalRankManager.getEveryDayRank(param, eleventhDayTest != null ? eleventhDayTest : ELEVENTH_DAY);
            sprintRank = carnivalRankManager.getLastRank(param);
        } else {
            carnivalRank = carnivalRankManager.getEveryDayRank(param);
        }
        return IndexVO.builder()
                .curTimes(userTodayDrawTimes)
                .luckBagOpen(luckBagOpen)
                .task(taskProcess)
                .curStep(curStep)
                .todayIndex(todayIndex)
                .changeStepOne(changeStepOne)
                .changeStepTwo(changeStepTwo)
                .carnivalRank(carnivalRank)
                .sprintRank(sprintRank)
                .build();
    }

    private List<CommonProcess> getStepProcess(Integer curStep, Integer todayIndex, int step) {
        List<CommonProcess> result = new ArrayList<>();
        for (int i = 0; i < 7; i++) {
            CommonProcess process;
            if (curStep < step || (curStep == step && i > todayIndex)) {
                // 以后的
                process = CommonProcess.ofNoStart();
            } else {
                Long allTaskTimes = carnivalDataManager.getAllTaskTimes(step, i);
                int competeNeedTimes = LEVEL_COMPETE_NEED_TIMES[i];
                if (curStep == step && todayIndex == i) {
                    // 今天的
                    process = CommonProcess.processing(allTaskTimes, (long)competeNeedTimes);
                } else {
                    // 历史的
                    if (allTaskTimes >= competeNeedTimes) {
                        process = CommonProcess.ofOpened((long)competeNeedTimes);
                    } else {
                        process = CommonProcess.ofFinish(allTaskTimes);
                    }
                }
            }
            result.add(process);
        }
        return result;
    }

    @ActivityCheck(activityCode = "astrology_carnival_month")
    public PrizeItem openLuckBag(Integer luckBagIndex) {
        if (luckBagIndex >= LUCK_BAG_NEED_TIMES.length) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "福袋未找到");
        }
        BaseParam param = BaseParam.ofMDC();
        Long uid = param.getUid();

        Long userTodayDrawTimes = carnivalDataManager.getUserTodayDrawTimes(uid);
        if (userTodayDrawTimes < LUCK_BAG_NEED_TIMES[luckBagIndex]) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "当前不满足条件");
        }

        // 开启宝箱
        Boolean openBox = carnivalDataManager.openLuckBag(uid, luckBagIndex);

        if (!Boolean.TRUE.equals(openBox)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "宝箱剩余开启次数不足");
        }

        String poolCode = BASE_POOL_CODE + luckBagIndex;
        List<DrawPoolItemDO> drawPoolItemDOList = drawPoolItemService.getByPoolCode(poolCode);
        List<DrawPoolItemDTO> drawPoolItems = probStrategy.getDrawPoolItems(drawPoolItemDOList, 1, Boolean.TRUE);
        List<PrizeItem> prizeItemList = drawPoolItems.stream().map(DrawPoolItemDTO::convert2PrizeItem).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(prizeItemList)) {
            log.error("抽奖失败, 参数 uid -> {}, box -> {}", uid, luckBagIndex);
            throw new ServiceException(ErrorCode.SERVER_EXCEPTION, "抽奖失败");
        }
        PrizeItem prizeItem = prizeItemList.get(0);
        sendPrizeManager.sendPrize(param, prizeItemList.stream().map(p -> SendPrizeDTO.of(p, ACTIVITY_CODE)).collect(Collectors.toList()));

        notifyComponent.npcNotify(uid,
                String.format("恭喜您在占星狂欢月中获得%s金币的%s奖励，奖励已发放至您的背包，请注意查收哦～", prizeItem.getValueGold(), prizeItem.getPrizeName()));

        carnivalTrackManager.openLuckBag(uid, luckBagIndex, prizeItem);

        return prizeItem;
    }

    @ActivityCheck(activityCode = "astrology_carnival_month")
    public PrizeItem receiveAppendReward(Integer taskIndex) {
        if (taskIndex >= TASK_NEED_TIMES.length) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "任务不存在");
        }
        BaseParam param = BaseParam.ofMDC();
        Long uid = param.getUid();

        Long userTaskTimes = carnivalDataManager.getUserTaskTimes(uid, taskIndex);
        if (userTaskTimes < TASK_NEED_TIMES[taskIndex]) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "当前不满足条件");
        }

        // 开启
        Boolean openAppendReward = carnivalDataManager.receiveTask(uid, taskIndex);
        if (!Boolean.TRUE.equals(openAppendReward)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "活动期间仅可领取1次");
        }

        // 发放奖励
        List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(1L, ACTIVITY_CODE, "task-box-"+taskIndex);
        if (CollectionUtils.isEmpty(scenePrizeDOList)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "开启失败，奖励不存在");
        }
        SendPrizeDTO sendPrizeDTO = SendPrizeDTO.of(scenePrizeDOList.get(0), uid);
        sendPrizeManager.sendPrize(param, Collections.singletonList(sendPrizeDTO));

        notifyComponent.npcNotify(uid,
                String.format("恭喜您在占星狂欢月中获得%s金币的%s奖励，奖励已发放至您的背包，请注意查收哦～", sendPrizeDTO.getValueGold(), sendPrizeDTO.getPrizeName()));

        carnivalTrackManager.receiveAppendReward(uid, taskIndex, sendPrizeDTO);

        return PrizeItem.builder()
                .prizeKey(sendPrizeDTO.getPrizeValue())
                .prizeName(sendPrizeDTO.getPrizeName())
                .prizeIcon(sendPrizeDTO.getPrizeIcon())
                .valueGold(Math.toIntExact(sendPrizeDTO.getValueGold()))
                .prizeNum(sendPrizeDTO.getPrizeNum())
                .build();
    }

}
