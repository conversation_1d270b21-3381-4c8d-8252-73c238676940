package cn.yizhoucp.ump.biz.project.biz.manager.activity.lovestory;

import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.common.ActivityComponent;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class LoveStoryBizManager implements ActivityComponent {

    @Resource
    RedisManager redisManager;

    @Resource
    LoveStoryRankManager loveStoryRankManager;

    @Resource
    LoveStoryTrackManager loveStoryTrackManager;
    @Resource
    LoveStoryRedisKeyBuilder loveStoryRedisKeyBuilder;

    private static int BASE_COVERT_VALUE = 1;

    @Override
    public String getActivityCode() {
        return LoveStoryConstant.ACTIVITY_CODE;
    }

    /**
     * 爱意好礼回调
     *
     * @param param
     * @param coinGiftGivedModelList
     * @return
     */
    @ActivityCheck(activityCode = LoveStoryConstant.ACTIVITY_CODE, isThrowException = false)
    public Boolean sendGiftHandler(BaseParam param, List<CoinGiftGivedModel> coinGiftGivedModelList) {
        if (Objects.isNull(param) || CollectionUtils.isEmpty(coinGiftGivedModelList)) {
            log.warn("love_story sendGiftHandler 参数错误 param :{} coinGiftGivedModelList :{}",
                    JSONObject.toJSONString(param), JSONObject.toJSONString(coinGiftGivedModelList));
            return Boolean.FALSE;
        }
        Long uid = param.getUid();
        for (CoinGiftGivedModel coinGiftGivedModel : coinGiftGivedModelList) {
            if (!LoveStoryConstant.PRESENT_KEY_LIST.contains(coinGiftGivedModel.getGiftKey())) {
                log.info("love_story 不是爱意好礼礼物 跳过");
                continue;
            }
            //送的三款指定礼物，解锁特殊礼物
            if (!LoveStoryConstant.Gift1.GIFT_4.getGiftKey().equals(coinGiftGivedModel.getGiftKey())) {
                if (redisManager.setnx(loveStoryRedisKeyBuilder.specialTag(uid), "1", DateUtil.ONE_MONTH_SECOND)) {
                    loveStoryTrackManager.allActivityTaskFinish(uid);
                }
                if (redisManager.setnx(loveStoryRedisKeyBuilder.specialTag(coinGiftGivedModel.getToUid()), "1", DateUtil.ONE_MONTH_SECOND)) {
                    loveStoryTrackManager.allActivityTaskFinish(coinGiftGivedModel.getToUid());
                }
            }
            Long coin = coinGiftGivedModel.getCoin();
            long score = BASE_COVERT_VALUE * (Objects.isNull(coin) ? 0 : coin);
            log.info("love_story sendGiftHandler 新增分数 uid :{} coin :{} score :{}", uid, coin, score);
            loveStoryRankManager.incrRankValue(uid, score, loveStoryRedisKeyBuilder.rankKey());
        }
        return Boolean.TRUE;
    }
}
