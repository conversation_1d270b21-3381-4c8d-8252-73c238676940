package cn.yizhoucp.ump.biz.project.dal.mp.dataobject;

import cn.yizhoucp.ump.api.vo.evaluatereward.RuleVO;
import cn.yizhoucp.ump.biz.project.biz.enums.evalutereward.RuleStatus;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.Date;

@TableName("evaluate_reward_rule")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EvaluateRewardRuleDO implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String vestChannel;

    private String deviceBrand;

    private String text;

    @TableField("`status`")
    private String status;

    private Integer reward;

    private String rewardType;

    private Integer rate;
    public RuleVO convertToVO() {
        RuleVO ruleVO = new RuleVO();
        BeanUtils.copyProperties(this, ruleVO);
        ruleVO.setStatusDesc(RuleStatus.valueOf(this.status).getShowDesc());
        return ruleVO;
    }

}
