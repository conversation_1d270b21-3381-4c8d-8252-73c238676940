package cn.yizhoucp.ump.biz.project.biz.manager.activity.starryTale;

import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

@Service
public class StarryTaleRedisServices {
    @Resource
    private RedisManager redisManager;

    public void resetLitGiftGrid(Long uid) {
        String key = String.format(StarryTaleConstant.LIT_RECORD, uid);
        redisManager.delete(key);
    }

    public Boolean isLitGiftGrid(Long uid, String giftKey) {
        String key = String.format(StarryTaleConstant.LIT_RECORD, uid);
        if (giftKey == null) {
            return Boolean.FALSE;
        }
        return redisManager.hHasKey(key, giftKey);
    }

    public void litGiftGrid(Long uid, String giftKey) {
        String key = String.format(StarryTaleConstant.LIT_RECORD, uid);
        redisManager.hincr(key, giftKey, 1, DateUtil.ONE_MONTH_SECOND);
    }

    public void incrementDrawItem(Long uid, String poolCode, Long num) {
        String key = String.format(StarryTaleConstant.DRAW_ITEM_KEY, poolCode, uid);
        redisManager.incrLong(key, num, DateUtil.ONE_MONTH_SECOND);
    }

    public Long getDrawItem(Long uid, String poolCode) {
        String key = String.format(StarryTaleConstant.DRAW_ITEM_KEY, poolCode, uid);
        return Optional.ofNullable(redisManager.getLong(key)).orElse(0L);
    }

    public void decrementDrawItem(Long uid, String poolCode, Long num) {
        String key = String.format(StarryTaleConstant.DRAW_ITEM_KEY, poolCode, uid);
        redisManager.decrLong(key, num, DateUtil.ONE_MONTH_SECOND);
    }

    public void deleteRewarded(Long uid, Integer row, Integer column) {
        String key = String.format(StarryTaleConstant.REWARDED_KEY, StarryTaleConstant.ACTIVITY_CODE, uid, row, column);
        redisManager.delete(key);
    }

    public Boolean rewarded(Long uid, Integer row, Integer column) {
        String key = String.format(StarryTaleConstant.REWARDED_KEY, StarryTaleConstant.ACTIVITY_CODE, uid, row, column);
        return redisManager.hasKey(key);
    }

    public void setRewarded(Long uid, Integer row, Integer column) {
        String key = String.format(StarryTaleConstant.REWARDED_KEY, StarryTaleConstant.ACTIVITY_CODE, uid, row, column);
        redisManager.set(key, System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND);
    }

    public Boolean isReset(Long uid) {
        String key = String.format(StarryTaleConstant.RESET_KEY, StarryTaleConstant.ACTIVITY_CODE, DateUtil.getNowYyyyMMdd(), uid);
        return redisManager.hasKey(key);
    }

    public void setReset(Long uid) {
        String key = String.format(StarryTaleConstant.RESET_KEY, StarryTaleConstant.ACTIVITY_CODE, DateUtil.getNowYyyyMMdd(), uid);
        redisManager.set(key, System.currentTimeMillis(),DateUtil.ONE_MONTH_SECOND);
    }
}
