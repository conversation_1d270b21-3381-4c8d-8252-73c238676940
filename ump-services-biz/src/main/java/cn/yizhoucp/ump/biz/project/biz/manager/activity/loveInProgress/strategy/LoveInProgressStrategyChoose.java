package cn.yizhoucp.ump.biz.project.biz.manager.activity.loveInProgress.strategy;

import cn.yizhoucp.ump.biz.project.biz.manager.activity.loveInProgress.common.LoveInProgressEnums;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.StrategyManager;
import org.springframework.stereotype.Component;

/**
 * 恋爱进行时策略选择类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 14:49 2025/2/14
 */
@Component
public class LoveInProgressStrategyChoose extends StrategyManager<LoveInProgressEnums.ButtonEnum, ExecutableStrategy> {
}
