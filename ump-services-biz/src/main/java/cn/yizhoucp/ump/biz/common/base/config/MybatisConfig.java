package cn.yizhoucp.ump.biz.common.base.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR> pepper
 * @Classname MybatisConfig
 * @Description
 * @Date 2022/3/15 16:16
 */
@Configuration
public class MybatisConfig {
    /**
     * 新的分页插件,一缓和二缓遵循mybatis的规则,需要设置 MybatisConfiguration#useDeprecatedExecutor = false 避免缓存出现问题(该属性会在旧插件移除后一同移除)
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor());
        return interceptor;
    }

    @Bean
    public MetaObjectHandler MybatisDateConfig() {
        return new MetaObjectHandler() {

            /**
             * 插入元对象字段填充（用于插入时对公共字段的填充）
             *
             * @param metaObject 元对象
             */
            @Override
            public void insertFill(MetaObject metaObject) {
                if (metaObject.getGetterType("createTime").equals(Date.class)) {
                    Date now = new Date();
                    this.setFieldValByName("createTime", now, metaObject);
                    this.setFieldValByName("updateTime", now, metaObject);
                } else {
                    this.setFieldValByName("createTime", LocalDateTime.now(), metaObject);
                    this.setFieldValByName("updateTime", LocalDateTime.now(), metaObject);
                }
            }

            /**
             * 更新元对象字段填充（用于更新时对公共字段的填充）
             *
             * @param metaObject 元对象
             */
            @Override
            public void updateFill(MetaObject metaObject) {
                if (metaObject.getGetterType("updateTime").equals(Date.class)){
                    this.setFieldValByName("updateTime", new Date(), metaObject.metaObjectForProperty("updateTime"));
                } else {
                    this.setFieldValByName("updateTime", LocalDateTime.now(), metaObject.metaObjectForProperty("updateTime"));
                }
            }
        };
    }

}
