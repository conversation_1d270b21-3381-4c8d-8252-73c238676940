package cn.yizhoucp.ump.biz.project.biz.manager.activity.lanternFestival2025;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.yizhoucp.depth.api.client.DepthFeignService;
import cn.yizhoucp.depth.api.vo.CardiacRelationVO;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.SystemNPC;
import cn.yizhoucp.ms.core.base.enums.*;
import cn.yizhoucp.ms.core.base.enums.dressup.DressUpType;
import cn.yizhoucp.ms.core.base.enums.sns.UserRelationType;
import cn.yizhoucp.ms.core.base.util.AppUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.CommonListVO;
import cn.yizhoucp.ms.core.vo.CommonResultVO;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.ms.core.vo.productServices.CoinGiftProductVO;
import cn.yizhoucp.ms.core.vo.userservices.UserBaseVO;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.starter.cassandra.base.kafka.YzKafkaProducerManager;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.activity.base.GiftVO;
import cn.yizhoucp.ump.api.vo.activity.lanternFestival2025.LanternMissionListVO;
import cn.yizhoucp.ump.api.vo.activity.lanternFestival2025.LanternMissionVO;
import cn.yizhoucp.ump.api.vo.activity.lanternFestival2025.LanternRiddlesListVO;
import cn.yizhoucp.ump.api.vo.activity.lanternFestival2025.LanternShopVO;
import cn.yizhoucp.ump.api.vo.jimu.rank.CpRankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.api.vo.jimu.rank.RewardVO;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw.inner.ExpiredTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw.inner.PrizeTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.lanternFestival2025.LanternMissionEnum;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.lanternFestival2025.LanternRedisEnum;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.lanternFestival2025.LanternRiddlesEnum;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.lanternFestival2025.LanternRiddlesGiftEnum;
import cn.yizhoucp.ump.biz.project.biz.enums.pop.IdempotentTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.christmasBell.ChristmasBellConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.pop.PopManager;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.FeignImService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.FeignSnsService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.product.FeignProductService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.FeignUserService;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.LoginPopDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 元宵节活动
 * @create 2025-01-10
 **/
@Service
@Slf4j
public class LanternFestivalSupport {

    @Resource
    FeignUserService feignUserService;

    @Resource
    FeignSnsService feignSnsService;

    @Resource
    LanternFestivalRedisManager lanternRedisManager;

    @Resource
    SendPrizeManager sendPrizeManager;

    @Resource
    FeignImService feignImService;

    @Resource
    FeignProductService feignProductService;

    @Resource
    RedisManager redisManager;

    @Resource
    YzKafkaProducerManager yzKafkaProducerManager;

    @Resource
    DepthFeignService depthFeignService;

    @Resource
    PopManager popManager;

    // 活动 code
    private static final String ACTIVITY_CODE = "lantern_festival_2025";
    // 灯谜奖励小助手消息
    private static final String LANTERN_RIDDLES_MESSAGE = "恭喜在「欢乐闹元宵」猜中「%s」主题全部灯谜，奖励已领取，快去查看吧～";
    // 兑换礼物小助手消息
    private static final String EXCHANGE_GIFT_MESSAGE = "恭喜在「欢乐闹元宵」活动中成功兑换%s礼物一个，礼物已经下发至背包，快去领取吧～";
    // 榜单奖励小助手消息
    private static final String TOTAL_REWARD_MSG = "恭喜在「欢乐闹元宵」中，排名%s，礼物已经至下发至背包了哦～快去查看吧～";
    // 元宵小助手消息
    private static final String YUANXIAO_SEND_MESSAGE = "恭喜在「欢乐闹元宵」活动中，成功赠送%s礼物，获取%s个元宵，快去活动页面查看吧～";
    private static final String YUANXIAO_RECEIVE_MESSAGE = "恭喜在「欢乐闹元宵」活动中，成功收到%s礼物，获取%s个元宵，快去活动页面查看吧～";
    // 隐藏用户头像
    private static final String HIDE_USER_AVATAR = "https://res-cdn.nuan.chat/nuanliao-fronted/activity/guanxuan-jun/default-avatar.png";
    // 隐藏用户昵称
    private static final String HIDE_USER_NAME = "神仙眷侣";
    // 元宵活动礼物
    private static final Map<String,Long> LANTERN_FESTIVAL_ACTIVITY_GIFT_MAP = new HashMap<>();
    // 元宵商店礼物
    private static final Map<String, Integer> LANTERN_SHOP_GIFT_MAP = new HashMap<>();
    // 榜单礼物信息
    private static final List<RewardVO> RANK_GIFT_LIST = new ArrayList<>();
    // 任务初始状态
    private static final String MISSION_START = "0";
    // 任务领取
    private static final String MISSION_RECEIVE = "1";
    // 任务完成
    private static final String MISSION_COMPLETE = "2";
    // 任务奖励
    private static final String MISSION_REWARD = "3";

    static {
        LANTERN_FESTIVAL_ACTIVITY_GIFT_MAP.put("HS_GIFT", 1L);
        LANTERN_FESTIVAL_ACTIVITY_GIFT_MAP.put("JLSY_GIFT", 2L);
        LANTERN_FESTIVAL_ACTIVITY_GIFT_MAP.put("WDSV_GIFT", 4L);
        LANTERN_FESTIVAL_ACTIVITY_GIFT_MAP.put("YXDH1_GIFT", 10L);
        LANTERN_FESTIVAL_ACTIVITY_GIFT_MAP.put("HDRZ_GIFT", 22L);
        LANTERN_FESTIVAL_ACTIVITY_GIFT_MAP.put("BYXH_GIFT", 25L);
        LANTERN_FESTIVAL_ACTIVITY_GIFT_MAP.put("XRCL_GIFT", 35L);
        LANTERN_FESTIVAL_ACTIVITY_GIFT_MAP.put("SYZY_GIFT", 35L);

        LANTERN_SHOP_GIFT_MAP.put("YYXQ_GIFT", 5);
        LANTERN_SHOP_GIFT_MAP.put("ZFJ_GIFT", 10);
        LANTERN_SHOP_GIFT_MAP.put("XHYW_GIFT", 26);
        LANTERN_SHOP_GIFT_MAP.put("GBDH_GIFT", 50);
        LANTERN_SHOP_GIFT_MAP.put("MHSL_GIFT", 66);
        LANTERN_SHOP_GIFT_MAP.put("YGMF_GIFT", 169);
        LANTERN_SHOP_GIFT_MAP.put("XCZX_GIFT", 263);
        LANTERN_SHOP_GIFT_MAP.put("KZLY_GIFT", 520);
        LANTERN_SHOP_GIFT_MAP.put("HKSL_GIFT", 666);

        RewardVO g1 = RewardVO.builder().giftKey("HYLL_GIFT").icon("https://res-cdn.nuan.chat/gift-image/2023-11/1699502378331278.png").value(13140L).build();
        RewardVO g2 = RewardVO.builder().giftKey("TXXT_GIFT").icon("https://res-cdn.nuan.chat/gift-image/2024-09/1727274512317573.png").value(5200L).build();
        RewardVO g3 = RewardVO.builder().giftKey("LMXY2_GIFT").icon("https://res-cdn.nuan.chat/gift-image/2024-09/1727264778621740.png").value(3344L).build();
        RewardVO g4 = RewardVO.builder().giftKey("YZCW_GIFT").icon("https://res-cdn.nuan.chat/gift-image/2024-09/1727357115313993.png").value(1314L).build();
        RewardVO g5 = RewardVO.builder().giftKey("WG_GIFT").icon("https://res-cdn.nuan.chat/gift-image/2024-11/1732674622428883.png").value(999L).build();
        RANK_GIFT_LIST.add(g1);
        RANK_GIFT_LIST.add(g2);
        RANK_GIFT_LIST.add(g3);
        RANK_GIFT_LIST.add(g4);
        RANK_GIFT_LIST.add(g5);
    }


    /**
     * 发送小助手消息
     */
    public void npcMessage(Long userId, String msg) {
        feignImService.npcTalk(SystemNPC.LANLING_LITTLE.getCode(), msg, ServicesNameEnum.lanling_services.getCode(), userId);
    }

    /**
     * 获取好友列表
     *
     * @param userId 用户 id
     * @param appId  应用 id
     * @return CommonListVO
     */
    public CommonListVO getFriendList(Long userId, Long appId) {
        if (null == userId || null == appId) {
            return new CommonListVO();
        }
        // 获取好友、关注、粉丝
        Map<String, List<Long>> followMap = feignSnsService.getUserRelationMap(userId, appId, null).successData();
        // 获取密友
        Map<Long, CardiacRelationVO> relationVOMap = depthFeignService.getAllUserDepth(userId).successData();
        if (CollectionUtil.isEmpty(followMap) && CollectionUtil.isEmpty(relationVOMap)) {
            return new CommonListVO();
        }
        List<Long> idList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(relationVOMap)) {
            idList.addAll(new ArrayList<>(relationVOMap.keySet()));
        }
        if (CollectionUtil.isNotEmpty(followMap.get(UserRelationType.friend.getCode()))) {
            idList.addAll(followMap.get(UserRelationType.friend.getCode()));
        }
        if (CollectionUtil.isNotEmpty(followMap.get(UserRelationType.subscribe.getCode()))) {
            idList.addAll(followMap.get(UserRelationType.subscribe.getCode()));
        }
        if (CollectionUtil.isNotEmpty(followMap.get(UserRelationType.fans.getCode()))) {
            idList.addAll(followMap.get(UserRelationType.fans.getCode()));
        }
        log.debug("getFriendList userId {} idList {}", userId, JSON.toJSONString(idList));
        if (CollectionUtil.isEmpty(idList)) {
            return new CommonListVO();
        }
        // 去重
        idList = idList.stream().distinct().collect(Collectors.toList());
        List<UserVO> userList = new ArrayList<>();
        // 获取用户信息，并过滤被拉黑用户
        for (int i = 0; i < idList.size(); i += 500) {
            // 分批处理，每次取最多 500 条数据
            List<Long> batchList = idList.subList(i, Math.min(i + 500, idList.size()));
            try {
                List<UserVO> batchResult = feignUserService.acquireUsersBulkListPost(batchList).successData();
                if (!CollectionUtils.isEmpty(batchResult)) {
                    userList.addAll(batchResult);
                }
            } catch (Exception e) {
                log.error("lantern get user error", e);
            }
        }
        List<UserVO> newUserList = userList.stream().filter(user -> CommonStatus.enable.getCode().equals(user.getStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(newUserList)) {
            return new CommonListVO();
        }
        List<cn.yizhoucp.ump.api.vo.activity.base.UserVO> result = new ArrayList<>();
        for (UserVO userVO : newUserList) {
            cn.yizhoucp.ump.api.vo.activity.base.UserVO user = cn.yizhoucp.ump.api.vo.activity.base.UserVO.builder()
                    .uid(userVO.getId())
                    .name(userVO.getName())
                    .avatar(userVO.getAvatar())
                    .build();
            result.add(user);
        }
        return CommonListVO.success(result);
    }


    /**
     * 灯谜列表
     *
     * @param userId  用户 id
     * @param extDate 参数
     * @return LanternRiddlesListVO
     */
    public LanternRiddlesListVO lanternRiddlesList(Long userId, String extDate) {
        if (null == userId || StringUtils.isBlank(extDate)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        LanternRiddlesListVO result = new LanternRiddlesListVO();
        JSONObject jsonObject = JSON.parseObject(extDate);
        String theme = Convert.toStr(jsonObject.get("theme"), "idiom");
        // 获取我的花灯
        Long userLanternCount = lanternRedisManager.getUserLanternCount(userId);
        result.setLanternCount(userLanternCount);
        // 获取我的灯谜记录
        List<Long> recordSet = lanternRedisManager.getUserLanternRiddlesRecord(userId, theme);
        // 获取谜语列表
        List<LanternRiddlesEnum> riddlesEnums = LanternRiddlesEnum.getRiddleListByTheme(theme);
        if (CollectionUtil.isEmpty(riddlesEnums)) {
            return result;
        }
        List<LanternRiddlesListVO.LanternRiddlesVO> riddlesList = new ArrayList<>();
        for (LanternRiddlesEnum riddlesEnum : riddlesEnums) {
            String answer = riddlesEnum.getAnswer();
            if (LanternRiddlesEnum.love1.getId().equals(riddlesEnum.getId())) {
                answer = lanternRedisManager.getLanternLoveAnswer(userId);
            }
            LanternRiddlesListVO.LanternRiddlesVO riddlesVO = LanternRiddlesListVO.LanternRiddlesVO.builder()
                    .id(riddlesEnum.getId())
                    .riddle(riddlesEnum.getRiddle())
                    .answer(answer)
                    .status(recordSet != null && recordSet.contains(riddlesEnum.getId())).build();
            riddlesList.add(riddlesVO);
        }
        result.setRiddlesList(riddlesList);
        // 奖励状态
        String userReceiveStatus = getUserReceiveStatus(userId, theme);
        result.setReceiveStatus(userReceiveStatus);
        // 获取礼物列表
        List<GiftVO> giftList = new ArrayList<>();
        List<LanternRiddlesGiftEnum> giftListByTheme = LanternRiddlesGiftEnum.getGiftListByTheme(theme);
        if (CollectionUtil.isNotEmpty(giftListByTheme)) {
            for (LanternRiddlesGiftEnum giftEnum : giftListByTheme) {
                GiftVO giftVO = GiftVO.builder()
                        .key(giftEnum.getKey())
                        .icon(giftEnum.getIcon())
                        .name(giftEnum.getName())
                        .coin(giftEnum.getCoin())
                        .build();
                giftList.add(giftVO);
            }
            result.setGiftList(giftList);
        }
        return result;
    }

    /**
     * 获取用户灯谜奖励领取状态
     * @param userId 用户 id
     * @param theme 主题
     * @return String
     */
    public String getUserReceiveStatus(Long userId, String theme) {
        String result = "0";
        if (null == userId || StringUtils.isBlank(theme)) {
            return result;
        }
        // 判断用户是否已经领取
        Boolean lanternRiddlesReceiveStatus = lanternRedisManager.getLanternRiddlesReceiveStatus(userId, theme);
        if (lanternRiddlesReceiveStatus) {
            return "2";
        }
        List<Long> record = lanternRedisManager.getUserLanternRiddlesRecord(userId, theme);
        if (CollectionUtil.isEmpty(record)) {
            return result;
        }
        List<LanternRiddlesEnum> riddleList = LanternRiddlesEnum.getRiddleListByTheme(theme);
        if (CollectionUtil.isEmpty(riddleList)) {
            return result;
        }
        result = "1";
        for (LanternRiddlesEnum riddlesEnum : riddleList) {
            if (!record.contains(riddlesEnum.getId())) {
                result = "0";
                break;
            }
        }
        return result;
    }

    /**
     * 猜灯谜
     *
     * @param userId  用户 id
     * @param extDate 参数
     * @return CommonResultVO
     */
    public CommonResultVO guessingLanternRiddles(Long userId, String extDate) {
        if (null == userId || StringUtils.isBlank(extDate)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        JSONObject jsonObject = JSON.parseObject(extDate);
        Long riddleId = Convert.toLong(jsonObject.get("riddleId"), null);
        String answer = Convert.toStr(jsonObject.get("answer"), null);
        if (null == riddleId) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        // 获取灯谜
        LanternRiddlesEnum riddlesEnum = LanternRiddlesEnum.getRiddleById(riddleId);
        if (null == riddlesEnum) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "灯谜不存在");
        }
        // 校验答案
        if (riddlesEnum.getId().equals(LanternRiddlesEnum.love1.getId())) {
            String[] answerArray = riddlesEnum.getAnswer().split("/");
            List<String> list = Arrays.asList(answerArray);
            if (!list.contains(answer)) {
                return CommonResultVO.fail("回答错误哦～就差一点啦～");
            }
        } else {
            if (!riddlesEnum.getAnswer().equals(answer)) {
                return CommonResultVO.fail("回答错误哦～就差一点啦～");
            }
        }
        // 记录灯谜记录
        lanternRedisManager.updateUserLanternRiddlesRecord(userId, riddleId, riddlesEnum.getTheme());
        if (LanternRiddlesEnum.love1.getId().equals(riddlesEnum.getId())) {
            // 记录爱情故事答案
            lanternRedisManager.updateLanternLoveAnswer(userId, answer);
        }
        // 埋点
        List<Long> riddlesRecordList = lanternRedisManager.getUserLanternRiddlesRecord(userId, riddlesEnum.getTheme());
        List<LanternRiddlesEnum> riddleList = LanternRiddlesEnum.getRiddleListByTheme(riddlesEnum.getTheme());
        boolean result = true;
        for (LanternRiddlesEnum riddle : riddleList) {
            if (!riddlesRecordList.contains(riddle.getId())) {
                result = false;
                break;
            }
        }
        if (result) {
            allActivityTaskFinish(userId, riddlesEnum.getDesc());
        }
        return CommonResultVO.success("回答正确～");
    }

    /**
     * 选择灯谜
     * @param userId  用户 id
     * @param extDate 参数
     * @return CommonResultVO
     */
    public CommonResultVO chooseLanternRiddles(Long userId, String extDate) {
        if (null == userId || StringUtils.isBlank(extDate)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        JSONObject jsonObject = JSON.parseObject(extDate);
        Long riddleId = Convert.toLong(jsonObject.get("riddleId"), null);
        if (null == riddleId) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        // 获取灯谜
        LanternRiddlesEnum riddlesEnum = LanternRiddlesEnum.getRiddleById(riddleId);
        if (null == riddlesEnum) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "灯谜不存在");
        }
        // 获取花灯
        Long userLanternCount = lanternRedisManager.getUserLanternCount(userId);
        if (userLanternCount < 1) {
            return CommonResultVO.fail("花灯数量不足");
        }
        // 消耗花灯
        lanternRedisManager.updateUserLanternCount(userId, -1L);
        return CommonResultVO.success();
    }

    /**
     * 领取灯谜奖励
     *
     * @param userId  用户 id
     * @param extDate 参数
     * @return CommonResultVO
     */
    public CommonResultVO receiveLanternReward(Long userId, Long appId, String extDate) {
        log.info("receiveLanternReward userId:{} extDate {}", userId, extDate);
        if (null == userId || StringUtils.isBlank(extDate)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        JSONObject jsonObject = JSON.parseObject(extDate);
        String theme = Convert.toStr(jsonObject.get("theme"), null);
        if (StringUtils.isBlank(theme)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        // 判断是否可领取
        String userReceiveStatus = getUserReceiveStatus(userId, theme);
        if ("0".equals(userReceiveStatus)) {
            return CommonResultVO.fail("灯谜未完成，不可领取~");
        }
        if ("2".equals(userReceiveStatus)) {
            return CommonResultVO.fail("已经领取过啦~");
        }
        List<LanternRiddlesGiftEnum> giftList = LanternRiddlesGiftEnum.getGiftListByTheme(theme);
        if (CollectionUtil.isEmpty(giftList)) {
            return CommonResultVO.fail("奖励不存在");
        }
        // 记录状态
        lanternRedisManager.updateLanternRiddlesReceiveRecord(userId, theme);
        // 下发奖励
        for (LanternRiddlesGiftEnum giftEnum : giftList) {
            BaseParam baseParam = BaseParam.builder()
                    .uid(userId).appId(appId).unionId(null).build();
            String key = giftEnum.getKey();
            String prizeType = giftEnum.getType();
            if ("gift".equals(prizeType)) {
                SendPrizeDTO sendPrizeDTO = SendPrizeDTO.builder()
                        .appId(appId)
                        .prizeValue(key)
                        .prizeType(PrizeTypeEnum.PRIZE_GIFT.getCode())
                        .expiredType(ExpiredTypeEnum.EFFECTIVE_DAYS.getCode())
                        .prizeEffectiveDay(14)
                        .prizeNum(1)
                        .toUid(userId)
                        .scene(ACTIVITY_CODE)
                        .fee(false)
                        .build();
                sendPrizeManager.sendSceneGift(baseParam, sendPrizeDTO);
            } else {
                sendPrizeManager.sendDressUp(baseParam.getAppId(), baseParam.getUid(), DressUpType.getByCode(prizeType), key, 14, 1);
            }
            allActivityReceiveAward(userId, "riddles", key, giftEnum.getCoin(), 1);
        }
        // 发送小助手消息
        npcMessage(userId, String.format(LANTERN_RIDDLES_MESSAGE, LanternRiddlesEnum.getThemeNameByTheme(theme)));
        return CommonResultVO.success();
    }


    /**
     * 任务列表
     * @param userId 用户 id
     * @return CommonListVO
     */
    public CommonListVO lanternMissionList(Long userId) {
        if (null == userId) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        List<LanternMissionListVO> result = new ArrayList<>();
        for (LanternMissionEnum missionEnum : LanternMissionEnum.values()) {
            String status = getUserMissionStatus(userId, missionEnum.getId(), missionEnum.getGiftCount());
            LanternMissionListVO missionListVO = LanternMissionListVO.builder()
                    .id(missionEnum.getId())
                    .key(missionEnum.getKey())
                    .name(missionEnum.getName())
                    .icon(missionEnum.getIcon())
                    .giftCount(missionEnum.getGiftCount())
                    .lanternCount(missionEnum.getLanternCount())
                    .status(status)
                    .build();
            result.add(missionListVO);
        }
        return CommonListVO.success(result);
    }

    /**
     * 获取用户任务状态
     * @param userId 用户 id
     * @param missionId 任务 id
     * @return String
     */
    public String getUserMissionStatus(Long userId, Long missionId, Long missionCount) {
        if (null == userId || null == missionId) {
            return MISSION_START;
        }
        LanternMissionVO missionInfo = lanternRedisManager.getMissionInfo(userId, missionId);
        if (null == missionInfo) {
            return MISSION_START;
        }
        return missionInfo.getStatus();
    }

    /**
     * 用户领取任务
     * @param userId 用户 id
     * @param extDate 参数
     * @return CommonResultVO
     */
    public CommonResultVO receiveMission(Long userId, String extDate) {
        if (null == userId || StringUtils.isBlank(extDate)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        JSONObject jsonObject = JSON.parseObject(extDate);
        Long missionId = Convert.toLong(jsonObject.get("missionId"), null);
        if (null == missionId) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        LanternMissionEnum mission = LanternMissionEnum.getMissionById(missionId);
        if (null == mission) {
            return CommonResultVO.fail("任务不存在");
        }
        // 更新用户领取状态
        lanternRedisManager.updateMissionInfo(userId, missionId, 0L, MISSION_RECEIVE);
        return CommonResultVO.success();
    }

    /**
     * 领取任务奖励
     * @param userId 用户 id
     * @param extDate 参数
     * @return CommonResultVO
     */
    public CommonResultVO receiveMissionReward(Long userId, String extDate) {
        if (null == userId || StringUtils.isBlank(extDate)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        JSONObject jsonObject = JSON.parseObject(extDate);
        Long missionId = Convert.toLong(jsonObject.get("missionId"), null);
        if (null == missionId) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        LanternMissionEnum mission = LanternMissionEnum.getMissionById(missionId);
        if (null == mission) {
            return CommonResultVO.fail("任务不存在");
        }
        // 判断是否领取过
        LanternMissionVO missionInfo = lanternRedisManager.getMissionInfo(userId, missionId);
        if (null != missionInfo && MISSION_REWARD.equals(missionInfo.getStatus())) {
            return CommonResultVO.fail("已经领取过奖励啦");
        }
        if (null != missionInfo && !MISSION_COMPLETE.equals(missionInfo.getStatus())) {
            return CommonResultVO.fail("任务未完成，请先完成任务");
        }
        // 更新领取状态
        lanternRedisManager.updateMissionInfo(userId, missionId, 0L, MISSION_REWARD);
        // 下发奖励
        lanternRedisManager.updateUserLanternCount(userId, mission.getLanternCount());
        return CommonResultVO.success();
    }

    /**
     * 元宵商店
     *
     * @param userId 用户 id
     * @return LanternShopVO
     */
    public LanternShopVO lanternShop(Long userId, Long appId) {
        if (null == userId) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        LanternShopVO result = new LanternShopVO();
        List<String> giftKeyList = new ArrayList<>(LANTERN_SHOP_GIFT_MAP.keySet());
        if (CollectionUtil.isEmpty(giftKeyList)) {
            return result;
        }
        List<CoinGiftProductVO> coinGiftProductList = feignProductService.batchGetGiftByGiftKey(appId, JSON.toJSONString(giftKeyList)).successData();
        if (CollectionUtil.isEmpty(coinGiftProductList)) {
            return result;
        }
        // 获取礼物
        Map<String, CoinGiftProductVO> giftMap = coinGiftProductList.stream().collect(Collectors.toMap(CoinGiftProductVO::getGiftKey, vo -> vo, (k1, k2) -> k1));
        List<GiftVO> giftList = new ArrayList<>();
        for (Map.Entry<String, Integer> gift : LANTERN_SHOP_GIFT_MAP.entrySet()) {
            String key = gift.getKey();
            Integer count = gift.getValue();
            if (giftMap.containsKey(key)) {
                CoinGiftProductVO productVO = giftMap.get(key);
                if (null == productVO) {
                    continue;
                }
                GiftVO giftVO = GiftVO.builder()
                        .key(productVO.getGiftKey())
                        .name(productVO.getName())
                        .icon(productVO.getIcon())
                        .coin(productVO.getNeedCoin())
                        .count(count).build();
                giftList.add(giftVO);
            }
        }
        result.setRewardGiftList(giftList);
        // 获取我的信息
        UserBaseVO selfUser = feignUserService.getUserBaseVO(appId, userId).successData();
        result.setSelfInfo(selfUser);
        // 获取元宵
        Long friendId = lanternRedisManager.getUserFriendRelation(userId);
        if (null == friendId) {
            result.setYuanxiaoCount(0L);
        } else {
            UserBaseVO friendUser = feignUserService.getUserBaseVO(appId, friendId).successData();
            result.setFriendInfo(friendUser);
            result.setYuanxiaoCount(lanternRedisManager.getUserYuanxiaoCount(userId, friendId));
        }
        return result;
    }

    /**
     * 兑换礼物
     *
     * @param userId  用户 id
     * @param extDate 参数
     * @return CommonResultVO
     */
    public CommonResultVO exchangeGift(Long userId, Long appId, String extDate) {
        if (null == userId || StringUtils.isBlank(extDate)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        JSONObject jsonObject = JSON.parseObject(extDate);
        String giftKey = Convert.toStr(jsonObject.get("giftKey"), null);
        if (StringUtils.isBlank(giftKey)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        // 判断元宵是否足够
        Integer needYuanxiaoCount = LANTERN_SHOP_GIFT_MAP.getOrDefault(giftKey, null);
        if (null == needYuanxiaoCount) {
            return CommonResultVO.fail("礼物不存在");
        }
        CoinGiftProductVO coinGiftProductVO = feignProductService.getGiftByGiftKey(appId, giftKey).successData();
        if (null == coinGiftProductVO) {
            return CommonResultVO.fail("礼物不存在");
        }
        Long toUid = lanternRedisManager.getUserFriendRelation(userId);
        if (null == toUid) {
            return CommonResultVO.fail("请先选择好友");
        }
        Long userYuanxiaoCount = lanternRedisManager.getUserYuanxiaoCount(userId, toUid);
        if (null == userYuanxiaoCount || userYuanxiaoCount < needYuanxiaoCount) {
            return CommonResultVO.fail("元宵数量不足");
        }
        // 扣除元宵
        lanternRedisManager.updateUserYuanxiaoCount(userId, toUid, -Convert.toLong(needYuanxiaoCount));
        // 下发礼物
        BaseParam baseParam = BaseParam.builder()
                .uid(userId).appId(appId).unionId(null).build();
        SendPrizeDTO sendPrizeDTO = SendPrizeDTO.builder()
                .appId(appId)
                .prizeValue(giftKey)
                .prizeType(PrizeTypeEnum.PRIZE_GIFT.getCode())
                .expiredType(ExpiredTypeEnum.EFFECTIVE_DAYS.getCode())
                .prizeEffectiveDay(14)
                .prizeNum(1)
                .toUid(userId)
                .scene(ACTIVITY_CODE)
                .fee(false)
                .build();
        sendPrizeManager.sendSceneGift(baseParam, sendPrizeDTO);
        // 发送小助手消息
        npcMessage(userId, String.format(EXCHANGE_GIFT_MESSAGE, coinGiftProductVO.getName()));
        // 埋点
        allActivityReceiveAward(userId, "store", giftKey, coinGiftProductVO.getNeedCoin(), 1);
        return CommonResultVO.success();
    }

    /**
     * 活动榜单
     * @param currentUid 用户 id
     * @return RankVO
     */
    public RankVO getRank(Long currentUid) {
        RankVO result = new RankVO();
        String key = LanternRedisEnum.lantern_activity_luck_total_list.getKey();
        Set<ZSetOperations.TypedTuple<Object>> top10List = redisManager.reverseRangeWithScores(key, 0L, 9L);
        if (CollectionUtil.isEmpty(top10List)) {
            top10List = new HashSet<>();
        }
        List<Long> idList = new ArrayList<>();
        for (ZSetOperations.TypedTuple<Object> typedTuple : top10List) {
            String uidStr = Convert.toStr(typedTuple.getValue(),"");
            idList.addAll(AppUtil.openSplicUserId(uidStr));
        }
        List<UserVO> userVOList = new ArrayList<>();
        if (!CollectionUtil.isEmpty(idList)) {
            // 获取用户信息
            userVOList = feignUserService.acquireUsersBulkListPost(idList).successData();
            if (CollectionUtil.isEmpty(userVOList)) {
                userVOList = new ArrayList<>();
            }
        }
        List<CpRankItem> cpRankItemList = new ArrayList<>();
        Map<Long, UserVO> userMap = userVOList.stream().collect(Collectors.toMap(UserVO::getId, v -> v, (k1, k2) -> k1));
        long rank = 1;
        int giftIndex = 0;
        for (ZSetOperations.TypedTuple<Object> typedTuple : top10List) {
            String uidStr = Convert.toStr(typedTuple.getValue(),"");
            List<Long> uidList = AppUtil.openSplicUserId(uidStr);
            if (CollectionUtil.isEmpty(uidList)) {
                continue;
            }
            Long fromUid = uidList.get(0);
            Long toUid = uidList.get(1);
            // 是否隐藏头像
            boolean hide = true;
            if (uidList.contains(currentUid)) {
                // 榜单两个用户，我在第一个，好友在第二个
                fromUid = currentUid;
                toUid = uidList.stream()
                        .filter(uid -> !uid.equals(currentUid))
                        .findFirst()
                        .orElse(toUid);
                hide = false;
            }
            UserVO fromUser = userMap.getOrDefault(fromUid, new UserVO());
            UserVO toUser = userMap.getOrDefault(toUid, new UserVO());
            Long score = Convert.toLong(typedTuple.getScore(), 0L);
            List<RewardVO> rewardVOList = new ArrayList<>();
            if (giftIndex < RANK_GIFT_LIST.size()) {
                RewardVO rewardVO = RANK_GIFT_LIST.get(giftIndex++);
                rewardVOList.add(rewardVO);
            }
            CpRankItem rankItem = CpRankItem.builder()
                    .rank(rank)
                    .maleUid(fromUser.getId())
                    .maleUserName(hide ? HIDE_USER_NAME : fromUser.getName())
                    .maleAvatar(hide ? HIDE_USER_AVATAR : fromUser.getAvatar())
                    .femaleUid(toUser.getId())
                    .femaleUserName(hide ? HIDE_USER_NAME : toUser.getName())
                    .femaleAvatar(hide ? HIDE_USER_AVATAR : toUser.getAvatar())
                    .value(score)
                    .rewardVOList(rewardVOList)
                    .hide(hide).build();
            cpRankItemList.add(rankItem);
            rank++;
        }
        // 榜单不够 10 位，填充空白列表
        if (rank <= 10) {
            for (long i = rank; i < 11; i++) {
                List<RewardVO> rewardVOList = new ArrayList<>();
                if (giftIndex < RANK_GIFT_LIST.size()) {
                    RewardVO rewardVO = RANK_GIFT_LIST.get(giftIndex++);
                    rewardVOList.add(rewardVO);
                }
                CpRankItem rankItem = CpRankItem.builder()
                        .rank(rank).rewardVOList(rewardVOList).build();
                cpRankItemList.add(rankItem);
                rank++;
            }
        }
        result.setCpRankItemList(cpRankItemList);
        // 获取我的排名
        result.setMyCpRankItem(getMyRank(currentUid));
        return result;
    }

    /**
     * 获取榜单中我的排名
     * @param currentUid 当前用户 id
     * @return CpRankItem
     */
    public CpRankItem getMyRank(Long currentUid) {
        CpRankItem result = new CpRankItem();
        Long friendUid = lanternRedisManager.getUserFriendRelation(currentUid);
        Long luckCount = 0L;
        Long rank = -1L;
        String key = LanternRedisEnum.lantern_activity_luck_total_list.getKey();
        if (null != friendUid) {
            // 获取福气值
            luckCount = Convert.toLong(redisManager.score(key, AppUtil.splicUserId(currentUid, friendUid)), 0L);
            UserVO friendUser = feignUserService.getBasic(friendUid, 1L).successData();
            if (null != friendUser) {
                result.setFemaleUid(friendUser.getId());
                result.setFemaleUserName(friendUser.getName());
                result.setFemaleAvatar(friendUser.getAvatar());
            }
            // 获取排名
            rank = Convert.toLong(redisManager.reverseRank(key, AppUtil.splicUserId(currentUid, friendUid)), -2L) + 1;
        }
        // 获取差距
        if (rank == 1) {
            result.setDiffVal(0L);
        } else {
            long diffRank = 9L;
            if (rank >= 2) {
                diffRank = rank - 2;
            }
            Set<ZSetOperations.TypedTuple<Object>> preList = redisManager.reverseRangeWithScores(key, diffRank, diffRank);
            if (CollectionUtil.isNotEmpty(preList)) {
                ZSetOperations.TypedTuple<Object> firstElement = preList.iterator().next();
                Long score = Convert.toLong(firstElement.getScore(),0L);
                if (score >= luckCount) {
                    result.setDiffVal(Convert.toLong(score) - luckCount);
                }
            }
        }
        // 10 名以上显示未上榜
        if (rank > 10) {
            rank = -1L;
        }
        result.setRank(rank);
        result.setValue(luckCount);
        result.setHide(false);
        UserVO currentUser = feignUserService.getBasic(currentUid, 1L).successData();
        if (null != currentUser) {
            result.setMaleUid(currentUser.getId());
            result.setMaleUserName(currentUser.getName());
            result.setMaleAvatar(currentUser.getAvatar());
        }
        return result;
    }

    /**
     * 选择好友
     * @param userId 用户 id
     * @param extDate 扩展数据
     * @return CommonResultVO
     */
    public CommonResultVO chooseFriend(Long userId, String extDate) {
        if (null == userId || StringUtils.isBlank(extDate)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        Long toUid = 0L;
        JSONObject jsonObject = JSON.parseObject(extDate);
        toUid = Convert.toLong(jsonObject.get("toUid"), 0L);
        if (toUid == 0L) {
            return CommonResultVO.fail("请选择好友~");
        }
        lanternRedisManager.updateUserFriendRelation(userId, toUid);
        return CommonResultVO.success();
    }

    /**
     * 送礼更新
     * @param param 用户参数
     * @param coinGiftGivedModelList 礼物信息
     * @return sendGiftHandler
     */
    @ActivityCheck(activityCode = ACTIVITY_CODE, isThrowException = false)
    public Boolean sendGiftHandler(BaseParam param, List<CoinGiftGivedModel> coinGiftGivedModelList) {
        if (Objects.isNull(param) || CollectionUtils.isEmpty(coinGiftGivedModelList)) {
            log.warn("lantern_festival_2025 sendGiftHandler 参数错误 param :{} coinGiftGivedModelList :{}",
                    JSONObject.toJSONString(param), JSONObject.toJSONString(coinGiftGivedModelList));
            return Boolean.FALSE;
        }
        try {
            Long userId = param.getUid();
            Long appId = param.getAppId();
            for (CoinGiftGivedModel giftGivedModel : coinGiftGivedModelList) {
                if (null == giftGivedModel) {
                    continue;
                }
                String giftKey = giftGivedModel.getGiftKey();
                Long toUid = giftGivedModel.getToUid();
                // 是否任务礼物
                LanternMissionEnum missionEnum = LanternMissionEnum.getMissionByKey(giftKey);
                if (null != missionEnum) {
                    // 更新任务记录
                    completeMission(userId, giftGivedModel.getProductCount(), missionEnum);
                    completeMission(toUid, giftGivedModel.getProductCount(), missionEnum);
                }
                // 判断两个人是否好友、关注、粉丝、密友
                String relation = feignSnsService.getUserRelation(userId, toUid, appId).getData();
                Map<Long, CardiacRelationVO> relationVOMap = depthFeignService.getAllUserDepth(userId).successData();
                boolean cardiacRelation = CollectionUtil.isNotEmpty(relationVOMap)
                        && relationVOMap.containsKey(toUid);
                if (UserRelationType.friend.getCode().equals(relation)
                        || UserRelationType.subscribe.getCode().equals(relation)
                        || UserRelationType.fans.getCode().equals(relation)
                        || cardiacRelation) {
                    // 是否私聊赠送的活动礼物
                    boolean chatGift = GiftFrom.chat.getCode().equals(giftGivedModel.getFrom())
                            || GiftFrom.gift_in_return_im.getCode().equals(giftGivedModel.getFrom());
                    if (LANTERN_FESTIVAL_ACTIVITY_GIFT_MAP.containsKey(giftKey)
                            && !GiftWay.PACKET.getCode().equals(giftGivedModel.getGiftWay())
                            && chatGift) {
                        // 更新用户元宵数量，送礼和收礼用户都需要更新
                        Long rewardCount = LANTERN_FESTIVAL_ACTIVITY_GIFT_MAP.get(giftKey);
                        Long count = Convert.toLong(giftGivedModel.getProductCount(), 1L) * rewardCount;
                        updateUserYuanxiaoCount(userId, toUid, count, param);
                        updateUserYuanxiaoCount(toUid, userId, count, param);
                        // 小助手消息
                        String sendMsg = String.format(YUANXIAO_SEND_MESSAGE, giftGivedModel.getName(), count);
                        String receiveMsg = String.format(YUANXIAO_RECEIVE_MESSAGE, giftGivedModel.getName(), count);
                        npcMessage(userId, sendMsg);
                        npcMessage(toUid, receiveMsg);
                    }
                    // 是否榜单礼物(非活动礼物，非任务礼物、非背包礼物)
                    if (!LANTERN_FESTIVAL_ACTIVITY_GIFT_MAP.containsKey(giftKey) && missionEnum == null
                            && !GiftWay.PACKET.getCode().equals(giftGivedModel.getGiftWay())
                            && chatGift) {
                        // 更新活动总榜
                        updateLuckyCount(userId, toUid, appId, giftGivedModel.getCoin());
                    }
                }
            }
        } catch (Exception e) {
            log.info("lantern_festival_2025 sendGiftHandler error {}", JSON.toJSONString(e));
        }
        return Boolean.TRUE;
    }

    /**
     * 更新用户元宵数量，需要弹窗
     */
    public void updateUserYuanxiaoCount(Long fromUid, Long toUid, Long count, BaseParam param) {
        lanternRedisManager.updateUserYuanxiaoCount(fromUid, toUid, count);
        // 弹窗
        String url = String.format("yuanxiao-prize-push?num=%s&from=h5dialog", count);
        popManager.popByLoginPopDO(param.getAppId(), param.getUnionId(), fromUid, LoginPopDO.builder()
                .unionId(param.getUnionId())
                .idempotentType(IdempotentTypeEnum.NONE.name())
                .pushType("h5dialog")
                .url(url).build());
    }

    /**
     * 完成任务更新数据
     * @param userId 用户 id
     * @param productCount 礼物数量
     * @param missionEnum 任务枚举
     */
    public void completeMission(Long userId, Long productCount, LanternMissionEnum missionEnum) {
        log.debug("completeMission userId:{} productCount:{} missionEnum:{}", userId, productCount, JSON.toJSONString(missionEnum));
        if (null == userId || null == productCount || null == missionEnum) {
            return;
        }
        Long missionId = missionEnum.getId();
        // 获取任务状态
        LanternMissionVO missionInfo = lanternRedisManager.getMissionInfo(userId, missionId);
        if (null == missionInfo) {
            log.debug("no receive mission userId:{} missionId:{}", userId, missionId);
            return;
        }
        // 判断任务状态
        String status = missionInfo.getStatus();
        if (MISSION_COMPLETE.equals(status) || MISSION_REWARD.equals(status)) {
            log.debug("mission is complete userId:{} missionId:{}", userId, missionId);
            return;
        }
        Long newCount = Convert.toLong(missionInfo.getCount(),0L) + productCount;
        if (newCount >= missionEnum.getGiftCount()) {
            lanternRedisManager.updateMissionInfo(userId, missionId, productCount, MISSION_COMPLETE);
            // 埋点
            allActivityTaskFinish(userId, missionEnum.getDesc());
        } else {
            lanternRedisManager.updateMissionInfo(userId, missionId, productCount, MISSION_RECEIVE);
        }
    }

    /**
     * 更新福气值
     * @param fromUid 发送者
     * @param toUid 接收者
     * @param appId appId
     * @param coin  金币
     */
    public void updateLuckyCount(Long fromUid, Long toUid, Long appId, Long coin) {
        if (null == fromUid || null == toUid || null == coin) {
            return;
        }
        // 更新福气值榜单
        Double luckyCount = Convert.toDouble(coin, 0d) * 10;
        lanternRedisManager.updateLanternActivityTotalList(fromUid, toUid, luckyCount);
    }

    /**
     * 活动结束，下发奖励
     * @return Boolean
     */
    public Boolean lanternFestivalSendActivityReward() {
        try {
            String key =  LanternRedisEnum.lantern_activity_luck_total_list.getKey();
            Set<ZSetOperations.TypedTuple<Object>> top5List = redisManager.reverseRangeWithScores(key, 0L, 4L);
            if (CollectionUtil.isEmpty(top5List)) {
                log.info("lanternFestivalSendActivityReward top5List is null");
            }
            Map<String, Integer> rewardGiftMap = new HashMap<>();
            rewardGiftMap.put("HYLL_GIFT", 13140);
            rewardGiftMap.put("TXXT_GIFT", 5200);
            rewardGiftMap.put("LMXY2_GIFT", 3344);
            rewardGiftMap.put("YZCW_GIFT", 1314);
            rewardGiftMap.put("WG_GIFT", 999);

            List<String> giftList = new ArrayList<>();
            giftList.add("HYLL_GIFT");
            giftList.add("TXXT_GIFT");
            giftList.add("LMXY2_GIFT");
            giftList.add("YZCW_GIFT");
            giftList.add("WG_GIFT");

            int rank = 1;
            int giftIndex = 0;
            for (ZSetOperations.TypedTuple<Object> typedTuple : top5List) {
                String uidStr = Convert.toStr(typedTuple.getValue(),"");
                log.info("lanternFestivalSendActivityReward userId {}", uidStr);
                List<Long> uidList = AppUtil.openSplicUserId(uidStr);
                if (CollectionUtil.isEmpty(uidList)) {
                    continue;
                }
                // 判断前五福气值是否达到 5200000
                Long score = Convert.toLong(typedTuple.getScore(), 0L);
                if (score < 5200000) {
                    log.info("lanternFestivalSendActivityReward score is less 5200000 uid {} score {}", uidStr, score);
                    continue;
                }
                String giftKey = giftList.get(giftIndex++);
                Integer coin = rewardGiftMap.get(giftKey);
                // 下发奖励，两个人都需要
                Long userId = uidList.get(0);
                Long toUid = uidList.get(1);
                BaseParam baseParam1 = BaseParam.builder()
                        .uid(userId).appId(1L).unionId(null).build();
                SendPrizeDTO sendPrizeDto1 = SendPrizeDTO.builder()
                        .appId(1L)
                        .prizeValue(giftKey)
                        .prizeType(PrizeTypeEnum.PRIZE_GIFT.getCode())
                        .expiredType(ExpiredTypeEnum.EFFECTIVE_DAYS.getCode())
                        .prizeEffectiveDay(14)
                        .prizeNum(1)
                        .toUid(userId)
                        .scene(ChristmasBellConstant.ACTIVITY_CODE)
                        .fee(false)
                        .build();
                sendPrizeManager.sendSceneGift(baseParam1, sendPrizeDto1);
                // 下发第二个人
                BaseParam baseParam2 = BaseParam.builder()
                        .uid(toUid).appId(1L).unionId(null).build();
                SendPrizeDTO sendPrizeDto2 = SendPrizeDTO.builder()
                        .appId(1L)
                        .prizeValue(giftKey)
                        .prizeType(PrizeTypeEnum.PRIZE_GIFT.getCode())
                        .expiredType(ExpiredTypeEnum.EFFECTIVE_DAYS.getCode())
                        .prizeEffectiveDay(14)
                        .prizeNum(1)
                        .toUid(userId)
                        .scene(ChristmasBellConstant.ACTIVITY_CODE)
                        .fee(false)
                        .build();
                sendPrizeManager.sendSceneGift(baseParam2, sendPrizeDto2);
                // 小助手消息
                npcMessage(userId, String.format(TOTAL_REWARD_MSG, rank));
                npcMessage(toUid, String.format(TOTAL_REWARD_MSG, rank));
                rank++;
                log.info("lanternFestivalSendActivityReward userId {} toUid {} giftKey {}", userId, toUid, giftKey);
                // 埋点
                allActivityReceiveAward(userId, "rank", giftKey, Convert.toLong(coin), 2);
            }
        } catch (Exception e) {
            log.info("lanternFestivalSendActivityReward error {}", JSON.toJSONString(e));
        }
        return true;
    }

    /**
     * 通用埋点_活动任务完成
     *
     * @param uid      用户 id
     * @param taskType 任务类型
     */
    public void allActivityTaskFinish(Long uid, String taskType) {
        Map<String, Object> params = new HashMap<>();
        params.put("activity_type", "2025_lantern_festival");
        params.put("attribute_type", "platform_activity");
        params.put("task_type", Optional.ofNullable(taskType).orElse(""));
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_task_finish", params, ServicesNameEnum.ump_services.getCode());
    }

    /**
     * 通用埋点_ 活动领取奖励
     *
     * @param uid         用户 id
     * @param awardType   奖品类型
     * @param awardKey    奖品 key
     * @param awardAmount 奖品对应的金币价值
     * @param awardCount  获得奖励的个数
     */
    public void allActivityReceiveAward(Long uid, String awardType, String awardKey, Long awardAmount, Integer awardCount) {
        Map<String, Object> params = new HashMap<>();
        params.put("activity_type", "2025_lantern_festival");
        params.put("attribute_type", "platform_activity");
        params.put("award_type", Optional.ofNullable(awardType).orElse(""));
        params.put("award_key", Optional.ofNullable(awardKey).orElse(""));
        params.put("award_amount", Optional.ofNullable(awardAmount).orElse(0L));
        params.put("award_count", Optional.ofNullable(awardCount).orElse(0));
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_receive_award", params, ServicesNameEnum.ump_services.getCode());
    }


}
