package cn.yizhoucp.ump.biz.project.biz.util;

import cn.yizhoucp.starter.redis.manager.RedisManager;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.data.redis.core.ZSetOperations;

import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Description 榜单相关工具类  基于 redis的zset
 * @date 2021-03-24 14:17
 * @Source lanling-BoardUtil
 */
public class BoardUtil {

    private static RedisManager redisManager;

    private BoardUtil() {
    }

    ;

    public static void init(RedisManager redisManager) {
        BoardUtil.redisManager = redisManager;
    }

    /**
     * 获取榜单Top 条
     *
     * @param boardKey
     * @param topNum
     * @return
     */
    public static List<Pair<String, Double>> topBoard(String boardKey, Long topNum) {
        return topBoard(boardKey, 0L, topNum);
    }

    /**
     * 获取榜单指定位置的数量
     *
     * @param boardKey
     * @param start
     * @param end
     * @return
     */
    public static List<Pair<String, Double>> topBoard(String boardKey, Long start, Long end) {
        List<Pair<String, Double>> result = Lists.newArrayList();
        Set<ZSetOperations.TypedTuple<Object>> typedTuples = redisManager.reverseRangeByScoreWithScores(boardKey, 0, Double.MAX_VALUE, start, end);
        if (CollectionUtils.isEmpty(typedTuples)) {
            return result;
        }

        Iterator<ZSetOperations.TypedTuple<Object>> metaDataIterator = typedTuples.iterator();
        //数据处理成可用数据
        while (metaDataIterator.hasNext()) {
            // 获取下一个
            ZSetOperations.TypedTuple<Object> typedTuple = metaDataIterator.next();
            String key = (String) typedTuple.getValue();
            Double score = typedTuple.getScore();
            result.add(Pair.of(key, score));
        }
        return result;
    }

    /**
     * 获取指定排行的数据
     * @param boardKey
     * @param rank
     * @return
     */
    public static Double getScoreByRank(String boardKey, Long rank){
        Set<ZSetOperations.TypedTuple<Object>> typedTuples = redisManager.reverseRangeWithScores(boardKey, rank,rank);

        List<Pair<String, Double>> pairs = BoardUtil.handleZSetData(typedTuples);
        if (!CollectionUtils.isEmpty(pairs)){
            return pairs.get(0).getRight();
        }
        return 0.0;
    }

    public static Long getRankByKey(String boardKey, String key) {
        return redisManager.zSetRank(boardKey, key);
    }

    public static List<Pair<String,Double>> handleZSetData(Set<ZSetOperations.TypedTuple<Object>> typedTuples){
        List<Pair<String, Double>> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(typedTuples)){
            return result;
        }
        Iterator<ZSetOperations.TypedTuple<Object>> metaDataIterator = typedTuples.iterator();
        //数据处理成可用数据
        while (metaDataIterator.hasNext()) {
            // 获取下一个
            ZSetOperations.TypedTuple<Object> typedTuple = metaDataIterator.next();
            String key = (String) typedTuple.getValue();
            Double score = typedTuple.getScore();
            result.add(Pair.of(key, score));
        }
        return result;
    }

    /**
     * 用户加入榜单 默认为增量添加
     * @param boardKey
     * @param memberKey
     * @param score
     * @return
     */
    public static Double joinBoard(String boardKey,String memberKey,Double score){
        return joinBoard(boardKey,memberKey,score,false);
    }

    /**
     * 用户加入榜单 可配置是否覆盖分值
     * @param boardKey
     * @param memberKey
     * @param score
     * @param coverScore 是否覆盖分值
     * @return
     */
    public static Double joinBoard(String boardKey,String memberKey,Double score ,Boolean coverScore) {
        Double dataScore = score;
        if (!coverScore) {
            Double scoreOld = redisManager.score(boardKey, memberKey);
            if (null != scoreOld) {
                dataScore += scoreOld;
            }
        }
        Boolean aBoolean = redisManager.zSetAdd(boardKey, memberKey, dataScore, DateUtil.ONE_MONTH_SECOND, TimeUnit.SECONDS);
        if (Boolean.TRUE.equals(aBoolean)) {
            return dataScore;
        } else {
            return -1d;
        }
    }

}
