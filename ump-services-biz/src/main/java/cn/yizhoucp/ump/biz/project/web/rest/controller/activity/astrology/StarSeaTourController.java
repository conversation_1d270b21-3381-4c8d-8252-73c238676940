package cn.yizhoucp.ump.biz.project.web.rest.controller.activity.astrology;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.feign.activity.astrology.StarSeaTourFeignService;
import cn.yizhoucp.ump.api.vo.activity.base.GiftVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.astrology.starSeaTour.StarSeaTourIndexManager;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
public class StarSeaTourController {

    @Resource
    private StarSeaTourIndexManager starSeaTourIndexManager;

    @GetMapping("/api/inner/activity/star_sea_tour/task_star_sea_receive")
    public Result<GiftVO> taskStarSeaReceive(BaseParam param, String taskCode) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> starSeaTourIndexManager.taskStarSeaReceive(param, taskCode));
    }

    @GetMapping("/api/inner/activity/star_sea_tour/guide")
    public Result<Boolean> guide(BaseParam param, Integer type) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> starSeaTourIndexManager.guide(param, type));
    }

    @GetMapping("/api/inner/activity/star_sea_tour/task_guide_receive")
    public Result<Integer> taskGuideReceive(BaseParam param, String taskCode) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> starSeaTourIndexManager.taskGuideReceive(param, taskCode));
    }

}
