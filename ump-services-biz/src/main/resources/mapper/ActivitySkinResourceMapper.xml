<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.yizhoucp.ump.biz.project.biz.manager.activity_skin_admin.infrastructure.mapper.ActivitySkinResourceMapper">
    <resultMap id="BaseResultMap" type="cn.yizhoucp.ump.biz.project.biz.manager.activity_skin_admin.domin.model.ActivitySkinResource">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="parentKey" column="parent_key" jdbcType="VARCHAR"/>
        <result property="resourceKey" column="resource_key" jdbcType="VARCHAR"/>
        <result property="label" column="label" jdbcType="VARCHAR"/>
        <result property="resourceConfig" column="resource_config" jdbcType="VARCHAR"/>
        <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
        <result property="updatedAt" column="updated_at" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,parent_key,resource_key,
        label,resource_config,created_at,
        updated_at
    </sql>
</mapper>