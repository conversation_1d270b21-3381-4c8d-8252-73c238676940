<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.yizhoucp.ump.biz.project.dal.mybatis.mapper.LnUserSpiritAnimalMapper">

    <resultMap id="BaseResultMap" type="cn.yizhoucp.ump.biz.project.dal.mybatis.domain.LnUserSpiritAnimal">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="uniqueKey" column="unique_key" jdbcType="VARCHAR"/>
            <result property="nickname" column="nickname" jdbcType="VARCHAR"/>
            <result property="stamina" column="stamina" jdbcType="BIGINT"/>
            <result property="carryType" column="carry_type" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,unique_key,
        nickname,stamina,carry_type,
        create_time,update_time
    </sql>
</mapper>
