# ===================================================================
# Spring Cloud Config bootstrap configuration for the "dev" profile
# ===================================================================

spring:
    cloud:
        nacos:
            config:
                server-addr: nacos-headless.matrix-dev-base.svc.cluster.local:8848
                namespace: public
                shared-configs:
                    - dataId: serviceAppIdEnum.properties
                      group: APP_GROUP
                      refresh: true
                    - dataId: vestChannelEnum.properties
                      group: APP_GROUP
                      refresh: true



nacos:
    serverAddr: nacos-headless.matrix-dev-base.svc.cluster.local:8848
    configs:
        - cacheKey: serviceAppIdEnum
          dateId: serviceAppIdEnum.properties
          group: APP_GROUP
        - cacheKey: vestChannelEnum
          dateId: vestChannelEnum.properties
          group: APP_GROUP
