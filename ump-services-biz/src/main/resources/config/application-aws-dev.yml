# ===================================================================
# Spring Boot configuration for the "dev" profile.
#
# This configuration overrides the application.yml file.
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

cdn:
  resCdn: https://chatie-backend-cdn.myrightone.com
  sns: https://chatie-user-cdn.myrightone.com

environment: demo

spring:
  profiles:
    active: test
    include: swagger
  cloud:
    nacos:
      discovery:
        server-addr: nacos-headless.matrix-dev-base.svc.cluster.local:8848

  devtools:
    restart:
      enabled: true
    livereload:
      enabled: false # we use gulp + BrowserSync for livereload
  jackson:
    serialization.indent_output: true

  jpa:
    show-sql: true
    properties:
      hibernate.format_sql: true
      hibernate.type: trace
      hibernate.dialect: org.hibernate.dialect.MySQL5Dialect
      hibernate.jdbc.batch_size: 500
      hibernate.order_inserts: true
      hibernate.order_updates: true
      hibernate.physical_naming_strategy: org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy

  messages:
    cache-seconds: 2
  thymeleaf:
    cache: false
  redis:
    host: redis-matrix-master.database.svc.cluster.local
    password: cjnM0pfCx0
    port: 6379
    pool:
      max-active: 8
      max-wait: 1
      max-idle: 8
      min-idle: 0
    timeout: 0

  datasource:
    hikari:
      driver-class-name: com.mysql.jdbc.Driver
      jdbc-url: ***************************************************************************************************************************************************************************
      username: root
      password: KNNmVQQhA0
      connection-init-sql: "SET NAMES 'utf8mb4' COLLATE 'utf8mb4_unicode_ci'"
      connection-test-query: SELECT 1
      connection-timeout: 5000
      idle-timeout: 180000
      minimum-idle: 10
      maximum-pool-size: 100
      validation-timeout: 5000
      pool-name: ump-services-datasource-pool

logging:
  config: classpath:logback-spring-dev.xml

# ===================================================================
# JHipster specific properties
# ===================================================================
jhipster:
  security:
    encrypt:
      key: dbb6e7c46b5fc376fb8ceed310776523
    authentication:
      jwt:
        secret: my-secret-token-to-change-in-production
        # Token is valid 24 hours
        tokenValidityInSeconds: 86400
  mail: # specific JHipster mail property, for standard properties see MailProperties
    from: headstartServer@localhost
    baseUrl: # keep empty to use the server's default URL
  metrics: # DropWizard Metrics configuration, used by MetricsConfiguration
    jmx.enabled: true
    graphite:
      enabled: false
      host: localhost
      port: 2003
      prefix: AssetShowServer
    logs: # Reports Dropwizard metrics in the logs
      enabled: false
      reportFrequency: 60 # in seconds
  logging:
    logstash: # Forward logs to logstash over a socket, used by LoggingConfiguration
      enabled: false
      host: localhost
      port: 5000
      queueSize: 512
    spectator-metrics: # Reports Spectator Circuit Breaker metrics in the logs
      enabled: false
      # edit spring.metrics.export.delay-millis to set report frequency

rocketmq:
  name-server: http://name-server-service.rocketmq.svc.cluster.local:9876
  producer:
    group: GID_UMP_GROUP
    send-message-timeout: 3000 # 发送消息超时时长
  consumer:
    name: GID_UMP_
    global: GROUP

### 0.5 added  redisson
redission:
  address: redis://redis-matrix-master.database.svc.cluster.local:6379
  password: cjnM0pfCx0

# 与风控对接相关配置
right:
  base_audit_url: https://audit-server.dev.myrightone.com
  audit_report_url: /api/audit/save
  base_risk_url: http://prism-server-dev.prism-dev.svc.cluster.local
  async_scan_url: /api/prismData/async
  sync_scan_url: /api/prismData/sync
  scan_result_url: /api/prismData/result/
  do_next_action_url: /api/prismData/doNextAction/
  base_feedback_url: https://huofeng.cp-dev.yizhoucp.cn/
  callback_properties:
    - run_env: dev01
      callback_url: http://gateway.matrix-dev.svc.cluster.local/callback/lanling/risk
    - run_env: dev02
      callback_url: http://gateway.matrix-dev.svc.cluster.local/callback/lanling/risk
    - run_env: dev03
      callback_url: http://gateway.matrix-dev.svc.cluster.local/callback/lanling/risk

# 活动地址
activity:
  h5: https://h5-test.myrightone.com

feign:
  client:
    config:
      default:
        ReadTimeout: 30000
        ConnectTimeout: 30000
