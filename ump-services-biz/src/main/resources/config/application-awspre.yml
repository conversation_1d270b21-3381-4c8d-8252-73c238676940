cdn:
  resCdn: https://chatie-backend-cdn.myrightone.com
  sns: https://chatie-user-cdn.myrightone.com

environment: awspre

spring:
  cloud:
    nacos:
      discovery:
        server-addr: nacos.nacos-v2.svc.cluster.local:8848

  profiles:
    active: awspre
    include: swagger
  devtools:
    restart:
      enabled: true
    livereload:
      enabled: false # we use gulp + BrowserSync for livereload
  jackson:
    serialization.indent_output: true

  jpa:
    show-sql: false
    properties:
      hibernate.format_sql: true
      hibernate.type: trace
      hibernate.dialect: org.hibernate.dialect.MySQL5Dialect
      hibernate.jdbc.batch_size: 500
      hibernate.order_inserts: true
      hibernate.order_updates: true
      hibernate.physical_naming_strategy: org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy

  messages:
    cache-seconds: 2
  thymeleaf:
    cache: false

  redis:
    host: redis-matrix-prod
    port: 6379
    pool:
      max-active: 8
      max-wait: 1
      max-idle: 8
      min-idle: 0
    timeout: 0

  datasource:
    hikari:
      driver-class-name: com.mysql.jdbc.Driver
      jdbc-url: **********************************************************************************************************************************************
      connection-init-sql: "SET NAMES 'utf8mb4' COLLATE 'utf8mb4_unicode_ci'"
      connection-test-query: SELECT 1
      connection-timeout: 5000
      idle-timeout: 180000
      minimum-idle: 10
      maximum-pool-size: 100
      validation-timeout: 5000
      pool-name: ump-services-datasource-pool
    hikari2:
      driver-class-name: com.mysql.jdbc.Driver
      jdbc-url: **********************************************************************************************************************************************
      connection-init-sql: "SET NAMES 'utf8mb4' COLLATE 'utf8mb4_unicode_ci'"
      connection-test-query: SELECT 1
      connection-timeout: 5000
      idle-timeout: 180000
      minimum-idle: 10
      maximum-pool-size: 100
      validation-timeout: 5000
      pool-name: ump-services-datasource-pool-2
  kafka:
    bootstrap-servers: kafka-broker.matrix-pre.svc.cluster.local:9092
    producer:
      # 重试次数
      retries: 0
      # 应答级别:多少个分区副本备份完成时向生产者发送ack确认(可选0、1、all/-1)
      acks: 1
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    consumer:
      # 默认消费者组
      group-id: user-aws-group
      # 最早未被当前消费组消费的 offset
      auto-offset-reset: earliest
      # 批量一次最大拉取数据量
      max-poll-records: 1
      # 自动提交
      auto-commit-interval: 1000
      enable-auto-commit: true

feign:
  client:
    config:
      default:
        ReadTimeout: 30000
        ConnectTimeout: 30000

logging:
#  config: classpath:logback-spring-awsprod.xml
  config: classpath:logback-spring-awspre.xml

# ===================================================================
# JHipster specific properties
# ===================================================================
jhipster:
  security:
    encrypt:
      key: dbb6e7c46b5fc376fb8ceed310776523
    authentication:
      jwt:
        secret: QGwZNLBq3ihsHadtaAWMpTwg
        # Token is valid 24 hours
        tokenValidityInSeconds: 86400
  mail: # specific JHipster mail property, for standard properties see MailProperties
    from: headstartServer@localhost
    baseUrl: # keep empty to use the server's default URL
  metrics: # DropWizard Metrics configuration, used by MetricsConfiguration
    jmx.enabled: true
    graphite:
      enabled: false
      host: localhost
      port: 2003
      prefix: AssetShowServer
    logs: # Reports Dropwizard metrics in the logs
      enabled: false
      reportFrequency: 60 # in seconds
  logging:
    logstash: # Forward logs to logstash over a socket, used by LoggingConfiguration
      enabled: false
      host: localhost
      port: 5000
      queueSize: 512
    spectator-metrics: # Reports Spectator Circuit Breaker metrics in the logs
      enabled: false
      # edit spring.metrics.export.delay-millis to set report frequency

rocketmq:
  name-server: http://rocketmq-name-server:9876
  producer:
    group: GID_UMP_GROUP
    send-message-timeout: 3000 # 发送消息超时时长
  consumer:
    name: GID_UMP_
    global: GROUP

redission:
  address: redis://redis-matrix-prod:6379


# 活动地址
activity:
  h5: https://h5.nuan.chat

# 与风控对接相关配置
right:
  base_audit_url: https://audit-server.myrightone.com
  audit_report_url: /api/audit/save
  base_risk_url: http://prism-server-api.prism-server.svc.cluster.local
  async_scan_url: /api/prismData/async
  sync_scan_url: /api/prismData/sync
  scan_result_url: /api/prismData/result/
  do_next_action_url: /api/prismData/doNextAction/
  base_feedback_url: http://api.myrightone.com/
  callback_properties:
    - run_env: prod
      callback_url: http://gateway.matrix.svc.cluster.local/callback/lanling/risk
