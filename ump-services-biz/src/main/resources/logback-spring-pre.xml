<?xml version="1.0" encoding="UTF-8"?>

<configuration scan="true">
    <property name="PATTERN" value="%-5level [%d{HH:mm:ss.SSS}] [%X{traceId}] [%X{uid}] [%logger{36} - %M - %L] - %msg%n"/>
    <!-- 开发环境 -->
    <springProfile name="pre">
        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder charset="UTF-8">
                <pattern>${PATTERN}</pattern>
            </encoder>
        </appender>
        <logger name="pro.moba.bs.asset" level="INFO"/>
        <logger name="javax.activation" level="WARN"/>
        <logger name="javax.mail" level="WARN"/>
        <logger name="javax.xml.bind" level="WARN"/>
        <logger name="ch.qos.logback" level="WARN"/>
        <logger name="com.codahale.metrics" level="WARN"/>
        <logger name="com.netflix" level="WARN"/>
        <logger name="com.netflix.discovery" level="INFO"/>
        <logger name="com.ryantenney" level="WARN"/>
        <logger name="com.sun" level="WARN"/>
        <logger name="com.zaxxer" level="WARN"/>
        <logger name="io.undertow" level="WARN"/>
        <logger name="org.apache" level="WARN"/>
        <logger name="org.apache.catalina.startup.DigesterFactory" level="OFF"/>
        <logger name="org.bson" level="WARN"/>
        <logger name="org.hibernate.validator" level="WARN"/>
        <logger name="org.hibernate" level="WARN"/>
        <logger name="org.hibernate.ejb.HibernatePersistence" level="OFF"/>
        <logger name="org.springframework" level="WARN"/>
        <logger name="org.springframework.web" level="WARN"/>
        <logger name="org.springframework.security" level="WARN"/>
        <logger name="org.springframework.cache" level="WARN"/>
        <logger name="org.thymeleaf" level="WARN"/>
        <logger name="org.xnio" level="WARN"/>
        <logger name="springfox" level="WARN"/>
        <logger name="sun.rmi" level="WARN"/>
        <logger name="sun.rmi.transport" level="WARN"/>
        <logger name="org.mongodb.driver.cluster" level="INFO" />
        <logger name="org.springframework.social.wechat" level="DEBUG" />
        <logger name="RocketmqClient" level="ERROR"/>
        <logger name="RocketmqRemoting" level="ERROR"/>
        <root level="DEBUG">
            <appender-ref ref="CONSOLE"/>
        </root>
    </springProfile>
</configuration>
